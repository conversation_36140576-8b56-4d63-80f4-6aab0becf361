import logging

import click

from utils import (
    fetch_credentials,
    fetch_vault_client,
    generate_config_and_take_backup,
)


logging.basicConfig(format='%(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)


@click.command()
@click.option('--env', type=click.Choice(['prod', 'preprod', 'stage']), help='Infra environment', required=True)
def generate_config(env):
    vault_client = fetch_vault_client(env=env)
    credentials = fetch_credentials(client=vault_client, service="sonar", component="api")

    generate_config_and_take_backup(
        template_file_path="kubernetes/{}/default.yml".format(env),
        output_file_path="{}-config.yml".format(env),
        backup_folder="config-backups",
        credentials=credentials,
    )

    generate_config_and_take_backup(
        template_file_path="kubernetes/common.yml",
        output_file_path="{}-common.yml".format(env),
        backup_folder="config-backups",
        credentials=credentials,
    )

if __name__ == "__main__":
    generate_config()
