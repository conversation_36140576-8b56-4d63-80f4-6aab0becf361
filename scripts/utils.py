import difflib
import logging
import os
import os.path
import sys
from datetime import datetime

import hvac
import ruamel.yaml
from colorama import Back, Fore, Style, init
from jinja2 import Template


logger = logging.getLogger(__name__)


def fetch_vault_client(env):
    if env == 'preprod':
        env = 'prod'
    client = hvac.Client(url="https://vault-ui-{}.grofer.io".format(env))
    client.auth.github.login(token=os.environ.get("VAULT_AUTH_GITHUB_TOKEN"))
    if not client.is_authenticated():
        raise ValueError("Could not authenticate to <PERSON>ault")
    return client


def render_yaml_friendly_key(input_key):
    return input_key.lower().replace("/", "_").replace("-", "_").replace(".", "_")


def fetch_credentials(client, service, component, prefix=""):
    vault_path = "data/services/{}/{}/{}".format(service, component, prefix)

    if vault_path.endswith("/"):
        credentials = {}
        for key in client.list(vault_path)["data"]["keys"]:
            key_creds = fetch_credentials(client, service, component, prefix+key)
            credentials.update(key_creds)
        return credentials
    else:
        credentials = {}
        for key, value in client.read(vault_path)["data"].items():
            cred_key = render_yaml_friendly_key(prefix + "/" + key)
            credentials[cred_key] = value
        return credentials


def color_diff(diff):
    for line in diff:
        if line.startswith('+'):
            yield Fore.GREEN + line + Fore.RESET
        elif line.startswith('-'):
            yield Fore.RED + line + Fore.RESET
        elif line.startswith('^'):
            yield Fore.BLUE + line + Fore.RESET
        else:
            yield line


def generate_config_and_take_backup(template_file_path, output_file_path, backup_folder, credentials):
    backup_file = None

    if not os.path.isdir(backup_folder):
        os.makedirs(backup_folder)

    with open(template_file_path, "r") as f:
        t = Template(f.read())
        helm_config_str = t.render(**credentials)

    helm_config = ruamel.yaml.round_trip_load(helm_config_str, preserve_quotes=True)

    if os.path.exists(output_file_path):
        now = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name, extension = os.path.splitext(output_file_path)
        backup_file = "{}/{}.{}.{}".format(backup_folder, file_name, now, extension)
        logger.info("Overwriting existing config. Taking backup : {}".format(backup_file))
        os.rename(output_file_path, backup_file)

    with open(output_file_path, "w") as f:
        ruamel.yaml.round_trip_dump(helm_config, f)

    if backup_file:
        with open(output_file_path, "r") as f:
            with open(backup_file, "r") as f2:
                diff = difflib.unified_diff(f2.readlines(), f.readlines(), fromfile=backup_file, tofile=output_file_path)
                diff = list(color_diff(diff))
                if diff:
                    logger.info("".join(diff))
                else:
                    logger.info("No changes detected")
