apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "chart.fullname" . }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
    {{- toYaml .Values.podAnnotations | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "chart.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ .Values.aws.serviceAccountName }}
      containers:
      - name: backend
        image: {{ $.Values.backendImage }}
        envFrom:
        - secretRef:
            name: {{ include "chart.fullname" . -}}-secret
        env:
        {{- range .Values.backend.env }}
        - name: {{ .name }}
          {{- if .value }}
          value: {{ .value | quote }}
          {{- else if .valueFrom }}
          valueFrom: {{ toYaml .valueFrom | nindent 12 }}
          {{- end }}
        {{- end }}
        livenessProbe:
          httpGet:
            path: /docs
            port: 8080
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /docs
            port: 8080
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 20
          failureThreshold: 3
        resources:
          {{- toYaml .Values.backend.resources | nindent 10 }}
      - name: frontend
        image: {{ $.Values.frontendImage }}
        envFrom:
        - secretRef:
            name: {{ include "chart.fullname" . -}}-secret
        ports:
        - name: http
          containerPort: 80
        livenessProbe:
          httpGet:
            path: /login
            port: http
        readinessProbe:
          httpGet:
            path: /login
            port: http
        resources:
          {{- toYaml .Values.backend.resources | nindent 10 }}
