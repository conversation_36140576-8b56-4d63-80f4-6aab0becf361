apiVersion: v1
kind: Service
metadata:
  name: {{ include "chart.fullname" . }}
  labels:
    {{- include "chart.labels" . | nindent 4 }}
{{- with .Values.service.annotations }}
  annotations:
{{- toYaml . | nindent 4 }}
{{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
  - port: {{ .Values.service.port }}
    targetPort: http
    protocol: TCP
    name: https
  - port: 80
    targetPort: http
    name: http
  selector:
    {{- include "chart.selectorLabels" . | nindent 4 }}
