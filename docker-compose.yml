version: "3.8"

services:

  db:
    image: public.ecr.aws/zomato/postgres:12
    volumes:
    - app-db-data:/var/lib/postgresql/data
    user: postgres
    env_file:
    - .env
    ports:
    - 5432:5432
    healthcheck:
      test: ["CMD-SHELL", "pg_isready", "--quiet"]
      interval: 1s
      timeout: 5s
      retries: 10

  # pgadmin:
  #   image: dpage/pgadmin4
  #   restart: always
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: root
  #   ports:
  #   - 5050:80

  migrations:
    build:
      context: ./backend
      args:
        ENVIRONMENT: migration
    env_file:
    - .env
    depends_on:
      db:
        condition: service_healthy
    command: >
      bash -c 'cd /app && alembic upgrade head'

  backend:
    build:
      context: ./backend
    command: ["ddtrace-run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"]
    ports:
    - 8080:8080
    volumes:
    - ./backend/src:/app/src
    depends_on:
    - migrations
    env_file:
    - .env
    environment:
      OAUTHLIB_RELAX_TOKEN_SCOPE: 1 # To Fix : Warning: Scope has changed
      OAUTHLIB_INSECURE_TRANSPORT: 1 # To Fix : Warning: OAuth2 must utilize https

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    volumes:
    - ./frontend/src/:/app/src
    ports:
    - 3000:3000

  redis:
    image: public.ecr.aws/zomato/redis:6.2
    ports:
    - 6379:6379
    volumes:
    - redis_data:/data

  # nginx:
  #   build:
  #     context: ./frontend
  #     dockerfile: Dockerfile
  #   volumes:
  #   - ./frontend/:/app/
  #   ports:
  #   - 80:80

  pytest:
    build:
      context: ./backend
      dockerfile: Dockerfile.pytest
    volumes:
      - ./backend/src:/app/src
    env_file:
      - .env
    depends_on:
      - db
      - redis

volumes:
  app-db-data:
  redis_data: