<template>
  <div class="md:flex">
    <Navigation v-if="!hideNavigation" @showNav="showNav" @hideNav="hideNav" />

    <div class="w-full" :class="{ 'opacity-100': !navOpen, 'opacity-60': navOpen }">
      <div v-if="canMount">
        <router-view />
      </div>
      <div v-else>
        <DotLoader />
      </div>
    </div>
  </div>
</template>

<script>

import Navigation from "./components/Navigation.vue";
import Footer from "./components/Footer.vue";
import { mapState, mapActions } from 'pinia'
import { useUserStore } from './stores/users'
import DotLoader from "./components/DotLoader.vue";
import { fetchStoreToCityMapping } from "./utils/storeMappings";
import { getUserAccessMapping } from "./utils/utils";
import isEmpty from "lodash.isempty";
import { TenantMapping } from "./constants";

export default {
  name: "app",

  components: {
    Navigation,
    Footer,
    DotLoader
  },

  data() {
    return {
      hideNavigation: false,
      navOpen: false,
      canMount: false,
    };
  },


  computed: {
    isNavigationVisible() {
      return this.hideNavigation;
    },

    ...mapState(useUserStore, ['isLoggedIn', 'getActiveTenant']),
  },

  methods: {
    checkRoute() {
      if (this.$route.query["hideNav"] && this.$route.query["hideNav"] == 'true') {
        this.hideNavigation = true;
        return;
      }

      if (this.$route.name.includes("Login") || this.$route.name.includes("Logout")) {
        this.hideNavigation = true;
        return;
      }

      if (!this.isLoggedIn) {
        this.handleLogin();
      }

      if (this.$route.name !== 'Google SSO Callback') {
        let activeTenant = this.$route.path.includes('bistro') ? TenantMapping.bistro : TenantMapping.blinkit
        this.updateActiveTenant(activeTenant)
      }

      this.hideNavigation = false;
    },

    showNav() {
      this.navOpen = true;
    },

    hideNav() {
      this.navOpen = false;
    },

    async refreshStoreCityMapping() {
      await fetchStoreToCityMapping(true);
    },

    handleLogin() {
      if (this.getActiveTenant === TenantMapping.bistro) {
        this.$router.replace("/bistro/login");
      } else {
        this.$router.replace("/login");
      }
    },

    ...mapActions(useUserStore, ['updateActiveTenant']),
  },

  watch: {
    $route() {
      this.checkRoute();
    },

    isNavigationVisible(val) {
      if (val) this.navOpen = false;
    }
  },

  async mounted() {
    let userAccessMapping = getUserAccessMapping();
    if (!isEmpty(userAccessMapping)) {
      try {
        await this.refreshStoreCityMapping();
      } catch (error) {
        let status = error?.response?.status;
        if (status && status === 403) {
          this.canMount = true;
          this.logout();
          console.log("Unauthenticated")

          this.handleLogin();
        }
      };
    }
    /** else: will go to any of the pages where we are already fetching the list, 
    so skip here (or it'll get stuck here because the user access mapping is not set) */
    this.canMount = true;
  },
}

</script>
