import { apiV2 } from "../../api";
import { PROMO_METRIC_LIST } from "../../utils/metrics";

// todo: need to update the metrics
export const ORDER_METRIC_BREAKDOWN = {
  base: {
    isDefaultOpen: true,
    showSearchSpike: true,
    showOrdersPerMinuteBtn: true,
    icon: "pi pi-chart-line",
    title: "Business",
    metrics: [
      {
        metricList: [
          "gmv", // GMV
          "order_count", // Cart Volume
          "aov", // AOV
          "platform_cost", // Customer Paid Charges (includes Night Charge now)
          "handling_charge", // Handling Charge
          "small_cart_charge", // Small Cart Charge
          "delivery_cost", // Delivery Cost
          "convenience_charge", // Convenience Charge
          "night_charge", // Night Charge
          "slot_charge", // Surge Charge,
          "new_transacting_users_count", // New Transacting Customers
        ],
        currDayFetchApi: apiV2.fetchAllBaseMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllBaseMetrics,
      },
      // { // drop promo metrics
      //   metricList: PROMO_METRIC_LIST,
      //   currDayFetchApi: apiV2.fetchAllBaseMetrics,
      //   previousDayFetchApi: apiV2.fetchCustomDateAllBaseMetrics,
      // },
      {
        // ATC %, C2Co %
        currDayFetchApi: apiV2.fetchAllFunnelMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllFunnelMetrics,
      },
      {
        // "Daily Active Users",
        // "DAU Conversion Percentage",
        // "Priority Customers %",
        currDayFetchApi: apiV2.fetchAllDauMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllDauMetrics,
      },
    ],
    metricsToShow: [
      "GMV",
      "Cart Volume",
      "AOV",
      "Daily Active Users",
      "DAU Conversion Percentage",
      "ATC %",
      "C2Co %",
      "New Transacting Customers",
      "Customer Paid Charges",
    ],
  },

  serviceability: {
    title: "Serviceability",
    icon: "pi pi-cog",
    metrics: [
      {
        metricList: [
          "billed_to_assigned", // Direct Handover %
          "surge_shown_carts_percentage", // Surge Checkouts %
          "surge_paid_carts_percentage", // Surge Paid Carts %
          "batched_orders", // Batched Order %
          "rain_order_percentage", // Rain Order %
        ],
        currDayFetchApi: apiV2.fetchAllBaseMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllBaseMetrics,
      },
      {
        // "Demand Based Block %",
        // "Unserviceable DAU %",
        // "Surge Charge",
        // "Surge Seen %",
        // "Picker Surge Seen %",
        // "Rain Surge Seen %",
        currDayFetchApi: apiV2.fetchAllSurgeSeenMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllSurgeSeenMetrics,
      },
      {
        // "Rider Surge Seen %",
        currDayFetchApi: apiV2.fetchAllRiderMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllRiderMetrics,
      },
      {
        // "Daily Active Users",
        // "DAU Conversion Percentage",
        // "Priority Customers %",
        //  Unserviceable DAU %
        currDayFetchApi: apiV2.fetchAllDauMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllDauMetrics,
      },
    ],
    metricsToShow: [
      "Direct Handover %",
      "Surge Seen %",
      "Surge Checkouts %",
      "Rider Login hrs",
      "New Riders",
      "Batched Order %",
      "Rain Order %",
      "Demand Based Block %",
      "Unserviceable DAU %",
    ],
  },

  slas: {
    title: "SLAs",
    icon: "pi pi-clock",
    metrics: [
      {
        metricList: [
          "order_count", // Cart Volume > gives "OPD Per Store","OPD Per Store",
          "free_delivery_percentage", // Free DC %
          "percentage_orders_delivered_in_10mins", // %Delivery < 10mins
          "percentage_orders_delivered_in_15mins", // %Delivery < 15mins
          "percentage_orders_delivered_in_30mins", // %Delivery < 30mins
          "cancellation_percentage", // Order Cancellation (moved from Business)
          "opd_per_store", // OPD Per Store
          "active_stores_count", // Active Stores Count
          "checkout_to_picker_assigned", // %Picker assigned 10 secs
        ],
        currDayFetchApi: apiV2.fetchAllBaseMetrics,
        previousDayFetchApi: apiV2.fetchCustomDateAllBaseMetrics,
      },
    ],
    metricsToShow: [
      "%Delivery < 10mins",
      "%Delivery < 15mins",
      "%Delivery < 30mins",
      "Order Cancellation",
      "%Picker assigned 10 secs",
      "Free DC %",
      "OPD Per Store",
      "Active Stores Count",
    ],
  },
};

export const FLATTENED_HOURLY_METRICS = [
  {
    isDefaultOpen: true,
    apiKey: "order_metrics_1",
    metricKey: "Cart Volume",
    icon: "pi pi-shopping-cart",
  },
  { apiKey: "order_metrics_1", metricKey: "AOV", icon: "pi pi-money-bill" },
  {
    apiKey: "store_hau_metrics",
    metricKey: "Hourly Active Users",
    icon: "pi pi-clock",
  },
  {
    apiKey: "store_hau_metrics",
    metricKey: "HAU Conversion Percentage",
    icon: "pi pi-chart-line",
  },
  { apiKey: "funnel_metrics", metricKey: "ATC %", icon: "pi pi-shopping-cart" },
  { apiKey: "funnel_metrics", metricKey: "C2Co %", icon: "pi pi-check-circle" },
  {
    apiKey: "order_metrics_1",
    metricKey: "Surge Checkouts %",
    icon: "pi pi-chart-line",
  },
  {
    apiKey: "surge_seen_metrics",
    metricKey: "Surge Seen %",
    icon: "pi pi-chart-bar",
  },
  {
    apiKey: "pan_india_rider_metrics",
    metricKey: "Rider Login hrs",
    icon: "pi pi-user-plus",
  },
  {
    apiKey: "order_metrics_2",
    metricKey: "Direct Handover %",
    icon: "pi pi-arrow-right",
  },
  {
    apiKey: "order_metrics_2",
    metricKey: "%Picker assigned 10 secs",
    icon: "pi pi-user",
  },
  { apiKey: "order_metrics_2", metricKey: "Rain Order %", icon: "pi pi-cloud" },
];
