import { api } from "../../api";

export const ORDER_METRIC_BREAKDOWN = {
  base: {
    isDefaultOpen: true,
    icon: "pi pi-chart-line",
    title: "Business",
    metrics: [
      {
        metricKeyForParams: "base-metric",
        metricList: [
          "gmv", // GMV
          "order_count", // Cart Volume
          "aov", // AOV
          "unique_carts", // Transacting Customers,
          "asp",
          "ipc",
          "total_items_sold",
        ],
        currDayFetchApi: api.fetchProductMetrics,
        previousDayFetchApi: api.fetchProductMetrics,
      },
    ],
    metricsToShow: [
      "GMV",
      "Cart Volume",
      "AOV",
      "Cart Pen",
      "Total Items Sold",
      "Average Selling Price",
      "IPC",
    ],
  },
  customer: {
    title: "Customer",
    icon: "pi pi-users",
    metrics: [
      {
        metricKeyForParams: "base-metric",
        metricList: [
          "transacting_users_count", // Transacting Customers
          "new_transacting_users_count", // New Transacting Customers //? will be fetched separately, as we are breaking metricList by 2
        ],
        currDayFetchApi: api.fetchProductMetrics,
        previousDayFetchApi: api.fetchProductMetrics,
      },
    ],
    metricsToShow: ["New Transacting Customers", "Transacting Customers"],
  },
  complaints: {
    title: "Complaints",
    icon: "pi pi-exclamation-circle",
    metrics: [
      {
        metricKeyForParams: "base-metric",
        metricList: ["canceled_quantity"],
        currDayFetchApi: api.fetchProductMetrics,
        previousDayFetchApi: api.fetchProductMetrics,
      },
      {
        metricKeyForParams: "complaints",
        currDayFetchApi: api.fetchProductComplaints,
        previousDayFetchApi: api.fetchProductComplaints,
      },
    ],
    metricsToShow: ["Cancelled Quantity", "All Complaints"],
  },
  promos: {
    title: "Promos",
    icon: "pi pi-tag",
    metrics: [
      {
        metricKeyForParams: "freebie",
        currDayFetchApi: api.fetchFreebieMetrics,
        previousDayFetchApi: api.fetchYesterdayFreebieMetrics,
      },
    ],
    metricsToShow: ["Freebie %"],
  },
};
