export const COLLAPSIBLE_METRIC_LIST = [
  {
    isDefaultOpen: true,
    apiKey: "order_metrics",
    metricKey: "Cart Volume",
    icon: "pi pi-shopping-cart",
  },
  {
    apiKey: "dau_metrics",
    metricKey: "DAU Conversion Percentage",
    icon: "pi pi-chart-line",
  },
  {
    apiKey: "order_metrics",
    metricKey: "GMV",
    icon: "pi pi-money-bill",
  },
  {
    apiKey: "order_metrics",
    metricKey: "AOV",
    icon: "pi pi-money-bill",
  },
  {
    apiKey: "dau_metrics",
    metricKey: "Daily Active Users",
    icon: "pi pi-users",
  },
  {
    apiKey: "order_metrics",
    metricKey: "Surge Checkouts %",
    icon: "pi pi-arrow-up",
  },
  {
    apiKey: "dau_metrics",
    metricKey: "Demand Based Block %",
    icon: "pi pi-times-circle",
  },
  {
    apiKey: "dau_metrics",
    metricKey: "Unserviceable DAU %",
    icon: "pi pi-ban",
  },
  {
    apiKey: "dau_metrics",
    metricKey: "Express OOH Based Block %",
    icon: "pi pi-clock",
  },
  {
    apiKey: "dau_metrics",
    metricKey: "Express Manual Based Block %",
    icon: "pi pi-lock",
  },
  {
    apiKey: "rider_metrics",
    metricKey: "Rider Login hrs",
    icon: "pi pi-clock",
  },
  {
    apiKey: "new_rider_metrics",
    metricKey: "New Riders",
    icon: "pi pi-user-plus",
  },
  {
    apiKey: "order_metrics",
    metricKey: "Rain Order %",
    icon: "pi pi-cloud",
  },
];

export const ANALYTICS_ORDER_METRICS = [
  {
    key: "Cart Volume",
    icon: "pi pi-shopping-cart",
    iconStyle: { color: "blue" },
  },
  { key: "GMV", icon: "pi pi-money-bill", iconStyle: { color: "green" } },
  {
    key: "Daily Active Users",
    icon: "pi pi-users",
    iconStyle: { color: "orange" }, // More intuitive color for user-related metrics
  },
  {
    key: "DAU Conversion Percentage",
    icon: "pi pi-percentage",
    iconStyle: { color: "purple" }, // Better contrast for conversion metrics
  },
];
