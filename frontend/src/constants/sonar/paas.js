import { api } from "../../api";
import { PASS_METRIC_LIST } from "../../utils/metrics";

// PAAS Metric Groups configuration
export const PAAS_METRIC_GROUPS = {
  business: {
    isDefaultOpen: true,
    title: "Business",
    icon: "pi pi-print",
    metrics: [
      {
        metricList: PASS_METRIC_LIST,
        fetchApi: api.fetchPaasMetrics,
      },
      {
        metricKey: "complaints",
        fetchApi: api.fetchPaasComplaints,
      },
    ],
    metricsToShow: [
      "Total Orders",
      "Cart Volume",
      "Cancellations",
      "Complaints",
    ],
  },
};
