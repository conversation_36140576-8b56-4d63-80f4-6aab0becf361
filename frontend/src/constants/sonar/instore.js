import { api, apiV2 } from "../../api";
import {
    INSTORE_ORDER_METRIC_LIST, INSTORE_ALL_ACTIVE_TIME_METRICS_LIST,
} from "../../utils/metrics.js";

export const INSTORE_METRIC_BREAKDOWN = {
  business: {
    isDefaultOpen: true,
    icon: "pi pi-chart-line",
    title: "Business",
    metrics: [
      {
        metricList: INSTORE_ORDER_METRIC_LIST, 
        currDayFetchApi: api.fetchAllOrderMetricsForInstore,
        previousDayFetchApi: apiV2.fetchAllOrderMetricsForInstore,
      },
      {
        metricList: ["true_fill_rate"],
        currDayFetchApi: api.fetchAllOrderMetricsForInstore,
        previousDayFetchApi: apiV2.fetchAllOrderMetricsForInstore,
      },
    ],
    key: "business",
    metricsToShow: [
      "Cart Volume", 
      "Unique SKU/Order", 
      "Picking Time Per Item", 
      "IPO", 
      "Total Orders", 
      "Fill Rate %", 
      "Direct Handover %",
      "True Fill Rate %"
    ],
  },

serviceability: {
  isDefaultOpen: false,
  icon: "pi pi-cog",
  title: "Serviceability", 
  metrics: [
    {
      currDayFetchApi: api.fetchAllDauMetricsForInstore,
      previousDayFetchApi: apiV2.fetchAllDauMetricsForInstore,
    },
    {
      metricList: ["total_complaints"],
      currDayFetchApi: api.fetchAllComplaintsMetricsForInstore,
      previousDayFetchApi: apiV2.fetchAllComplaintsMetricsForInstore,
    },
    {
      metricList: ["picker_surge_orders", "rider_plus_picker_surge_orders"],
      currDayFetchApi: api.fetchAllOrderMetricsForInstore,
      previousDayFetchApi: apiV2.fetchAllOrderMetricsForInstore,
    },
  ],
  key: "serviceability",
  metricsToShow: [
    "Demand Based Block %", 
    "Unserviceable DAU %",
    "Picker Surge Orders %",
    "Both Surge Orders %",
    "Complaints %"
  ],
},

slas: {
  isDefaultOpen: false,
  icon: "pi pi-clock",
  title: "SLAs",
  metrics: [
    {
      metricList: ["percent_instore_sla_less_than_150_sec", "checkout_to_picker_assigned", "picker_assigned_to_picking_start"],
      currDayFetchApi: api.fetchAllOrderMetricsForInstore,
      previousDayFetchApi: apiV2.fetchAllOrderMetricsForInstore,
    },
    {
      metricList: INSTORE_ALL_ACTIVE_TIME_METRICS_LIST, 
      currDayFetchApi: api.fetchAllActiveTimeMetricsForInstore,
      previousDayFetchApi: apiV2.fetchAllActiveTimeMetricsForInstore,
    },
  ],
  key: "slas",
  metricsToShow: [
    "Instore SLA % < 2.5 mins",
    "%Picker assigned 10 secs",
    "Picking Start % (5 secs)",
    "Total Active Hrs", 
    "Picker Active Time %",
    "Putter Active Time %", 
    "Auditor Active Time %", 
    "FNV Active Time %",
    "IPH", 
    "OPH", 
    "New Manpower", 
    "On-Demand Active Hours"
  ],
},
};

export const FLATTENED_INSTORE_HOURLY_METRICS = [
  // Order Metrics
  {
    metricKey: "Cart Volume",
    apiKey: "instore_order_metrics",
    isDefaultOpen: true,
    icon: "pi pi-shopping-cart",
  },
  {
    metricKey: "Instore SLA % < 2.5 mins", 
    apiKey: "instore_order_metrics",
    isDefaultOpen: false,
    icon: "pi pi-clock",
  },
  {
    metricKey: "Picking Time Per Item",
    apiKey: "instore_order_metrics", 
    isDefaultOpen: false,
    icon: "pi pi-stopwatch",
  },
  {
    metricKey: "%Picker assigned 10 secs",
    apiKey: "instore_order_metrics",
    isDefaultOpen: false, 
    icon: "pi pi-user",
  },
  {
    metricKey: "Picking Start % (5 secs)",
    apiKey: "instore_order_metrics",
    isDefaultOpen: false,
    icon: "pi pi-play",
  },
  {
    metricKey: "Picker Surge Orders %",
    apiKey: "instore_order_metrics",
    isDefaultOpen: false,
    icon: "pi pi-chart-line",
  },
  {
    metricKey: "Both Surge Orders %", 
    apiKey: "instore_order_metrics",
    isDefaultOpen: false,
    icon: "pi pi-chart-bar",
  },
  {
    metricKey: "Fill Rate %",
    apiKey: "instore_order_metrics",
    isDefaultOpen: false,
    icon: "pi pi-check-circle",
  },
  {
    metricKey: "IPO",
    apiKey: "instore_order_metrics", 
    isDefaultOpen: false,
    icon: "pi pi-box",
  },
  // Active Time Metrics
  {
    metricKey: "Total Active Hrs",
    apiKey: "instore_active_time_metrics",
    isDefaultOpen: false,
    icon: "pi pi-clock",
  },
  {
    metricKey: "Picker Active Time %",
    apiKey: "instore_active_time_metrics",
    isDefaultOpen: false,
    icon: "pi pi-user",
  },
  {
    metricKey: "Putter Active Time %", 
    apiKey: "instore_active_time_metrics",
    isDefaultOpen: false,
    icon: "pi pi-user-plus",
  },
  {
    metricKey: "Auditor Active Time %",
    apiKey: "instore_active_time_metrics",
    isDefaultOpen: false,
    icon: "pi pi-search",
  },
  {
    metricKey: "FNV Active Time %",
    apiKey: "instore_active_time_metrics", 
    isDefaultOpen: false,
    icon: "pi pi-eye",
  },
  {
    metricKey: "OPH",
    apiKey: "instore_active_time_metrics",
    isDefaultOpen: false,
    icon: "pi pi-chart-line",
  },
  {
    metricKey: "IPH",
    apiKey: "instore_active_time_metrics",
    isDefaultOpen: false,
    icon: "pi pi-chart-bar", 
  },
  // Complaints Metrics
  {
    metricKey: "Complaints %",
    apiKey: "instore_complaints_metrics",
    isDefaultOpen: false,
    icon: "pi pi-exclamation-triangle",
  }
];