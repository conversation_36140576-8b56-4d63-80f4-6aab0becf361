export const StoreNameConstants = Object.freeze({
  PREFIX: ["Super Store - ", "Super Store ", "SS "],
});

export const MergedCityStoreConstants = Object.freeze({
  chandigarh: ["Chandigarh", "Panch<PERSON>la", "Kharar", "Zirakpur", "Mohali"],
});

export const PanIndiaConstants = Object.freeze({
  PAN_INDIA: { name: "#Pan-India", code: "pan-india" },
  ALL: { name: "All", code: "all" },
  OVERALL: { name: "All", code: "overall" },
  PAN_INDIA_GROUP: { type: "pan-india", name: "Pan-India" },
});

export const CitiesToExcludeFromCityMapping = Object.freeze({
  chandigarh: ["Panchkula", "Kharar", "Zirakpur", "Mohali"],
});

export const insights = Object.freeze({
  DELTA_COLUMNS: [
    "Conv Δ Contri",
    "ATC Δ Serv Contri",
    "ATC Δ Demand Contri",
    "ATC Δ Misc Contri",
    "C2Co Δ Serv Contri",
    "C2Co Δ Demand Contri",
    "C2Co Δ Misc Contri",
    "Search Spike %",
    "Conversion Drop %",
  ],
  REVERSE_COLUMNS: [
    "Unserviceable DAU %",
    "Demand Based Block %",
    "Express OOH Based Block %",
    "Express Manual Based Block %",
    "Priority Customers Demand Block %",
  ],
});

export const HourList = Object.freeze([
  { key: "0th Hour", value: 0 },
  { key: "1st Hour", value: 1 },
  { key: "2nd Hour", value: 2 },
  { key: "3rd Hour", value: 3 },
  { key: "4th Hour", value: 4 },
  { key: "5th Hour", value: 5 },
  { key: "6th Hour", value: 6 },
  { key: "7th Hour", value: 7 },
  { key: "8th Hour", value: 8 },
  { key: "9th Hour", value: 9 },
  { key: "10th Hour", value: 10 },
  { key: "11th Hour", value: 11 },
  { key: "12th Hour", value: 12 },
  { key: "13th Hour", value: 13 },
  { key: "14th Hour", value: 14 },
  { key: "15th Hour", value: 15 },
  { key: "16th Hour", value: 16 },
  { key: "17th Hour", value: 17 },
  { key: "18th Hour", value: 18 },
  { key: "19th Hour", value: 19 },
  { key: "20th Hour", value: 20 },
  { key: "21st Hour", value: 21 },
  { key: "22nd Hour", value: 22 },
  { key: "23rd Hour", value: 23 },
]);

// export const ATHMetrics = Object.freeze(['ATH GMV', 'ATH Cart Volume']);

export const ATHMetricRelation = Object.freeze({
  GMV: "ATH GMV",
  "Cart Volume": "ATH Cart Volume",
});

export const MetricNameMapping = Object.freeze({
  "Blocks Contribution to ATC": "Blocks",
  "DAU Contribution to ATC": "DAU Mix",
  "Avail & Other Contribution to ATC": "Availability & Others",
  "Charges & Others": "Charges & Others",
  "Blocks Contribution to C2Co": "Blocks",
  "DAU Contribution to C2Co": "DAU Mix",
  "Surge Contribution": "Surge",
  "Avail & Other Contribution to C2Co": "Charges & Others",
  "Search Impressions": "Search DAUs",
});

export const PTypeMetricNameMapping = Object.freeze({
  "Search Impressions": "Search DAUs",
  "ATC %": "Platform ATC",
  "Unique Impressions": "Platform Impressions",
});

export const HomeExtraMetricsMapping = Object.freeze({
  "Surge Checkouts %": ["Surge Paid Checkouts %", "Rain Surge Orders %"],
  "Surge Seen %": [
    "Rider Surge Seen %",
    "Picker Surge Seen %",
    "Rain Surge Seen %",
  ],
  "Unserviceable DAU %": [
    "Express OOH Based Block %",
    "Express Manual Based Block %",
    "Longtail Manual Based Block %",
    "Longtail OOH Based Block %",
  ],
  "Demand Based Block %": [
    "Express Demand Based Block %",
    "Longtail Demand Based Block %",
    "Priority Customers %",
    "Priority Customers Demand Block %",
    "Priority Customers OOH Block %",
    "Priority Customers Manual Block %",
  ],
  // "Priority Customers %": [. >> moved into Demand Based Block %
  //   "Priority Customers Demand Block %",
  //   "Priority Customers OOH Block %",
  //   "Priority Customers Manual Block %",
  // ],
  "Active Stores Count": ["Express Outlet Count", "Longtail Outlet Count"],
  "Customer Paid Charges": [
    "Delivery Charge",
    "Surge Charge",
    "Handling Charge",
    "Convenience Charge",
    "Night Charge",
    "Small Cart Charge",
  ],
  "Promo %": ["Promo Orders", "Promo Charge"],
});

export const BistroHomeExtraMetricsMapping = Object.freeze({
  "Rider Handshake time": [
    "Direct Rider Handshake time",
    "Indirect Rider Handshake time",
  ],
  "Promo %": ["Promo Orders", "Promo Charge"],
  "Average KPT": [
    "Average Wait Time",
    "Average Preparation Time",
    "Average Assembly Time",
    "% Orders Breaching Wait+Prep Time",
    "% Orders Breaching Assembly Time",
  ],
  "Unserviceable DAU %": [
    "Express OOH Based Block %",
    "Express Manual Based Block %",
  ],
});



export const TenantMapping = Object.freeze({
  blinkit: "Blinkit",
  bistro: "Bistro",
});

export const MERCHANT_TYPES = Object.freeze({
  all: "ALL",
  merchant: "MERCHANT",
  blinkit: "BLINKIT",
});

export const RATINGS_MAPPING = {
  "Rating 5 Count": "⭐️ ⭐️ ⭐️ ⭐️ ⭐️",
  "Rating 4 Count": "⭐️ ⭐️ ⭐️ ⭐️",
  "Rating 3 Count": "⭐️ ⭐️ ⭐️",
  "Rating 2 Count": "⭐️ ⭐️",
  "Rating 1 Count": "⭐️",
};

export const CITY_METRICS_LIST = [
  "gmv",
  "total_items_sold",
  "unique_carts",
  "order_count",
];

export const ASSORTED_TYPE_METRIC_MAPPING = Object.freeze({
  // business
  "Cart Volume": "order_count",

  //servicebality
  "Surge Checkouts %": "surge_shown_carts_percentage",
  "Direct Handover %": "billed_to_assigned",
  "Batched Order %": "batched_orders",
  "Rain Order %": "rain_order_percentage",

  // SLA
  "%Delivery < 10mins": "percentage_orders_delivered_in_10mins",
  "%Delivery < 15mins": "percentage_orders_delivered_in_15mins",
  "%Delivery < 30mins": "percentage_orders_delivered_in_30mins",
  "Order Cancellation": "cancellation_percentage",
  "%Picker assigned 10 secs": "checkout_to_picker_assigned",
  "Free DC %": "free_delivery_percentage",
});
