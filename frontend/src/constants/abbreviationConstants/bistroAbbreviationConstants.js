export const BistroAbbreviationConstants = Object.freeze({
  GMV: {
    value: "Gross Merchandise Value",
    explanation: "The total selling price of all items sold.",
    source: "OMS > Order Lifecycle Events",
    field: "total_cost",
    calculation: "Sum of total_cost",
    showWeekTrend: true,
  },
  "Cart Volume": {
    value: "Cart Volume",
    explanation: "The number of unique carts created.",
    source: "OMS > Order Lifecycle Events",
    field: "cart_id",
    calculation: "count of distinct cart_id",
    showWeekTrend: true,
  },
  AOV: {
    value: "Average Order Value",
    explanation: "The average selling price of an order.",
    source: "OMS > Order Lifecycle Events ",
    field: "total_cost, cart_id",
    calculation: "Sum of total_cost / count of distinct cart_id",
    showWeekTrend: true,
  },
  "Daily Active Users": {
    value: "Daily Active Users",
    explanation: "Unique device count (web not included) for that city",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
    showWeekTrend: true,
  },
  "DAU Conversion Percentage": {
    value: "Daily Active Users Conversion Percentage",
    explanation:
      "Percentage of customers who placed an order out of total active customers on app",
    source:
      "App events (jumbo pipeline) for DAU, OMS > Order Lifecycle Events for transacting users",
    field: "device ID",
    calculation: "transacting users / active users",
    showWeekTrend: true,
  },
  "Surge Checkouts %": {
    value: "Surge shown carts percentage",
    explanation: "Percentage of orders checked out during surge",
    source: "OMS > Order Lifecycle Events",
    field:
      "order > slot_properties > serviceability > surge_charge_v2 > surge_amount, cart_id",
    calculation: "Count of cart_ids where surge_amount > 0 / cart_id",
    showWeekTrend: true,
  },
  "ORDERS - HOURLY TRENDS": {
    value: "ORDERS - HOURLY TRENDS",
    explanation: "The number of unique orders placed for that hour",
    source: "OMS > Order Lifecycle Events",
    field: "cart_id",
    calculation: "count of distinct cart_id",
  },
  "% order delivered in 15 mins": {
    value: "% order delivered in 15 mins",
    explanation:
      "The percentage of orders delivered to customer within 15 minutes.",
    source: "OMS > Order Lifecycle Events",
    field:
      "current_status, delivery_timestamp, insert_timestamp (order creation time)",
    calculation:
      "Count of orders where delivery_timestamp - insert_timestamp <= 15 minutes / Total Orders",
  },
  "Order Cancellation": {
    value: "Order Cancellation",
    explanation: "The percentage of canceled orders",
    source: "OMS > Order Lifecycle Events",
    field: "current_status, id",
    calculation:
      "Count of orders where current_status='CANCELLED'/ Total Orders",
    showWeekTrend: true,
  },
  "Direct Handover %": {
    value: "Direct Handover %",
    explanation:
      "Percentage of orders where a partner was assigned before billing was completed",
    calculation:
      "100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / count(id)",
    showWeekTrend: true,
  },
  "Batched Order %": {
    value: "Batched Order %",
    explanation: "Percentage of orders batched together in a trip",
    showWeekTrend: true,
  },
  "Indirect Rider Handshake time": {
    value: "Indirect Rider Handshake time",
    explanation:
      "Avg wait time to pick the order where a partner is assigned after the billing is completed",
    calculation:
      "AVG(CASE WHEN (order_enroute_timestamp - order_billed_timestamp) >= 0 then order_enroute_timestamp - order_billed_timestamp else 0 end)",
    showWeekTrend: true,
  },
  "Promo %": {
    value: "Promo %",
    explanation: "Percentage of orders with discount",
    showWeekTrend: true,
  },
});
