export const BlinkitAbbreviationConstants = Object.freeze({
  GMV: {
    value: "Gross Merchandise Value",
    explanation: "The total selling price of all items sold.",
    source: "OMS > Order Lifecycle Events",
    field: "total_cost",
    calculation: "Sum of total_cost",
    showWeekTrend: true,
  },
  "Cart Volume": {
    value: "Cart Volume",
    explanation: "The number of unique carts created.",
    source: "OMS > Order Lifecycle Events",
    field: "cart_id",
    calculation: "count of distinct cart_id",
    showWeekTrend: true,
  },
  "Total Orders": {
    value: "Total Orders",
    explanation: "The number of unique orders placed.",
    source: "OMS > Order Lifecycle Events ",
    field: "id(order id) ",
    calculation: "count of distinct id",
    showWeekTrend: true,
  },
  AOV: {
    value: "Average Order Value",
    explanation: "The average selling price of an order.",
    source: "OMS > Order Lifecycle Events ",
    field: "total_cost, cart_id",
    calculation: "Sum of total_cost / count of distinct cart_id",
    showWeekTrend: true,
  },
  "Daily Active Users": {
    value: "Daily Active Users",
    explanation: "Unique device count (web not included) for that city",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
    showWeekTrend: true,
  },
  "New Transacting Customers": {
    value: "New Transacting Customers",
    explanation: "The number of customers who made their first purchase.",
    source: "Feature Store, OMS > Order Lifecycle Events",
    field: "is_first_order_completed from FS, customer_id from OMS",
    calculation:
      "Count distinct transacting customer_id when is_first_order_completed = false",
    showWeekTrend: true,
  },
  "Transacting Customers": {
    value: "Transacting Customers",
    explanation: "Unique customer checked outs.",
    source: "OMS > Order Lifecycle Events",
    field: "customer_id",
    calculation: "Count distinct transacting customer_id",
    showWeekTrend: true,
  },
  "DAU Conversion Percentage": {
    value: "Daily Active Users Conversion Percentage",
    explanation:
      "Percentage of customers who placed an order out of total active customers on app",
    source:
      "App events (jumbo pipeline) for DAU, OMS > Order Lifecycle Events for transacting users",
    field: "device ID",
    calculation: "transacting users / active users",
    showWeekTrend: true,
  },
  "Unique SKU/Order": {
    value: "Unique Stock Keeping Unit per Order",
    explanation: "The average number of items per order.",
    source: "OMS > Order Lifecycle Events",
    field: "Order > Items > Count, cart_id",
    calculation: "SUM(item_count) / count distint cart_id ",
    showWeekTrend: true,
  },
  "Total Items Sold": {
    value: "Total Items Sold",
    explanation: "The total number of items sold.",
    source: "OMS > Order Lifecycle Events",
    field: "Order > Items > quantity > Count",
    calculation: "SUM(total_items_quantity)",
    showWeekTrend: true,
  },
  "Surge Checkouts %": {
    value: "Surge shown carts percentage",
    explanation: "Percentage of orders checked out during surge",
    source: "OMS > Order Lifecycle Events",
    field:
      "order > slot_properties > serviceability > surge_charge_v2 > surge_amount, cart_id",
    calculation: "Count of cart_ids where surge_amount > 0 / cart_id",
    showWeekTrend: true,
  },
  "Surge Paid Checkouts %": {
    value: "Surge paid carts percentage",
    explanation: "Percentage of orders which paid surge",
    source: "OMS > Order Lifecycle Events",
    field:
      "order > slot_properties > checkout_properties > slot_charge as checkout_slot_charge, cart_id",
    calculation: "Count of cart_ids where checkout_slot_charge > 0 / cart_id",
  },
  "Rain Surge Orders %": {
    value: "Rain surge carts percentage",
    explanation: "Percentage of orders checked out during rain surge ",
    source: "OMS > Order Lifecycle Events",
    field:
      "order > slot_properties > serviceability > surge_charge_v2 > source AS surge_type, cart_id",
    calculation: "Count of cart_ids where surge_type = 'rain_surge' / cart_id",
    showWeekTrend: true,
  },
  "Unserviceable DAU %": {
    value: "Unserviceable Daily Active Users %",
    explanation:
      "Percentage of unique users blocked due to outside operating hours, manual store block, and manual polygon area throttle.",
    source:
      "serviceability_checkouts_block_info_events (jumbo) for block reason, App Events for DAU\nField: reason, device id",
    calculation:
      "count user_id when reason not in ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  / dau",
    filters:
      "merchant_id not in (28759,31149) and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH') and block_type = 'COMPLETE'",
    showWeekTrend: true,
  },
  "Demand Based Block %": {
    value: "Demand Based Block %",
    explanation:
      "Percentage of unique users blocked due to demand-supply mismatch during operating hours.",
    source:
      "serviceability_checkouts_block_info_events (jumbo) for block reason, App Events for DAU\nField: reason, device id",
    field: "reason, device id",
    calculation:
      "count user_id when reason in ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  / dau",
    filters:
      "merchant_id not in (28759,31149)\n and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH')\n and block_type = 'COMPLETE'",
    showWeekTrend: true,
  },
  "% ETA below 10mins": {
    value: "% ETA below 10mins",
    explanation:
      "The percentage of orders where estimated Delivery Partner arrival at customer location is below 10 minutes.",
    showWeekTrend: true,
  },
  "<15 mins ETA%": {
    value: "<15 mins ETA%",
    explanation:
      "The percentage of orders where estimated Delivery Partner arrival at customer location is below 15 minutes.",
    showWeekTrend: true,
  },
  "% ETA below 30mins": {
    value: "% ETA below 30mins",
    explanation:
      "The percentage of orders where estimated Delivery Partner arrival at customer location is below 30 minutes.",
    showWeekTrend: true,
  },
  "% orders arrived in 10mins": {
    value: "% orders arrived in 10mins",
    explanation:
      "The percentage of orders where Delivery Partner reached customer location within 10 minutes.",
    calculation:
      "100 * SUM(CASE WHEN (reached_doorstep_timestamp - insert_timestamp) <= 600000 then 1 else 0 end) / count(id) as percentage_order_arrived_in_10mins",
    showWeekTrend: true,
  },
  "% orders arrived in 15mins": {
    value: "% orders arrived in 15mins",
    explanation:
      "The percentage of orders where Delivery Partner reached customer location within 15 minutes.",
    calculation:
      "100 * SUM(CASE WHEN (reached_doorstep_timestamp - insert_timestamp) <= 900000 then 1 else 0 end) / count(id) as percentage_order_arrived_in_15mins",
    showWeekTrend: true,
  },
  "%Delivery < 10mins": {
    value: "%Delivery < 10mins",
    explanation:
      "The percentage of orders delivered to customer within 10 minutes.",
    source: "OMS > Order Lifecycle Events",
    field:
      "current_status, delivery_timestamp, insert_timestamp (order creation time)",
    calculation:
      "Count of orders where delivery_timestamp - insert_timestamp <= 10 minutes / Total Orders",
    showWeekTrend: true,
  },
  "%Delivery < 15mins": {
    value: "%Delivery < 15mins",
    explanation:
      "The percentage of orders delivered to customer within 15 minutes.",
    source: "OMS > Order Lifecycle Events",
    field:
      "current_status, delivery_timestamp, insert_timestamp (order creation time)",
    calculation:
      "Count of orders where delivery_timestamp - insert_timestamp <= 15 minutes / Total Orders",
    showWeekTrend: true,
  },
  "%Delivery < 30mins": {
    value: "%Delivery < 30mins",
    explanation:
      "The percentage of orders delivered to customer within 30 minutes.",
    source: "OMS > Order Lifecycle Events",
    field:
      "current_status, delivery_timestamp, insert_timestamp (order creation time)",
    calculation:
      "Count of orders where delivery_timestamp - insert_timestamp <= 30 minutes / Total Orders",
    showWeekTrend: true,
  },
  "Order Cancellation": {
    value: "Order Cancellation",
    explanation: "The percentage of canceled orders",
    source: "OMS > Order Lifecycle Events",
    field: "current_status, id",
    calculation:
      "Count of orders where current_status='CANCELLED'/ Total Orders",
    showWeekTrend: true,
  },
  "Delivery Charge": {
    value: "Delivery Charges Per Order",
    explanation: "Average delivered charged per order.",
    source: "OMS > Order Lifecycle Events",
    field: "delivery_cost, cart_id",
    calculation: "Sum of delivery cost / total distinct cart ids\n",
    showWeekTrend: true,
    hasAbsoluteTrendValues: true,
  },
  "Rider Login hrs": {
    value: "Rider Login hrs",
    explanation:
      "Total partner login hours for Blinkit Rider Partners (20 min lag)",
    source:
      "zomato.realtime_enriched_store.driver_logins\nzomato.carthero_prod.delivery_driver_service_mappings\nzomato.carthero_prod.delivery_drivers\nzomato.driver_service.driver_store_mappings\nzomato.carthero_prod.localities\nzomato.carthero_prod.cities\nzomato.carthero_prod.users\ndim_merchant",
    field: "eventtime, isblinkit, eventname",
    calculation:
      "cumulative sum of login time from zomato.realtime_enriched_store.driver_logins aggregated on delivery_driver_id and blinkit_store_id",
    filters:
      "source_id = 17110\ntype = 'merchant'  \nname like '%%ES%%' \ndriver_service_id = 13\nsystem_enabled = 1 \ndriver_enabled = 1\nactive=1 \ncategory_name = 'BLINKIT'\neventname in ('LOGIN', 'LOGOUT', 'OFFLINE')",
    showWeekTrend: true,
  },
  "New Riders": {
    value: "New Riders",
    explanation: "New riders who have joined Blinkit since 2021",
    source:
      "OMS > Order Lifecycle Events (real-time)\ndwh.fact_supply_chain_order_details (T-1)",
    field: "delivery_fe_id",
    calculation:
      "A helper table (rider_fod_metrics_v2) is maintained for all delivery_drivers having their first_order_date and last_order_date updated daily\nIf the delivery_fe_id is not present in rider_fod_metrics_v2 for today then rider is considered new_rider\nFor past (t-7, t-14, t-28) if rider fod matches the date then it is considered new rider for that day",
    filters:
      "current_status = 'DELIVERED' \norg_channel_id in ('1', '2')\ncity_name not in ('Not in service area')",
    showWeekTrend: true,
  },
  "Direct Handover %": {
    value: "Direct Handover %",
    explanation:
      "Percentage of orders where a partner was assigned before billing was completed",
    calculation:
      "100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / count(id)",
    showWeekTrend: true,
  },
  "%Picker assigned 10 secs": {
    value: "%Picker assigned 10 secs",
    explanation:
      "Percentage of orders where picker was assigned within 10 seconds of checkout",
    source: "OMS > Order Lifecycle Events ",
    field: "picker_assigned_timestamp, picker_assignment_queued_time, order_id",
    calculation:
      "100 * SUM(CASE WHEN (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / count(id) as percentage_checkout_to_picker_assigned",
    showWeekTrend: true,
  },
  "Picking Start % (5 secs)": {
    value: "Picking Start % (5 secs)",
    explanation:
      "The percentage of orders where picking was started within 5 seconds of picker assignment",
    source: "OMS > Order Lifecycle Events ",
    field: "picking_start_time, picker_assigned_timestamp, order_id",
    calculation:
      "100 * SUM(CASE WHEN (picking_start_time - picker_assigned_timestamp) <= 5000 then 1 else 0 end) / count(id) as percentage_picker_assigned_to_picking_start",
    showWeekTrend: true,
  },
  "Rain Order %": {
    value: "Rain Order %",
    explanation:
      "Percentage of orders checked out during rains from serviceability.",
    calculation:
      "100 * SUM(CASE WHEN serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / count(id)",
    showWeekTrend: true,
  },
  "Batched Order %": {
    value: "Batched Order %",
    explanation: "Percentage of orders batched together in a trip",
    showWeekTrend: true,
  },
  "Customer Paid Charges": {
    value: "Customer Paid Charges",
    explanation: "Avg of cumulative charges paid by our customers per order.",
    showWeekTrend: true,
    hasAbsoluteTrendValues: true,
  },
  "ATC %": {
    value: "Add to Cart %",
    explanation:
      "Count of devices who has added something to order out of total Daily Active Customers/Devices",
    source: "App events (Sonar Insights)",
    field: "device ID",
    calculation: "Unique device count of ATC / DAU",
    showWeekTrend: true,
  },
  "C2Co %": {
    value: "Cart to Checkout %",
    explanation:
      "Count of devices who have placed an order out of total devices who have visited the cart page",
    source: "App events (Sonar Insights)",
    field: "device ID",
    calculation:
      "Unique device count of checkout / Unique device count of cart page",
    showWeekTrend: true,
  },
  "Instore SLA % < 2.5 mins": {
    value: "Instore SLA % < 2.5 mins",
    explanation:
      "The percentage of in-store orders processed within 2.5 minutes.",
    source: "OMS > Order Lifecycle Events ",
    field: "order_billed_timestamp, checkout_timestamp, order_id",
    calculation:
      "100 * SUM(CASE WHEN (order_billed_timestamp - checkout_timestamp) <= 150000 THEN 1 ELSE 0 END) / count(id) AS percent_instore_sla_less_than_150_sec",
    showWeekTrend: true,
  },
  "Picking Time Per Item": {
    value: "Picking Time Per Item",
    explanation: "The time taken to pick each item",
    source: "OMS > Order Lifecycle Events ",
    field:
      "pick_completion_time, picking_start_time, total_items_quantity, paas_quantity, paas_sku",
    calculation:
      "SUM((CASE WHEN (pick_completion_time < 0 OR picking_start_time < 0) THEN 0 ELSE pick_completion_time - picking_start_time END)/1000) / SUM(((total_items_quantity - jsonextractscalar(paas_metrics, '$.quantity', 'INT', 0)) + jsonextractscalar(paas_metrics, '$.sku', 'INT', 0))) as ppi_in_seconds",
    showWeekTrend: true,
  },
  "Picker Surge Orders %": {
    value: "Picker Surge Orders %",
    explanation:
      "The percentage number of orders where the surge was applied because of picker shortage",
    source: "OMS > Order Lifecycle Events ",
    field: "cart_id",
    calculation:
      "100 * (DISTINCTCOUNT( CASE WHEN surge_type = 'mec_picker_surge' THEN cart_id ELSE -1 END) - 1) / COUNT(DISTINCT(cart_id)) AS picker_surge_orders",
    showWeekTrend: true,
  },
  "Both Surge Orders %": {
    value: "Both Surge Orders %",
    explanation:
      "The number of orders where surge was applied becuase of the picker as well rider shortage",
    calculation:
      "100 * (DISTINCTCOUNT( CASE WHEN surge_type = 'mec_fe_surge_and_mec_picker_surge' THEN cart_id ELSE -1 END) - 1) / COUNT(DISTINCT(cart_id)) AS both_surge_orders",
    showWeekTrend: true,
  },
  "Fill Rate %": {
    value: "Fill Rate %",
    explanation: "The percentage of orders filled compared to orders placed.",
    source: "OMS > Order Lifecycle Events ",
    calculation:
      "100 * ((SUM( case when (sm_fill_rate = 100 AND current_status != 'APPROVED') then 1 else 0 end)))/(SUM( CASE WHEN current_status != 'APPROVED' then 1 else 0 end)) as sm_fill_rate",
    showWeekTrend: true,
  },
  "True Fill Rate %": {
    value: "True Fill Rate %",
    explanation:
      "The percentage of fully filled orders, excluding those with complaints in the categories [item_missing, wrong_items_received]. \
    i.e. if mentioned complaint is raised against a fully filled order, it is treated as not fully filled.",
    source: "OMS > Order Lifecycle Events ",
    calculation:
      "100.00 * (SUM( case when (sm_fill_rate = 100 AND current_status != 'APPROVED' AND id NOT IN (SELECT order_id from fact_order_complaints where complaint_type in ('item_missing', 'wrong_items_received'))) then 1 else 0 end ))/(SUM( CASE WHEN current_status != 'APPROVED' then 1 else 0 end)) as true_fill_rate",
    showWeekTrend: true,
  },
  IPO: {
    value: "Total Procured Items Quantity",
    explanation: "The total number of items procured.",
    source: "OMS > Order Lifecycle Events ",
    field: "total_procured_items_quantity, order_id",
    calculation:
      "SUM(total_procured_items_quantity) / count(id) as total_procured_items_quantity",
    showWeekTrend: true,
  },
  "Total Active Hrs": {
    value: "Total Active Hrs",
    explanation:
      "Total login time of picker, putter, auditor, fnv and others combined",
    source: "dwh.agg_hourly_outlet_role_instore_employee_login",
    field: "hourly_active_minute",
    calculation: "sum(hourly_active_minute) AS total_active_time",
    showWeekTrend: true,
  },
  "Picker Active Time %": {
    value: "Picker Active Time %",
    explanation:
      "The percentage of login time of pickers from total active time",
    source: "dwh.agg_hourly_outlet_role_instore_employee_login",
    field: "hourly_active_minute",
    calculation:
      "sum(CASE WHEN ROLE='PICKER' THEN hourly_active_minute END) picker_active_time / SUM(total_active_time)",
    showWeekTrend: true,
  },
  "Putter Active Time %": {
    value: "Putter Active Time %",
    explanation:
      "The percentage of login time of pickers from total active time",
    source: "dwh.agg_hourly_outlet_role_instore_employee_login",
    field: "hourly_active_minute",
    calculation:
      "sum(CASE WHEN ROLE='PUTTER' THEN hourly_active_minute END) picker_active_time / SUM(total_active_time)",
    showWeekTrend: true,
  },
  "Auditor Active Time %": {
    value: "Auditor Active Time %",
    explanation:
      "The percentage of login time of putter from total active time",
    source: "dwh.agg_hourly_outlet_role_instore_employee_login",
    field: "hourly_active_minute",
    calculation:
      "sum(CASE WHEN ROLE='AUDITOR' THEN hourly_active_minute END) picker_active_time / SUM(total_active_time)",
    showWeekTrend: true,
  },
  "FNV Active Time %": {
    value: "FNV Active Time %",
    explanation: "The percentage of login time of fnv from total active time",
    source: "dwh.agg_hourly_outlet_role_instore_employee_login",
    field: "hourly_active_minute",
    calculation:
      "sum(CASE WHEN ROLE='FNV' THEN hourly_active_minute END) picker_active_time / SUM(total_active_time)",
    showWeekTrend: true,
  },
  OPH: {
    value: "Orders picked per hour",
    explanation: "The average number of orders picked per hour.",
    source: "dwh.fact_supply_chain_order_details",
    field: "order_id, hourly_active_minute",
    calculation:
      "(SUM(total_orders) / (SUM(picker_active_time)/3600)) AS orders_picked_per_hour",
    showWeekTrend: true,
  },
  IPH: {
    value: "Items putaway per hour",
    explanation:
      "The average number of (items put away + Milk put away + Perishable put away) per hour.",
    source:
      "lake_storeops.item_activity, dwh.agg_hourly_outlet_role_instore_employee_login",
    field: "l2_category_id, l0_category_id, hourly_active_minute",
    calculation:
      "- sum(case when (l2_category_id not in  (1425,31,116,198,1097,1956,949,1389,1778,63,1367,1369,950,138,1091,1093,1091,1093,1094,33,97,197,1127,1185)) and (l0_category_id <> 1487) then qty end) as packaged_qty\n\n- (SUM(packaged_putaway+milk_putaway+perishables_others_putaway) / (SUM(putter_active_time)/3600)) AS items_putaway_per_hour",
    showWeekTrend: true,
  },
  "New Manpower": {
    value: "New Manpower",
    explanation: "The number of new people joined in the past 21 days",
    source: "lake_storeops.item_activity",
    field: "first_shiftstart, last_modified_date",
    calculation:
      "case when date_diff('day',first_shiftstart,date(cast(from_unixtime(last_modified_date/pow(10,6), 'Asia/Kolkata') as timestamp))) <=21 then 'New'",
    showWeekTrend: true,
  },
  "Total Complaints": {
    value: "Total Complaints",
    showWeekTrend: true,
  },
  "Complaints %": {
    value: "Complaints %",
    explanation:
      "The percentage of orders in which any time of complaint was raised",
    source:
      "- Pub Sub -  blinkit.storeops.order-complaint\n\n- OMS > Order Lifecycle Events ",
    field: "order_id",
    calculation: "100 * (complaints / total_orders)",
    showWeekTrend: true,
  },
  "Active Stores Count": {
    value: "Active Stores Count",
    showWeekTrend: true,
    hasAbsoluteTrendValues: true,
  },
  "OPD Per Store": {
    value: "OPD Per Store",
    explanation: "Order per store - total orders / total active stores",
    showWeekTrend: true,
  },
  "ORDERS - HOURLY TRENDS": {
    value: "ORDERS - HOURLY TRENDS",
    explanation: "The number of unique orders placed for that hour",
    source: "OMS > Order Lifecycle Events",
    field: "cart_id",
    calculation: "count of distinct cart_id",
  },
  "ACTIVE USERS - HOURLY TRENDS": {
    value: "ACTIVE USERS - HOURLY TRENDS",
    explanation: "Unique device count (web not included) for that hour",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
  },
  "ACTIVE USER CONVERSION - HOURLY TRENDS": {
    value: "ACTIVE USER CONVERSION - HOURLY TRENDS",
    explanation:
      "Percentage of customers who placed an order out of total active customers on the app",
    source:
      "App events (jumbo pipeline) for DAU, OMS > Order Lifecycle Events for transacting users",
    field: "device ID",
    calculation: "transacting users / active users",
  },
  "ATC % - HOURLY TRENDS": {
    value: "ATC % - HOURLY TRENDS",
  },
  "C2Co % - HOURLY TRENDS": {
    value: "C2Co % - HOURLY TRENDS",
  },
  "SURGE CHECKOUTS % - HOURLY TRENDS": {
    value: "Surge shown carts hourly percentage",
    explanation: "Percentage of orders checked out during surge for that hour",
    source: "OMS > Order Lifecycle Events",
    field:
      "order > slot_properties > serviceability > surge_charge_v2 > surge_amount, cart_id",
    calculation: "Count of cart_ids where surge_amount > 0 / cart_id",
  },
  "RIDER LOGIN HOURS - HOURLY TRENDS": {
    value: "RIDER LOGIN HOURS - HOURLY TRENDS",
    explanation:
      "Total partner login hours for Blinkit Rider Partners (20 min lag) aggregated hourly",
    source:
      "zomato.realtime_enriched_store.driver_logins\nzomato.carthero_prod.delivery_driver_service_mappings\nzomato.carthero_prod.delivery_drivers\nzomato.driver_service.driver_store_mappings\nzomato.carthero_prod.localities\nzomato.carthero_prod.cities\nzomato.carthero_prod.users\ndim_merchant",
    field: "eventtime, isblinkit, eventname",
    calculation:
      "cumulative sum of login time from zomato.realtime_enriched_store.driver_logins aggregated on delivery_driver_id and blinkit_store_id",
    filters:
      "source_id = 17110\ntype = 'merchant'  \nname like '%%ES%%' \ndriver_service_id = 13\nsystem_enabled = 1 \ndriver_enabled = 1\nactive=1 \ncategory_name = 'BLINKIT'\neventname in ('LOGIN', 'LOGOUT', 'OFFLINE')",
  },
  "ORDER CANCELLATION - HOURLY TRENDS": {
    value: "ORDER CANCELLATION - HOURLY TRENDS",
    explanation: "The percentage of cancelled orders",
    source: "OMS > Order Lifecycle Events",
    field: "current_status, id",
    calculation:
      "Count of orders where current_status='CANCELLED'/ Total Orders",
  },
  "% ORDER DELIVERED IN 15 MINS - HOURLY TRENDS": {
    value: "% ORDER DELIVERED IN 15 MINS - HOURLY TRENDS",
    explanation:
      "The percentage of orders delivered to customer within 15 minutes.",
    source: "OMS > Order Lifecycle Events",
    field:
      "current_status, delivery_timestamp, insert_timestamp (order creation time)",
    calculation:
      "Count of orders where delivery_timestamp - insert_timestamp <= 15 minutes / Total Orders",
  },
  "DIRECT HANDOVER % - HOURLY TRENDS": {
    value: "DIRECT HANDOVER % - HOURLY TRENDS",
    explanation:
      "Percentage of orders where a partner was assigned before billing was completed",
    calculation:
      "100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / count(id)",
  },
  "PICKER ASSIGNMENT % - HOURLY TRENDS": {
    value: "PICKER ASSIGNMENT % - HOURLY TRENDS",
    explanation:
      "Percentage of orders where picker was assigned within 10 seconds of checkout",
    source: "OMS > Order Lifecycle Events ",
    field: "picker_assigned_timestamp, checkout_timestamp, order_id",
    calculation:
      "100 * SUM(CASE WHEN (picker_assigned_timestamp - checkout_timestamp) <= 10000 then 1 else 0 end) / count(id) as percentage_checkout_to_picker_assigned",
  },
  "RAIN ORDER % - HOURLY TRENDS": {
    value: "RAIN ORDER % - HOURLY TRENDS",
    explanation:
      "Percentage of orders checked out while >40% of the polygon was raining i.e. when rain banner was shown to the customer",
    calculation:
      "100 * SUM(CASE WHEN serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / count(id)",
  },
  "ORDERS - CITIES TRENDS": {
    value: "ORDERS - CITIES TRENDS",
    explanation: "The number of unique orders placed in each city",
    source: "OMS > Order Lifecycle Events",
    field: "cart_id",
    calculation: "count of distinct cart_id",
  },
  "GMV - CITIES TRENDS": {
    value: "Gross Merchandise Value",
    explanation: "The total selling price of all items sold in each city.",
    source: "OMS > Order Lifecycle Events",
    field: "total_cost",
    calculation: "Sum of total_cost",
  },
  "DAU - CITIES TRENDS": {
    value: "Daily Active Users",
    explanation:
      "Unique device count (web not included) in that city/overall India",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
  },
  "DAU CONVERSION - CITIES TRENDS": {
    value: "Daily Active Users Conversion",
    explanation:
      "Percentage of customers who placed an order out of total active customers on app.",
    source:
      "App events (jumbo pipeline) for DAU, OMS > Order Lifecycle Events for transacting users",
    field: "device ID",
    calculation: "transacting users / active users",
  },
  "DEMAND BASED BLOCK % - CITIES TRENDS": {
    value: "Demand Based Block %",
    explanation:
      "Percentage of unique users blocked due to demand-supply mismatch during operating hours.",
    source:
      "serviceability_checkouts_block_info_events (jumbo) for block reason, App Events for DAU\nField: reason, device id",
    field: "reason, device id",
    calculation:
      "count user_id when reason in ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  / dau",
    filters:
      "merchant_id not in (28759,31149)\n and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH')\n and block_type = 'COMPLETE'",
  },
  "UNSERVICEABLE DAU % - CITIES TRENDS": {
    value: "Unserviceable DAU %",
    explanation:
      "Percentage of unique users blocked due to outside operating hours, manual store block, and manual polygon area throttle.",
    source:
      "serviceability_checkouts_block_info_events (jumbo) for block reason, App Events for DAU\nField: reason, device id",
    calculation:
      "count user_id when reason not in ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  / dau",
    filters:
      "merchant_id not in (28759,31149) and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH') and block_type = 'COMPLETE'",
  },
  "RIDER LOGIN HOURS - CITIES TRENDS": {
    value: "RIDER LOGIN HOURS - CITIES TRENDS",
    explanation:
      "Total partner login hours for Blinkit Rider Partners (20 min lag) aggregated on the city",
    source:
      "zomato.realtime_enriched_store.driver_logins\nzomato.carthero_prod.delivery_driver_service_mappings\nzomato.carthero_prod.delivery_drivers\nzomato.driver_service.driver_store_mappings\nzomato.carthero_prod.localities\nzomato.carthero_prod.cities\nzomato.carthero_prod.users\ndim_merchant",
    field: "eventtime, isblinkit, eventname",
    calculation:
      "cumulative sum of login time from zomato.realtime_enriched_store.driver_logins aggregated on delivery_driver_id and blinkit_store_id",
    filters:
      "source_id = 17110\ntype = 'merchant'  \nname like '%%ES%%' \ndriver_service_id = 13\nsystem_enabled = 1 \ndriver_enabled = 1\nactive=1 \ncategory_name = 'BLINKIT'\neventname in ('LOGIN', 'LOGOUT', 'OFFLINE')",
  },
  "NEW RIDERS - CITIES TRENDS": {
    value: "NEW RIDERS - CITIES TRENDS",
    explanation:
      "New and relapsed riders who have joined Blinkit after a gap of 90 days aggregated on city",
    source:
      "OMS > Order Lifecycle Events (real-time)\ndwh.fact_supply_chain_order_details (T-1)",
    field: "delivery_fe_id",
    calculation:
      "A helper table (rider_fod_metrics_v2) is maintained for all delivery_drivers having their first_order_date and last_order_date updated daily\nIf the delivery_fe_id is not present in rider_fod_metrics_v2 for today then rider is considered new_rider\nFor past (t-7, t-14, t-28) if rider fod matches the date then it is considered new rider for that day",
    filters:
      "current_status = 'DELIVERED' \norg_channel_id in ('1', '2')\ncity_name not in ('Not in service area')",
  },
  "ORDER CANCELLATION - CITIES TRENDS": {
    value: "ORDER CANCELLATION - CITIES TRENDS",
    explanation: "The percentage of canceled orders",
    source: "OMS > Order Lifecycle Events",
    field: "current_status, id",
    calculation:
      "Count of orders where current_status='CANCELLED'/ Total Orders",
  },
  "ORDERS - STORES TRENDS": {
    value: "ORDERS - STORES TRENDS",
    explanation: "The number of unique orders placed in each store",
    source: "OMS > Order Lifecycle Events",
    field: "cart_id",
    calculation: "count of distinct cart_id",
  },
  " GMV - STORES TRENDS": {
    value: "Gross Merchandise Value",
    explanation: "The total selling price of all items sold in each store.",
    source: "OMS > Order Lifecycle Events",
    field: "total_cost",
    calculation: "Sum of total_cost",
  },
  "DAU - STORES TRENDS": {
    value: "Daily Active Users",
    explanation: "Unique device count (web not included) in that store",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
  },
  "DAU CONVERSION - STORES TRENDS": {
    value: "Daily Active Users Conversion",
    explanation:
      "Percentage of customers who placed an order out of total active customers on app.",
    source:
      "App events (jumbo pipeline) for DAU, OMS > Order Lifecycle Events for transacting users",
    field: "device ID",
    calculation: "transacting users / active users",
  },
  "DEMAND BASED BLOCK % - STORES TRENDS": {
    value: "Demand Based Block %",
    explanation:
      "Percentage of unique users blocked due to demand-supply mismatch during operating hours.",
    source:
      "serviceability_checkouts_block_info_events (jumbo) for block reason, App Events for DAU\nField: reason, device id",
    field: "reason, device id",
    calculation:
      "count user_id when reason in ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  / dau",
    filters:
      "merchant_id not in (28759,31149)\n and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH')\n and block_type = 'COMPLETE'",
  },
  "UNSERVICEABLE DAU % - STORES TRENDS": {
    value: "Unserviceable DAU %",
    explanation:
      "Percentage of unique users blocked due to outside operating hours, manual store block, and manual polygon area throttle..",
    source:
      "serviceability_checkouts_block_info_events (jumbo) for block reason, App Events for DAU\nField: reason, device id",
    calculation:
      "count user_id when reason not in ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  / dau",
    filters:
      "merchant_id not in (28759,31149) and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH') and block_type = 'COMPLETE'",
  },
  "RIDER LOGIN HOURS - STORES TRENDS": {
    value: "RIDER LOGIN HOURS - STORES TRENDS",
    explanation:
      "Total partner login hours for Blinkit Rider Partners (20 min lag) aggregated on store.",
    source:
      "zomato.realtime_enriched_store.driver_logins\nzomato.carthero_prod.delivery_driver_service_mappings\nzomato.carthero_prod.delivery_drivers\nzomato.driver_service.driver_store_mappings\nzomato.carthero_prod.localities\nzomato.carthero_prod.cities\nzomato.carthero_prod.users\ndim_merchant",
    field: "eventtime, isblinkit, eventname",
    calculation:
      "cumulative sum of login time from zomato.realtime_enriched_store.driver_logins aggregated on delivery_driver_id and blinkit_store_id",
    filters:
      "source_id = 17110\ntype = 'merchant'  \nname like '%%ES%%' \ndriver_service_id = 13\nsystem_enabled = 1 \ndriver_enabled = 1\nactive=1 \ncategory_name = 'BLINKIT'\neventname in ('LOGIN', 'LOGOUT', 'OFFLINE')",
  },
  "NEW RIDERS - STORES TRENDS": {
    value: "NEW RIDERS - STORES TRENDS",
    explanation:
      "New and relapsed riders who have joined Blinkit after a gap of 90 days aggregated on store.",
    source:
      "OMS > Order Lifecycle Events (real-time)\ndwh.fact_supply_chain_order_details (T-1)",
    field: "delivery_fe_id",
    calculation:
      "A helper table (rider_fod_metrics_v2) is maintained for all delivery_drivers having their first_order_date and last_order_date updated daily\nIf the delivery_fe_id is not present in rider_fod_metrics_v2 for today then rider is considered new_rider\nFor past (t-7, t-14, t-28) if rider fod matches the date then it is considered new rider for that day",
    filters:
      "current_status = 'DELIVERED' \norg_channel_id in ('1', '2')\ncity_name not in ('Not in service area')",
  },
  "ORDER CANCELLATION - STORES TRENDS": {
    value: "ORDER CANCELLATION - STORES TRENDS",
    explanation: "The percentage of canceled orders",
    source: "OMS > Order Lifecycle Events",
    field: "current_status, id",
    calculation:
      "Count of orders where current_status='CANCELLED'/ Total Orders",
  },
  "CART VOLUME - HOURLY TRENDS": {
    value: "CART VOLUME - HOURLY TRENDS",
    explanation: "The number of unique carts created in an hour",
    calculation: "COUNT(DISTINCT(cart_id)) as order_count",
  },
  "INSTORE SLA % LESS THAN 2.5 MINS - HOURLY TRENDS": {
    value: "INSTORE SLA % LESS THAN 2.5 MINS - HOURLY TRENDS",
    explanation:
      "The percentage of distinct orders whose SLA from picker assign timestamp to order billed timestamp is within 2.5 minutes",
    calculation:
      "100 * SUM(CASE WHEN ((order_billed_timestamp - picker_assignment_queued_time) <= 150000) THEN 1 ELSE 0 END) / (COUNT(DISTINCT(id)) -1) AS percent_instore_sla_less_than_150_sec",
  },
  "PICKING TIME PER ITEM - HOURLY TRENDS": {
    value: "PICKING TIME PER ITEM - HOURLY TRENDS",
    explanation: "The time taken to pick each item",
    calculation:
      "SUM((CASE WHEN (pick_completion_time < 0 OR picking_start_time < 0) THEN 0 ELSE pick_completion_time - picking_start_time END)/1000) / SUM(((total_items_quantity - jsonextractscalar(paas_metrics, '$.quantity', 'INT', 0)) + jsonextractscalar(paas_metrics, '$.sku', 'INT', 0))) as ppi_in_seconds",
  },
  "PICKER ASSIGNMENT % (10 secs) - HOURLY TRENDS": {
    value: "PICKER ASSIGNMENT % (10 secs) - HOURLY TRENDS",
    explanation:
      "The percentage of orders where picker was assigned within 10 seconds of order checkout",
    calculation:
      "100 * SUM(CASE WHEN (picker_assigned_timestamp - checkout_timestamp) <= 10000 then 1 else 0 end) / count(id) as percentage_checkout_to_picker_assigned",
    showWeekTrend: false,
  },
  "PICKING START % (5 secs) - HOURLY TRENDS": {
    value: "PICKING START % (5 secs) - HOURLY TRENDS",
    explanation:
      "The percentage of orders where picking was started within 5 seconds of picker assignment.",
    calculation:
      "100 * SUM(CASE WHEN (picking_start_time - picker_assigned_timestamp) <= 5000 then 1 else 0 end) / count(id) as percentage_picker_assigned_to_picking_start",
    showWeekTrend: false,
  },
  "PICKER SURGE ORDERS % - HOURLY TRENDS": {
    value: "PICKER SURGE ORDERS % - HOURLY TRENDS",
    explanation:
      "The number of orders where the surge was applied because of picker shortage",
    calculation:
      "100 * (DISTINCTCOUNT( CASE WHEN surge_type = 'mec_picker_surge' THEN cart_id ELSE -1 END) - 1) / COUNT(DISTINCT(cart_id)) AS picker_surge_orders",
    showWeekTrend: false,
  },
  "BOTH SURGE ORDERS % - HOURLY TRENDS": {
    value: "BOTH SURGE ORDERS % - HOURLY TRENDS",
    explanation:
      "The number of orders where the surge was applied because of picker as well as rider shortage",
    calculation:
      "100 * (DISTINCTCOUNT( CASE WHEN surge_type = 'mec_fe_surge_and_mec_picker_surge' THEN cart_id ELSE -1 END) - 1) / COUNT(DISTINCT(cart_id)) AS both_surge_orders",
    showWeekTrend: false,
  },
  "FILL RATE % - HOURLY TRENDS": {
    value: "FILL RATE % - HOURLY TRENDS",
    explanation:
      "The percentage of orders where all the items were fulfilled in an order (SM Fill Rate) ",
    calculation:
      "100 * (SUM(CASE WHEN (current_status != 'APPROVED')  then 1 else 0 end) - (SUM( case when (sm_fill_rate = 0 AND current_status != 'APPROVED') then 1 else 0 end)))/(SUM( CASE WHEN (current_status != 'APPROVED')  then 1 else 0 end)) as sm_fill_rate",
    showWeekTrend: false,
  },
  "IPO - HOURLY TRENDS": {
    value: "IPO - HOURLY TRENDS",
    explanation: "The number of procured items per order",
    calculation:
      "SUM(total_procured_items_quantity) / count(id) as total_procured_items_quantity",
    showWeekTrend: false,
  },
  "TOTAL ACTIVE HOURS - HOURLY TRENDS": {
    value: "TOTAL ACTIVE HOURS - HOURLY TRENDS",
    explanation:
      "Total login time of picker, putter, auditor, fnv and others combined",
    calculation: "SUM(total_active_time) AS total_active_time_in_seconds",
    showWeekTrend: false,
  },
  "PICKER ACTIVE HOURS % - HOURLY TRENDS": {
    value: "PICKER ACTIVE HOURS % - HOURLY TRENDS",
    explanation:
      "The percentage of login time of pickers from total active time",
    calculation:
      "100 * (SUM(picker_active_time) / SUM(total_active_time)) AS picker_active_time_percentage",
    showWeekTrend: false,
  },
  "PUTTER ACTIVE HOURS % - HOURLY TRENDS": {
    value: "PUTTER ACTIVE HOURS % - HOURLY TRENDS",
    explanation:
      "The percentage of login time of putters from total active time",
    calculation:
      "100 * (SUM(putter_active_time) / SUM(total_active_time)) AS putter_active_time_percentage",
    showWeekTrend: false,
  },
  "AUDITOR ACTIVE HOURS % - HOURLY TRENDS": {
    value: "AUDITOR ACTIVE HOURS % - HOURLY TRENDS",
    explanation:
      "The percentage of login time of auditors from total active time",
    calculation:
      "100 * (SUM(auditor_active_time) / SUM(total_active_time)) AS auditor_active_time_percentage",
    showWeekTrend: false,
  },
  "FNV ACTIVE HOURS % - HOURLY TRENDS": {
    value: "FNV ACTIVE HOURS % - HOURLY TRENDS",
    explanation: "The percentage of login time of FNV from total active time",
    calculation:
      "100 * (SUM(fnv_active_time) / SUM(total_active_time)) AS fnv_active_time_percentage",
    showWeekTrend: false,
  },
  "OPH - HOURLY TRENDS": {
    value: "OPH - HOURLY TRENDS",
    explanation: "The average number of orders picked per hour",
    calculation:
      "(SUM(total_orders) / (SUM(picker_active_time)/3600)) AS orders_picked_per_hour",
    showWeekTrend: false,
  },
  "IPH - HOURLY TRENDS": {
    value: "IPH - HOURLY TRENDS",
    explanation: "The average number of items put away per hour",
    calculation:
      "(SUM(packaged_putaway) / (SUM(putter_active_time)/3600)) AS items_putaway_per_hour",
    showWeekTrend: false,
  },
  "COMPLAINTS % - HOURLY TRENDS": {
    value: "COMPLAINTS % - HOURLY TRENDS",
    explanation:
      "The percentage of orders in which any time of complaint was raised",
    calculation: "COUNT(1) as total_complaints",
    showWeekTrend: false,
  },
  "WAREHOUSE DISPATCH METRICS": {
    value: "WAREHOUSE DISPATCH METRICS",
    explanation: "Dispatch metrics aggregated on warehouse level",
    source:
      "WMS > outbound-container + dispatch-consignment-trip (real-time)\npo.sto + po.sto_items (batched)",
    field:
      "total_quantity, epoch_expected_dispatch_time, epoch_event_ts, destination_outlet_id\nreserved_quantity (total_demand_quantity), dispatch_time, merchant_id, outlet_id",
    calculation:
      "Join wms dispatch real-time and batched sto demand data on dispatch_time, outlet_id, destination_outlet_id -> if epoch_expected_dispatch_time - epoch_event_ts >= -1800 (30 mins) then dispatch is considered on_time\n",
    hasNoTrendMetric: true,
  },
  "OTIF %": {
    value: "Cumulative On Time Item Fill Rate From 12:00 AM",
    explanation:
      "Percentage of Items Dispatched within the expected dispatch time from total items for which demand was raised after 12:00 AM",
    source:
      "WMS > outbound-container + dispatch-consignment-trip (real-time)\npo.sto + po.sto_items (batched)",
    field:
      " total_quantity, epoch_expected_dispatch_time, epoch_event_ts, destination_outlet_id\n reserved_quantity (total_demand_quantity), dispatch_time, merchant_id, outlet_id",
    calculation:
      "Join wms dispatch real-time and batched sto demand data on dispatch_time, outlet_id, destination_outlet_id -> if epoch_expected_dispatch_time - epoch_event_ts >= -1800 (30 mins) then dispatch is considered on time\n\nsum(on_time_dispatch_qty)/sum(total_demand_quantity)",
    hasNoTrendMetric: true,
  },
  "OTD %": {
    value: "Cumulative On Time Dispatch From 12:00 AM",
    explanation:
      "Percentage of on-time dispatches from  total number of demands raised",
    source:
      "WMS > outbound-container + dispatch-consignment-trip (real-time)\npo.sto + po.sto_items (batched)",
    field:
      " total_quantity, epoch_expected_dispatch_time, epoch_event_ts, destination_outlet_id\nreserved_quantity (total_demand_quantity), dispatch_time, merchant_id, outlet_id",
    calculation:
      "Join wms dispatch real-time and batched sto demand data on dispatch_time, outlet_id, destination_outlet_id -> if epoch_expected_dispatch_time - epoch_event_ts >= -1800 (30 mins) then dispatch is considered on time\n\nmean(on_time)",
    hasNoTrendMetric: true,
  },
  "Dispatch Fill Rate %": {
    value: "Cumulative Total Dispatch Fill Rate From 12:00 AM",
    explanation:
      "Percentage of Total Items Dispatched from total items for which demand was raised after 12:00 AM",
    source:
      "WMS > outbound-container + dispatch-consignment-trip (real-time)\npo.sto + po.sto_items (batched)",
    field:
      " total_quantity, epoch_expected_dispatch_time, epoch_event_ts, destination_outlet_id\n reserved_quantity (total_demand_quantity), dispatch_time, merchant_id, outlet_id",
    calculation:
      "Join wms dispatch real-time and batched sto demand data on dispatch_time, outlet_id, destination_outlet_id -> if epoch_expected_dispatch_time - epoch_event_ts >= -1800 (30 mins) then dispatch is considered on time\n\nsum(total_quantity)/sum(total_demand_quantity)",
    hasNoTrendMetric: true,
  },
  "WAREHOUSE DISPATCH METRICS - STORES TRENDS": {
    value: "WAREHOUSE DISPATCH METRICS - STORES TRENDS",
    explanation:
      "Dispatch metrics aggregated on store level for each warehouse",
    source:
      "WMS > outbound-container + dispatch-consignment-trip (real-time)\npo.sto + po.sto_items (batched)",
    field:
      "total_quantity, epoch_expected_dispatch_time, epoch_event_ts, destination_outlet_id\nreserved_quantity (total_demand_quantity), dispatch_time, merchant_id, outlet_id",
    calculation:
      "Join wms dispatch real-time and batched sto demand data on dispatch_time, outlet_id, destination_outlet_id -> if epoch_expected_dispatch_time - epoch_event_ts >= -1800 (30 mins) then dispatch is considered on_time\n",
    hasNoTrendMetric: true,
  },
  "CURRENT RATE PROJECTION (FROM 7 AM)": {
    value: "CURRENT RATE PROJECTION (FROM 7 AM)",
    explanation:
      "Projected Order Volume and GMV based on the current rate of orders today",
    calculation: `Current Rate Metric = Actual Value for 'Realised Hours' + Normalised Projected Values for Unrealised Hours'\n
Normalised Projected Values = Unrealised Projection + (Unrealised Projection * Realised Deviation)\n
Realised Deviation = (Actual Value for 'Realised Hours' - Projected Value for 'Realised Hours') / (Projected Value for 'Realised Hours')`,
    showWeekTrend: true,
  },
  "PROJECTED ORDER METRICS- TODAY": {
    value: "PROJECTED ORDER METRICS- TODAY",
    explanation:
      "Projected Order Volume and GMV for today based on historical metrics",
    source: "not available",
    calculation:
      "sum(carts*aov) AS proj_gmv, round(sum(carts),0)*0.993 AS proj_carts",
    showWeekTrend: true,
  },
  "PROJECTED CART VOLUME - HOURLY": {
    value: "PROJECTED CART VOLUME - HOURLY",
    explanation: "Projected cart volume and Difference with Actual WoW",
  },
  "PROJECTED CART VOLUME - DAILY TRENDS": {
    value: "PROJECTED CART VOLUME - DAILY TRENDS",
    explanation: `Daily Projection of Order Volume for the next 15 days.\n
    Dark grey cells indicates weekend. Amber cells denote an event.`,
    source: "not available",
    calculation:
      "sum(carts*aov) AS proj_gmv, round(sum(carts),0)*0.993 AS proj_carts",
    hasNoTrendMetric: true,
  },
  "PROJECTED GMV - DAILY TRENDS": {
    value: "PROJECTED GMV - DAILY TRENDS",
    explanation: `Daily Projection of GMV for the next 15 days.\n
    Dark grey cells indicates weekend. Amber cells denote an event.`,
    source: "not available",
    calculation:
      "sum(carts*aov) AS proj_gmv, round(sum(carts),0)*0.993 AS proj_carts",
    hasNoTrendMetric: true,
  },
  "Delivered Cart Volume": {
    value: "Delivered Cart Volume",
    explanation: "Count of total delivered carts",
    source: "OMS",
    field: "cart_id",
    calculation: "COUNT(DISTINCT cart_id)",
  },
  "Absolute Retained Margin": {
    value: "Absolute Retained Margin",
    explanation: "Sum of total Retained Margin",
    source: "OMS",
    field: "rm",
    calculation: "SUM(absolute_retained_margin)",
  },
  "Retained Margin": {
    value: "Retained Margin",
    explanation: "Total Retained Margin percentage out of total GMV",
    source: "OMS",
    field: "rm, gmv",
    calculation: "Total Retained Margin / Total GMV",
  },
  "Weekly Active Users": {
    value: "Weekly Active Users",
    explanation:
      "Unique device count (web not included) for that City/Pan-India for that week.",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
  },
  "Monthly Active Users": {
    value: "Monthly Active Users",
    explanation:
      "Unique device count (web not included) for that City/Pan-India for that month.",
    source: "App events (jumbo pipeline)",
    field: "device ID",
    calculation: "Count of unique device IDs",
  },
  "Order Conversion": {
    value: "Order Conversion",
    explanation: "No of customers who placed an order out of DAU",
    source: "OMS",
    field: "conv, dau",
    calculation: "Transacting Users * 100.00 / DAU",
  },
  "Total Items/Order": {
    value: "Total Items/Order",
    explanation: "Average no of items per order",
    source: "OMS",
    field: "conv, dau",
    field: "product_quantity, order_id",
    calculation: "Total Product Quantity / total count of the Orders",
  },
  "TOTAL ITEMS SOLD - CITIES TRENDS": {
    value: "TOTAL ITEMS SOLD - CITIES TRENDS",
    explanation: "The total number of items sold in each city.",
    source: "OMS > Order Lifecycle Events",
    field: "Order > Items > quantity > Count",
    calculation: "SUM(total_items_quantity)",
  },
  "Average Selling Price": {
    value: "Average Selling Price",
    explanation: "The Average Selling Price of the selected items.",
    source: "OMS",
    field: "total_cost",
    calculation: "AVG(price)",
    showWeekTrend: true,
  },
  "Canceled Quantity": {
    value: "Canceled Quantity",
    explanation: "Total count of products where order is cancelled.",
    source: "OLS",
    field: "product_quantity, order_id",
    calculation: "COUNT(qty)",
    showWeekTrend: true,
  },
  "All Complaints": {
    value: "All Complaints",
    explanation:
      "Total and category wise count of complaints on the selected item.",
    source: "Storeops Order Complaints",
    field: "complaint_id, complaint_type",
    calculation: "COUNT(distinct complaint_id)",
    showWeekTrend: true,
  },
  "Order Per Store/Day": {
    value: "Order Per Store/Day",
    explanation:
      "Order per store - total orders / total active stores on that day.",
  },
  "Surge Seen %": {
    value: "Surge Seen %",
    explanation: "Percentage of Cart Instances where surge was applicable.",
    source: "",
    field: "",
    calculation: "",
    showWeekTrend: true,
  },
  "Rider Surge Seen %": {
    value: "Rider Surge Seen %",
    explanation:
      "Percentage of Cart Instances where rider surge was applicable.",
    source: "",
    field: "",
    calculation: "",
    showWeekTrend: true,
  },
  "Picker Surge Seen %": {
    value: "Picker Surge Seen %",
    explanation:
      "Percentage of Cart Instances where picker surge was applicable.",
    source: "",
    field: "",
    calculation: "",
    showWeekTrend: true,
  },
  "Rain Surge Seen %": {
    value: "Rain Surge Seen %",
    explanation:
      "Percentage of Cart Instances where rain surge was applicable.",
    source: "",
    field: "",
    calculation: "",
    showWeekTrend: true,
  },
  "Express OOH Based Block %": {
    value: "Express OOH Based Block %",
    explanation:
      "Percentage of unique express users blocked due to outside operating hours.",
    source: "",
    field: "",
    calculation: "",
    showWeekTrend: true,
  },
  "Express Manual Based Block %": {
    value: "Express Manual Based Block %",
    explanation:
      "Percentage of unique express users blocked due to manual store block and polygon throttle.",
    source: "",
    field: "",
    calculation: "",
    showWeekTrend: true,
  },
  "Promo %": {
    value: "Promo %",
    explanation: "Percentage of orders with discount",
    showWeekTrend: true,
  },
  "Express Demand Based Block %": {
    value: "Express Demand Based Block %",
    explanation:
      "Percentage of unique express users blocked due to demand-supply mismatch",
    showWeekTrend: true,
  },
  "Longtail Demand Based Block %": {
    value: "Longtail Demand Based Block %",
    explanation:
      "Percentage of unique users serviceable under express but blocked under longtail due to demand-supply mismatch",
    showWeekTrend: true,
  },
  "Longtail OOH Based Block %": {
    value: "Longtail OOH Based Block %",
    explanation:
      "Percentage of unique users serviceable under express but blocked under longtail due to outside operating hours",
    showWeekTrend: true,
  },
  "Longtail Manual Based Block %": {
    value: "Longtail Manual Based Block %",
    explanation:
      "Percentage of unique users serviceable under express but blocked under longtail due to manual store block and polygon throttle",
    showWeekTrend: true,
  },
});
