import {
  HomeIcon,
  GlobeAltIcon,
  BuildingLibraryIcon,
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  LightBulbIcon,
  PresentationChartLineIcon,
  ShoppingCartIcon,
  ClockIcon,
  BuildingOffice2Icon,
  ArchiveBoxXMarkIcon,
  TagIcon,
  ArrowTrendingDownIcon,
  UserGroupIcon,
  TruckIcon,
  PrinterIcon,
} from "@heroicons/vue/24/outline";

export const PAGE_ID_ROUTE_MAPPING = Object.freeze({
  1: "/",
  2: "/cities-stores-trend",
  3: "/stores",
  4: "/instore",
  5: "/warehouses",
  6: "/availability",
  7: "/wtd-mtd",
  8: "/current-rate",
  9: "/insights",
  10: "/product-details",
  11: "/city-insights",
  12: "/store-insights",
  13: "/ptype-insights",
  14: "/complaints",
  15: "/item-insights",
  18: "/category-insights",
  19: "/bad-stocks",
  20: "/emergency-services",
  24: "/group-maker",
  25: "/paas",
});

export const BISTRO_PAGE_ID_ROUTE_MAPPING = Object.freeze({
  16: "/bistro",
  17: "/bistro/product-details",
  21: "/bistro/cities-stores-trend",
  22: "/bistro/wtd-mtd",
  23: "/bistro/inkitchen",
});

export const SONAR_NAVIGATION = [
  { name: "Home", href: "/", current: false, hidden: false, Icon: HomeIcon },
  {
    name: "Cities / Stores",
    href: "/cities-stores-trend",
    current: false,
    hidden: false,
    Icon: GlobeAltIcon,
  },
  {
    name: "Stores ( Live )",
    href: "/stores",
    current: false,
    hidden: false,
    Icon: BuildingOffice2Icon,
  },
  {
    name: "Instore",
    href: "/instore",
    current: false,
    hidden: false,
    Icon: BuildingStorefrontIcon,
  },
  {
    name: "Warehouses",
    href: "/warehouses",
    current: false,
    hidden: false,
    Icon: BuildingLibraryIcon,
  },
  {
    name: "Availability",
    href: "/availability",
    current: false,
    hidden: false,
    Icon: ClockIcon,
  },
  {
    name: "WTD / MTD",
    href: "/wtd-mtd",
    current: false,
    hidden: false,
    Icon: CalendarDaysIcon,
  },
  {
    name: "Projection",
    href: "/current-rate",
    current: false,
    hidden: false,
    Icon: PresentationChartLineIcon,
  },
  {
    name: "Insights",
    href: "/insights",
    current: false,
    hidden: false,
    Icon: LightBulbIcon,
  },
  {
    name: "Product Details",
    href: "/product-details",
    current: false,
    hidden: false,
    Icon: ShoppingCartIcon,
  },
  {
    name: "Category Insights",
    href: "/category-insights",
    current: false,
    hidden: false,
    Icon: TagIcon,
  },
  {
    name: "Complaints",
    href: "/complaints",
    current: false,
    hidden: false,
    Icon: ArchiveBoxXMarkIcon,
  },
  {
    name: "Bad Stocks",
    href: "/bad-stocks",
    current: false,
    hidden: false,
    Icon: ArrowTrendingDownIcon,
  },
  {
    name: "Emergency Services",
    href: "/emergency-services",
    current: false,
    hidden: false,
    Icon: TruckIcon,
  },
  {
    name: "PAAS",
    href: "/paas",
    current: false,
    hidden: false,
    Icon: PrinterIcon,
  },
  {
    name: "Group Maker",
    href: "/group-maker",
    current: false,
    hidden: false,
    Icon: UserGroupIcon,
  },
];

// not showing icon as it is not in the allowedNavs list yet.....
export const BISTRO_NAVIGATION = [
  {
    name: "Home",
    href: "/bistro",
    current: false,
    hidden: false,
    Icon: HomeIcon,
  },
  {
    name: "Product Details",
    href: "/bistro/product-details",
    current: false,
    hidden: false,
    Icon: ShoppingCartIcon,
  },
  {
    name: "Cities / Stores",
    href: "/bistro/cities-stores-trend",
    current: false,
    hidden: false,
    Icon: GlobeAltIcon,
  },
  {
    name: "WTD / MTD",
    href: "/bistro/wtd-mtd",
    current: false,
    hidden: false,
    Icon: CalendarDaysIcon,
  },
  {
    name: "In Kitchen",
    href: "/bistro/inkitchen",
    current: false,
    hidden: false,
    Icon: BuildingStorefrontIcon,
  },
];
