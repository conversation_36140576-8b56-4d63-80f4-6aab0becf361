<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedCityName && computedCityName.displayName ?
                computedCityName.displayName : this.selectedCity }}</div>
            <div class="text-3xl font-bold text-gray-900" v-if="this.selectedStore">Store: {{
                this.selectedStore.merchant_name_id }} </div>
        </div>
    </header>

    <div
        class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10">
        <div>
            <div class="text-left pb-2 font-semibold italic">City :</div>
            <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
                placeholder="click to select city" class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Store :</div>
            <v-select v-model="selectedStore" :options="computedStoreList" label="merchant_name_id"
                @update:model-value="onStoreChange($event)" :clearable="false" placeholder="click to select store"
                class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Date :</div>
            <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single" :maxDate="new Date()"
                :minDate="computedMinDate" showButtonBar inputClass="w-full" panelClass="p-1" class="w-full"
                :inputStyle="{ height: '36px', marginBottom: '4px' }" @update:model-value="onDateChange($event)"
                dateFormat="yy-mm-dd" />
        </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">
            Some internal error occured. <br />
            Please reach out to data <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>

    <main v-else>
        <WaterMark></WaterMark>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <div class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
                <div v-for="metric in computedAllMetrics" :key="metric.name"
                    class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow"
                    :class="metric.name === 'Ratings' ? 'hover:shadow-lg' : ''" @click="handleRatings(metric.name)">
                    <div class="flex justify-between px-1 md:px-2">
                        <span
                            class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                            {{ metric.name }}
                            <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                            <i v-if="hasExtraMetrics(metric.name)" class="pi pi-plus-circle ml-2"
                                style="font-size: 0.7rem !important;" @click="showExtraMetrics(metric.name)"></i>
                        </span>
                        <AnimatedNumber
                            :styles="['text-lg', 'font-semibold', highlightATHMetrics(metric) ? 'linear-wipe' : '']"
                            :value="metric.curr.value" :type="metric.curr.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs font-light gray-900"></span>
                        <div class="flex justify-between">
                            <div v-for="diff in metric.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                    </template>
                                    <template v-else>-</template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric"> »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="showComplaintsPanel" v-for="metric in computedTotalComplaintsMetrics" :key="metric.name"
                    class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow md:hover:shadow-lg"
                    @click="showComplaints = !showComplaints">
                    <div class="flex justify-between px-1 md:px-2">
                        <span
                            class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
                                metric.name }}
                            <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                        </span>
                        <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr?.value"
                            :type="metric?.curr?.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs font-light gray-900"></span>
                        <div class="flex justify-between">
                            <div v-for="diff in metric.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                    </template>
                                    <template v-else>
                                        -
                                    </template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric">
                                    »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <MetricsGrid v-for="metricGridItem in computedMetricConfig" :shouldRender="metricGridItem.shouldRender"
            :metrics="metricGridItem.metrics" :metricItemName="metricGridItem.metricItemName"
            :title="metricGridItem.title" :isReverse="metricGridItem.isReverse" :showHeader="showHeader" />


        <Dialog v-model:visible="showComplaints" modal header="All Complaints"
            :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
            <div class="w-80 md:w-full">
                <DataTable :value="computedTableData" tableClass="text-xs md:text-md" showGridlines removableSort
                    scrollable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                    <Column field="complaint_type" header="Complaint Type" bodyClass="p-1 md:px-4 md:py-3"
                        headerClass="p-1 md:px-4 md:py-3">
                    </Column>
                    <Column v-for="(date, index) in computedTableColumns" :key="index" :field="date" :header="date"
                        sortable>
                    </Column>
                </DataTable>
            </div>
        </Dialog>


        <Dialog v-model:visible="showRatings" modal :header="selectedRatingsItem"
            :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
            <div class="w-80 md:w-full">
                <DataTable :value="computedRatingsTableData" tableClass="text-xs md:text-md" showGridlines removableSort
                    scrollable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                    <Column field="rating_type" header="Ratings" bodyClass="p-1 md:px-4 md:py-3"
                        headerClass="p-1 md:px-4 md:py-3">
                    </Column>
                    <Column v-for="(date, index) in computedRatingsTableColumns" :key="index" :field="date"
                        :header="date" sortable>
                    </Column>
                </DataTable>
            </div>
        </Dialog>

        <Dialog v-model:visible="visible" modal :header="extraMetricKey" :style="{ width: '45rem' }"
            :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :dismissableMask="true">
            <div>
                <div v-if="computedNote" class="mb-3 text-xs md:text-sm italic">
                    <strong>Note : </strong>
                    <span>{{ computedNote }}</span>
                </div>
                <div v-for="metric in computedExtraMetrics" :key="metric.name"
                    class="md:bg-white py-1.5 md:py-4 border-b md:border-b-0 md:rounded ">
                    <div class="flex justify-between">
                        <span
                            class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
                                metric.name }}
                            <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                        </span>
                        <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                            :type="metric.curr.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs font-light gray-900"></span>
                        <div class="flex justify-between">
                            <div v-for="diff in metric.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                    </template>
                                    <template v-else>
                                        -
                                    </template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric"> »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Dialog>
    </main>
</template>


<script>
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import { getModifiedStoreList, getPanIndiaStoreCityList, getUserAccessMapping, getUserAllowedStoresMapping, isCityUser, isGlobalUser, noPermissionRoute, formatDateToString } from "../../utils/utils.js";
import { bistroApi } from "../../api/index.js";
import { MetricChange } from "../../interfaces/index.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import MetricsGrid from "../../components/MetricsGrid.vue";
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { BISTRO_PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { ABSOLUTE_METRIC_DELTA, hourlyMetrics, metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import SelectButton from "primevue/selectbutton";
import { homeMetricGridMapping } from "../../configurations/Bistro/homeMetricGridMapping.js";
import { BISTRO_ORDER_METRIC_LIST, BISTRO_HOURLY_ORDER_METRIC_LIST, BISTRO_PROMO_METRIC_LIST } from "../../utils/bistroMetrics.js";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import vSelect from 'vue-select';
import { BistroHomeExtraMetricsMapping, PanIndiaConstants, RATINGS_MAPPING } from "../../constants/index.js";
import Dialog from "primevue/dialog";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import startCase from "lodash.startcase";
import Calendar from "primevue/calendar";
import { ATHMetricRelation } from "../../constants/index.js";
import Confetti from "vue-confetti";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

    components: {
        Loading,
        AnimatedNumber,
        MetricsGrid,
        WaterMark,
        InfoDialog,
        SelectButton,
        'v-select': vSelect,
        Dialog,
        DataTable,
        Column,
        Calendar,
        Confetti
    },

    computed: {
        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        computedRatings() {
            if (isEmpty(this.ratingMetrics)) return {}

            return {
                name: 'Ratings',
                type: 'number',
                data: this.ratingMetrics?.find((metricData) => metricData.name === 'Average Rating')?.data,
                ratingCountData: this.ratingMetrics?.find((metricData) => metricData.name === 'Total Count')?.data
            }
        },

        computedAllBlockMetrics() {
            let baseMetrics = [
                ...this.allMetrics,
                ...this.dauMetrics,
                ...this.riderMetrics,
                ...this.ipcMetrics
            ];

            if (!isEmpty(this.ratingMetrics)) {
                baseMetrics = [...baseMetrics, this.computedRatings]
            }
            if (!isEmpty(this.promoMetrics)) {
                baseMetrics = [...baseMetrics, ...this.promoMetrics]
            }

            return sortedAllMetrics(baseMetrics, undefined, undefined, undefined, this.computedDate);
        },

        computedHomeExtraMetricsMapping() {
            return BistroHomeExtraMetricsMapping;
        },

        computedExtraMetrics() {
            const metricsToShow = this.computedHomeExtraMetricsMapping[this.extraMetricKey];
            const metrics = this.computedAllBlockMetrics.filter((metric) => metricsToShow.includes(metric.name))
            return this.calculatedMetrics(metrics)
        },

        computedAllMetrics() {
            const metricsToHide = Object.values(this.computedHomeExtraMetricsMapping).map(metric => metric).flat();
            const allMetrics = this.computedAllBlockMetrics.filter((metric) => !metricsToHide.includes(metric.name))
            return this.calculatedMetrics(allMetrics)
        },

        computedHourlyOrderMetrics() {
            let orderCountMetric = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Cart Volume", this.computedDate);
            let percentSurgeShownCartsMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Surge Checkouts %", this.computedDate);
            let directHandoverMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Direct Handover %", this.computedDate);
            let checkouttoEnrouteMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Checkout to Enroute Time", this.computedDate);
            let indirectHandoverWaitTimeMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Rider Handshake time", this.computedDate);
            let rainorderMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Rain Order %", this.computedDate);
            return {
                orderCountMetric,
                percentSurgeShownCartsMetrics,
                directHandoverMetrics,
                checkouttoEnrouteMetrics,
                indirectHandoverWaitTimeMetrics,
                rainorderMetrics
            }
        },

        computedHourlyUserMetrics() {
            let hauMetrics = hourlyMetrics(this.hourlyUserMetrics, false, [], "Hourly Active Users", this.computedDate);
            let hauConversionMetrics = hourlyMetrics(this.hourlyUserMetrics, false, [], "HAU Conversion Percentage", this.computedDate);
            return {
                hauMetrics: hauMetrics,
                hauConversionMetrics: hauConversionMetrics
            };
        },

        computedHourlyRiderLoginMetrics() {
            return hourlyMetrics(this.hourlyRiderLoginMetrics, false, [], "Rider Login hrs", this.computedDate);
        },

        computedMetricConfig() {
            return homeMetricGridMapping(this);
        },

        computedStoreList() {
            return this.selectedCityStores;
        },

        computedStoreId() {
            return this.selectedStore.frontend_merchant_id
        },

        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getBistroStoreCityList));

            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
            return cityList;
        },

        computedCityName() {
            let modifiedCityMapping = getPanIndiaStoreCityList(this.getBistroStorePageCityMapping);
            let cityName = modifiedCityMapping[this.selectedCity]["name"];

            return {
                displayName: modifiedCityMapping[this.selectedCity]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        showComplaintsPanel() {
            return !isEmpty(this.allComplaints);
        },

        computedAllComplaints() {
            const red = this.allComplaints.reduce((obj, item) => {
                obj[item?.complaint_type] = isEmpty(obj?.[item?.complaint_type]) ? [item] : [...obj[item?.complaint_type], item];
                return obj;
            }, {});

            return Object.keys(red).map((type) => ({ complaint_type: type, data: red[type] }));
        },

        computedTableData() {
            return this.computedAllComplaints?.map(category => {
                const row = { complaint_type: startCase(category?.complaint_type) };
                category.data?.forEach(item => {
                    row[item.date] = item.count;
                });
                return row;
            });
        },

        computedTableColumns() {
            const dateColumns = [...new Set(this.computedAllComplaints?.flatMap(cat => cat.data?.map(d => d.date)))];

            // sorting dates in descending order:
            return dateColumns?.sort((a, b) => {
                var dateA = new Date(a);
                dateA = dateA.getTime();

                var dateB = new Date(b);
                dateB = dateB.getTime();

                return dateB - dateA;
            });
        },

        computedTotalComplaintsMetrics() {
            const complaintsAggregatedOnDate = this.allComplaints?.reduce((acc, item) => {
                if (item.date) {
                    if (acc.hasOwnProperty(item.date)) {
                        acc[item.date] += item.count ?? 0
                    } else {
                        acc[item.date] = item.count ?? 0
                    }
                }
                return acc
            }, {})


            const complaintData = [{
                name: "All Complaints",
                type: "number",
                data: Object.entries(complaintsAggregatedOnDate)?.map(([key, value]) => ({
                    date: key,
                    metric: value
                }))
            }]

            let metrics = [...sortedAllMetrics(complaintData, undefined, undefined, undefined, this.computedDate)]

            return metrics?.map(metric => {
                let data = metric.data;

                let currentVal = data[data.length - 1]
                let curr = currentVal ? {
                    meta: currentVal?.meta,
                    type: currentVal?.type,
                    value: currentVal?.value == "-" ? 0 : currentVal?.value
                } : currentVal

                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: true,
                    curr: curr,
                    diffs: diffs,
                }
            })?.filter(metric => metric.curr)
        },

        computedRatingsForTable() {
            const ratings = this.ratingMetrics
                ?.filter((data) => !['Total Count', 'Average Rating'].includes(data.name))
                ?.reduce((obj, metric) => {
                    obj[metric.name] = metric.data
                    return obj;
                }, {});

            return Object.keys(ratings)?.map((type) => ({ rating_type: type, data: ratings[type] }));
        },

        computedRatingsTableData() {
            const priorityArray = Object.values(RATINGS_MAPPING)
            return this.computedRatingsForTable?.map(rating => {
                const row = { rating_type: RATINGS_MAPPING[rating?.rating_type] };
                rating.data?.forEach(item => {
                    row[item.date] = item.metric;
                });
                return row;
            })?.sort((a, b) =>
                priorityArray.findIndex(x => x === a.rating_type) - priorityArray.findIndex(x => x === b.rating_type));
        },

        computedRatingsTableColumns() {
            const dateColumns = [...new Set(this.computedRatingsForTable?.flatMap(cat => cat.data?.map(d => d.date)))];

            return dateColumns?.sort((a, b) => {
                let dateA = new Date(a.date);
                let dateB = new Date(b.date);
                return dateB - dateA;
            });
        },

        computedDate() {
            return formatDateToString(this.selectedDate);
        },

        computedMinDate() {
            var day = new Date();
            day.setDate(day.getDate() - 25);
            return day;
        },

        showConfetti() {
            if (!this.isLoading && this.athHit && !this.getConfettiShown?.['present']) {
                return true;
            }
            return false;
        },

        ...mapState(useCityStore, ['getBistroHomePage', 'getBistroStoreToCityMapping', 'getBistroStoreCityList', 'getBistroStorePageCityMapping', 'getConfettiShown']),
        ...mapState(useUserStore, ['getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        filterMetricsWithNoData(metrics) {
            return metrics.filter(metric => metric.data.length > 0);
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getBistroStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            this.isLoading = false;
            let storeList = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            this.mappedList = storeList;

            if (this.isGlobalUser || this.isCityUser) {
                this.mappedList = getModifiedStoreList(storeList);
                this.mappedList[PanIndiaConstants.PAN_INDIA.code] = [];

                for (let cityName in this.mappedList) {
                    if (this.mappedList.hasOwnProperty(cityName)) {
                        let cityStores = this.mappedList[cityName];
                        if (((this.getBistroStoreToCityMapping && this.getBistroStoreToCityMapping?.find((item) => item?.city === (this.getBistroStoreCityList?.find((item) =>
                            item?.code === cityName)?.name)))?.data?.length <= cityStores?.length
                            || cityName === PanIndiaConstants.PAN_INDIA.code)) {
                            let panIndiaStore = {
                                frontend_merchant_id: '',
                                frontend_merchant_name: 'Overall',
                                backend_merchant_id: '',
                                name: cityName,
                                merchant_name_id: 'Overall'
                            };

                            cityStores.unshift(panIndiaStore);
                        }
                    }
                }
            }

            this.selectedCityStores = this.mappedList[this.selectedCity];
            if (this.selectedStore == null) {
                this.selectedStore = this.selectedCityStores[0];
            }
            this.isLoading = true;
            this.fetchAllMetrics();
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const allMetricsCancelTokenSource = axios.CancelToken.source();
                const promoMetricsCancelTokenSource = axios.CancelToken.source();
                const dauMetricsCancelTokenSource = axios.CancelToken.source();
                const hourlyMetricsCancelTokenSource = axios.CancelToken.source();
                const HAUMetricsCancelTokenSource = axios.CancelToken.source();
                const RiderMetricsCancelTokenSource = axios.CancelToken.source();
                const hourlyRiderMetricsCancelTokenSource = axios.CancelToken.source();
                const complaintsMetricsCancelTokenSource = axios.CancelToken.source();
                const athMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [allMetricsCancelTokenSource, promoMetricsCancelTokenSource, dauMetricsCancelTokenSource, hourlyMetricsCancelTokenSource, HAUMetricsCancelTokenSource, RiderMetricsCancelTokenSource, hourlyRiderMetricsCancelTokenSource, complaintsMetricsCancelTokenSource, athMetricsCancelTokenSource];

                if (!this.computedDate) {
                    this.isError = true;
                    throw new Error("Invalid date");
                }

                let metricsToFetch = []
                for (let idx = 0; idx < BISTRO_ORDER_METRIC_LIST.length; idx += 3) {
                    metricsToFetch.push(BISTRO_ORDER_METRIC_LIST.slice(idx, idx + 3));
                }
                metricsToFetch.push(["new_transacting_users_count"]);

                let promoMetricsToFetch = []
                for (let idx = 0; idx < BISTRO_PROMO_METRIC_LIST.length; idx += 3) {
                    promoMetricsToFetch.push(BISTRO_PROMO_METRIC_LIST.slice(idx, idx + 3));
                }

                let allMetrics = metricsToFetch.map((metrics) => bistroApi.fetchAllMetrics(metrics, this.computedCityName.cityName, this.computedStoreId, this.computedDate, allMetricsCancelTokenSource.token))

                let promoMetrics = promoMetricsToFetch.map((metrics) => bistroApi.fetchAllMetrics(metrics, this.computedCityName.cityName, this.computedStoreId, this.computedDate, promoMetricsCancelTokenSource.token))

                let dauMetrics = bistroApi.fetchAllDAUMetrics(this.computedCityName.cityName, this.computedStoreId, this.computedDate, dauMetricsCancelTokenSource.token)

                let hourlyMetrics = bistroApi.fetchHourlyMetrics(BISTRO_HOURLY_ORDER_METRIC_LIST, this.computedDate, this.computedCityName.cityName, this.computedStoreId, hourlyMetricsCancelTokenSource.token)

                let hauMetrics = bistroApi.fetchStoreHAU(this.computedCityName.cityName, this.computedStoreId, this.computedDate, HAUMetricsCancelTokenSource.token)

                let riderMetrics = bistroApi.fetchRiderMetrics(this.computedCityName.cityName, this.computedStoreId, this.computedDate, RiderMetricsCancelTokenSource.token)

                let hourlyRiderLoginMetrics = bistroApi.fetchHourlyRiderMetrics(this.computedCityName.cityName, this.computedStoreId, this.computedDate, hourlyRiderMetricsCancelTokenSource.token)

                let complaintsMetrics = bistroApi.fetchOrderComplaints(this.computedCityName.cityName, this.computedStoreId, this.computedDate, complaintsMetricsCancelTokenSource.token)

                let ratingMetrics = bistroApi.fetchOrderMetricsRatings(this.computedCityName.cityName, this.computedStoreId, this.computedDate, complaintsMetricsCancelTokenSource.token)

                let athMetrics = bistroApi.fetchATHMetricsBistro(this.computedCityName.cityName, this.computedStoreId, this.computedDate, athMetricsCancelTokenSource.token)

                let ipcMetrics = bistroApi.fetchIPCMetrics({ city: this.computedCityName.cityName, store: this.computedStoreId, date_str: this.computedDate, cancelToken: athMetricsCancelTokenSource.token })

                let [
                    allMetricsResponse,
                    promoMetricsResponse,
                    dauMetricsResponse,
                    hourlyMetricsResponse,
                    hauMetricsResponse,
                    riderMetricsResponse,
                    hourlyRiderLoginMetricsResponse,
                    complaintsMetricsResponse,
                    ratingMetricsResponse,
                    athMetricsResponse,
                    ipcMetricsResponse
                ] = await Promise.all([
                    Promise.all(allMetrics),
                    Promise.all(promoMetrics),
                    dauMetrics,
                    hourlyMetrics,
                    hauMetrics,
                    riderMetrics,
                    hourlyRiderLoginMetrics,
                    complaintsMetrics,
                    ratingMetrics,
                    athMetrics,
                    ipcMetrics
                ]);

                if (allMetricsResponse) {
                    let allMetrics = []
                    allMetricsResponse.forEach((response) => {
                        let metrics = this.filterMetricsWithNoData(response.data.metrics);
                        allMetrics.push(...metrics);
                    })
                    this.allMetrics = allMetrics
                }

                if (promoMetricsResponse) {
                    let promoMetrics = []
                    promoMetricsResponse.forEach((response) => {
                        let metrics = this.filterMetricsWithNoData(response.data.metrics);
                        promoMetrics.push(...metrics);
                    })
                    this.promoMetrics = promoMetrics
                }

                if (dauMetricsResponse) this.dauMetrics = this.filterMetricsWithNoData(dauMetricsResponse.data.metrics);

                if (hourlyMetricsResponse) this.hourlyOrderMetrics = hourlyMetricsResponse.data.metrics;

                if (hauMetricsResponse) this.hourlyUserMetrics = hauMetricsResponse.data.metrics;

                if (riderMetricsResponse) this.riderMetrics = this.filterMetricsWithNoData(riderMetricsResponse.data.metrics);

                if (hourlyRiderLoginMetricsResponse) this.hourlyRiderLoginMetrics = hourlyRiderLoginMetricsResponse.data.metrics;

                if (complaintsMetricsResponse) this.allComplaints = complaintsMetricsResponse.data.complaints;

                if (ratingMetricsResponse) this.ratingMetrics = ratingMetricsResponse.data.metrics;

                if (athMetricsResponse) this.athMetrics = this.filterMetricsWithNoData(athMetricsResponse.data.ath_data);

                if (ipcMetricsResponse) this.ipcMetrics = this.filterMetricsWithNoData(ipcMetricsResponse.data.metrics);

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error?.response?.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        start() {
            this.$confetti.start();
        },

        stop() {
            this.$confetti.stop();
        },

        love() {
            this.$confetti.update({
                particles: [{ type: 'heart' }, { type: 'circle' }, { type: 'rect' }],
                defaultColors: ['red', 'pink', '#ba0000'],
            });
        },

        highlightATHMetrics(metric) {
            const metricName = metric.name;
            const metricValue = metric.curr?.value;
            const metricDate = metric.curr?.meta?.date;

            if (ATHMetricRelation[metricName]) {
                const relatedMetric = this.athMetrics.find(m => m.name === ATHMetricRelation[metricName])?.data?.[0];

                if (relatedMetric) {
                    const meetsCondition = metricDate === relatedMetric.date
                        ? metricValue >= relatedMetric.metric
                        : metricValue > relatedMetric.metric;

                    if (meetsCondition) {
                        this.athHit = true;
                        return true;
                    }
                }
            }
            return false;
        },

        midnightTask() {
            this.pageLocalStorage["selectedDate"] = new Date();
            this.pageLocalStorage["currentDate"] = (new Date()).getDate();
            this.updateBistroHomePage(this.pageLocalStorage);
            this.updateConfettiShown({ present: false });
            window.location.reload();
        },

        resetAllMetrices() {
            this.allMetrics = []
            this.dauMetrics = []
            this.hourlyOrderMetrics = []
            this.hourlyUserMetrics = []
            this.riderMetrics = []
            this.hourlyRiderLoginMetrics = []
            this.allComplaints = []
            this.ratingMetrics = []
            this.athMetrics = []
            this.ipcMetrics = []
        },

        calculatedMetrics(metrics) {
            return metrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                if (metric.name === 'Ratings') {
                    return {
                        name: metric.name,
                        reverseStyle: metricsForStyleReversal.includes(metric.name),
                        curr: curr,
                        diffs: diffs,
                        ratingCount: curr?.meta?.metric
                    }
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },

        refreshFiltersList() {
            fetchStoreToCityMapping(false, true);
        },

        onStoreChange() {
            this.isLoading = true
            this.isError = false
            this.resetAllMetrices()
            this.fetchAllMetrics()
            this.pageLocalStorage['selectedStore'] = this.selectedStore
            this.updateBistroHomePage(this.pageLocalStorage)
        },

        onCityChange(event) {
            this.isLoading = true;
            this.isError = false;
            this.selectedCityStores = this.mappedList[this.selectedCity];
            this.selectedStore = this.selectedCityStores[0];
            this.resetAllMetrices();
            this.fetchAllMetrics();
            this.pageLocalStorage['selectedStore'] = this.selectedStore
            this.updateBistroHomePage(this.pageLocalStorage)
        },

        onDateChange(event) {
            this.isLoading = true;
            this.isError = false;
            this.resetAllMetrices();
            this.fetchAllMetrics();
            this.pageLocalStorage['selectedDate'] = this.selectedDate
            this.updateBistroHomePage(this.pageLocalStorage);
        },


        handleRatings(metricName) {
            if (metricName !== 'Ratings') return
            this.showRatings = true
        },

        hasExtraMetrics(metricName) {
            return Object.keys(this.computedHomeExtraMetricsMapping).includes(metricName)
        },

        showExtraMetrics(metricName) {
            if (this.hasExtraMetrics(metricName)) {
                this.extraMetricKey = metricName;
                this.visible = true;
            }
            return;
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateBistroHomePage', 'updateConfettiShown']),
    },

    data() {
        return {
            showHeader: false,
            isLoading: true,
            isError: false,
            intervalId: null,
            canAccessPage: false,
            selectedDate: "",
            cancelTokens: [],
            pageLocalStorage: {},
            selectedCity: null,
            mappedList: {},
            selectedCityStores: [],
            selectedStore: null,
            isGlobalUser: false,
            isCityUser: false,
            showComplaints: false,
            showRatings: false,
            extraMetricKey: "",
            visible: false,
            allMetrics: [],
            promoMetrics: [],
            hourlyOrderMetrics: [],
            hourlyUserMetrics: [],
            dauMetrics: [],
            riderMetrics: [],
            hourlyRiderLoginMetrics: [],
            allComplaints: [],
            ratingMetrics: [],
            athMetrics: [],
            ipcMetrics: [],
            athHit: false,
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        },

        showConfetti(newVal) {
            if (newVal) {
                this.start();
                setTimeout(() => {
                    this.stop();
                    this.updateConfettiShown({ present: true });
                }, 3000);
            }
        }
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/bistro/login');
        }
        this.canAccessPage = this.getAllowedNavs.includes(BISTRO_PAGE_ID_ROUTE_MAPPING[16]);
        if (!this.canAccessPage) noPermissionRoute(true)

        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (isEmpty(this.getBistroStoreToCityMapping)) this.refreshFiltersList();
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = true;
            this.isError = false;

            this.pageLocalStorage = this.getBistroHomePage ? this.getBistroHomePage : {}
            if (this.pageLocalStorage?.selectedStore) this.selectedStore = this.pageLocalStorage?.selectedStore

            let currentDate = (new Date()).getDate()
            if (this.pageLocalStorage && currentDate !== this.pageLocalStorage?.currentDate) {
                this.midnightTask()
            } else {
                this.selectedDate = isEmpty(this.pageLocalStorage?.selectedDate) ? new Date() : new Date(this.pageLocalStorage.selectedDate)
            }

            if (this.pageLocalStorage?.selectedStore) this.selectedCity = this.pageLocalStorage?.selectedStore?.name
            else if (this.isGlobalUser) this.selectedCity = PanIndiaConstants.PAN_INDIA.code;
            else this.selectedCity = Object.keys(this.getBistroStorePageCityMapping)[0]

            this.fetchCityStoreMapping();
            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}
</script>
