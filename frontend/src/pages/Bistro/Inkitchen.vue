<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedCityName && computedCityName.displayName ?
                computedCityName.displayName : this.selectedCity }}</div>
            <div class="text-3xl font-bold text-gray-900" v-if="this.selectedStore">Store: {{
                this.selectedStore.merchant_name_id }} </div>
        </div>
    </header>

    <div
        class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10">
        <div>
            <div class="text-left pb-2 font-semibold italic">City :</div>
            <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
                placeholder="click to select city" class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Store :</div>
            <v-select v-model="selectedStore" :options="computedStoreList" label="merchant_name_id"
                @update:model-value="onStoreChange($event)" :clearable="false" placeholder="click to select store"
                class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Date :</div>
            <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single" :maxDate="new Date()"
                :minDate="computedMinDate" showButtonBar inputClass="w-full" panelClass="p-1" class="w-full"
                :inputStyle="{ height: '36px', marginBottom: '4px' }" @update:model-value="onDateChange($event)"
                dateFormat="yy-mm-dd" />
        </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">
            Some internal error occured. <br />
            Please reach out to data <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>

    <main v-else>
        <WaterMark></WaterMark>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <div class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
                <div v-for="metric in computedAllMetrics" :key="metric.name"
                    class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow"
                    :class="metric.name === 'Ratings' ? 'hover:shadow-lg' : ''" @click="handleRatings(metric.name)">
                    <div class="flex justify-between px-1 md:px-2">
                        <span class="text-sm font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                            {{ metric.name }}
                            <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                        </span>
                        <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                            :type="metric.curr.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs font-light gray-900"></span>
                        <div class="flex justify-between">
                            <div v-for="diff in metric.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                    </template>
                                    <template v-else>-</template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric"> »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
            <div class="inline-block md:flex justify-between mb-4">
                <span class="text-lg font-bold text-blue-900 inline-flex items-center">
                    Station KPT
                    <InfoDialog metrickey="Station KPT" :showHeader="showHeader" class="p-0 ml-2 border border-black" />
                </span>
                <div class="text-xs md:text-sm font-thin mt-1 md:mt-0">Click on station cards to view detailed metrics
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 lg:gap-4">
                <MetricBox v-for="metric in computedKPTMetrics" :key="metric['city']"
                    :name="getStationName(metric['city'])" :curr="metric.curr" :prev="metric.prev"
                    @onClick="handleStationClick" isReverse hasHover />
            </div>
        </div>

        <div class="max-w-7xl mx-auto p-1 md:px-4 md:py-6">
            <div class="bg-green-100/40 rounded-xl shadow-custom p-2 my-4 md:my-0 md:p-6">
                <div class="md:grid grid-cols-3 items-center mb-2">
                    <p class="text-md md:text-lg font-semibold">Station Metric</p>

                    <div class="max-w-md relative col-start-3">
                        <v-select v-model="selectedStation" label="station_name"
                            :reduce="station => station?.station_id" :options="stationList"
                            @update:model-value="onStationChange($event)" :clearable="false"
                            class="vs-custom border-0 rounded-lg shadow-sm bg-white hover:shadow-md transition-shadow duration-200"
                            :class="{
                                'ring-2 ring-green-500': true,
                                'shadow-md': true
                            }" :styles="{
                                dropdown: { backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' },
                                option: { padding: '10px 12px', cursor: 'pointer', borderRadius: '4px' },
                                'selected-option': { padding: '10px 12px', fontWeight: '500' }
                            }" />
                    </div>
                </div>

                <div v-if="stationMetricLoading" class="flex items-center justify-center py-8">
                    <i class="pi pi-spin pi-spinner" style="color: blue;"></i>
                </div>

                <div v-else-if="stationMetricError" class="flex items-center justify-center">
                    <span class="text-center p-2 bg-red-200 rounded-lg text-xs md:text-sm font-semibold text-red-600">
                        Failed to load station metrics. Please try again.
                    </span>
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
                    <div v-for="metric in computedAllStationMetrics" :key="metric.name"
                        class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow"
                        :class="metric.name === 'Ratings' ? 'hover:shadow-lg' : ''" @click="handleRatings(metric.name)">
                        <div class="flex justify-between px-1 md:px-2">
                            <span class="text-sm font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                                {{ metric.name }}
                                <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                            </span>
                            <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                                :type="metric.curr.type" />
                        </div>
                        <div class="flex justify-between items-center px-2">
                            <span class="text-xs font-light gray-900"></span>
                            <div class="flex justify-between">
                                <div v-for="diff in metric.diffs" :key="diff.date">
                                    <span class="text-xs font-[650] mr-0.5"
                                        v-bind:class="diff.style(metric.reverseStyle)">
                                        <template v-if="diff.change() !== '-'">
                                            {{ diff.change() }}
                                            {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                        </template>
                                        <template v-else>-</template>
                                    </span>
                                    <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                        class="text-xs font-bold mr-1 text-positive-metric"> »
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="showComplaintsPanel" v-for="metric in computedTotalComplaintsMetrics" :key="metric.name"
                        class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow md:hover:shadow-lg"
                        @click="showComplaints = !showComplaints">
                        <div class="flex justify-between px-1 md:px-2">
                            <span class="text-sm font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                                {{ metric.name }}
                                <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                            </span>
                            <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr?.value"
                                :type="metric?.curr?.type" />
                        </div>
                        <div class="flex justify-between items-center px-2">
                            <span class="text-xs font-light gray-900"></span>
                            <div class="flex justify-between">
                                <div v-for="diff in metric.diffs" :key="diff.date">
                                    <span class="text-xs font-[650] mr-0.5"
                                        v-bind:class="diff.style(metric.reverseStyle)">
                                        <template v-if="diff.change() !== '-'">
                                            {{ diff.change() }}
                                            {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </span>
                                    <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                        class="text-xs font-bold mr-1 text-positive-metric">
                                        »
                                    </span>
                                </div>
                            </div>
                        </div>

                        <Dialog v-model:visible="showRatings" modal header="Station Rating" :style="{ width: '75rem' }"
                            :dismissableMask="true" class="p-4">
                            <div class="w-80 md:w-full">
                                <DataTable :value="computedRatingsTableData" tableClass="text-xs md:text-md"
                                    showGridlines removableSort scrollable paginator :rows="10"
                                    :rowsPerPageOptions="[5, 10, 20, 50]">
                                    <Column field="rating_type" header="Ratings" class="p-4" />
                                    <Column v-for="(date, index) in computedRatingsTableColumns" :key="index"
                                        :field="date" :header="date" sortable />
                                </DataTable>
                            </div>
                        </Dialog>

                        <Dialog v-model:visible="showComplaints" modal header="All Complaints"
                            :style="{ width: '75rem' }" :dismissableMask="true" class="p-4">
                            <div class="w-80 md:w-full">
                                <DataTable :value="computedTableData" tableClass="text-xs md:text-md" showGridlines
                                    removableSort scrollable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                                    <Column field="complaint_type" header="Complaint Type" class="p-4" />
                                    <Column v-for="(date, index) in computedTableColumns" :key="index" :field="date"
                                        :header="date" sortable />
                                </DataTable>
                            </div>
                        </Dialog>
                    </div>
                </div>
            </div>
        </div>

        <MetricsGrid v-for="metricGridItem in computedMetricConfig" :shouldRender="metricGridItem.shouldRender"
            :metrics="metricGridItem.metrics" :metricItemName="metricGridItem.metricItemName"
            :title="metricGridItem.title" :isReverse="metricGridItem.isReverse" :showHeader="showHeader" />
    </main>
</template>


<script>
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import { getModifiedStoreList, getPanIndiaStoreCityList, getUserAccessMapping, getUserAllowedStoresMapping, isCityUser, isGlobalUser, noPermissionRoute, formatDateToString } from "../../utils/utils.js";
import { bistroApi } from "../../api/index.js";
import { MetricChange } from "../../interfaces/index.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import MetricsGrid from "../../components/MetricsGrid.vue";
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { BISTRO_PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { ABSOLUTE_METRIC_DELTA, cityMetrics, hourlyMetrics, metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import SelectButton from "primevue/selectbutton";
import { BISTRO_INSTORE_METRIC_LIST, BISTRO_INSTORE_HOURLY_METRIC_LIST } from "../../utils/bistroMetrics.js";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import vSelect from 'vue-select';
import { PanIndiaConstants, RATINGS_MAPPING, } from "../../constants/index.js";
import { instoreMetricGridMapping } from "../../configurations/Bistro/instoreMetricGridMapping.js";
import Calendar from "primevue/calendar";
import Dialog from "primevue/dialog";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import startCase from "lodash.startcase";
import MetricBox from "../../components/MetricBox.vue";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {
    name: 'BistroInkitchen',

    components: {
        Loading,
        AnimatedNumber,
        MetricsGrid,
        WaterMark,
        InfoDialog,
        SelectButton,
        'v-select': vSelect,
        Calendar,
        Dialog,
        DataTable,
        Column,
        MetricBox
    },

    computed: {
        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        computedAllMetrics() {
            const allMetrics = sortedAllMetrics(this.allMetrics, undefined, undefined, undefined, this.computedDate);
            return this.calculatedMetrics(allMetrics)
        },

        computedRatings() {
            if (isEmpty(this.stationRatings)) return null

            return {
                name: 'Ratings',
                type: 'number',
                data: this.stationRatings?.find((metricData) => metricData.name === 'Average Rating')?.data,
                ratingCountData: this.stationRatings?.find((metricData) => metricData.name === 'Total Count')?.data
            }
        },

        computedAllStationMetrics() {
            const stationMetrics = [...this.baseStationMetric, this.computedRatings].filter(Boolean);
            const allMetrics = sortedAllMetrics(stationMetrics, undefined, undefined, undefined, this.computedDate);
            return this.calculatedMetrics(allMetrics)
        },

        computedHourlyOrderMetrics() {
            let kptMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Average KPT", this.computedDate);
            let avgWaitTimeMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Average Wait Time", this.computedDate);
            let avgPrepTimeMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Average Preparation Time", this.computedDate);
            let avgAssemblyTimeMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Average Assembly Time", this.computedDate);
            let breachingWaitPrepTimeMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "% Orders Breaching Wait+Prep Time", this.computedDate);
            let breachingAssemblyTimeMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "% Orders Breaching Assembly Time", this.computedDate);

            return {
                kptMetrics,
                avgWaitTimeMetrics,
                avgPrepTimeMetrics,
                avgAssemblyTimeMetrics,
                breachingWaitPrepTimeMetrics,
                breachingAssemblyTimeMetrics
            }
        },

        computedMetricConfig() {
            return instoreMetricGridMapping(this);
        },

        computedStoreList() {
            return this.selectedCityStores;
        },

        computedStoreId() {
            return this.selectedStore.frontend_merchant_id
        },

        computedDate() {
            return formatDateToString(this.selectedDate);
        },

        computedMinDate() {
            var day = new Date();
            day.setDate(day.getDate() - 25);
            return day;
        },

        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getBistroStoreCityList));

            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
            return cityList;
        },

        computedCityName() {
            let modifiedCityMapping = getPanIndiaStoreCityList(this.getBistroStorePageCityMapping);
            let cityName = modifiedCityMapping[this.selectedCity]["name"];

            return {
                displayName: modifiedCityMapping[this.selectedCity]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },
        showComplaintsPanel() {
            return !isEmpty(this.stationComplaints);
        },

        computedAllComplaints() {
            const red = this.stationComplaints.reduce((obj, item) => {
                obj[item?.complaint_type] = isEmpty(obj?.[item?.complaint_type]) ? [item] : [...obj[item?.complaint_type], item];
                return obj;
            }, {});

            return Object.keys(red).map((type) => ({ complaint_type: type, data: red[type] }));
        },

        computedTableData() {
            return this.computedAllComplaints?.map(category => {
                const row = { complaint_type: startCase(category?.complaint_type) };
                category.data?.forEach(item => {
                    row[item.date] = item.count;
                });
                return row;
            });
        },

        computedTableColumns() {
            const dateColumns = [...new Set(this.computedAllComplaints?.flatMap(cat => cat.data?.map(d => d.date)))];

            // sorting dates in descending order:
            return dateColumns?.sort((a, b) => {
                var dateA = new Date(a);
                dateA = dateA.getTime();

                var dateB = new Date(b);
                dateB = dateB.getTime();

                return dateB - dateA;
            });
        },

        computedTotalComplaintsMetrics() {
            const complaintsAggregatedOnDate = this.stationComplaints?.reduce((acc, item) => {
                if (item.date) {
                    if (acc.hasOwnProperty(item.date)) {
                        acc[item.date] += item.count ?? 0
                    } else {
                        acc[item.date] = item.count ?? 0
                    }
                }
                return acc
            }, {})


            const complaintData = [{
                name: "All Complaints",
                type: "number",
                data: Object.entries(complaintsAggregatedOnDate)?.map(([key, value]) => ({
                    date: key,
                    metric: value
                }))
            }]

            let metrics = [...sortedAllMetrics(complaintData, undefined, undefined, undefined, this.computedDate)]

            return metrics?.map(metric => {
                let data = metric.data;

                let currentVal = data[data.length - 1]
                let curr = currentVal ? {
                    meta: currentVal?.meta,
                    type: currentVal?.type,
                    value: currentVal?.value == "-" ? 0 : currentVal?.value
                } : currentVal

                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: true,
                    curr: curr,
                    diffs: diffs,
                }
            })?.filter(metric => metric.curr)
        },

        computedRatingsForTable() {
            const ratings = this.stationRatings
                ?.filter((data) => !['Total Count', 'Average Rating'].includes(data.name))
                ?.reduce((obj, metric) => {
                    obj[metric.name] = metric.data
                    return obj;
                }, {});

            return Object.keys(ratings)?.map((type) => ({ rating_type: type, data: ratings[type] }));
        },

        computedRatingsTableData() {
            const priorityArray = Object.values(RATINGS_MAPPING)
            return this.computedRatingsForTable?.map(rating => {
                const row = { rating_type: RATINGS_MAPPING[rating?.rating_type] };
                rating.data?.forEach(item => {
                    row[item.date] = item.metric;
                });
                return row;
            })?.sort((a, b) =>
                priorityArray.findIndex(x => x === a.rating_type) - priorityArray.findIndex(x => x === b.rating_type));
        },

        computedRatingsTableColumns() {
            const dateColumns = [...new Set(this.computedRatingsForTable?.flatMap(cat => cat.data?.map(d => d.date)))];

            return dateColumns?.sort((a, b) => {
                let dateA = new Date(a.date);
                let dateB = new Date(b.date);
                return dateB - dateA;
            });
        },

        computedKPTMetrics() {
            return cityMetrics(this.stationWiseKpt, "Station Average KPT");
        },

        ...mapState(useCityStore, ['getBistroInkitchenPage', 'getBistroStoreToCityMapping', 'getBistroStoreCityList', 'getBistroStorePageCityMapping']),
        ...mapState(useUserStore, ['getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        filterMetricsWithNoData(metrics) {
            return metrics.filter(metric => metric.data.length > 0);
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getBistroStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            this.isLoading = false;
            let storeList = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            this.mappedList = storeList;

            if (this.isGlobalUser || this.isCityUser) {
                this.mappedList = getModifiedStoreList(storeList);
                this.mappedList[PanIndiaConstants.PAN_INDIA.code] = [];

                for (let cityName in this.mappedList) {
                    if (this.mappedList.hasOwnProperty(cityName)) {
                        let cityStores = this.mappedList[cityName];
                        if (((this.getBistroStoreToCityMapping && this.getBistroStoreToCityMapping?.find((item) => item?.city === (this.getBistroStoreCityList?.find((item) =>
                            item?.code === cityName)?.name)))?.data?.length <= cityStores?.length
                            || cityName === PanIndiaConstants.PAN_INDIA.code)) {
                            let panIndiaStore = {
                                frontend_merchant_id: '',
                                frontend_merchant_name: 'Overall',
                                backend_merchant_id: '',
                                name: cityName,
                                merchant_name_id: 'Overall'
                            };

                            cityStores.unshift(panIndiaStore);
                        }
                    }
                }
            }

            this.selectedCityStores = this.mappedList[this.selectedCity];
            if (this.selectedStore == null) {
                this.selectedStore = this.selectedCityStores[0];
            }
        },

        async fetchStationList() {
            try {
                let stationList = bistroApi.fetchStationMapping()
                let [stationListResponse] = await Promise.all([stationList])
                if (stationListResponse) this.stationList = stationListResponse?.data?.metrics || [];

                this.selectedStation = this.pageLocalStorage?.station || this.stationList?.[0]?.station_id;

                this.isLoading = true;
                this.fetchAllMetrics();
            }
            catch (error) {
                if (axios.isCancel(error)) console.log("Request canceled", error.message);
                else this.isError = true;
            };
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests('base');
            this.fetchStationMetrics();

            try {
                const allMetricsCancelTokenSource = axios.CancelToken.source();
                const hourlyMetricsCancelTokenSource = axios.CancelToken.source();
                const stationWiseKptMetricsCancelToken = axios.CancelToken.source();

                this.cancelTokens.base = [allMetricsCancelTokenSource, hourlyMetricsCancelTokenSource, stationWiseKptMetricsCancelToken];

                if (!this.computedDate) {
                    this.isError = true;
                    throw new Error("Invalid date");
                }

                let metricsToFetch = []
                for (let idx = 0; idx < BISTRO_INSTORE_METRIC_LIST.length; idx += 3) {
                    metricsToFetch.push(BISTRO_INSTORE_METRIC_LIST.slice(idx, idx + 3));
                }
                let allMetrics = metricsToFetch.map((metrics) => bistroApi.fetchAllMetrics(metrics, this.computedCityName.cityName, this.computedStoreId, this.computedDate, allMetricsCancelTokenSource.token))

                let hourlyMetrics = bistroApi.fetchHourlyMetrics(
                    BISTRO_INSTORE_HOURLY_METRIC_LIST,
                    this.computedDate,
                    this.computedCityName.cityName,
                    this.computedStoreId,
                    hourlyMetricsCancelTokenSource.token
                )

                let stationWiseKptMetrics = bistroApi.fetchStationKptMetrics({
                    city: this.computedCityName.cityName,
                    store: this.computedStoreId,
                    date_str: this.computedDate,
                    cancelToken: stationWiseKptMetricsCancelToken.token
                })

                let [
                    allMetricsResponse,
                    hourlyMetricsResponse,
                    stationWiseKptMetricsResponse
                ] = await Promise.all([
                    Promise.all(allMetrics),
                    hourlyMetrics,
                    stationWiseKptMetrics
                ]);

                if (allMetricsResponse) {
                    let allMetrics = []
                    allMetricsResponse.forEach((response) => {
                        let metrics = this.filterMetricsWithNoData(response.data.metrics);
                        allMetrics.push(...metrics);
                    })
                    this.allMetrics = allMetrics
                }

                if (hourlyMetricsResponse) this.hourlyOrderMetrics = hourlyMetricsResponse.data.metrics;

                if (stationWiseKptMetricsResponse) this.stationWiseKpt = stationWiseKptMetricsResponse?.data?.metrics;

                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isError = true;
                    let status = error?.response?.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            } finally {
                this.isLoading = false;
            };
        },

        async fetchStationMetrics() {
            this.stationMetricLoading = true;
            this.stationMetricError = false;

            this.cancelPreviousRequests('station');

            try {
                const stationComplaintsCancelToken = axios.CancelToken.source();
                const stationBaseMetricsCancelToken = axios.CancelToken.source();
                const stationRatingsCancelToken = axios.CancelToken.source();
                const stationWiseKptMetricsCancelToken = axios.CancelToken.source();

                this.cancelTokens.station = [stationComplaintsCancelToken, stationBaseMetricsCancelToken, stationRatingsCancelToken, stationWiseKptMetricsCancelToken];

                if (!this.computedDate) {
                    this.stationMetricError = true;
                    throw new Error("Invalid date");
                }

                let stationMetrics = bistroApi.fetchStationBaseMetric({
                    metrics: ["station_cart_volume", "station_avg_kpt", "station_wait_time", "station_prep_time"],
                    city: this.computedCityName.cityName,
                    store: this.computedStoreId,
                    station_id: this.selectedStation,
                    date_str: this.computedDate,
                    cancelToken: stationBaseMetricsCancelToken.token
                })

                let stationComplaints = bistroApi.fetchStationComplaints({
                    city: this.computedCityName.cityName,
                    store: this.computedStoreId,
                    station_id: this.selectedStation,
                    date_str: this.computedDate,
                    cancelToken: stationComplaintsCancelToken.token
                })

                let stationRatings = bistroApi.fetchStationsRatingMetrics({
                    city: this.computedCityName.cityName,
                    store: this.computedStoreId,
                    station_id: this.selectedStation,
                    date_str: this.computedDate,
                    cancelToken: stationRatingsCancelToken.token
                })

                let [
                    stationMetricsResponse,
                    stationComplaintsResponse,
                    stationRatingsResponse
                ] = await Promise.all([
                    stationMetrics,
                    stationComplaints,
                    stationRatings
                ]);

                if (stationMetricsResponse) this.baseStationMetric = this.filterMetricsWithNoData(stationMetricsResponse.data.metrics);
                if (stationComplaintsResponse) this.stationComplaints = stationComplaintsResponse.data.complaints;
                if (stationRatingsResponse) this.stationRatings = stationRatingsResponse.data.metrics;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.stationMetricError = true;
                    let status = error?.response?.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            } finally {
                this.stationMetricLoading = false;
            };
        },

        cancelPreviousRequests(type) {
            if (type) {
                this.cancelTokens?.[type].forEach((source) => {
                    source.cancel('Operation canceled due to new request.');
                });
                this.cancelTokens[type] = []; // Clear the tokens
            } else {
                this.cancelTokens = Object.values(this.cancelTokens)?.map(([key, values]) => {
                    values?.forEach((source) => {
                        source.cancel('Operation canceled due to new request.');
                    })
                    return key
                })?.reduce((acc, it) => {
                    acc[it] = []
                    return acc
                }, {})
            }
        },

        resetAllMetrices() {
            this.allMetrics = []
            this.hourlyOrderMetrics = []
        },

        calculatedMetrics(metrics) {
            return metrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                if (metric.name === 'Ratings') {
                    return {
                        name: metric.name,
                        reverseStyle: metricsForStyleReversal.includes(metric.name),
                        curr: curr,
                        diffs: diffs,
                        ratingCount: curr?.meta?.metric
                    }
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },

        refreshFiltersList() {
            fetchStoreToCityMapping(false, true);
        },

        onStoreChange() {
            this.isLoading = true
            this.isError = false

            this.resetAllMetrices()
            this.fetchAllMetrics()

            this.pageLocalStorage['selectedStore'] = this.selectedStore
            this.updateBistroInkitchenPage(this.pageLocalStorage)
        },

        onCityChange(event) {
            this.isLoading = true;
            this.isError = false;

            this.selectedCityStores = this.mappedList[this.selectedCity];
            this.selectedStore = this.selectedCityStores[0];

            this.resetAllMetrices();
            this.fetchAllMetrics();

            this.pageLocalStorage['selectedStore'] = this.selectedStore
            this.updateBistroInkitchenPage(this.pageLocalStorage)
        },

        onDateChange(event) {
            this.isLoading = true;
            this.isError = false;

            this.resetAllMetrices();
            this.fetchAllMetrics();

            this.pageLocalStorage['selectedDate'] = this.selectedDate
            this.updateBistroInkitchenPage(this.pageLocalStorage);
        },

        onStationChange(event) {
            this.pageLocalStorage['station'] = this.selectedStation
            this.fetchStationMetrics();
        },

        handleRatings(metricName) {
            if (metricName !== 'Ratings') return
            this.showRatings = true
        },

        getStationName(stationId) {
            const station = this.stationList.find(s => s.station_id == stationId);
            return station?.station_name || null;
        },

        handleStationClick({ val }) {
            const stationId = this.stationList.find(s => s.station_name == val)?.station_id;
            this.selectedStation = stationId;
            this.onStationChange();
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateBistroInkitchenPage']),
    },

    data() {
        return {
            showHeader: false,
            isLoading: true,
            isError: false,
            stationMetricLoading: true,
            stationMetricError: false,
            intervalId: null,
            canAccessPage: false,
            selectedDate: false,
            cancelTokens: {
                base: [],
                station: []
            },
            selectedCity: null,
            mappedList: {},
            selectedCityStores: [],
            selectedStore: null,
            isGlobalUser: false,
            isCityUser: false,
            allMetrics: [],
            hourlyOrderMetrics: [],
            selectedStation: null,
            stationList: [],
            baseStationMetric: [],
            stationComplaints: [],
            stationWiseKpt: [],
            stationRatings: [],
            showRatings: false,
            showComplaints: false,
            pageLocalStorage: {}
        }

    },

    watch: {
        $route() {
            this.checkRoute();
        },
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/bistro/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(BISTRO_PAGE_ID_ROUTE_MAPPING[23]);
        if (!this.canAccessPage) noPermissionRoute(true)

        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (isEmpty(this.getBistroStoreToCityMapping)) this.refreshFiltersList();
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = true;
            this.isError = false;

            this.pageLocalStorage = this.getBistroInkitchenPage ? this.getBistroInkitchenPage : {}

            if (this.pageLocalStorage?.selectedStore) this.selectedStore = this.pageLocalStorage?.selectedStore

            let currentDate = (new Date()).getDate()
            if (this.pageLocalStorage && currentDate !== this.pageLocalStorage?.currentDate) {
                this.pageLocalStorage["selectedDate"] = new Date();
                this.pageLocalStorage["currentDate"] = (new Date()).getDate();
                this.updateBistroInkitchenPage(this.pageLocalStorage);
                window.location.reload()
            } else {
                this.selectedDate = isEmpty(this.pageLocalStorage?.selectedDate) ? new Date() : new Date(this.pageLocalStorage.selectedDate)
            }

            if (this.pageLocalStorage?.selectedStore) this.selectedCity = this.pageLocalStorage?.selectedStore?.name
            else if (this.isGlobalUser) this.selectedCity = PanIndiaConstants.PAN_INDIA.code;
            else this.selectedCity = Object.keys(this.getBistroStorePageCityMapping)[0]

            this.fetchCityStoreMapping();
            this.fetchStationList();

            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}
</script>

<style scoped>
.shadow-custom {
    box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
}
</style>
