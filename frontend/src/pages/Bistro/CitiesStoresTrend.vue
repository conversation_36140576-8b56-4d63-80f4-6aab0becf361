<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedCityName && computedCityName.displayName ?
                computedCityName.displayName : this.selectedCity }}</div>
        </div>
    </header>

    <div
        class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10 [&>div]:mt-2 [&>div]:md:mt-0">
        <div>
            <div class="text-left pb-2 font-semibold italic">City :</div>
            <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
                placeholder="click to select city" class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Hour :</div>
            <v-select :reduce="computedHourList => computedHourList.value" v-model="selectedHour"
                :options="computedHourList" label="key" @update:model-value="onHourChange($event)" :clearable="false"
                placeholder="click to select hour" class="w-full bg-white" />
        </div>
        <div class="flex items-end justify-center md:justify-end">
            <SelectButton v-model="yesterday" :options="yesterdayOptions" optionLabel="label" optionValue="value"
                aria-labelledby="basic" @update:modelValue="toggleYesterday" />
        </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">
            Some internal error occured. <br />
            Please reach out to data <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>

    <main v-else>
        <WaterMark></WaterMark>

        <MetricsGrid v-for="metricGridItem in computedMetricConfig" :key="metricGridItem.title"
            :shouldRender="metricGridItem.shouldRender" :metrics="metricGridItem.metrics"
            :metricItemName="metricGridItem.metricItemName" :title="metricGridItem.title"
            :isReverse="metricGridItem.isReverse" :isShortenedNumber="metricGridItem.isShortenedNumber"
            :showHeader="showHeader" />

        <div v-if="computedMetrics.avgRatingMetrics?.length > 0"
            class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">

            <div class="inline-block md:flex justify-between">
                <span :class="['text-lg font-bold text-blue-900', 'inline-flex items-center']">
                    Ratings - {{ computedPanIndiaState ? "CITIES" : "STORES" }} TRENDS
                    <InfoDialog :metrickey="`Ratings - ${computedPanIndiaState ? 'CITIES' : 'STORES'}} TRENDS`"
                        :showHeader="this.showHeader" class="p-0 ml-2 border border-black" />
                </span>
                <div class="text-xs md:text-sm font-thin mt-1 md:mt-0">Click on ratings card to view detailed info
                </div>
            </div>

            <div class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4">
                <MetricBox v-for="metric in computedMetrics.avgRatingMetrics"
                    :key="metric[computedPanIndiaState ? 'city' : 'frontend_merchant_name']"
                    :name="metric[computedPanIndiaState ? 'city' : 'frontend_merchant_name']" :curr="metric.curr"
                    :prev="metric.prev" @onClick="handleShowRatings" hasHover />
            </div>
        </div>

        <Dialog v-model:visible="showRatings" modal :header="`${selectedCityStoreForRating} - Ratings`"
            :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
            <div class="w-80 md:w-full">
                <DataTable :value="computedRatingsTableData" tableClass="text-xs md:text-md" showGridlines removableSort
                    scrollable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                    <Column field="rating_type" header="Ratings" bodyClass="p-1 md:px-4 md:py-3"
                        headerClass="p-1 md:px-4 md:py-3">
                    </Column>
                    <Column v-for="(date, index) in computedRatingsTableColumns" :key="index" :field="date"
                        :header="date" sortable>
                    </Column>
                </DataTable>
            </div>
        </Dialog>
    </main>
</template>


<script>
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import { AddMissingStores, getCurrentHour, getHourList, getPanIndiaStoreCityList, getUserAccessMapping, isCityUser, isGlobalUser, noPermissionRoute } from "../../utils/utils.js";
import { bistroApi } from "../../api/index.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import MetricsGrid from "../../components/MetricsGrid.vue";
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { BISTRO_PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { ABSOLUTE_METRIC_DELTA, cityMetrics, storeMetrics } from "../../utils/metrics.js";
import SelectButton from "primevue/selectbutton";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import vSelect from 'vue-select';
import { PanIndiaConstants, RATINGS_MAPPING } from "../../constants/index.js";
import { CitiesStoresTrendMetricGridMapping } from "../../configurations/Bistro/CitiesStoresTrendMetricGridMapping.js";
import MetricBox from "../../components/MetricBox.vue";
import Dialog from "primevue/dialog";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

    components: {
        Loading,
        AnimatedNumber,
        MetricsGrid,
        WaterMark,
        InfoDialog,
        SelectButton,
        'v-select': vSelect,
        InfoDialog,
        MetricBox,
        Dialog,
        DataTable,
        Column
    },

    computed: {
        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getBistroStoreCityList));

            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
            return cityList;
        },

        computedHourList() {
            return getHourList(null, !!this.yesterday) || [];
        },

        yesterdayOptions() {
            return [
                { label: 'Today', value: false },
                { label: 'Yesterday', value: true }
            ]
        },

        computedPanIndiaState() {
            return this.selectedCity === PanIndiaConstants.PAN_INDIA.code;
        },

        computedCityName() {
            let modifiedCityMapping = getPanIndiaStoreCityList(this.getBistroStorePageCityMapping);
            let cityName = modifiedCityMapping[this.selectedCity]["name"];

            return {
                displayName: modifiedCityMapping[this.selectedCity]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        computedMetrics() {
            let metricsFunc = this.computedPanIndiaState ? cityMetrics : storeMetrics;
            let cartMetrics = metricsFunc(this.cityMetrics, "Cart Volume");
            let gmvMetrics = metricsFunc(this.cityMetrics, "GMV");
            let dauMetrics = metricsFunc(this.cityMetrics, "Daily Active Users");
            let dauConversionMetrics = metricsFunc(this.cityMetrics, "DAU Conversion Percentage")
            let demandBasedBlocksConversionMetrics = metricsFunc(this.cityMetrics, "Demand Based Block %")
            let serviceBasedBlocksConversionMetrics = metricsFunc(this.cityMetrics, "Unserviceable DAU %")
            let expressServiceabilityOohBasedBlocksConversionMetrics = metricsFunc(this.cityMetrics, "Express OOH Based Block %")
            let expressServiceabilityManualBasedBlocksConversionMetrics = metricsFunc(this.cityMetrics, "Express Manual Based Block %")
            let riderLoginMetrics = metricsFunc(this.riderLoginMetrics, "Rider Login hrs")
            // let newRiderMetrics = metricsFunc(this.newRiderMetrics, "New Riders")
            let AOVMertics = metricsFunc(this.cityMetrics, "AOV")
            let surgeCheckoutMetrics = metricsFunc(this.cityMetrics, "Surge Checkouts %");
            let deliveredIn15MinsMetrics = metricsFunc(this.cityMetrics, "%Delivery < 15mins");
            let avgRatingMetrics = metricsFunc(this.ratingMetrics, "Average Rating");

            // sort function also used to filter out metrics, that are not present...
            let sortedGMVMetrics = this.sortMetricsViaCartMetrics(cartMetrics, gmvMetrics)
            let sortedDAUMetrics = this.sortMetricsViaCartMetrics(cartMetrics, dauMetrics)
            let sortedDAUConversionMetrics = this.sortMetricsViaCartMetrics(cartMetrics, dauConversionMetrics)
            let sortedDemandBasedBlocksConversionMetrics = this.sortMetricsViaCartMetrics(cartMetrics, demandBasedBlocksConversionMetrics)
            let sortedServiceBasedBlocksConversionMetrics = this.sortMetricsViaCartMetrics(cartMetrics, serviceBasedBlocksConversionMetrics)
            let sortedExpressServiceabilityOohBasedBlocksConversionMetrics = this.sortMetricsViaCartMetrics(cartMetrics, expressServiceabilityOohBasedBlocksConversionMetrics)
            let sortedExpressServiceabilityManualBasedBlocksConversionMetrics = this.sortMetricsViaCartMetrics(cartMetrics, expressServiceabilityManualBasedBlocksConversionMetrics)
            let sortedRiderLoginMetrics = this.sortMetricsViaCartMetrics(cartMetrics, riderLoginMetrics)
            // let sortedNewRiderMetrics = this.sortMetricsViaCartMetrics(cartMetrics, newRiderMetrics)
            let sortedAOVMertics = this.sortMetricsViaCartMetrics(cartMetrics, AOVMertics)
            let sortedSurgeCheckoutMetrics = this.sortMetricsViaCartMetrics(cartMetrics, surgeCheckoutMetrics)
            let sortedDeliveredIn15MinsMetrics = this.sortMetricsViaCartMetrics(cartMetrics, deliveredIn15MinsMetrics)
            let sortedAvgRatingMetrics = this.sortMetricsViaCartMetrics(cartMetrics, avgRatingMetrics)

            const mapping = {
                cartMetrics: cartMetrics,
                dauMetrics: sortedDAUMetrics,
                dauConversionMetrics: sortedDAUConversionMetrics,
                demandBasedBlocksConversionMetrics: sortedDemandBasedBlocksConversionMetrics,
                serviceBasedBlocksConversionMetrics: sortedServiceBasedBlocksConversionMetrics,
                riderLoginMetrics: sortedRiderLoginMetrics,
                // newRiderMetrics: sortedNewRiderMetrics,
                expressServiceabilityOohBasedBlocksConversionMetrics: sortedExpressServiceabilityOohBasedBlocksConversionMetrics,
                expressServiceabilityManualBasedBlocksConversionMetrics: sortedExpressServiceabilityManualBasedBlocksConversionMetrics,
                surgeCheckoutMetrics: sortedSurgeCheckoutMetrics,
                deliveredIn15MinsMetrics: sortedDeliveredIn15MinsMetrics,
                avgRatingMetrics: sortedAvgRatingMetrics
            };

            if (this.isSensitiveDataPresent(sortedGMVMetrics)) {
                Object.assign(mapping, { gmvMetrics: sortedGMVMetrics });
            }

            if (this.isSensitiveDataPresent(sortedAOVMertics)) {
                Object.assign(mapping, { AOVMertics: sortedAOVMertics });
            }

            return mapping
        },

        computedMetricConfig() {
            return CitiesStoresTrendMetricGridMapping(this);
        },

        computedRatingsForTable() {
            const metricKey = this.computedPanIndiaState ? "name" : "frontend_merchant_id";
            return this.ratingMetrics?.map((ratingsData) =>
            ({
                date: ratingsData.date,
                data: ratingsData?.data?.find((data) => data?.[metricKey] === this.selectedCityStoreForRating)?.data
                    ?.filter((data) => !['Total Count', 'Average Rating'].includes(data.name))
            }))
        },

        computedRatingsTableData() {
            const priorityArray = Object.values(RATINGS_MAPPING)

            const ratingsTableData = this.computedRatingsForTable.reduce((acc, item) => {
                item?.data?.forEach((data) => {
                    if (!acc[data?.name]) {
                        acc[data?.name] = {
                            [item.date]: data?.metric ?? 0
                        }
                    } else {
                        acc[data.name] = {
                            ...acc[data.name],
                            [item.date]: data?.metric ?? 0
                        }
                    }
                })
                return acc
            }, {})

            return Object.entries(ratingsTableData)?.map(([ratingType, value]) => ({
                rating_type: RATINGS_MAPPING[ratingType],
                ...value
            }))?.sort((a, b) =>
                priorityArray.findIndex(x => x === a.rating_type) - priorityArray.findIndex(x => x === b.rating_type));
        },

        computedRatingsTableColumns() {
            const dateColumns = [...new Set(this.computedRatingsForTable.map(data => data.date))]
            return dateColumns?.sort((a, b) => {
                let dateA = new Date(a.date);
                let dateB = new Date(b.date);
                return dateB - dateA;
            });
        },

        ...mapState(useCityStore, ['getBistroCitiesStoresTrendPage', 'getBistroStoreToCityMapping', 'getBistroStoreCityList', 'getBistroStorePageCityMapping']),
        ...mapState(useUserStore, ['getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        onCityChange() {
            this.isError = false;
            this.isLoading = true;

            this.pageLocalStorage['city'] = this.selectedCity;
            this.updateBistroCitiesStoresTrendPage(this.pageLocalStorage)

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        onHourChange() {
            this.isError = false;
            this.isLoading = true;

            this.pageLocalStorage['hour'] = this.selectedHour;
            this.pageLocalStorage['yesterday'] = this.yesterday;
            this.updateBistroCitiesStoresTrendPage(this.pageLocalStorage)

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        toggleYesterday(isToggled) {
            let dateToSet = isToggled ? this.getPreviousISTDate() : this.getCurrentISTDate();
            this.selectedUserDate = dateToSet;

            this.yesterday = isToggled;

            if (!this.yesterday && !getHourList(null, this.yesterday)
                .some((item) => item.value === this.selectedHour)) {
                this.selectedHour = -1; //full day
            }

            this.pageLocalStorage['hour'] = this.selectedHour;
            this.pageLocalStorage['yesterday'] = this.yesterday;
            this.updateBistroCitiesStoresTrendPage(this.pageLocalStorage)

            this.isLoading = true;
            this.isError = false;

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        filterMetricsWithNoData(metrics) {
            return metrics.filter(metric => metric.data.length > 0);
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                let storeMapping = []
                if (!this.computedPanIndiaState) {
                    storeMapping = this.cityStoreMapping.find(cityObj =>
                        cityObj.city === this.computedCityName.cityName)?.["data"];
                }

                const cityMetricsCancelTokenSource = axios.CancelToken.source();
                const citiesRiderMetricsCancelTokenSource = axios.CancelToken.source();
                // const citiesNewRiderMetricsCancelTokenSource = axios.CancelToken.source();

                // this.cancelTokens = [cityMetricsCancelTokenSource, citiesRiderMetricsCancelTokenSource, citiesNewRiderMetricsCancelTokenSource];
                this.cancelTokens = [cityMetricsCancelTokenSource, citiesRiderMetricsCancelTokenSource];

                let cityMetrics = this.computedPanIndiaState ? bistroApi.fetchCityMetrics(this.selectedUserDate, this.selectedHour, cityMetricsCancelTokenSource.token)
                    : bistroApi.fetchStoreMetrics(this.computedCityName.cityName, this.selectedUserDate, this.selectedHour, cityMetricsCancelTokenSource.token)

                let citiesRiderMetrics = this.computedPanIndiaState ? bistroApi.fetchCitiesRiderMetrics(this.selectedUserDate, this.selectedHour, citiesRiderMetricsCancelTokenSource.token)
                    : bistroApi.fetchStoresRiderMetrics(this.computedCityName.cityName, this.selectedUserDate, this.selectedHour, citiesRiderMetricsCancelTokenSource.token)

                // let citiesNewRiderMetrics = this.computedPanIndiaState ? bistroApi.fetchCitiesNewRiderMetrics(this.selectedUserDate, this.selectedHour, citiesNewRiderMetricsCancelTokenSource.token)
                //     : bistroApi.fetchStoresNewRiderMetrics(this.computedCityName.cityName, this.selectedUserDate, this.selectedHour, citiesNewRiderMetricsCancelTokenSource.token)

                let ratingMetrics = this.computedPanIndiaState ? bistroApi.fetchCitiesRatingMetrics(this.selectedUserDate, this.selectedHour, citiesRiderMetricsCancelTokenSource.token)
                    : bistroApi.fetchStoresRatingMetrics(this.computedCityName.cityName, this.selectedUserDate, this.selectedHour, citiesRiderMetricsCancelTokenSource.token)

                let [cityMetricsResponse,
                    citiesRiderMetricsResponse,
                    ratingMetricsResponse,
                    // citiesNewRiderMetricsResponse
                ] = await Promise.all([
                    cityMetrics,
                    citiesRiderMetrics,
                    ratingMetrics
                    // citiesNewRiderMetrics
                ]);

                if (this.computedPanIndiaState) {
                    if (cityMetricsResponse) this.cityMetrics = cityMetricsResponse.data.metrics;
                    if (citiesRiderMetricsResponse) this.riderLoginMetrics = citiesRiderMetricsResponse.data.metrics;
                    if (ratingMetricsResponse) this.ratingMetrics = ratingMetricsResponse.data.metrics;
                    // if (citiesNewRiderMetricsResponse) this.newRiderMetrics = citiesNewRiderMetricsResponse.data.metrics;
                } else {
                    if (cityMetricsResponse) this.cityMetrics = AddMissingStores(storeMapping, cityMetricsResponse.data.metrics);
                    if (citiesRiderMetricsResponse) this.riderLoginMetrics = AddMissingStores(storeMapping, citiesRiderMetricsResponse.data.metrics);
                    if (ratingMetricsResponse) this.ratingMetrics = AddMissingStores(storeMapping, ratingMetricsResponse.data.metrics);
                    // if (citiesNewRiderMetricsResponse) this.newRiderMetrics = AddMissingStores(storeMapping, citiesNewRiderMetricsResponse.data.metrics);
                }

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error?.response?.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.cityMetrics = []
            this.riderLoginMetrics = []
            this.ratingMetrics = []
            // this.newRiderMetrics = []
        },

        refreshFiltersList() {
            fetchStoreToCityMapping(false, true);
        },

        getCurrentISTDate() {
            let now = new Date();
            return new Date(now.getTime() + 5.5 * 60 * 60 * 1000).toISOString().split('T')[0];
        },

        getPreviousISTDate() {
            let now = new Date();
            return new Date(now.getTime() - (86400000 * 1) + (5.5 * 60 * 60 * 1000)).toISOString().split('T')[0];
        },

        async fetchStores() {
            this.cityStoreMapping = this.getBistroStoreToCityMapping;
            this.isLoading = true;
            this.isError = false;

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        isSensitiveDataPresent(metrics) {
            for (const metric of metrics) {
                if ((metric?.curr?.value && metric.curr.value !== "-") || (metric?.prev?.value && metric.prev.value !== "-")) {
                    return true;
                }
            }
            return false;
        },

        sortMetricsViaCartMetrics(cartMetrics, metricsToBeSorted) {
            let metricKey = this.computedPanIndiaState ? 'city' : 'frontend_merchant_id'
            const filteredMetrics = this.filterMetrics(metricsToBeSorted)

            return filteredMetrics.sort((a, b) => {
                let indexOfA = cartMetrics.findIndex(metric => metric[metricKey] === a[metricKey]);
                let indexOfB = cartMetrics.findIndex(metric => metric[metricKey] === b[metricKey]);
                return indexOfA - indexOfB;
            });
        },

        filterMetrics(metrics) {
            return metrics?.filter((metric) => metric?.curr?.value !== "-" && metric?.prev?.value !== "-")
        },

        handleShowRatings({ val }) {
            if (!val) {
                this.selectedCityStoreForRating = ""
                this.showRatings = false
                return
            }
            this.selectedCityStoreForRating = this.computedPanIndiaState ? val : val?.match(/\(([^)]+)\)/)?.[1]
            this.showRatings = true
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateBistroCitiesStoresTrendPage']),
    },

    data() {
        return {
            showHeader: false,
            isLoading: true,
            isError: false,
            intervalId: null,
            canAccessPage: false,
            isGlobalUser: false,
            isCityUser: false,
            yesterday: false,
            selectedCity: null,
            selectedHour: null,
            selectedUserDate: null,
            showRatings: false,
            selectedCityStoreForRating: null,

            cityStoreMapping: {},
            cancelTokens: [],
            userAccessMapping: {},

            cityMetrics: [],
            riderLoginMetrics: [],
            ratingMetrics: [],
            // newRiderMetrics: [],

            pageLocalStorage: {}
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        },
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/bistro/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(BISTRO_PAGE_ID_ROUTE_MAPPING[21]);

        if (!this.canAccessPage) noPermissionRoute(true);

        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (isEmpty(this.getBistroStoreToCityMapping)) this.refreshFiltersList();
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = true;
            this.isError = false;

            this.pageLocalStorage = this.getBistroCitiesStoresTrendPage;
            if (!this.pageLocalStorage) this.pageLocalStorage = {};

            if (this.isGlobalUser) {
                this.selectedCity = isEmpty(this.pageLocalStorage?.city) ? PanIndiaConstants.PAN_INDIA.code : this.pageLocalStorage?.city;
            } else {
                this.selectedCity = isEmpty(this.pageLocalStorage?.city) ? Object.keys(this.getBistroStorePageCityMapping)[0] : this.pageLocalStorage?.city;
            }

            this.yesterday = this.pageLocalStorage?.yesterday ? this.pageLocalStorage?.yesterday : false;

            this.selectedHour = this.pageLocalStorage?.hour ? this.pageLocalStorage?.hour : -1;
            if (!this.yesterday && this.selectedHour > getCurrentHour()) this.selectedHour = -1;

            this.selectedUserDate = this.yesterday ? this.getPreviousISTDate() : this.getCurrentISTDate();

            this.fetchStores();
            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}
</script>
