<template>
  <form @submit.prevent="handleFormSubmit" class="max-w-7xl mx-auto px-4 py-4 mb-4">
        <div class="flex flex-col lg:flex-row gap-4 lg:gap-6">
          <div class="flex flex-col sm:flex-row gap-4 flex-1">
            <!-- City Filter -->
            <div class="flex-1">
              <div class="text-left pb-2 font-semibold italic">City :</div>
              <v-select :reduce="computedCityList => computedCityList.code" v-model="formData.selectedCity"
                :options="computedCityList" label="name" :clearable="false"
                placeholder="click to select city"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 w-full" 
                @update:model-value="onCityChange" />
            </div>
            <!-- Store Filter -->
            <div class="flex-1">
              <div class="text-left pb-2 font-semibold italic">Store :</div>
              <v-select v-model="formData.selectedStore" :options="computedStoreList" label="merchant_name_id"
                :clearable="false" placeholder="click to select store"
                :disabled="formData.selectedCity === '#Pan-India'"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 w-full" />
            </div>
            <!-- P-Type Filter -->
            <div class="flex-1">
              <div class="text-left pb-2 font-semibold italic">P-Type :</div>
              <v-select v-model="formData.selectedPType" :options="computedPTypeList" :clearable="true"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 w-full"
                :multiple="true" :close-on-select="false" placeholder="click to select p-type" 
                @update:model-value="onPTypeChange" />
            </div>
            <!-- P-Name Filter -->
            <div class="flex-1">
              <div class="text-left pb-2 font-semibold italic">P-Name :</div>
                <AsyncSelect v-model="formData.selectedPName" 
                    selectClass="w-full bg-gray-50 text-gray-900 text-sm rounded-lg"
                    apiFunc="fetchProductFilters" searchKey="pname" apiType="bistroApi"
                    :params="computedParams" multiple   :close-on-select="false"
                    @update:model-value="onPNameChange" />
            </div>
          </div>
          </div>
          
          <!-- Additional Filters Button -->
          <div class="flex items-center justify-end">
           <div class="flex items-center">
            <Button v-tooltip.top="{ value: 'Additional Filters', position: 'top' }" 
              icon="pi pi-sliders-h" iconPos="right" aria-label="Filter" 
              class="filter-btn text-black" text @click="toggleOverlay" 
              label="More Filters" type="button" />
            
            <OverlayPanel ref="overlayPanel" style="width: 400px;">
              <div class="p-4">
                <div class="text-lg font-semibold mb-4">More Filters:</div>
                
                <div class="space-y-4">
                  <AsyncSelect v-model="formData.selectedPId" title="P-Id:" 
                    selectClass="w-full bg-gray-50 text-gray-900 text-sm rounded-lg"
                    apiFunc="fetchProductFilters" searchKey="pid" apiType="bistroApi"
                    :params="computedParams"  :multiple="true" minSearchLength="2"/>
                  
                  <AsyncSelect v-model="formData.selectedL1" title="L1-Category:" 
                    selectClass="w-full bg-gray-50 text-gray-900 text-sm rounded-lg"
                    apiFunc="fetchProductFilters" searchKey="l1_category" apiType="bistroApi"
                    :params="computedParams"  :multiple="true" minSearchLength="2"/>
                  
                  <AsyncSelect v-model="formData.selectedL2" title="L2-Category:" 
                    selectClass="w-full bg-gray-50 text-gray-900 text-sm rounded-lg"
                    apiFunc="fetchProductFilters" searchKey="l2_category" apiType="bistroApi"
                    :params="computedParams"  :multiple="true" minSearchLength="2"/>
                                    
                </div>
              </div>
            </OverlayPanel>
          </div>
        </div>

     <!-- Form Action Buttons -->
      <div class="flex justify-center mt-4 gap-3">
        <Button 
          label="Apply Filters" 
          severity="primary" 
          size="normal" 
          type="submit"
        />
      </div>
    </form>
  </template>
  
  <script>
  import Button from "primevue/button";
  import vSelect from "vue-select";
  import AsyncSelect from "../../../components/AsyncSelect.vue";
  import OverlayPanel from 'primevue/overlaypanel';
  import Tooltip from 'primevue/tooltip';
  import { useScreenSize } from "../../../composables/useScreenSize.js";
  import isEmpty from "lodash.isempty";
  
  export default {
    name: "HeaderFilter",
    
    components: {
      Button,
      "v-select": vSelect,
      AsyncSelect,
      OverlayPanel
    },
  
    directives: {
      tooltip: Tooltip
    },
  
    props: {
      computedCityList: {
        type: Array,
        default: () => []
      },
      computedStoreList: {
        type: Array,
        default: () => []
      },
      computedPTypeList: {
        type: Array,
        default: () => []
      },
      currentFilters: {
        type: Object,
        default: () => ({
          selectedCity: null,
          selectedStore: null,
          selectedPType: [],
          selectedPName: [],
          selectedPId: [],
          selectedL1: [],
          selectedL2: [],
        })
      },

    },
  
    setup() {
      const { isMobile } = useScreenSize();
      return {
        isMobile
      };
    },
  
    data() {
      return {
      // Form data - internal state 
      formData: { 
        ...this.currentFilters
      }
    };
    },
     
    computed: {
      computedParams() {
        let params = {};
        if (!isEmpty(this.formData.selectedPType)) {
          params['ptype'] = this.formData.selectedPType;
        }
        if (!isEmpty(this.formData.selectedPName)) {
          params['pname'] = this.formData.selectedPName;
        }
        params['l0_category'] = 'Bistro';
        if (!isEmpty(this.formData.selectedL1)) {
          params['l1_category'] = this.formData.selectedL1;
        }
        if (!isEmpty(this.formData.selectedL2)) {
          params['l2_category'] = this.formData.selectedL2;
        }
        if (!isEmpty(this.formData.selectedPId)) {
          params['pid'] = this.formData.selectedPId;
        }
        return params;
      },

    },
  
    emits: [
      'filters-applied',
      'refresh-filters',
      'store-list-update-needed',
    ],
  
    methods: {

      handleFormSubmit() {
        const filterData = {
          ...this.formData
        };

        this.$emit('filters-applied', filterData);
        // Close overlay if open
        if (this.$refs.overlayPanel) {
          this.$refs.overlayPanel.hide();
        }
      },
      
      toggleOverlay(event) {
        if (this.$refs.overlayPanel) {
          this.$refs.overlayPanel.toggle(event);
        }
      },

      clearDependentFilters() {
        this.formData = {
          ...this.formData,
          selectedPId: [],
          selectedL1: [],
          selectedL2: [],
        };
      },

      onPTypeChange() {
        this.formData.selectedPName = [];
        this.clearDependentFilters();
      },

      onPNameChange() {
        this.clearDependentFilters();
      },

      onCityChange(newCityCode) {
        this.formData.selectedStore = this.computedStoreList[0];
        this.updateStoreList();
      },

      refreshFilters() {
        this.$emit('refresh-filters');
      },
      // Update store list when city changes
      updateStoreList() {
        this.$emit('store-list-update-needed', this.formData.selectedCity);
      }
    },
    mounted() {
    },

      watch: {
      // Update form data when currentFilters prop changes
      currentFilters: {
      handler(newFilters) {
        // Only update, when the formData and data coming from the parent is different
        if (JSON.stringify(this.formData) !== JSON.stringify(newFilters)) {
          this.formData = {
            ...newFilters
          };
        }
      },
      deep: true,
      immediate: true
    }
  },
};
  </script>
  
  <style scoped>
  .btn {
    flex: 1;
    min-width: max-content;
    border: 1px solid #4f4f4f;
  }
  
  :deep(.btn .p-button-label) {
    font-weight: 300;
  }
  
  :deep(.btn .p-button-icon) {
    font-size: 0.75rem;
  }
  
  .selected-filter {
    border: 1px solid #328616;
    background-color: #F6FFF8;
  }
  
  :deep(.selected-filter .p-badge) {
    color: #000;
    font-size: 1rem;
    font-weight: 300;
    background-color: #F6FFF8;
    padding: 0;
  }
  
  :deep(.filter-btn .p-button-icon) {
    font-size: 1.5rem;
  }
  
  :deep(.p-sidebar) {
    border-top-left-radius: 16px !important;
    border-top-right-radius: 16px !important;
  }
  
  :deep(.p-sidebar .p-sidebar-header) {
    border-bottom: 1px solid #efefef;
  }
  
  :deep(.p-tabview) {
    display: flex !important;
  }
  
  :deep(.p-tabview .p-tabview-nav li .p-tabview-nav-link) {
    border-radius: 0 !important;
    border: none;
    padding: 0.75rem 3rem 0.75rem 0;
    font-size: 0.75rem;
    font-weight: 300;
  }
  
  :deep(.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link) {
    border-right: 2px solid #10b981;
    font-weight: 600;
  }
  
  :deep(.p-tabview-nav) {
    flex-direction: column;
  }
  
  :deep(.p-tabview-ink-bar) {
    display: none;
  }
  
  :deep(.p-tabview-nav-container) {
    height: 44vh;
    overflow-y: auto;
  }
  
  :deep(.p-tabview-panels) {
    width: 100%;
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
    border-left: 1px solid #efefef;
    border-radius: 0;
    height: 44vh;
  }
  
  :deep(.p-listbox) {
    border: none;
    box-shadow: none;
  }
  
  :deep(.p-listbox-list-wrapper) {
    height: 44vh;
    overflow-y: auto;
    font-size: 0.75rem;
  }
  
  :deep(.vs__dropdown-menu) {
    font-size: 0.75rem !important;
  }
  </style>