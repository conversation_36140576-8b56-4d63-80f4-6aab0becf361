<template>
    <div v-if="shouldRender" :class="containerClass">
        <Accordion :activeIndex="0" class="bg-transparent">
            <AccordionTab>
                <template #header>
                    <div class="flex justify-between items-center w-full">
                        <div class="flex items-center gap-2 md:gap-3">
                            <i :class="getIconClass()" style="color: #ca8a04;"></i>
                            <div :class="[titleClass, 'text-black']">{{ title }}</div>
                            <InfoDialog :metrickey="title" :showHeader="showHeader" class="p-0 ml-2 border border-black" />
                        </div>
                    </div>
                </template>

                <div v-if="shouldShowClickInstruction" class="text-xs mb-4 ">
                    *Click on <strong>items</strong> to view detailed tabular data.
                </div>

                <div v-if="!metrics?.length" class="flex justify-center items-center p-4">
                    <span class="text-sm md:text-md text-gray-600">Data not present</span>
                </div>
                <div v-else class="relative">
                    <div ref="scrollContainer" @scroll.passive="checkScrollEnd"
                        :class="[
                            'overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400',
                            scrollableContainerClass
                        ]">
                        <div :class="gridClass">
                            <MetricBox v-for="metric in metrics" :key="metric[metricItemName]" :name="metric[metricItemName]"
                                :metricName="metric.name" :curr="metric.curr" :prev="metric.prev" :diffs="metric.diffs"
                                 :reverseStyle="metric.reverseStyle" :isReverse="isReverse" 
                                 :isShortenedNumber="isShortenedNumber" @onClick="handleMetricClick" :hasHover="shouldHaveHoverEffect" :isRating="metric.isRating" :countCurr="metric.countCurr"/>
                        </div>
                    </div>
                    
                    <!-- scroll indicator -->
                    <span v-if="showScrollIndicator"
                        class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-gray-100 to-transparent pointer-events-none flex items-end justify-center pb-1 backdrop-blur-sm">
                        <span class="text-xs text-gray-600 bg-gray-100 px-2 rounded flex items-center text-center border border-gray-200">
                            <i class="pi pi-angle-down"></i>
                            <span>Scroll for more</span>
                        </span>
                    </span>
                </div>
            </AccordionTab>
        </Accordion>
    </div>
</template>

<script>
import MetricBox from './MetricBox.vue';
import Button from 'primevue/button';
import InfoDialog from '../../../components/InfoDialog.vue';
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';

export default {
    name: "MetricsGrid",

    components: {
        MetricBox,
        Button,
        InfoDialog,
        Accordion,
        AccordionTab
    },

    props: {
        shouldRender: {
            type: Boolean,
            required: true
        },
        metrics: {
            type: Array,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        metricItemName: {
            type: String,
            default: 'displayHour'
        },
        containerClass: {
            type: String,
            default: 'max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 md:mt-8'
        },
        titleClass: {
            type: String,
            default: 'text-sm md:text-lg font-bold text-yellow-600'
        },
        gridClass: {
            type: String,
            default: 'grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-2 md:mt-4'
        },
        isReverse: {
            type: Boolean,
            required: false
        },
        isShortenedNumber: {
            type: Boolean,
            default: false
        },
        showHeader: {
            type: Boolean,
            required: true
        },
        enableScrolling: {
            type: Boolean,
            default: true
        },
        maxHeight: {
            type: String,
            default: 'max-h-96'
        },
        scrollThreshold: {
            type: Number,
            default: 6
        }
    },

    data() {
        return {
            showScrollIndicator: false
        }
    },

    computed: {
        scrollableContainerClass() {
            if (!this.enableScrolling) return ''
            return `${this.maxHeight}`
        },

        shouldShowScroll() {
            if (!this.enableScrolling) return false
            return this.metrics && this.metrics.length > this.scrollThreshold
        },

        shouldShowClickInstruction() {
            return this.title.includes('RATINGS') || this.title.includes('ALL COMPLAINTS')
        },
        shouldHaveHoverEffect() {
            return this.title.includes('RATINGS') || this.title.includes('ALL COMPLAINTS')
        }
    },

    watch: {
        metrics: {
            handler() {
                if (this.enableScrolling) {
                    this.$nextTick(() => {
                        this.checkScrollEnd()
                    })
                }
            },
            deep: true
        }
    },

    emits: ['onMetricClick'],
        
    methods: {
        handleMetricClick(event) {
            this.$emit('onMetricClick', { 
                itemName: event.val, 
                title: this.title 
            })
        },

        getIconClass() {
            // Return appropriate icon based on title type
            if (this.title.includes('GMV') || this.title.includes('AOV')) {
                return 'pi pi-dollar';
            } else if (this.title.includes('CART') || this.title.includes('ORDER')) {
                return 'pi pi-shopping-cart';
            } else if (this.title.includes('ITEMS SOLD') || this.title.includes('QUANTITY')) {
                return 'pi pi-shopping-cart';
            } else if (this.title.includes('RATINGS')) {
                return 'pi pi-star';
            } else if (this.title.includes('ALL COMPLAINTS')) {
                return 'pi pi-exclamation-triangle';
            } else {
                return 'pi pi-chart-line';
            }
        },

        checkScrollEnd() {
            if (!this.$refs.scrollContainer || !this.enableScrolling) return
            const el = this.$refs.scrollContainer
            const scrollable = el.scrollHeight > el.clientHeight
            const atBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 1
            this.showScrollIndicator = scrollable && !atBottom
        }
    },

    mounted() {
        if (this.enableScrolling) {
            this.$nextTick(() => {
                this.checkScrollEnd()
            })
            window.addEventListener('resize', this.checkScrollEnd)
        }
    },

    beforeUnmount() {
        if (this.enableScrolling) {
            window.removeEventListener('resize', this.checkScrollEnd)
        }
    }
}
</script>

<style scoped>
/* Custom scrollbar for metric sections */
.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.scrollbar-thumb-gray-300:hover::-webkit-scrollbar-thumb {
    background: #9ca3af;
}

/* Accordion customization for minimal styling */
:deep(.p-accordion .p-accordion-tab) {
    margin-bottom: 1rem;
}

:deep(.p-accordion .p-accordion-header) {
    background: #f1f5f9 !important;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem 1rem;
}

:deep(.p-accordion .p-accordion-header .p-accordion-header-link) {
    background: #f1f5f9 !important;
    color: inherit;
}

:deep(.p-accordion .p-accordion-header:not(.p-disabled).p-highlight) {
    background: #f1f5f9 !important;
    border-color: #cbd5e1;
}

:deep(.p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link) {
    background: #f1f5f9 !important;
}

:deep(.p-accordion .p-accordion-header:not(.p-disabled):hover) {
    background: #f1f5f9 !important;
    border-color: #94a3b8;
}

:deep(.p-accordion .p-accordion-header:not(.p-disabled):hover .p-accordion-header-link) {
    background: #f1f5f9 !important;
}

:deep(.p-accordion .p-accordion-content) {
    border: 1px solid #e2e8f0;
    border-top: 0;
    border-radius: 0 0 8px 8px;
    background: #f8fafc;
    padding: 1rem;
}

:deep(.p-accordion .p-accordion-toggle-icon) {
    color: #64748b;
}
</style>