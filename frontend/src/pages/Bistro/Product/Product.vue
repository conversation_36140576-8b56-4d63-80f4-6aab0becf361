<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedCityName && computedCityName.displayName ?
                computedCityName.displayName : this.appliedFilters.selectedCity }}</div>
            <div class="text-3xl font-bold text-gray-900" v-if="this.appliedFilters.selectedStore">Store: {{
                this.appliedFilters.selectedStore.merchant_name_id }} </div>
        </div>
    </header>

    <HeaderFilter 
        :computedCityList="computedCityList"
        :computedStoreList="computedStoreList" 
        :computedPTypeList="computedPTypeList"
        :currentFilters="currentFilters"
        :isApplying="isLoading"
        @filters-applied="onFiltersApplied"
        @refresh-filters="refreshFiltersList"
        @store-list-update-needed="onStoreListUpdateNeeded"
    />
    <div class="flex justify-center">
        <SelectButton v-model="yesterday" :options="yesterdayOptions" optionLabel="label" optionValue="value"
        aria-labelledby="basic" @update:modelValue="toggled" />
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>
    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{
            this.appliedFilters.selectedCity }}
            city is invalid or some internal error occured.<br />Please select another city or reach out to data
            <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>
    <main v-else>
        <WaterMark></WaterMark>

    <div v-for="(metricGridItem, index) in computedMetricConfig" :key="metricGridItem.title">
        <MetricsGrid
            :shouldRender="metricGridItem.shouldRender" 
            :metrics="metricGridItem.metrics"
            :metricItemName="metricGridItem.metricItemName" 
            :title="metricGridItem.title"
            :containerClass="metricGridItem.containerClass" 
            :titleClass="metricGridItem.titleClass"
            :gridClass="metricGridItem.gridClass" 
            :showHeader="showHeader" 
            @onMetricClick="handleMetricGridClick" />
    </div>
        

        <Dialog v-model:visible="showComplaints" modal :header="selectedComplaintsItem"
            :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
            <div class="w-80 md:w-full">
                <DataTable :value="computedTableData" tableClass="text-xs md:text-md" showGridlines removableSort
                    scrollable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                    <Column field="complaint_type" header="Complaint Type" bodyClass="p-1 md:px-4 md:py-3"
                        headerClass="p-1 md:px-4 md:py-3">
                    </Column>
                    <Column v-for="(date, index) in computedTableColumns" :key="index" :field="date" :header="date"
                        sortable>
                    </Column>
                </DataTable>
            </div>
        </Dialog>

        <Dialog v-model:visible="showRatings" modal :header="selectedRatingsItem"
            :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
            <div class="w-80 md:w-full">
                <DataTable :value="computedRatingsTableData" tableClass="text-xs md:text-md" showGridlines removableSort
                    scrollable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                    <Column field="rating_type" header="Ratings" bodyClass="p-1 md:px-4 md:py-3"
                        headerClass="p-1 md:px-4 md:py-3">
                    </Column>
                    <Column v-for="(date, index) in computedRatingsTableColumns" :key="index" :field="date"
                        :header="date" sortable>
                    </Column>
                </DataTable>
            </div>
        </Dialog>
    </main>
</template>

<script>

import Loading from "../../../components/Loading.vue";
import AnimatedNumber from "../../../components/AnimatedNumber.vue"
import { getModifiedStoreList, getPanIndiaStoreCityList, getUserAccessMapping, getUserAllowedStoresMapping, isCityUser, isGlobalUser, noPermissionRoute } from "../../../utils/utils.js";
import WaterMark from "../../../components/WaterMark.vue";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../../stores/users.js';
import 'vue-select/dist/vue-select.css';
import isEmpty from "lodash.isempty";
import axios from "axios";
import { BISTRO_PAGE_ID_ROUTE_MAPPING } from "../../../constants/pages.js";
import SelectButton from "primevue/selectbutton";
import { useCityStore } from "../../../stores/cities.js";
import { bistroApi } from "../../../api/index.js";
import InfoDialog from '../../../components/InfoDialog.vue';
import { ABSOLUTE_METRIC_DELTA, metricsForStyleReversal, sortedAllMetrics } from "../../../utils/metrics.js";
import { MetricChange } from "../../../interfaces/index.js";
import { fetchStoreToCityMapping } from "../../../utils/storeMappings.js";
import vSelect from 'vue-select';
import { PanIndiaConstants, RATINGS_MAPPING } from "../../../constants/index.js";
import Dialog from "primevue/dialog";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import startCase from 'lodash.startcase';
import { useScreenSize } from "../../../composables/useScreenSize.js";
import MetricsGrid from "./MetricsGrid.vue";
import { ProductMetricGridMapping } from "../../../configurations/Bistro/ProductMetricGridMapping.js";
import { Metric } from "../../../interfaces/index.js";
import HeaderFilter from "./HeaderFilter.vue";

export default {

    components: {
        Loading,
        AnimatedNumber,
        WaterMark,
        SelectButton,
        InfoDialog,
        'v-select': vSelect,
        Dialog,
        DataTable,
        Column,
        MetricsGrid,
        HeaderFilter
    },

    computed: {
        yesterdayOptions() {
            return [
                { label: 'Today', value: false },
                { label: 'Yesterday', value: true }
            ]
        },

        currentFilters() {
            return {
                ...this.appliedFilters
            };
        },

        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        computedRatings() {
            return this.ratingMetrics?.reduce((acc, itemMetric) => {
                if (!itemMetric?.item_name) return acc;

                acc[itemMetric.item_name] = {
                    name: 'Ratings',
                    type: 'number',
                    data: itemMetric?.data?.find((metricData) => metricData.name === 'Average Rating')?.data,
                    ratingCountData: itemMetric?.data?.find((metricData) => metricData.name === 'Total Count')?.data
                }

                return acc;
            }, {})
        }, 

        computedAllMetrics() {
            
            // Extract metrics for each type using the correct metric display names
            let gmvMetrics = this.processProductMetrics(this.allMetrics, {metricName: "GMV"});
            let orderCountMetrics = this.processProductMetrics(this.allMetrics, {metricName: "Cart Volume"}); // order_count maps to "Cart Volume"
            let totalItemsSoldMetrics = this.processProductMetrics(this.allMetrics, {metricName: "Total Items Sold"});
            let aovMetrics = this.processProductMetrics(this.allMetrics, {metricName: "AOV"});
            let canceledQuantityMetrics = this.processProductMetrics(this.allMetrics, {metricName: "Canceled Quantity"});
            let uniqueCartsMetrics = this.processProductMetrics(this.allMetrics, {metricName: "Cart Pen"}); // unique_carts maps to "Cart Pen"
            let ratingsMetrics = this.processProductMetrics(this.ratingMetrics, {isRatingData: true});
            let complaintsMetrics = this.processComplaintsForGrid();

            // Sort function to maintain consistent ordering across metrics 
            let sortedGmvMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, gmvMetrics);
            let sortedTotalItemsSoldMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, totalItemsSoldMetrics);
            let sortedAovMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, aovMetrics);
            let sortedCanceledQuantityMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, canceledQuantityMetrics);
            let sortedUniqueCartsMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, uniqueCartsMetrics);
            let sortedRatingsMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, ratingsMetrics);
            let sortedComplaintsMetrics = this.sortMetricsViaOrderCount(orderCountMetrics, complaintsMetrics);

            const mapping = {
                gmvMetrics: sortedGmvMetrics,
                orderCountMetrics: orderCountMetrics,
                totalItemsSoldMetrics: sortedTotalItemsSoldMetrics,
                aovMetrics: sortedAovMetrics,
                canceledQuantityMetrics: sortedCanceledQuantityMetrics,
                uniqueCartsMetrics: sortedUniqueCartsMetrics,
                ratingsMetrics: sortedRatingsMetrics,
                complaintsMetrics: sortedComplaintsMetrics
            };

            return mapping;
        },

        computedMetricConfig() {
            return ProductMetricGridMapping(this);
        },

        computedStoreList() {
            return this.selectedCityStores;
        },

        computedStoreId() {
            return this.appliedFilters.selectedStore.frontend_merchant_id
        },


        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getBistroStoreCityList));

            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
            return cityList;
        },

        computedCityName() {
            let modifiedCityMapping = getPanIndiaStoreCityList(this.getBistroStorePageCityMapping);
            let cityName = modifiedCityMapping[this.appliedFilters.selectedCity]["name"];

            return {
                displayName: modifiedCityMapping[this.appliedFilters.selectedCity]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        computedPTypeList() {
            return [...this.getPTypeList].sort();
        },


        computedAllComplaints() {
            if (!this.selectedComplaintsItem) return
            const red = this.allComplaints
                ?.filter((complaintItem) => complaintItem.item_name === this.selectedComplaintsItem)
                ?.reduce((obj, item) => {
                    obj[item?.complaint_type] = isEmpty(obj?.[item?.complaint_type]) ? [item] : [...obj[item?.complaint_type], item];
                    return obj;
                }, {});

            return Object.keys(red).map((type) => ({ complaint_type: type, data: red[type] }));
        },

        computedTableData() {
            return this.computedAllComplaints?.map(category => {
                const row = { complaint_type: startCase(category?.complaint_type) };
                category.data?.forEach(item => {
                    row[item.date] = item.count;
                });
                return row;
            });
        },

        computedTableColumns() {
            const dateColumns = [...new Set(this.computedAllComplaints?.flatMap(cat => cat.data?.map(d => d.date)))];

            return dateColumns?.sort((a, b) => {
                let dateA = new Date(a);
                let dateB = new Date(b);
                return dateB - dateA;
            });
        },

        computedRatingsForTable() {
            if (!this.selectedRatingsItem) return
            const ratings = this.ratingMetrics
                ?.find((ratingItem) => ratingItem.item_name === this.selectedRatingsItem)?.data
                ?.filter((data) => !['Total Count', 'Average Rating'].includes(data.name))
                ?.reduce((obj, metric) => {
                    obj[metric.name] = metric.data
                    return obj;
                }, {});

            return Object.keys(ratings).map((type) => ({ rating_type: type, data: ratings[type] }));
        },

        computedRatingsTableData() {
            const priorityArray = Object.values(RATINGS_MAPPING)
            return this.computedRatingsForTable?.map(rating => {
                const row = { rating_type: RATINGS_MAPPING[rating?.rating_type] };
                rating.data?.forEach(item => {
                    row[item.date] = item.metric;
                });
                return row;
            })
                ?.sort((a, b) =>
                    priorityArray.findIndex(x => x === a.rating_type) - priorityArray.findIndex(x => x === b.rating_type));
        },

        computedRatingsTableColumns() {
            const dateColumns = [...new Set(this.computedRatingsForTable?.flatMap(cat => cat.data?.map(d => d.date)))];

            return dateColumns?.sort((a, b) => {
                let dateA = new Date(a);
                let dateB = new Date(b);
                return dateB - dateA;
            });
        },

        ...mapState(useCityStore, ['getBistroProductPage', 'getBistroStoreToCityMapping', 'getBistroStoreCityList', 'getBistroStorePageCityMapping']),
        ...mapState(useUserStore, ['getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },


        toggled(val) {
            this.yesterday = val
            this.isLoading = true
            this.isError = false
            this.fetchAllMetrics()
            this.bistroProductPage = {
                ...this.bistroProductPage,
                yesterday: this.yesterday
            };
            this.updateBistroProductPage(this.bistroProductPage)
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getBistroStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            this.isLoading = false;
            let storeList = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            this.mappedList = storeList;

            if (this.isGlobalUser || this.isCityUser) {
                this.mappedList = getModifiedStoreList(storeList);
                this.mappedList[PanIndiaConstants.PAN_INDIA.code] = [];

                for (let cityName in this.mappedList) {
                    if (this.mappedList.hasOwnProperty(cityName)) {
                        let cityStores = this.mappedList[cityName];
                        if (((this.getBistroStoreToCityMapping && this.getBistroStoreToCityMapping?.find((item) => item?.city === (this.getBistroStoreCityList?.find((item) =>
                            item?.code === cityName)?.name)))?.data?.length <= cityStores?.length
                            || cityName === PanIndiaConstants.PAN_INDIA.code)) {
                            let panIndiaStore = {
                                frontend_merchant_id: '',
                                frontend_merchant_name: 'Overall',
                                backend_merchant_id: '',
                                name: cityName,
                                merchant_name_id: 'Overall'
                            };

                            cityStores.unshift(panIndiaStore);
                        }
                    }
                }
            }

            this.selectedCityStores = this.mappedList[this.appliedFilters.selectedCity];
            if (this.appliedFilters.selectedStore == null) {
                this.appliedFilters.selectedStore = this.selectedCityStores[0];
            }
            this.isLoading = true;
            this.fetchAllMetrics();
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const productMetricsCancelTokenSource = axios.CancelToken.source();
                const productComplaintMetricsCancelTokenSource = axios.CancelToken.source();
                const productRatingsMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [productMetricsCancelTokenSource, productComplaintMetricsCancelTokenSource, productRatingsMetricsCancelTokenSource];

                const createParams = (cancelToken, extraParams = {}) => {
                    const params = {
                        city: this.computedCityName.cityName,
                        store: this.computedStoreId,
                        yesterday_metric: this.yesterday,
                        cancelToken,
                        ...extraParams
                    };
                    
                    if (this.appliedFilters.selectedPType && this.appliedFilters.selectedPType.length > 0) {
                        params.ptype = this.appliedFilters.selectedPType;
                    }
                    if (this.appliedFilters.selectedPName && this.appliedFilters.selectedPName.length > 0) {
                        params.pname = this.appliedFilters.selectedPName;
                    }
                    if (this.appliedFilters.selectedL1 && this.appliedFilters.selectedL1.length > 0) {
                        params.l1_category = this.appliedFilters.selectedL1;
                    }
                    if (this.appliedFilters.selectedL2 && this.appliedFilters.selectedL2.length > 0) {
                        params.l2_category = this.appliedFilters.selectedL2;
                    }
                    if (this.appliedFilters.selectedPId && this.appliedFilters.selectedPId.length > 0) {
                        params.pid = this.appliedFilters.selectedPId;
                    }

                    params.l0_category = 'Bistro';
                    
                    return params;
                };

                // Create all API calls
                const productMetrics = bistroApi.fetchProductMetrics(createParams(
                    productMetricsCancelTokenSource.token,
                    { metrics: ["gmv", "order_count", "total_items_sold", "aov", "canceled_quantity", "unique_carts"]}
                ));

                const productComplaintParams = createParams(productComplaintMetricsCancelTokenSource.token);
                const productComplaintMetrics = bistroApi.fetchProductComplaints({
                    city: productComplaintParams.city,
                    store: productComplaintParams.store,
                    yesterday_metric: productComplaintParams.yesterday_metric,
                    cancelToken: productComplaintParams.cancelToken,
                    ptype: productComplaintParams.ptype,
                    l1_category: productComplaintParams.l1_category,
                    l2_category: productComplaintParams.l2_category,
                    pid: productComplaintParams.pid,
                    pname: productComplaintParams.pname,
                    l0_category: 'Bistro'
                });

                const productRatingsParams = createParams(productRatingsMetricsCancelTokenSource.token);
                const productRatingsMetrics = bistroApi.fetchProductRatings({
                    city: productRatingsParams.city,
                    store: productRatingsParams.store,
                    yesterday_metric: productRatingsParams.yesterday_metric,
                    cancelToken: productRatingsParams.cancelToken,
                    ptype: productRatingsParams.ptype,
                    l1_category: productRatingsParams.l1_category,
                    l2_category: productRatingsParams.l2_category,
                    pid: productRatingsParams.pid,
                    pname: productRatingsParams.pname,
                    l0_category: 'Bistro'
                });


                let [productMetricsResponse, productComplaintMetricsResponse, productRatingsMetricsResponse] = await Promise.all([
                    productMetrics,
                    productComplaintMetrics,
                    productRatingsMetrics
                ]);

                if (productMetricsResponse) {
                    const metrics = this.filterMetricsWithNoData(productMetricsResponse.data.metrics);
                    this.allMetrics = this.sortviaTotalItemsSold(metrics)
                }

                if (productComplaintMetricsResponse) this.allComplaints = productComplaintMetricsResponse.data.complaints;

                if (productRatingsMetricsResponse) this.ratingMetrics = productRatingsMetricsResponse.data.metrics;

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    console.log("error",JSON.stringify(error));
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        async fetchProductFilters() {
            try {
                const response = await bistroApi.fetchProductFilters({
                    l0_category: 'Bistro',
                    search_filter: 'ptype'
                });
                this.getPTypeList = response.data?.filtered_values || [];
            } catch (error) {
                console.error("Error fetching product filters:", error);
                this.getPTypeList = [];
            }
        },

        processProductMetrics(productData, options = {}) {
            const { 
                metricName = null, 
                isRatingData = false, 
            } = options;
            
            if (!productData || productData.length === 0) {
                return [];
            }

            let results = [];

            productData.forEach(product => { 
                const { item_name = "", data = [] } = product || {};
                if (!item_name) return;  
                const sortedMetrics = sortedAllMetrics(data, undefined, true, 5);

                let processedMetrics = sortedMetrics?.map(metric => {
                    let data = metric.data;
                    let curr = data[data.length - 1];
                    let diffs = [];
                    for (let j = 0; j < data.length - 1; j++) {
                        let prev = data[j];
                        diffs.push(new MetricChange(curr, prev, metric.name));
                    }
                    return {
                        productName: item_name,
                        name: metric.name,
                        reverseStyle: metricsForStyleReversal.includes(metric.name),
                        curr: curr,
                        diffs: diffs,
                    }
                })?.filter(metric => metric.curr && metric.curr.value !== "-");

                let filteredMetrics = [];
                
                if (isRatingData) {
                    // Filter for Average Rating only and add countCurr
                    const averageRatingMetric = processedMetrics?.find(metric => metric.name === "Average Rating");
                    const totalCountMetric = sortedMetrics?.find(metric => metric.name === "Total Count");
                    
                    if (averageRatingMetric && totalCountMetric) {
                        const countData = totalCountMetric.data;
                        const countCurr = countData[countData.length - 1];
                        
                        filteredMetrics = [{
                            ...averageRatingMetric,
                            countCurr: countCurr,
                            isRating: true
                        }];
                    }
                } else {
                    filteredMetrics = metricName 
                        ? processedMetrics?.filter(metric => metric.name === metricName)
                        : processedMetrics;
                }

                results = [...results, ...filteredMetrics];
            });
            
            const filteredResults = results.filter(metric => metric.curr && metric.curr.value !== "-");
            
            // Sort Cart Volume metrics in descending order by current value
            if (metricName === "Cart Volume") {
                return filteredResults.sort((a, b) => {
                    const aValue = parseFloat(a.curr?.value) || 0;
                    const bValue = parseFloat(b.curr?.value) || 0;
                    return bValue - aValue; 
                });
            }
            
            return filteredResults;
        },

        
        handleMetricGridClick(event) {
            const { itemName, title } = event;
            
            if (title === 'ALL COMPLAINTS') {
                this.handleComplaints(itemName);
            } else if (title === 'RATINGS') {
                this.handleRatings(itemName, 'Ratings');
            }
            
        },
    
        onFiltersApplied(filterData) {
            this.isLoading = true;
            this.isError = false;
            this.resetAllMetrics();
            
            this.appliedFilters = {
                ...filterData
            };
            
            if (this.appliedFilters.selectedCity) {
                this.selectedCityStores = this.mappedList[this.appliedFilters.selectedCity];
            }
            
            this.bistroProductPage = {
                ...this.bistroProductPage,
                appliedFilters: this.appliedFilters
            };
            this.updateBistroProductPage(this.bistroProductPage);
            this.fetchAllMetrics();
        },

        onStoreListUpdateNeeded(cityCode) {
            if (this.mappedList[cityCode]) {
                this.selectedCityStores = this.mappedList[cityCode];
            }
        },
    
        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; 
        },

        filterMetricsWithNoData(metrics) {
            return metrics.filter(metric => metric.data.length > 0);
        },

        sortviaTotalItemsSold(metrics) {
            const today = new Date(new Date().getTime()).toLocaleDateString('en-CA', { timeZone: 'Asia/Kolkata' });
            const yesterday = new Date(new Date().getTime() - 864e5).toLocaleDateString('en-CA', { timeZone: 'Asia/Kolkata' });

            const selectedDate = this.yesterday ? yesterday : today

            return metrics.sort((a, b) => {
                const { data: aData = [] } = a || {}
                const { data: bData = [] } = b || {}

                const metricA = aData?.find((item) => item?.name === 'Total Items Sold')?.data?.find((itemData) => itemData?.date === selectedDate)?.metric ?? 0
                const metricB = bData?.find((item) => item?.name === 'Total Items Sold')?.data?.find((itemData) => itemData?.date === selectedDate)?.metric ?? 0

                if (metricA > metricB) return -1
                if (metricA < metricB) return 1

                const cpA = aData?.find((item) => item?.name === 'Cart Pen')?.data?.find((itemData) => itemData?.date === selectedDate)?.metric ?? 0
                const cpB = bData?.find((item) => item?.name === 'Cart Pen')?.data?.find((itemData) => itemData?.date === selectedDate)?.metric ?? 0

                if (cpA > cpB) return -1
                if (cpA < cpB) return 1
                return 0
            })
        },

        sortMetricsViaOrderCount(orderCountMetrics, metricsToBeSorted) {
            if (!orderCountMetrics || !metricsToBeSorted) return metricsToBeSorted || [];
            
            const filteredMetrics = this.filterProductMetrics(metricsToBeSorted);
            
            return filteredMetrics.sort((a, b) => {
                let indexOfA = orderCountMetrics.findIndex(metric => metric.productName === a.productName);
                let indexOfB = orderCountMetrics.findIndex(metric => metric.productName === b.productName);
                return indexOfA - indexOfB;
            });
        },

        refreshFiltersList() {
            fetchStoreToCityMapping(false, true);
        },


        resetAllMetrics() {
            this.allMetrics = []
            this.allComplaints = []
            this.ratingMetrics = []
        },


        handleComplaints(itemName) {
            this.showComplaints = true
            this.selectedComplaintsItem = itemName
        },

        handleRatings(itemName, metricName) {
            if (metricName !== 'Ratings') return
            this.showRatings = true
            this.selectedRatingsItem = itemName
        },

        isSensitiveDataPresent(metrics) {
            for (const metric of metrics) {
                if (metric?.curr?.value && metric.curr.value !== "-") {
                    return true;
                }
            }
            return false;
        },
        
        filterProductMetrics(metrics) {
            return metrics?.filter((metric) => 
                    metric?.curr?.value !== "-" && 
                    metric?.curr?.value !== undefined
                ) || [];
        },

        processComplaintsForGrid() {
            if (!this.allComplaints || this.allComplaints.length === 0) {
                return [];
            }

            const complaintsByItem = this.allComplaints.reduce((acc, complaint) => {
                if (!complaint.item_name) return acc;
                
                if (!acc[complaint.item_name]) {
                    acc[complaint.item_name] = [];
                }
                acc[complaint.item_name].push(complaint);
                return acc;
            }, {});

            const results = [];
            Object.keys(complaintsByItem).forEach(itemName => {
                const itemComplaints = complaintsByItem[itemName];
                
                const complaintsAggregatedOnDate = itemComplaints.reduce((acc, item) => {
                    if (item.date) {
                        if (acc.hasOwnProperty(item.date)) {
                            acc[item.date] += item.count ?? 0;
                        } else {
                            acc[item.date] = item.count ?? 0;
                        }
                    }
                    return acc;
                }, {});

                const dateEntries = Object.entries(complaintsAggregatedOnDate)
                    .map(([date, count]) => ({ date, metric: count }));

                if (dateEntries.length > 0) {
                    const complaintData = [{
                        name: "All Complaints",
                        type: "number",
                        data: dateEntries
                    }];

                    // Use sortedAllMetrics for proper filtering, it will handle all edge cases
                    const sortedMetrics = sortedAllMetrics(complaintData, undefined, true, 5);

                    let processedMetrics = sortedMetrics?.map(metric => {
                        let data = metric.data;
                        let curr = data[data.length - 1];
                        let diffs = [];
                        for (let j = 0; j < data.length - 1; j++) {
                            let prev = data[j];
                            diffs.push(new MetricChange(curr, prev, metric.name));
                        }
                        return {
                            productName: itemName,
                            name: metric.name,
                            reverseStyle: true,
                            curr: curr,
                            diffs: diffs,
                        }
                    })?.filter(metric => metric.curr && metric.curr.value !== "-");

                    results.push(...processedMetrics);
                }
            });

            return results.filter(metric => metric.curr && metric.curr.value !== "-")
                        .sort((a, b) => {
                            let aVal = parseFloat(a.curr.value) || 0;
                            let bVal = parseFloat(b.curr.value) || 0;
                            return bVal - aVal;
                        });
        },

       

        clearProductFilters() {
            this.isLoading = true;
            this.selectedProducts = [];
            this.fetchAllMetrics();
            this.bistroProductPage = {
                ...this.bistroProductPage,
                selectedProducts: this.selectedProducts
            };
            this.updateBistroProductPage(this.bistroProductPage);
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateBistroProductPage']),
    },

        data() {
        return {
            showHeader: false,
            isLoading: true,
            isError: false,
            intervalId: null,
            cancelTokens: [],
            canAccessPage: false,
            yesterday: false,
            bistroProductPage: {},
            allMetrics: [],
            appliedFilters: {
                selectedCity: null,
                selectedStore: null,
                selectedPType: [],
                selectedPName: [],
                selectedPId: [],
                selectedL1: [],
                selectedL2: []
            },
            mappedList: {},
            selectedCityStores: [],
            isGlobalUser: false,
            isCityUser: false,
            allComplaints: [],
            showComplaints: false,
            selectedComplaintsItem: null,
            ratingMetrics: [],
            showRatings: false,
            selectedRatingsItem: null,
            selectedProducts: [],
            getPTypeList: []
        }
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(BISTRO_PAGE_ID_ROUTE_MAPPING[17]);
        if (!this.canAccessPage) noPermissionRoute(true)


        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);
        this.fetchProductFilters();

        if (isEmpty(this.getBistroStoreToCityMapping)) this.refreshFiltersList();
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = true;
            this.isError = false;

            this.bistroProductPage = this.getBistroProductPage
            this.yesterday = this.bistroProductPage?.yesterday || false

            // Initialize appliedFilters from localStorage or defaults
            if (this.bistroProductPage?.appliedFilters) {
                // Load entire appliedFilters from localStorage if available
                this.appliedFilters = {
                    ...this.appliedFilters,
                    ...this.bistroProductPage.appliedFilters
                };
            } 
            
            if (!this.appliedFilters.selectedCity) {
                if (this.isGlobalUser) {
                    this.appliedFilters.selectedCity = PanIndiaConstants.PAN_INDIA.code;
                } else {
                    this.appliedFilters.selectedCity = Object.keys(this.getBistroStorePageCityMapping)[0];
                }
            }

            this.checkRoute();
            this.fetchCityStoreMapping();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}

</script>
