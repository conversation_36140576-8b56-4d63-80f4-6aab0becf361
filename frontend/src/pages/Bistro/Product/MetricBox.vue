<template>
    <div class="metric-box border px-2 py-1 bg-gray-50 md:bg-gray-100 border-gray-200" :class="hasHover ? 'hover:shadow-md' : ''" @click="handleClick">
        <div class="flex">
            <span class="font-normal text-gray-500 text-xs md:text-sm truncate">{{name}}</span>
        </div>
        <div class="flex justify-end items-start mt-1 gap-1">
            <!-- Special handling for ratings -->
            <div v-if="isRating && countCurr" class="flex items-center">
                <AnimatedNumber :styles="['font-medium','text-sm','md:text-base','flex-shrink-0']" :value="curr.value" :type="curr.type" :isShortenedNumber="isShortenedNumber"/>
                <span class="text-xs md:text-sm text-gray-500 ml-1">({{ countCurr.value }})</span>
            </div>
            <AnimatedNumber v-else :styles="['font-medium','text-sm','md:text-base','flex-shrink-0']" :value="curr.value" :type="curr.type" :isShortenedNumber="isShortenedNumber"/>
        </div>
      <div>
    </div>
      <!-- Show multiple diffs if available, otherwise show single diff -->
      <div v-if="diffs && diffs.length > 0" class="flex justify-end items-center mt-1">
        <div class="flex items-center">
          <template v-for="(diff, index) in lastTwoDiffs" :key="diff.date">
            <AnimatedNumber 
              :value="diff.change()" 
              :type="absoluteMetricDeltaConstant && absoluteMetricDeltaConstant.includes(metricName) ? 'number' : 'percentage'"
              :styles="['text-[9px]', 'font-[650]', diff.style(reverseStyle)]"
            />
            <span v-if="index < lastTwoDiffs.length - 1" class="text-[10px] font-bold mx-1 text-gray-400">
              »
            </span>
          </template>
        </div>
      </div>
  
      <!-- Fallback to single diff for backward compatibility -->
      <AnimatedNumber v-else :styles="style" :value="diff" type="percentage"/>
   </div>
  </template>
  
  <script>
  import AnimatedNumber from '../../../components/AnimatedNumber.vue';
  import { Metric, MetricChange} from '../../../interfaces/index.js';
  import { ABSOLUTE_METRIC_DELTA } from '../../../utils/metrics.js';
  
  export default {
    components: {
      AnimatedNumber,
    },
  
    computed: {
      diff() {
        let val = new MetricChange(this.curr, this.prev).change();
        if(val === "-")return val;
        return parseFloat(new MetricChange(this.curr, this.prev).change());
      },
  
      style() {
        var metricChange = new MetricChange(this.curr, this.prev);
        var diff_style = metricChange.style(this.isReverse);
  
        return [
          'font-semibold',
          'text-sm',
          'mt-auto',
          diff_style
        ];
      },
  
      absoluteMetricDeltaConstant() {
        return ABSOLUTE_METRIC_DELTA;
        },
      
      lastTwoDiffs() {
        return this.diffs.length > 2 ? this.diffs.slice(-2) : this.diffs;
      },
    },
  
    emits: ['onClick'],
  
    methods: {
      handleClick() {
        this.$emit('onClick', { val: this.name })
      }
    },
  
    props: {
      name: String,
      metricName: String,
      curr: Metric,
      prev: Metric, 
      diffs: {      
        type: Array,
        default: () => []
      },
      reverseStyle: { 
        type: Boolean,
        default: false
      },
      styles: Array,
      isShortenedNumber: {
        type: Boolean,
        default: false,
      },
      isReverse: {
        type: Boolean,
        default: false,
      },
      hasHover: {
        type: Boolean,
        default: false,
      },
      isRating: {
        type: Boolean,
        default: false,
      },
      countCurr: {
        type: Metric,
        default: null,
      },
    },
  }
  
  </script>
  