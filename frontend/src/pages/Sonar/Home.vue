<template>
  <header class="bg-gradient-to-br from-white via-gray-50 to-gray-100 shadow-xl sticky top-0 backdrop-blur-sm z-20"
    v-if="showHeader">
    <div class="max-w-7xl mx-auto py-3">
      <div class="flex flex-col items-start space-y-4">
        <div class="flex items-center space-x-3">
          <div
            class="w-1.5 min-h-12 bg-gradient-to-b from-yellow-400 via-orange-400 to-orange-500 rounded-full shadow-md">
          </div>
          <div v-if="!groupView" class="flex flex-col">
            <div
              class="text-3xl font-black tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
              {{ computedCityName && computedCityName?.displayName ? computedCityName?.displayName : this.selectedCity
              }}
            </div>
            <div v-if="this.selectedStore && this.selectedStore?.merchant_name_id !== 'Overall'"
              class="text-lg text-gray-600 font-semibold mt-1 tracking-wide">
              {{ this.selectedStore?.merchant_name_id }}
            </div>
          </div>
          <div v-else class="flex items-center space-x-4">
            <div
              class="text-3xl font-black tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
              {{ selectedGroup?.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <div class="max-w-7xl mx-auto px-4 py-6">
    <!-- Mobile Filter Bottom Sheet -->
    <MobileFilterBottomSheet ref="mobileFilterBottomSheet" v-if="isMobile" :filterSummary="mobileFilterSummary"
      :activeFiltersCount="activeFiltersCount" :hasActiveFilters="hasActiveFilters"
      :deferredMode="hasPendingMobileChanges" @open="onMobileFilterOpen" @close="onMobileFilterClose"
      @apply="onMobileFilterApply" @clear-all="onMobileFilterClearAll">

      <template #filters>
        <div v-if="isGlobalUser" class="text-left text-gray-900 flex items-center justify-between gap-2">
          <p class="text-lg font-semibold">Group View</p>
          <VueToggle class="max-w-md" title="" name="VueToggle" :toggled="tempMobileFilters.groupView" @toggle="handleToggleGroupView" />
        </div>
          <!-- Date Filter (Mobile) -->
          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-calendar mr-2"></i>Date
            </label>
            <Calendar v-model="tempMobileFilters.selectedDate" showIcon iconDisplay="input" selectionMode="single"
              :maxDate="new Date()" :minDate="computedMinDate" showButtonBar class="w-full blinkit-calendar"
              :inputStyle="{ height: '40px', borderRadius: '0.75rem', borderColor: '#975A16', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }"
              dateFormat="yy-mm-dd" />
        </div>

        <!-- Group Filter (Mobile) - Show only when group view is enabled -->
        <div v-if="tempMobileFilters.groupView" class="space-y-3">
          <label class="block text-sm font-semibold text-yellow-600">
            <i class="pi pi-users mr-2"></i>Group
          </label>
          <v-select :loading="isGroupLoading" v-model="tempMobileFilters.selectedGroup"
            :options="computedGroupsAndCitiesList" label="name" :clearable="false"
            class="w-full filter-select bg-white rounded-xl shadow-md" :class="{ 'opacity-75': isGroupLoading }"
            :styles="mobileSelectStyles" />
        </div>

        <!-- City/Zone/Store Filters (Mobile) - Show only when group view is disabled -->
        <template v-if="!tempMobileFilters.groupView">
          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-map-marker mr-2"></i>City
            </label>
            <v-select :reduce="city => city.code" v-model="tempMobileFilters.selectedCity" :options="computedCityList"
              label="name" :clearable="false" placeholder="Select city" @update:model-value="onMobileCityChange($event)"
              class="w-full filter-select bg-white rounded-xl shadow-md" :styles="mobileSelectStyles" />
          </div>

          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-map-marker mr-2"></i>Zone
            </label>
            <v-select v-model="tempMobileFilters.selectedZone" :reduce="item => item.code"
              :options="computedMobileZoneList" label="name" @update:model-value="onMobileZoneChange($event)"
              :disabled="tempMobileFilters.selectedCity === computedPanIndiaConstants.PAN_INDIA.code"
              placeholder="Select zone" class="w-full filter-select bg-white rounded-xl shadow-md"
              :class="{ 'opacity-50': tempMobileFilters.selectedCity === computedPanIndiaConstants.PAN_INDIA.code }"
              :styles="mobileSelectStyles" />
          </div>

          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-shopping-cart mr-2"></i>Store
            </label>
            <v-select v-model="tempMobileFilters.selectedStore" :options="computedMobileStoreList"
              label="merchant_name_id"
              :disabled="tempMobileFilters.selectedCity === computedPanIndiaConstants.PAN_INDIA.code" :clearable="false"
              placeholder="Select store" class="w-full filter-select bg-white rounded-xl shadow-md"
              :class="{ 'opacity-50': tempMobileFilters.selectedCity === computedPanIndiaConstants.PAN_INDIA.code }"
              :styles="mobileSelectStyles" />
          </div>
        </template>

      </template>
    </MobileFilterBottomSheet>


    <!-- Desktop Filter Grid -->
    <div v-if="!isMobile" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-if="groupView" class="col-span-1 md:col-span-2">
        <label class="block text-sm font-semibold text-yellow-600 mb-2">
          <i class="pi pi-users mr-2"></i>Group
        </label>
        <v-select :loading="isGroupLoading" v-model="selectedGroup" :options="computedGroupsAndCitiesList" label="name"
          @update:model-value="onGroupChange($event)" :clearable="false"
          class="w-full filter-select bg-white rounded-xl shadow-md" :class="{ 'opacity-75': isGroupLoading }" :styles="{
            control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
            option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
          }" />
      </div>
      <template v-else>
        <div>
          <label class="block text-sm font-semibold text-yellow-600 mb-2">
            <i class="pi pi-map-marker mr-2"></i>City
          </label>
          <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
            :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
            placeholder="Select city" class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
              control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
              option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
            }" />
        </div>

        <div v-if="isGlobalUser || isCityUser">
          <label class="block text-sm font-semibold text-yellow-600 mb-2">
            <i class="pi pi-map-marker mr-2"></i>Zone
          </label>
          <v-select v-model="selectedZone" :reduce="item => item.code" :options="computedZoneList" label="name"
            :disabled="this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code"
            @update:model-value="onZoneChange($event)" placeholder="Select zone"
            class="w-full filter-select bg-white rounded-xl shadow-md"
            :class="{ 'opacity-50': this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code }" :styles="{
              control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
              option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
            }" />
        </div>

        <div>
          <label class="block text-sm font-semibold text-yellow-600 mb-2">
            <i class="pi pi-shopping-cart mr-2"></i>Store
          </label>
          <v-select v-model="selectedStore" :options="computedStoreList" label="merchant_name_id"
            :disabled="this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code"
            @update:model-value="onStoreChange($event)" :clearable="false" placeholder="Select store"
            class="w-full filter-select bg-white rounded-xl shadow-md"
            :class="{ 'opacity-50': this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code }" :styles="{
              control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
              option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
            }" />
        </div>
      </template>
      <div>
        <label class="block text-sm font-semibold text-yellow-600 mb-2">
          <i class="pi pi-calendar mr-2"></i>Date
        </label>
        <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single" :maxDate="new Date()"
          :minDate="computedMinDate" showButtonBar class="w-full blinkit-calendar"
          :inputStyle="{ height: '40px', borderRadius: '0.75rem', borderColor: '#975A16', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }"
          @update:model-value="onDateChange($event)" dateFormat="yy-mm-dd" />
      </div>
    </div>

    <!-- Desktop View Toggle -->
    <div v-if="isGlobalUser && !isMobile" class="flex justify-end pt-4">
      <SelectButton v-model="groupView" :options="groupViewOptions" optionLabel="label" optionValue="value"
        aria-labelledby="basic" @update:modelValue="toggleGroupView" class="shadow-md rounded-xl overflow-hidden"
        :class="{
          'bg-yellow-50': groupView,
          'text-yellow-600': groupView
        }" />
    </div>
  </div>

  <main>
    <WaterMark class="absolute inset-0 pointer-events-none z-10"></WaterMark>

    <div class="max-w-7xl mx-auto px-2 py-1">
      <Projections v-if="assetsLoaded" :cityCode="groupView ? computedGroup?.city : computedCityName.cityName"
        :storeCode="groupView ? null : computedStoreId" :zoneCode="groupView ? null : computedZoneName"
        :groupId="groupView ? computedGroup?.group_id : null" :enableGroupView="groupView" :selectedDate="selectedDate"
        :isGlobalUser="isGlobalUser" :isCityUser="isCityUser" />


      <MetricGroup v-if="assetsLoaded" v-for="(metricGroup, key, idx) in computedOrderMetricBreakdown"
        :key="metricGroup.key" :isDefaultOpen="metricGroup.isDefaultOpen" :title="metricGroup.title"
        :icon="metricGroup?.icon" :metricGroup="metricGroup"
        :cityCode="groupView ? computedGroup?.city : computedCityName.cityName"
        :storeCode="groupView ? null : computedStoreId" :zoneCode="groupView ? null : computedZoneName"
        :groupId="groupView ? computedGroup?.group_id : null" :enableGroupView="groupView" :selectedDate="selectedDate"
        :showHeader="showHeader" :idx="idx" :athMetrics="athMetrics" :openAccordions="metricGroupOpenAccordions"
        @showExtraMetrics="showExtraMetrics" @athHit="athHit = true" @metricsUpdated="updateMetricGroupMetrics"
        @updateOpenAccordion="updateOpenMetricGroupAccordion"
        @updateClosedAccordion="updateClosedMetricGroupAccordion" />
    </div>

    <div class="gap-3 mt-8 max-w-7xl mx-auto px-2 py-1">
      <div class="mt-2 md:mt-4 text-center md:text-left">
        <h2 class="text-sm md:text-lg font-bold text-gray-900 flex items-center gap-2">
          <i class="pi pi-chart-line"></i>
          Hourly Trends
        </h2>
      </div>

      <HourlyMetricsV2 v-if="assetsLoaded" :cityCode="groupView ? computedGroup?.city : computedCityName.cityName"
        :storeCode="groupView ? null : computedStoreId" :zoneCode="groupView ? null : computedZoneName"
        :groupId="groupView ? computedGroup?.group_id : null" :enableGroupView="groupView" :selectedDate="selectedDate"
        :showHeader="showHeader" :openAccordions="hourlyOpenAccordions" @updateOpenAccordion="updateOpenHourlyAccordion"
        @updateClosedAccordion="updateClosedHourlyAccordion" />
    </div>

    <Dialog v-model:visible="visible" modal :header="extraMetricKey" :style="{ width: '45rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :dismissableMask="true">
      <div>
        <div v-if="computedNote" class="mb-3 text-xs md:text-sm italic">
          <strong>Note : </strong>
          <span>{{ computedNote }}</span>
        </div>
        <div v-for="metric in computedExtraMetrics" :key="metric.name"
          class="md:bg-white py-1.5 md:py-4 border-b md:border-b-0 md:rounded ">
          <div class="flex justify-between">
            <span class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
              metric.name }}
              <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
            </span>
            <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
              :type="metric.curr.type" />
          </div>
          <div class="flex justify-between items-center px-2">
            <span class="text-xs font-light gray-900"></span>
            <div class="flex justify-between">
              <div v-for="diff in metric.diffs" :key="diff.date">
                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                  <template v-if="diff.change() !== '-'">
                    {{ diff.change() }}
                    {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                  </template>
                  <template v-else>
                    -
                  </template>
                </span>
                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                  class="text-xs font-bold mr-1 text-positive-metric"> »
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>

    <!-- bottom spacing -->
    <div class="h-44"></div>
  </main>

  <!-- Mobile Floating Action Buttons -->
  <MobileFloatingButtons :isMobile="isMobile" :hasActiveFilters="hasActiveFilters"
    :activeFiltersCount="activeFiltersCount" @openFilters="openMobileFilterFromFloatingButton" />
</template>


<script>

import Tooltip from "primevue/tooltip";
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import MobileFilterBottomSheet from "../../components/Common/MobileFilterBottomSheet.vue";
import MobileFloatingButtons from "../../components/Common/MobileFloatingButtons.vue";
import { useScreenSize } from "../../composables/useScreenSize.js";
import MetricGroup from "../../components/Home/MetricGroup.vue";
import { api } from "../../api/index.js";
import { MetricChange } from "../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics, ABSOLUTE_METRIC_DELTA } from "../../utils/metrics.js";
import { isGlobalUser, getUserAccessMapping, getUserAllowedStoresMapping, isCityUser, getModifiedStoreList, getCombinedCityNames, getPanIndiaStoreCityList, noPermissionRoute, formatDateToString, isDateToday } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import vSelect from 'vue-select';
import { ATHMetricRelation, CitiesToExcludeFromCityMapping, HomeExtraMetricsMapping, MergedCityStoreConstants, PanIndiaConstants } from "../../constants/index.js";
import Button from 'primevue/button';
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import Dialog from "primevue/dialog";
import Calendar from "primevue/calendar";
import SelectButton from "primevue/selectbutton";
import { ORDER_METRIC_BREAKDOWN, FLATTENED_HOURLY_METRICS } from "../../constants/sonar/home.js";
import Accordion from "primevue/accordion";
import AccordionTab from "primevue/accordiontab";
import Projections from "../../components/Home/Projections.vue";
import HourlyMetricsV2 from "../../components/Home/HourlyMetricsV2/index.vue";
import { demandBasedPriorityOrder } from "../../utils/metrics.js";
import VueToggle from "vue-toggle-component";

export default {

  components: {
    Loading,
    AnimatedNumber,
    MetricGroup,
    WaterMark,
    'v-select': vSelect,
    Button,
    InfoDialog,
    Tooltip,
    Dialog,
    Calendar,
    SelectButton,
    Accordion,
    AccordionTab,
    Projections,
    HourlyMetricsV2,
    MobileFilterBottomSheet,
    MobileFloatingButtons,
    VueToggle
  },

  directives: {
    tooltip: Tooltip
  },

  setup() {
    const { isMobile, isTablet, isDesktop, screenWidth } = useScreenSize();

    return {
      isMobile,
      isTablet,
      isDesktop,
      screenWidth
    };
  },

  computed: {
    computedPanIndiaConstants() {
      return PanIndiaConstants;
    },

    computedAbsoluteMetricDeltaConstant() {
      return ABSOLUTE_METRIC_DELTA;
    },

    computedOrderMetricBreakdown() {
      return ORDER_METRIC_BREAKDOWN;
    },

    computedCityList() {
      let staticList = [PanIndiaConstants.PAN_INDIA];
      let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
      if ((this.isGlobalUser || this.isCityUser)) {
        cityList.forEach((cityItem) => {
          if (Object.keys(MergedCityStoreConstants).includes(cityItem.code)) {
            cityItem.name = getCombinedCityNames(cityItem.code);
          }
        });
      }

      Object.keys(CitiesToExcludeFromCityMapping).forEach((item) => {
        if (cityList.filter((city) => city.code === item).length > 0) {
          cityList = cityList.filter((city) => !CitiesToExcludeFromCityMapping[item].includes(city.name));
        }
      })

      cityList = cityList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });

      if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
      return cityList;
    },

    computedStoreList() {
      return this.selectedCityStores;
    },

    computedMobileStoreList() {
      // For mobile filters, compute store list based on temporary filter values
      if (!this.tempMobileFilters.selectedCity || this.tempMobileFilters.selectedCity === this.computedPanIndiaConstants.PAN_INDIA.code) {
        return [];
      }

      let storeList = this.mappedList?.[this.tempMobileFilters.selectedCity] || [];

      // Filter by zone if selected
      if (this.tempMobileFilters.selectedZone) {
        storeList = storeList.filter((store) => store.zone === this.tempMobileFilters.selectedZone);
      }

      if (this.isMobileStoreOverallAllowed) {
        const overallStore = {
          frontend_merchant_id: "",
          frontend_merchant_name: "Overall",
          backend_merchant_id: "",
          name: this.tempMobileFilters.selectedCity,
          merchant_name_id: "Overall",
          zone: this.tempMobileFilters.selectedZone
        };

        if (!storeList.some((store) => store.merchant_name_id === 'Overall')) {
          storeList.unshift(overallStore);
        }
      }

      return storeList;
    },

    computedCityName() {
      if (!this.selectedCity) return {}
      let modifiedCityMapping = getPanIndiaStoreCityList(this.getStorePageCityMapping);

      let cityName = modifiedCityMapping[this.selectedCity]["name"];
      if ((this.isGlobalUser || this.isCityUser) &&
        Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.selectedCity]["code"])
      ) {
        cityName = MergedCityStoreConstants[this.selectedCity].join("&city=");
      }

      return {
        displayName: (Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.selectedCity]["code"]) && (this.isGlobalUser || this.isCityUser))
          ? getCombinedCityNames(this.selectedCity)
          : modifiedCityMapping[this.selectedCity]["name"],
        cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
      }
    },

    zoneList() {
      let zoneList = this.getZoneList?.filter(zoneList => zoneList.name !== "null")
      zoneList = zoneList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });
      return zoneList;
    },

    computedZoneList() {
      if (this.selectedCity === PanIndiaConstants.PAN_INDIA.code) return []

      let cityName = this.getStorePageCityMapping?.[this.selectedCity]?.name
      let zoneList = this.zoneList?.filter((zone) => zone.city === cityName)

      if (zoneList?.length === 1) {
        this.selectedZone = zoneList[0]?.code
        return zoneList
      }

      return zoneList
    },

    computedZoneName() {
      if (!this.selectedZone) return null;
      return this.computedZoneList?.find((zone) => zone?.code === this.selectedZone)?.name;
    },

    computedStoreId() {
      if (this.getStorePageCityMapping[this.selectedCity] == undefined) return;
      if (!this.selectedStore) return;
      return this.selectedStore["frontend_merchant_id"];
    },

    computedAllBlockMetrics() {
      // Use the aggregated metrics that are collected from all MetricGroup components
      const sortedMetrics = sortedAllMetrics(this.allMetricGroupMetrics, undefined, true, 5, this.computedDate)
      return sortedMetrics
    },

    showConfetti() {
      if (!this.isLoading && this.athHit && !this.getConfettiShown?.['present']) {
        return true;
      }
      return false;
    },

    computedHomeExtraMetricsMapping() {
      return HomeExtraMetricsMapping;
    },

    computedExtraMetrics() {
      const metricsToShow = this.computedHomeExtraMetricsMapping[this.extraMetricKey];
      const metrics = this.computedAllBlockMetrics.filter((metric) => metricsToShow.includes(metric.name))

      const sortedMetrics = metrics?.sort((a, b) => {
        const aIndex = demandBasedPriorityOrder.indexOf(a?.name);
        const bIndex = demandBasedPriorityOrder.indexOf(b?.name);

        return aIndex - bIndex;
      });
      return this.calculatedMetrics(sortedMetrics);
    },

    computedNote() {
      const NOTES = {
        "Surge Seen %": "Sum of reason level Surge might exceed Overall value. Surge applied due to both rider and picker stress is counted in both buckets.",
        "Surge Checkouts %": "Some orders avoid surge by crossing MOV amount.Surge paid orders are those that paid the surge amount.",
        "Unserviceable DAU %": "Longtail Block % numbers indicate percentage of unique users serviceable under express but blocked under longtail.",
        "Demand Based Block %": "Longtail Block % numbers indicate percentage of unique users serviceable under express but blocked under longtail."
      }

      return NOTES[this.extraMetricKey];
    },

    computedDate() {
      return formatDateToString(this.selectedDate);
    },

    computedIsNotToday() {
      return !isDateToday(this.selectedDate);
    },

    computedMinDate() {
      var day = new Date();
      day.setDate(day.getDate() - 25);
      return day;
    },

    computedInvalidDate() {
      return isEmpty(this.selectedDate)
    },

    computedGroupList() {
      return this.accessibleGroups
    },

    computedGroup() {
      if (this.selectedGroup?.type === PanIndiaConstants.PAN_INDIA_GROUP.type)
        return { isPanIndia: true }
      else if (this.selectedGroup?.type === "city")
        return { city: this.selectedGroup.name }
      else
        return { group_id: this.selectedGroup.id }
    },

    computedGroupsAndCitiesList() {
      if (this.isGroupLoading) return [PanIndiaConstants.PAN_INDIA_GROUP];
      return [PanIndiaConstants.PAN_INDIA_GROUP, ...this.groupsAndCitiesList]
    },

    groupViewOptions() {
      return [
        { label: 'Normal View', value: false },
        { label: 'Group View', value: true }
      ]
    },

    isStoreOverallAllowed() {
      if (this.isGlobalUser || this.isCityUser) return true;

      const userCityStores = this.mappedList?.[this.selectedCity];
      const actualCityStores = this.getStoreToCityMapping?.find((item) =>
        item?.city === this.computedCityName?.displayName
      )?.data?.length

      return userCityStores?.length >= actualCityStores
    },

    isMobileStoreOverallAllowed() {
      if (this.isGlobalUser || this.isCityUser) return true;

      const userCityStores = this.mappedList?.[this.tempMobileFilters?.selectedCity];
      const actualCityStores = this.getStoreToCityMapping?.find((item) =>
        item?.city === this.computedCityName?.displayName
      )?.data?.length

      return userCityStores?.length >= actualCityStores
    },

    ...mapState(useCityStore, ['getCityList', 'getCityMapping', 'getStorePageCityMapping', 'getStoreCityList', 'getStoreToCityMapping', 'getConfettiShown', 'getSonarHomePage', 'getZoneList', 'getZoneMapping']),
    ...mapState(useUserStore, ['user', 'isLoggedIn', 'getAllowedNavs']),

    // Mobile filter computed properties
    mobileFilterSummary() {
      const summary = [];

      // Use temp mobile filters when mobile filter is open and has pending changes
      const useTemp = this.hasPendingMobileChanges && this.tempMobileFilters;

      const currentGroupView = useTemp ? this.tempMobileFilters.groupView : this.groupView;
      const currentSelectedGroup = useTemp ? this.tempMobileFilters.selectedGroup : this.selectedGroup;
      const currentSelectedCity = useTemp ? this.tempMobileFilters.selectedCity : this.selectedCity;
      const currentSelectedZone = useTemp ? this.tempMobileFilters.selectedZone : this.selectedZone;
      const currentSelectedStore = useTemp ? this.tempMobileFilters.selectedStore : this.selectedStore;
      const currentSelectedDate = useTemp ? this.tempMobileFilters.selectedDate : this.selectedDate;

      if (currentGroupView && currentSelectedGroup) {
        summary.push({
          key: 'group',
          label: 'Group',
          value: currentSelectedGroup.name
        });
      } else {
        if (currentSelectedCity) {
          // Get city name from mapping
          const cityMapping = currentSelectedCity === PanIndiaConstants.PAN_INDIA.code ? PanIndiaConstants.PAN_INDIA : this.getCityMapping[currentSelectedCity];
          const cityName = cityMapping?.name || currentSelectedCity;
          summary.push({
            key: 'city',
            label: 'City',
            value: cityName
          });
        }

        if (currentSelectedZone) {
          // Get zone name from zone list
          const zoneMapping = this.zoneList?.find(zone => zone.code === currentSelectedZone);
          const zoneName = zoneMapping?.name || currentSelectedZone;
          summary.push({
            key: 'zone',
            label: 'Zone',
            value: zoneName
          });
        }

        if (currentSelectedStore && currentSelectedStore.merchant_name_id !== 'Overall') {
          summary.push({
            key: 'store',
            label: 'Store',
            value: currentSelectedStore.merchant_name_id
          });
        }
      }

      if (currentSelectedDate) {
        // Format date without timezone conversion to avoid T-1 issues
        const year = currentSelectedDate.getFullYear();
        const month = String(currentSelectedDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentSelectedDate.getDate()).padStart(2, '0');
        const dateStr = `${year}-${month}-${day}`;
        summary.push({
          key: 'date',
          label: 'Date',
          value: dateStr
        });
      }

      return summary;
    },

    activeFiltersCount() {
      return this.mobileFilterSummary.length;
    },

    hasActiveFilters() {
      return this.activeFiltersCount > 0;
    },

    computedMobileZoneList() {
      if (this.tempMobileFilters.selectedCity === PanIndiaConstants.PAN_INDIA.code) return []

      let cityName = this.getStorePageCityMapping?.[this.tempMobileFilters.selectedCity]?.name
      let zoneList = this.zoneList?.filter((zone) => zone.city === cityName)

      return zoneList || []
    },

    mobileSelectStyles() {
      return {
        control: (styles) => ({
          ...styles,
          borderColor: '#975A16',
          borderRadius: '0.75rem',
          padding: '4px',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
          zIndex: 10001
        }),
        option: (styles, { isFocused }) => ({
          ...styles,
          backgroundColor: isFocused ? '#FDF6B2' : null,
          padding: '8px',
          zIndex: 10002
        }),
        menu: (styles) => ({
          ...styles,
          zIndex: 10002
        })
      }
    }
  },

  methods: {
    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    // Mobile filter methods with deferred application
    onMobileFilterOpen() {
      // Initialize temporary state with current values
      this.tempMobileFilters = {
        selectedCity: this.selectedCity,
        selectedZone: this.selectedZone,
        selectedStore: this.selectedStore,
        selectedDate: this.selectedDate,
        selectedGroup: this.selectedGroup,
        groupView: this.groupView
      };
      this.hasPendingMobileChanges = false;

      // Always ensure groups are loaded for mobile view
      if (this.isGlobalUser) {
        this.getGroupsAndCitiesList();
      }

      // Ensure store list is populated for current city
      if (this.selectedCity && this.mappedList[this.selectedCity]) {
        this.selectedCityStores = this.mappedList[this.selectedCity];
      }
    },

    onMobileFilterClose() {
      // Reset temporary state if user closes without applying
      this.hasPendingMobileChanges = false;
    },

    onMobileFilterChange(filterKey, value) {
      // Update temporary state and mark as having pending changes
      this.tempMobileFilters[filterKey] = value;
      this.hasPendingMobileChanges = true;

      // Handle zone/store reset when city changes
      if (filterKey === 'selectedCity') {
        this.tempMobileFilters.selectedZone = null;
        this.tempMobileFilters.selectedStore = null;

        // Ensure store list is loaded for the new city
        // This is needed for the mobile store dropdown to show options
        if (value && value !== PanIndiaConstants.PAN_INDIA.code) {
          // Load stores for the selected city if not already loaded
          if (!this.mappedList?.[value]) {
            this.getStoreCityList(value);
          }
        }
      }

      // Handle store reset when zone changes
      if (filterKey === 'selectedZone') {
        this.tempMobileFilters.selectedStore = null;

        // Update zone temporarily to populate filtered store list
        this.selectedZone = value;
        if (!this.selectedZone) {
          this.selectedCityStores = this.mappedList?.[this.selectedCity];
        } else {
          this.selectedCityStores = this.mappedList?.[this.selectedCity]?.filter((store) => store.zone === this.selectedZone);

          // Add "Overall" to the store dropdown for the selected zone
          if (this.selectedCityStores && this.selectedZone) {
            const overallStore = {
              frontend_merchant_id: '',
              frontend_merchant_name: 'Overall',
              backend_merchant_id: '',
              name: this.selectedCity,
              merchant_name_id: 'Overall',
              zone: this.selectedZone
            };
            if (!this.selectedCityStores.some((store) => store.merchant_name_id === 'Overall')) {
              this.selectedCityStores.unshift(overallStore);
            }
          }
        }
      }
    },

    onMobileGroupViewChange(value) {
      // Update group view and reset relevant filters
      this.tempMobileFilters.groupView = value;
      this.hasPendingMobileChanges = true;

      if (value) {
        // Switching to group view - reset city/zone/store and load groups
        this.tempMobileFilters.selectedCity = null;
        this.tempMobileFilters.selectedZone = null;
        this.tempMobileFilters.selectedStore = null;

        // Always load groups when switching to group view
        this.getGroupsAndCitiesList();
      } else {
        // Switching to normal view - reset group
        this.tempMobileFilters.selectedGroup = null;
      }
    },

    onMobileFilterApply() {
      // Apply all temporary filters at once
      if (this.tempMobileFilters.groupView !== this.groupView) {
        this.groupView = this.tempMobileFilters.groupView;
        this.toggleGroupView(this.tempMobileFilters.groupView);
      }

      // Apply group filter
      if (this.tempMobileFilters.selectedGroup !== this.selectedGroup) {
        this.selectedGroup = this.tempMobileFilters.selectedGroup;
        this.onGroupChange(this.selectedGroup);
      }

      // Apply city filter
      if (this.tempMobileFilters.selectedCity !== this.selectedCity) {
        this.selectedCity = this.tempMobileFilters.selectedCity;
        this.onCityChange(this.selectedCity);
      }

      // Apply zone filter
      if (this.tempMobileFilters.selectedZone !== this.selectedZone) {
        this.selectedZone = this.tempMobileFilters.selectedZone;
        this.onZoneChange(this.selectedZone);
      }

      // Apply store filter
      if (this.tempMobileFilters.selectedStore !== this.selectedStore) {
        this.selectedStore = this.tempMobileFilters.selectedStore;
        this.onStoreChange(this.selectedStore);
      }

      // Apply date filter
      if (this.tempMobileFilters.selectedDate !== this.selectedDate) {
        this.selectedDate = this.tempMobileFilters.selectedDate;
        this.onDateChange(this.tempMobileFilters.selectedDate);
      }

      // Ensure localStorage is updated with all current selections
      this.pageLocalStorage = {
        ...this.pageLocalStorage,
        selectedDate: this.selectedDate,
        city: this.selectedCity,
        zone: this.selectedZone,
        store: this.selectedStore,
        groupView: this.groupView,
        selectedGroup: this.selectedGroup
      };
      this.updateSonarHomePage(this.pageLocalStorage);

      this.hasPendingMobileChanges = false;
    },

    onMobileFilterClearAll() {
      // Reset all temporary filters to default values
      this.tempMobileFilters = {
        selectedCity: PanIndiaConstants.PAN_INDIA.code,
        selectedZone: null,
        selectedStore: null,
        selectedDate: new Date(),
        selectedGroup: this.computedGroupsAndCitiesList[0],
        groupView: false
      };
      this.hasPendingMobileChanges = true;
    },

    onCityChange(event) {
      this.isLoading = true;
      this.isError = false;

      this.selectedZone = null

      this.selectedCityStores = this.mappedList[this.selectedCity];
      this.selectedStore = this.selectedCityStores && this.selectedCityStores.length > 0 ? this.selectedCityStores[0] : null;

      this.pageLocalStorage['city'] = this.selectedCity
      this.pageLocalStorage['zone'] = this.selectedZone
      this.pageLocalStorage['store'] = this.selectedStore;
      this.updateSonarHomePage(this.pageLocalStorage)

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    onZoneChange(event) {
      this.isLoading = true;
      this.isError = false;

      if (!this.selectedZone) {
        this.selectedCityStores = this.mappedList?.[this.selectedCity];
      } else {
        // Filter stores based on the selected zone
        // this.selectedStore = this.selectedCityStores[0];
        this.selectedCityStores = this.mappedList?.[this.selectedCity]?.filter((store) => store.zone === this.selectedZone);
      }


      // Add "Overall" to the store dropdown for the selected zone
      if (this.selectedCityStores && this.selectedZone && this.isStoreOverallAllowed) {
        const overallStore = {
          frontend_merchant_id: '',
          frontend_merchant_name: 'Overall',
          backend_merchant_id: '',
          name: this.selectedCity,
          merchant_name_id: 'Overall',
          zone: this.selectedZone
        };
        if (!this.selectedCityStores.some((store) => store.merchant_name_id === 'Overall')) {
          this.selectedCityStores.unshift(overallStore);
        }
      }

      this.selectedStore = this.selectedCityStores?.[0];

      this.pageLocalStorage['zone'] = this.selectedZone;
      this.pageLocalStorage['store'] = this.selectedStore;
      this.updateSonarHomePage(this.pageLocalStorage);

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    onStoreChange(event) {
      this.isLoading = true;
      this.isError = false;

      this.pageLocalStorage['store'] = this.selectedStore
      this.updateSonarHomePage(this.pageLocalStorage)

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    onMobileCityChange(event) {
      this.tempMobileFilters.selectedZone = null
      this.tempMobileFilters.selectedStore = isEmpty(this.computedMobileStoreList) ? null : this.computedMobileStoreList?.[0]
    },

    onMobileZoneChange(event) {
      this.tempMobileFilters.selectedStore = isEmpty(this.computedMobileStoreList) ? null : this.computedMobileStoreList?.[0]
    },


    toggleGroupView(isToggled) {
      this.groupView = isToggled;

      this.pageLocalStorage['groupView'] = this.groupView;
      this.updateSonarHomePage(this.pageLocalStorage)

      this.isLoading = true;
      this.isError = false;

      this.resetAllMetrics();

      if (isToggled) this.getGroupsAndCitiesList()

      this.fetchAllMetrics();
    },

    onGroupChange(event) {
      this.isLoading = true;
      this.isError = false;

      this.pageLocalStorage['group'] = this.selectedGroup
      this.updateSonarHomePage(this.pageLocalStorage)

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    onDateChange(event) {
      this.isLoading = true;
      this.isError = false;
      this.athHit = false;

      // Use the event parameter (selected date) instead of this.selectedDate
      // to avoid timezone offset issues
      this.selectedDate = event;

      // Store the date object directly to maintain consistency
      this.pageLocalStorage['selectedDate'] = event;
      this.updateSonarHomePage(this.pageLocalStorage);

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    filterMetricsWithNoData(metrics) {
      const parsedMetrics = JSON.parse(JSON.stringify(metrics))
      let filteredMetrics = parsedMetrics.filter(metric => metric.data.length > 0);
      return filteredMetrics;
    },

    refreshList() {
      fetchStoreToCityMapping();
    },

    async fetchCityStoreMapping() {
      let storeToCityMapping = this.getStoreToCityMapping;
      let userAccessMapping = getUserAccessMapping();
      this.isLoading = false;
      let storeList = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
      this.mappedList = storeList;

      if (this.isGlobalUser || this.isCityUser) {
        this.mappedList = getModifiedStoreList(storeList);
        this.mappedList[PanIndiaConstants.PAN_INDIA.code] = [];
        for (let cityName in this.mappedList) {
          if (this.mappedList.hasOwnProperty(cityName)) {
            let cityStores = this.mappedList[cityName];

            const actualStoreCountOfCity = this.getStoreToCityMapping?.find((item) =>
              item?.city === (this.getStoreCityList?.find((item) => item?.code === cityName)?.name)
            )?.data?.length

            if ((cityStores?.length >= actualStoreCountOfCity || cityName === PanIndiaConstants.PAN_INDIA.code || MergedCityStoreConstants[cityName])) {
              let panIndiaStore = {
                frontend_merchant_id: '',
                frontend_merchant_name: 'Overall',
                backend_merchant_id: '',
                name: cityName,
                merchant_name_id: 'Overall',
              };

              cityStores.unshift(panIndiaStore);
            }
          }
        }
      }

      if (this.selectedGroup) {
        this.selectedCity = null;
        this.selectedStore = null;
      } else {
        if (this.selectedZone) {
          const cityName = this.selectedCity

          const userStoreCountOfCity = this.mappedList?.[this.selectedCity]?.length
          const actualStoreCountOfCity = this.getStoreToCityMapping?.find((item) =>
            item?.city === (this.getStoreCityList?.find((item) => item?.code === cityName)?.name)
          )?.data?.length

          if ((userStoreCountOfCity >= actualStoreCountOfCity || cityName === PanIndiaConstants.PAN_INDIA.code || MergedCityStoreConstants[cityName])) {

            // Add "Overall" to the store dropdown for the selected zone
            const overallStore = {
              frontend_merchant_id: '',
              frontend_merchant_name: 'Overall',
              backend_merchant_id: '',
              name: this.selectedCity,
              merchant_name_id: 'Overall',
              zone: this.selectedZone
            };

            this.selectedCityStores = this.mappedList?.[this.selectedCity]?.filter((store) => store.zone === this.selectedZone);

            if (!this.selectedCityStores.some((store) => store.merchant_name_id === 'Overall')) {
              this.selectedCityStores.unshift(overallStore);
            }
          }
        } else { // (this.selectedStore == null)
          this.selectedCityStores = this.mappedList?.[this.selectedCity]
        }
      }

      this.selectedStore = this.pageLocalStorage?.store ? this.pageLocalStorage?.store : this.selectedCityStores?.[0];

      this.assetsLoaded = true

      this.isLoading = true;
      this.fetchAllMetrics();
    },

    async fetchAllMetrics() {
      if (this.groupView && this.isGroupLoading) return

      this.cancelPreviousRequests();

      try {
        const athMetricCancelTokenSource = axios.CancelToken.source();
        this.cancelTokens = [athMetricCancelTokenSource];

        // Setup common parameters
        let city_code = this.groupView ? this.computedGroup?.city : this.computedCityName.cityName
        let store_code = this.groupView ? null : this.computedStoreId

        let zone_code = this.groupView ? null : this.computedZoneName
        let group_id = this.groupView ? this.computedGroup?.group_id : null
        let enable_group_view = this.groupView

        let athMetric = api.fetchATHMetrics(city_code, store_code, group_id, enable_group_view, athMetricCancelTokenSource.token)

        let [athMetriResponse] = await Promise.all([athMetric]);

        if (athMetriResponse) this.athMetrics = this.filterMetricsWithNoData(athMetriResponse.data.ath_data);

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          this.isLoading = false;
          this.isError = true;
          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      };
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    start() {
      this.$confetti.start();
    },

    stop() {
      this.$confetti.stop();
    },

    love() {
      this.$confetti.update({
        particles: [{ type: 'heart' }, { type: 'circle' }, { type: 'rect' }],
        defaultColors: ['red', 'pink', '#ba0000'],
      });
    },

    highlightATHMetrics(metric) {
      const metricName = metric.name;
      const metricValue = metric.curr?.value

      if (ATHMetricRelation[metricName]) {
        const metricDate = metric.curr?.meta?.date;
        let metricToCompare = this.athMetrics.find((metric) => metric.name === ATHMetricRelation[metricName])?.data?.[0];

        if (!isEmpty(metricToCompare)) {
          const meetsCondition = metricDate === metricToCompare?.date ? metricValue >= metricToCompare?.metric : metricValue > metricToCompare?.metric;

          if (meetsCondition) {
            this.athHit = true;
            return true;
          }
        }
      }
      return false;
    },

    midnightTask() {
      this.pageLocalStorage["selectedDate"] = new Date();
      this.pageLocalStorage["currentDate"] = (new Date()).getDate();
      this.updateSonarHomePage(this.pageLocalStorage);
      this.updateConfettiShown({ present: false });
      window.location.reload()
    },

    resetAllMetrics() {
      // Reset only the hourly metrics and projections that are still fetched in this component
      this.athMetrics = []
      this.hourlySurgeMetrics = []
    },

    calculatedMetrics(metrics) {
      return metrics?.map(metric => {
        let data = metric.data;
        let curr = data[data.length - 1];
        let diffs = [];
        for (let j = 0; j < data.length - 1; j++) {
          let prev = data[j];
          diffs.push(new MetricChange(curr, prev, metric.name));
        }

        return {
          name: metric.name,
          reverseStyle: metricsForStyleReversal.includes(metric.name),
          curr: curr,
          diffs: diffs,
        }
      }).filter(metric => metric.curr && metric.curr.value !== "-");
    },

    hasExtraMetrics(metricName) {
      return Object.keys(this.computedHomeExtraMetricsMapping).includes(metricName)
    },

    showExtraMetrics(metricName) {
      if (this.hasExtraMetrics(metricName)) {
        this.extraMetricKey = metricName;
        this.visible = true;
      }
      return;
    },

    // Method to update metrics from a MetricGroup component
    updateMetricGroupMetrics(metrics) {
      // Add new metrics or replace existing ones
      const newMetrics = [...metrics];

      // Filter out any metrics with the same names from our collection
      const metricNames = newMetrics.map(m => m.name);
      this.allMetricGroupMetrics = this.allMetricGroupMetrics.filter(m => !metricNames.includes(m.name));

      // Add the new metrics to our collection
      this.allMetricGroupMetrics = [...this.allMetricGroupMetrics, ...newMetrics];
    },

    async getGroupsAndCitiesList() {
      this.isGroupLoading = true;

      await api.fetchCitiesAndGroups().then((response) => {
        this.groupsAndCitiesList = response.data;
      }).catch(e => {
        console.log("Error fetching groups: ", e);
        this.isGroupLoading = false;
      })
      this.isGroupLoading = false;
    },

    updateOpenMetricGroupAccordion(key) {
      this.metricGroupOpenAccordions.push(key)

      this.pageLocalStorage["metricGroupOpenAccordions"] = this.metricGroupOpenAccordions;
      this.updateSonarHomePage(this.pageLocalStorage);
    },

    updateClosedMetricGroupAccordion(key) {
      this.metricGroupOpenAccordions = this.metricGroupOpenAccordions.filter((item) => item !== key);

      this.pageLocalStorage["metricGroupOpenAccordions"] = this.metricGroupOpenAccordions;
      this.updateSonarHomePage(this.pageLocalStorage);
    },

    updateOpenHourlyAccordion(key) {
      this.hourlyOpenAccordions.push(key)

      this.pageLocalStorage["hourlyOpenAccordions"] = this.hourlyOpenAccordions;
      this.updateSonarHomePage(this.pageLocalStorage);
    },

    updateClosedHourlyAccordion(key) {
      this.hourlyOpenAccordions = this.hourlyOpenAccordions.filter((item) => item !== key);

      this.pageLocalStorage["hourlyOpenAccordions"] = this.hourlyOpenAccordions;
      this.updateSonarHomePage(this.pageLocalStorage);
    },

    // Floating button methods
    openMobileFilterFromFloatingButton() {
      // Call the mobile filter bottom sheet's openBottomSheet method directly
      if (this.$refs.mobileFilterBottomSheet) {
        this.$refs.mobileFilterBottomSheet.openBottomSheet();
      }
    },

    handleToggleGroupView(isToggled) {
      this.tempMobileFilters.groupView = isToggled;
    },

    ...mapActions(useUserStore, ['logout']),
    ...mapActions(useCityStore, ['updateStoreToCityMapping', 'updateConfettiShown', 'updateSonarHomePage']),
  },

  data() {
    return {
      showHeader: false,
      selectedCity: "",
      selectedZone: "",
      isLoading: true,
      isGroupLoading: false,
      mappedList: {},
      selectedCityStores: [],
      selectedStore: null,
      isError: false,
      intervalId: null,
      userAccessMapping: {},
      isGlobalUser: false,
      isCityUser: false,
      athHit: false,
      canAccessPage: false,
      extraMetricKey: "",
      visible: false,
      selectedDate: null,
      allMetricGroupMetrics: [], // Store metrics from all MetricGroup components
      athMetrics: [],
      cancelTokens: [],
      pageLocalStorage: [],
      accessibleGroups: [],
      selectedGroup: null,
      groupsAndCitiesList: [],
      assetsLoaded: false,
      metricGroupOpenAccordions: [],
      hourlyOpenAccordions: [],

      // Mobile filter temporary state
      tempMobileFilters: {
        selectedCity: null,
        selectedZone: null,
        selectedStore: null,
        selectedDate: null,
        selectedGroup: null,
        groupView: null
      },
      hasPendingMobileChanges: false
    }
  },

  watch: {
    $route() {
      this.checkRoute();
    },

    showConfetti(newval, oldval) {
      if (newval) {
        this.start();
        setTimeout(() => {
          this.stop();
          this.updateConfettiShown({ present: true });
        }, 3000)
      }
    },

    isGroupLoading(newval, oldval) {
      if (!newval) {
        this.selectedGroup = isEmpty(this.pageLocalStorage?.group) ? PanIndiaConstants.PAN_INDIA_GROUP : this.pageLocalStorage.group
        this.isLoading = true;
        this.isError = false;
        this.fetchAllMetrics();
      }
    },
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[1]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
    this.isCityUser = isCityUser(this.userAccessMapping);

    if (!this.getStoreToCityMapping.length) this.refreshList();
    if (isGlobalUser) this.getGroupsAndCitiesList();

    if (this.canAccessPage) {
      this.isLoading = true;
      this.isError = false;

      this.pageLocalStorage = this.getSonarHomePage ? this.getSonarHomePage : []

      let currentDate = (new Date()).getDate()
      if (this.pageLocalStorage && currentDate !== this.pageLocalStorage?.currentDate) {
        this.midnightTask()
      } else {
        // Handle date initialization properly to avoid timezone issues
        if (isEmpty(this.pageLocalStorage?.selectedDate)) {
          this.selectedDate = new Date();
        } else {
          // If it's already a Date object, use it directly
          if (this.pageLocalStorage.selectedDate instanceof Date) {
            this.selectedDate = this.pageLocalStorage.selectedDate;
          } else {
            // If it's a string, parse it carefully to avoid timezone issues
            const dateStr = this.pageLocalStorage.selectedDate;
            if (dateStr.includes('T')) {
              // ISO string format
              this.selectedDate = new Date(dateStr);
            } else {
              // Date-only format (YYYY-MM-DD) - treat as local date
              const [year, month, day] = dateStr.split('-').map(Number);
              this.selectedDate = new Date(year, month - 1, day);
            }
          }
        }
      }
      this.groupView = this.pageLocalStorage?.hasOwnProperty('groupView') ? this.pageLocalStorage['groupView'] : false;

      // this.selectedGroup =  stays null till isGroupLoading is false

      if (!this.selectedGroup) {
        if (this.isGlobalUser) {
          this.selectedCity = this.pageLocalStorage?.city ? this.pageLocalStorage?.city : PanIndiaConstants.PAN_INDIA.code;
        } else if (this.isCityUser) {
          this.selectedCity = this.pageLocalStorage?.city ? this.pageLocalStorage?.city : Object.keys(this.getCityMapping)[0];
        } else {
          this.selectedCity = this.pageLocalStorage?.city ? this.pageLocalStorage?.city : Object.keys(this.getStorePageCityMapping)[0];
        }
      }

      if (this.isGlobalUser || this.isCityUser) this.selectedZone = this.pageLocalStorage?.zone ? this.pageLocalStorage?.zone : null;

      this.metricGroupOpenAccordions = isEmpty(this.pageLocalStorage?.metricGroupOpenAccordions) ? [Object.values(ORDER_METRIC_BREAKDOWN).find((item) => item?.isDefaultOpen)?.title] : this.pageLocalStorage?.metricGroupOpenAccordions;
      this.hourlyOpenAccordions = isEmpty(this.pageLocalStorage?.hourlyOpenAccordions) ? FLATTENED_HOURLY_METRICS.filter((item) => item?.isDefaultOpen).map((item) => item?.metricKey) : this.pageLocalStorage?.hourlyOpenAccordions;
    }
  },

  mounted() {
    if (this.canAccessPage) {
      this.fetchCityStoreMapping();

      this.checkRoute();

      this.$nextTick(function () {
        this.intervalId = window.setInterval(() => {
          this.fetchAllMetrics();
        }, 120000);
      })
    }
  },

  beforeUnmount() {
    clearInterval(this.intervalId);
    this.cancelPreviousRequests();
  }
}
</script>
