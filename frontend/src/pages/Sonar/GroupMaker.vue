<template>
  <Toast position="top-center"><template #icon /></Toast>

  <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
      <div class="text-3xl font-bold text-gray-900">Group Maker</div>
    </div>
  </header>

  <div v-if="isGlobalGroupAllowed" class="max-w-7xl mx-auto px-4 py-3 flex justify-end">
    <Button label="Create New Group" severity="primary"
      class="mb-4 hover:scale-105 transition-transform duration-200 shadow-md" icon="pi pi-plus"
      @click="handleCreateGroup" />
  </div>

  <div v-if="isLoading" class="flex justify-center items-center min-h-[400px]">
    <Loading />
  </div>

  <div v-else-if="!isLoading && isError"
    class="flex flex-col items-center justify-center min-h-[400px] max-w-7xl mx-auto px-4 py-6">
    <WaterMark />
    <div class="text-center bg-red-50 rounded-lg p-8 shadow-lg border border-red-100">
      <h2 class="text-xl font-semibold text-red-800 mb-3">Error Occurred</h2>
      <p class="text-red-600 text-xs md:text-sm mt-4">
        Please retry in a while or reach out to data team at
        <a href="mailto:<EMAIL>"
          class="underline hover:text-red-800 transition-colors"><EMAIL></a>
        or #bl-data-support slack channel.
      </p>
    </div>
  </div>

  <main v-else class="bg-gray-50 min-h-screen">
    <WaterMark />
    <div class="max-w-7xl mx-auto px-4">
      <h2 class="text-xl font-semibold text-gray-700 mb-4">Available Groups</h2>
      <DataTable :value="computedAccessibleGroups" class="shadow-lg rounded-lg overflow-hidden bg-white"
        :class="{ 'p-datatable-sm': true }" showGridlines removableSort scrollable paginator :rows="10"
        :rowsPerPageOptions="[5, 10, 20, 50]" stripedRows>
        <Column field="name" header="Name" sortable />
        <Column field="type" header="Type" sortable />
        <Column field="is_global" header="Is Global" sortable>
          <template #body="slotProps">
            <span :class="slotProps.data.is_global ? 'text-green-600 font-medium' : 'text-gray-600'">
              {{ slotProps.data.is_global ? 'Yes' : 'No' }}
            </span>
          </template>
        </Column>
        <Column field="mappings" header="Mappings">
          <template #body="slotProps">
            <div class="text-sm">
              <div v-for="(mapping, index) in slotProps.data.mappings" :key="index" class="mb-2 last:mb-0">
                <span class="font-medium capitalize">{{ mapping.city }}</span>
                <template v-if="slotProps.data.type === 'STORE'">
                  <span class="text-gray-500 ml-2">
                    ({{ formatStores(mapping.stores) }}
                    <template v-if="mapping.stores?.length > 3">
                      <span v-tooltip.top="getRemainingStores(mapping.stores)">
                        +{{ mapping.stores?.length - 3 }}
                      </span>
                    </template>)
                  </span>
                </template>
                <template v-else>
                  <span class="text-gray-500 ml-2">(Overall)</span>
                </template>
              </div>
            </div>
          </template>
        </Column>
        <Column header="Actions" :exportable="false">
          <template #body="slotProps">
            <div class="flex gap-4" v-if="(this.getUserInfo.id) === slotProps?.data?.created_by?.toString()">
              <Button icon="pi pi-pencil" rounded outlined severity="info" class="hover:scale-105 transition-transform"
                @click="editGroup(slotProps?.data?.id)" />
              <Button icon="pi pi-trash" rounded outlined severity="danger" class="hover:scale-105 transition-transform"
                @click="deleteGroup(slotProps?.data?.id)" />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </main>


  <Dialog v-model:visible="showDialog" maximizable modal :header="isEditing ? 'Edit Group' : 'Create New Group'"
    :style="{ width: '50rem' }" :breakpoints="{ '960px': '75vw', '641px': '90vw' }" :dismissableMask="true"
    class="p-dialog-custom">

    <div v-if="isGroupLoading" class="flex justify-center items-center h-[70vh]">
      <Loading />
    </div>

    <div v-else class="flex flex-col h-[70vh]">
      <div class="grid grid-cols-4 gap-6 p-4 bg-gray-50 rounded-lg shadow-sm">
        <div class="col-span-3">
          <label class="block text-gray-700 text-sm font-semibold mb-2">Group Name</label>
          <InputText type="text" v-model="groupName" class="w-full p-2 rounded-md border-gray-300 shadow-sm"
            placeholder="Enter group name..." />
        </div>
        <div class="col-span-1">
          <label class="block text-gray-700 text-sm font-semibold mb-2">Group Type</label>
          <SelectButton v-model="groupType" :options="groupTypeOptions" optionLabel="label" optionValue="value"
            @update:modelValue="groupTypeToggled" aria-labelledby="basic" class="p-buttonset-sm" />
        </div>
        <!-- <div v-if="isGlobalGroupAllowed" class="col-span-1">
          <label class="block text-gray-700 text-sm font-semibold mb-2">Is Global</label>
          <SelectButton v-model="isGlobal" :options="globalOptions" optionLabel="label" optionValue="value"
            aria-labelledby="basic" class="p-buttonset-sm" />
        </div> -->
      </div>

      <div class="flex-1 overflow-y-auto p-6">
        <div class="mb-8">
          <label class="block text-gray-700 text-sm font-semibold mb-2">Select Cities</label>
          <v-select :reduce="city => city.code" v-model="selectedCities" :options="computedCityList" label="name"
            :clearable="false" placeholder="Select cities..." class="w-full bg-white rounded-md shadow-sm"
            :multiple="groupType === 'CITY'" :close-on-select="groupType !== 'CITY'" />
        </div>

        <div v-if="groupType === 'STORE'" class="space-y-6">
          <!-- for multiple cities -->
          <!-- <div v-for="city in computedCities" :key="city"
            class="p-4 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <label class="block text-gray-700 text-sm font-semibold mb-2">
              Select Stores for {{ getStorePageCityMapping[city]?.name }}
            </label>

            <v-select v-model="selectedStore[city]" :options="fetchCityStoreMapping(city)" label="merchant_name_id"
              @update:model-value="onStoreChange($event, city)" :clearable="false" placeholder="Select stores..."
              :reduce="store => store.frontend_merchant_id" class="w-full bg-white rounded-md shadow-sm" multiple
              :close-on-select="false" />
          </div> -->

          <!-- for single cities -->
          <div class="p-4 bg-gray-50 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <label class="block text-gray-700 text-sm font-semibold mb-2">
              Select Stores for {{ getStorePageCityMapping[computedCities]?.name }}
            </label>

            <v-select v-model="selectedStore[computedCities]" :options="fetchCityStoreMapping(computedCities)"
              label="merchant_name_id" @update:model-value="onStoreChange($event, computedCities)" :clearable="false"
              placeholder="Select stores..." :reduce="store => store.frontend_merchant_id"
              class="w-full bg-white rounded-md shadow-sm" multiple :close-on-select="false" />
          </div>
        </div>
      </div>

      <div class="flex justify-end gap-3 p-4 bg-gray-50 rounded-lg mt-4 shadow-sm">
        <Button label="Cancel" severity="secondary" outlined class="px-6 hover:bg-gray-100 transition-colors"
          @click="closeDialog" />
        <Button label="Create Group" severity="primary" icon="pi pi-check"
          class="px-6 hover:shadow-md transition-shadow" @click="createCityStoreGroup" />
      </div>
    </div>
  </Dialog>
</template>


<script>

import Tooltip from "primevue/tooltip";
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import { isGlobalUser, getUserAccessMapping, getUserAllowedStoresMapping, isCityUser, getModifiedStoreList, getCombinedCityNames, getPanIndiaStoreCityList, noPermissionRoute, getGlobalGrpAllowed } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import vSelect from 'vue-select';
import MetricsGrid from "../../components/MetricsGrid.vue";
import Button from 'primevue/button';
import isEmpty from "lodash.isempty";
import axios from "axios";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import Dialog from "primevue/dialog";
import { api } from "../../api/index.js";
import Toast from "primevue/toast";
import SelectButton from "primevue/selectbutton";
import InputText from "primevue/inputtext";
import Column from "primevue/column";
import DataTable from "primevue/datatable";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

  components: {
    Loading,
    AnimatedNumber,
    MetricsGrid,
    WaterMark,
    'v-select': vSelect,
    Button,
    Toast,
    Dialog,
    SelectButton,
    InputText,
    DataTable,
    Column
  },

  directives: {
    tooltip: Tooltip
  },

  computed: {
    computedCityList() {
      let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));

      cityList = cityList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });
      return cityList;
    },

    computedCities() {
      return this.selectedCities
    },

    groupTypeOptions() {
      return [
        { label: 'City', value: 'CITY' },
        { label: 'Store', value: 'STORE' }
      ]
    },

    globalOptions() {
      return [
        { label: 'Yes', value: true },
        { label: 'No', value: false }
      ]
    },

    computedAccessibleGroups() {
      return this.accessibleGroups;
    },

    ...mapState(useCityStore, ['getStorePageCityMapping', 'getStoreCityList', 'getStoreToCityMapping']),
    ...mapState(useUserStore, ['getAllowedNavs', 'getUserInfo']),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  methods: {
    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    onStoreChange(event, city) {
      this.selectedStore[city] = event
    },

    refreshList() {
      fetchStoreToCityMapping();
    },

    async fetchAllAccessibleStores() {
      let storeToCityMapping = this.getStoreToCityMapping
      let userAccessMapping = getUserAccessMapping();
      this.isLoading = false;
      let storeList = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
      this.mappedList = storeList;
    },

    groupTypeToggled(isToggled) {
      this.groupType = isToggled
      if (isToggled) this.selectedCities = []
    },

    fetchCityStoreMapping(city) {
      const storeList = [...(this.mappedList?.[city] || [])];

      let overallStore = {
        frontend_merchant_id: '',
        frontend_merchant_name: 'Overall',
        backend_merchant_id: '',
        name: city,
        merchant_name_id: 'Overall'
      };
      storeList.unshift(overallStore);

      return storeList;
    },

    async fetchData() {
      this.isLoading = true;
      this.isError = false;

      this.cancelPreviousRequests();

      try {
        const CancelTokenSource = axios.CancelToken.source();
        this.cancelTokens = [CancelTokenSource];

        let accessibleGroups = api.fetchAllAccessibleGroups(CancelTokenSource.token)

        let [accessibleGroupsResponse] = await Promise.all([accessibleGroups]);

        if (accessibleGroupsResponse) {
          this.accessibleGroups = accessibleGroupsResponse.data;
        }

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          this.isLoading = false;
          this.isError = true;
          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      }
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    createPayloadToCreateGroup() {
      if (!this.computedCities) {
        this.$toast.add({ severity: 'error', summary: "Required!", detail: "City is required", life: 3000 });
        return;
      }

      if (!this.groupType) {
        this.$toast.add({ severity: 'error', summary: "Required!", detail: "Group type is required", life: 3000 });
        return;
      }

      if (this.groupType === 'CITY') return {
        name: this.groupName,
        type: this.groupType,
        is_global: this.isGlobal,
        mappings: this.computedCities?.map((city) => {
          const cityName = this.getStorePageCityMapping?.[city]?.name
          return {
            city: cityName,
            stores: ["-1"]
          }
        })
      }


      // for multiple cities
      // const mappings = this.computedCities?.map((city) => {
      //   if (isEmpty(this.selectedStore?.[city])) {
      //     this.$toast.add({ severity: 'error', summary: "Required!", detail: "Select store for " + city, life: 3000 });
      //     return;
      //   }

      //   const cityName = this.getStorePageCityMapping?.[city]?.name
      //   if (this.selectedStore?.[city] === "Overall" || this.selectedStore?.[city]?.some((store) => store.merchant_name_id === 'Overall')) {
      //     return {
      //       city: cityName,
      //       stores: ["-1"]
      //     }
      //   }

      //   return {
      //     city: cityName,
      //     stores: this.selectedStore?.[city]
      //   }
      // })

      // for single city
      const mappings = [(() => {
        if (isEmpty(this.selectedStore?.[this.computedCities])) {
          this.$toast.add({ severity: 'error', summary: "Required!", detail: "Select store for " + this.computedCities, life: 3000 });
          return;
        }

        const cityName = this.getStorePageCityMapping?.[this.computedCities]?.name
        if (this.selectedStore?.[this.computedCities] === "Overall" || this.selectedStore?.[this.computedCities]?.some((store) => store.merchant_name_id === 'Overall')) {
          return {
            city: cityName,
            stores: ["-1"]
          }
        }

        return {
          city: cityName,
          stores: this.selectedStore?.[this.computedCities]
        }
      })()]

      if (!mappings) return

      return {
        name: this.groupName,
        type: this.groupType,
        is_global: this.isGlobal,
        mappings
      }
    },

    async createCityStoreGroup() {
      try {
        if (!this.groupName) {
          this.$toast.add({ severity: 'error', summary: "Required!", detail: "Group name cannot be empty", life: 3000 });
          return;
        }

        this.isLoading = true;
        const payload = this.createPayloadToCreateGroup();

        if (!payload) {
          throw new Error('Invalid group type or mappings');
        }

        const CancelTokenSource = axios.CancelToken.source();
        this.cancelTokens.push(CancelTokenSource);

        const response = this.isEditing
          ? await api.editGroup(this.groupId, payload, CancelTokenSource.token)
          : await api.createGroup(payload, CancelTokenSource.token);

        if (response?.data) {
          this.$toast.add({ severity: 'success', summary: 'Success', detail: `Group ${this.isEditing ? 'edited' : 'created'} successfully`, life: 3000 });

          // close modal
          this.showDialog = false;
          this.isEditing = false;

          // Reset form
          this.groupName = ""
          this.groupType = "CITY";
          this.selectedStore = {};
          this.selectedCities = "";

          // Refresh data
          await this.fetchData();
        }
      } catch (error) {
        const errorMessage = error.response?.data?.detail || `Failed to ${this.isEditing ? 'edit' : 'create'} group`;
        this.$toast.add({ severity: 'error', summary: 'Error', detail: errorMessage, life: 3000 });

        if (error.response?.status === 403) {
          this.logout();
          this.$router.replace('/login');
        }
      } finally {
        this.isLoading = false;
        this.isError = false;
        this.groupId = ""
      }
    },

    async deleteCityStoreGroup(groupId) {
      try {
        this.isLoading = true;
        const response = await api.deleteGroup(groupId);
        if (response?.data) {
          this.$toast.add({ severity: 'success', summary: 'Success', detail: 'Group created successfully', life: 3000 });
          await this.fetchData();
        }
      } catch (error) {
        const errorMessage = error.response?.data?.detail || 'Failed to delete group';
        this.$toast.add({ severity: 'error', summary: 'Error', detail: errorMessage, life: 3000 });

        if (error.response?.status === 403) {
          this.logout();
          this.$router.replace('/login');
        }
      } finally {
        this.isLoading = false;
        this.isError = false;
      }
    },

    openDialog() {
      this.showDialog = true;
    },

    closeDialog() {
      this.showDialog = false;
    },

    handleCreateGroup() {
      this.isEditing = false
      this.groupName = ""
      this.groupType = 'CITY'
      this.selectedCities = []
      this.selectedCityStores = []
      this.openDialog()
    },

    async editGroup(groupId) {
      this.groupId = groupId
      this.isEditing = true
      await this.fetchGroupDataById(groupId)
      this.openDialog()
    },

    async fetchGroupDataById(groupId) {
      try {
        this.isGroupLoading = true;
        const response = await api.getGroupById(groupId);
        if (response?.data) {
          this.groupName = response.data.name;
          this.groupType = response.data.type;
          this.selectedCities = response.data?.mappings?.map((mapping) => mapping.city?.toLowerCase());
          this.selectedStore = response.data?.mappings?.reduce((acc, mapping) => {
            if ((mapping.stores?.[0] === '-1')) {
              acc[mapping.city] = 'Overall'
              return acc
            }

            acc[mapping.city?.toLowerCase()] = mapping.stores
            return acc
          }, {});

          this.openDialog()
        }
      } catch (error) {
        const errorMessage = error.response?.data?.detail || 'Failed to load group';
        this.$toast.add({ severity: 'error', summary: 'Error', detail: errorMessage, life: 3000 });

        if (error.response?.status === 403) {
          this.logout();
          this.$router.replace('/login');
        }
      } finally {
        this.isGroupLoading = false;
      }
    },

    deleteGroup(groupId) {
      this.deleteCityStoreGroup(groupId)
    },


    formatStores(stores) {
      if (!stores || stores.length === 0) return 'Overall';
      if (stores[0] === '-1') return 'Overall';

      const displayStores = stores?.slice(0, 3);
      return displayStores?.map(store => store || 'Overall')?.join(', ');
    },

    getRemainingStores(stores) {
      if (stores.length <= 3) return '';
      return stores?.slice(3)?.map(store => store || 'Overall')?.join(', ');
    },

    ...mapActions(useUserStore, ['logout']),
    ...mapActions(useCityStore, ['updateStoreToCityMapping', 'updateConfettiShown', 'updateSonarHomePage']),
  },

  data() {
    return {
      showHeader: false,
      selectedCities: "",
      selectedCityStores: [],
      groupType: "CITY",
      groupName: "",
      selectedStore: {},
      mappedList: [],
      userAccessMapping: {},
      isGlobalUser: false,
      isCityUser: false,
      canAccessPage: false,
      isError: false,
      isLoading: true,
      cancelTokens: [],
      accessibleGroups: [],
      showDialog: false,
      isEditing: false,
      isGroupLoading: false,
      groupId: "",
      isGlobalGroupAllowed: false,
      isGlobal: true, // as long as only isGlobalGroupAllowed are making groups
    }
  },

  watch: {
    $route() {
      this.checkRoute();
    },
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[24]);
    if (!this.canAccessPage) noPermissionRoute();

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
    this.isCityUser = isCityUser(this.userAccessMapping);
    this.isGlobalGroupAllowed = getGlobalGrpAllowed();

    if (!this.getStoreToCityMapping.length) {
      this.refreshList();
    }
  },

  mounted() {
    if (this.canAccessPage) {
      this.isLoading = true;
      this.isError = false;

      this.fetchAllAccessibleStores();
      this.fetchData();

      this.checkRoute();
    }
  },

  beforeUnmount() {
    this.cancelPreviousRequests();
  }
}
</script>


<style scoped>
.dialog-footer {
  border-top: 1px solid #efefef;
}
</style>
