<template>
  <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
      <div class="text-3xl font-bold text-gray-900">
        City: {{ appliedCity && appliedCity.displayName ? appliedCity.displayName : finalValue.city }}
      </div>
      <div class="text-3xl font-bold text-gray-900" v-if="this.selectedStore">
        Store: {{ appliedStore.merchant_name_id }}
      </div>
    </div>
  </header>

  <div>
    <HeaderFilters :showHeader="showHeader" :selectedCity="selectedCity" :computedCityList="computedCityList"
      @update:selectedCity="handleCityChange" :selectedStore="selectedStore" :computedStoreList="computedStoreList"
      @update:selectedStore="handleStoreChange" :finalValue="finalValue" @finalClick="applyAllFilter" />
  </div>

  <div v-if="isLoading">
    <Loading />
  </div>

  <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
    <WaterMark></WaterMark>
    <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{ this.selectedCity }}
      city is invalid/inactive or some internal error
      occured.<br />Please select another city or reach out to data team at
      <EMAIL> or #bl-data-support slack channel.</span>
  </div>

  <main v-else>
    <WaterMark></WaterMark>

    <div class="max-w-7xl mx-auto p-1 lg:px-8 lg:py-4">
      <Paginator v-model:first="currIdx" :rows="20" :totalRecords="totalRecords" class="paginator"></Paginator>

      <div class="flex flex-wrap gap-6 my-4">
        <div v-for="imageData in badStocks"
          class="p-4 rounded-lg flex  bg-blue-50 hover:shadow-xl hover:shadow-gray-400"
          @click="showComplaint(imageData)">
          <img :src="imageData?.url" :alt="imageData?.item_name" class="h-48 w-48 rounded-lg" />
        </div>
      </div>

      <Paginator v-model:first="currIdx" :rows="20" :totalRecords="totalRecords" class="paginator"></Paginator>
    </div>

    <Dialog v-model:visible="visible" modal :header="badStockItemData?.item_name" :dismissableMask="true"
      :style="{ maxWidth: '75rem', margin: '1rem' }">
      <div class="m-2" style="max-width: 40rem;">
        <div class="space-y-2">
          <template v-for="[key, label] in [
            ['audit_date', 'Audit Date'],
            ['merchant_id', 'Merchant Id'],
            ['merchant_name', 'Merchant Name']
          ]">
            <div v-if="badStockItemData?.[key]" class="mb-4 text-left">
              <div class="text-xs">{{ label }}: </div>
              <div class="font-semibold">{{ badStockItemData?.[key] }}</div>
            </div>
          </template>
        </div>

        <div class="w-full flex items-center mt-4">
          <img :src="badStockItemData.url" alt="img" class="rounded-lg"
            style="max-height: 40rem; max-width: 40rem; flex: 2;" />
        </div>
      </div>
    </Dialog>
  </main>
</template>


<script>
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import { api } from "../../api/index.js";
import { getUserAccessMapping, getUserAllowedStoresMapping, isGlobalUser, isCityUser, getPanIndiaStoreCityList, noPermissionRoute } from "../../utils/utils.js";
import { mapActions, mapState } from "pinia";
import { useUserStore } from "../../stores/users.js";
import { useCityStore } from "../../stores/cities.js";
import { PanIndiaConstants } from "../../constants/index.js";
import HeaderFilters from '../../components/BadStocks/HeaderFilters.vue';
import isEmpty from 'lodash.isempty';
import axios from "axios";
import { fetchStoreToCityMapping } from '../../utils/storeMappings.js';
import Dialog from 'primevue/dialog';
import Paginator from 'primevue/paginator';
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {
  components: {
    Loading,
    WaterMark,
    HeaderFilters,
    Dialog,
    Paginator
  },

  computed: {
    computedCityList() {
      let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
      cityList.sort(function (a, b) {
        if (a.name < b.name) {
          return -1;
        }
        if (a.name > b.name) {
          return 1;
        }
        return 0;
      });

      if (this.isGlobalUser) {
        let staticList = [PanIndiaConstants.PAN_INDIA];
        cityList = staticList.concat(cityList);
      }
      return cityList;
    },

    computedStoreList() {
      return this.selectedCityStores;
    },

    computedCityName() {
      let modifiedCityMapping = this.isGlobalUser
        ? getPanIndiaStoreCityList(this.getStorePageCityMapping)
        : this.getStorePageCityMapping;

      if (isEmpty(modifiedCityMapping[this.selectedCity])) return;
      let cityName = modifiedCityMapping[this.selectedCity]["name"];

      return {
        displayName: modifiedCityMapping[this.selectedCity]["name"],
        cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : "",
      };
    },

    computedIsPanIndiaView() {
      return isEmpty(this.appliedCity) || isEmpty(this.appliedCity.cityName);
    },

    computedStoreId() {
      if (isEmpty(this.getStorePageCityMapping[this.selectedCity])) return;
      return this.selectedStore["frontend_merchant_id"];
    },

    totalBadStocks() {
      const images = this.badStocksData?.flatMap((data) => {
        const { media_urls, item_name, audit_date, merchant_id, merchant_name } = data || {}
        return media_urls?.map((url) => ({ url, item_name, audit_date, merchant_id, merchant_name }))
      });
      return images;
    },

    badStocks() {
      return this.totalBadStocks?.slice(this.currIdx, this.currIdx + 20);
    },

    totalRecords() {
      return this.totalBadStocks?.length;
    },

    ...mapState(useCityStore, ["getStorePageCityMapping", "getStoreCityList", "getStoreToCityMapping", "getBadStockPTypeList", "getBadStockL0CategoryList", "getBadStocksPage"]),
    ...mapState(useUserStore, ["user", "isLoggedIn", "getAllowedNavs"]),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  methods: {
    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    handleCityChange(city) {
      this.selectedCity = city;
      this.onCityChange();
    },

    handleStoreChange(store) {
      this.selectedStore = store;
    },

    onCityChange() {
      this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
      this.selectedStore = this.selectedCityStores[0];
    },

    applyAllFilter(values) {
      this.isLoading = true;
      this.isError = false;

      this.appliedStore = this.selectedStore;
      this.appliedCity = this.computedCityName;

      this.finalValue["city"] = this.computedCityName.cityName;
      this.finalValue["store"] = this.computedStoreId;
      this.finalValue["l0_category"] = values.productL0;
      this.finalValue["l1_category"] = values.productL1;
      this.finalValue["l2_category"] = values.productL2;
      this.finalValue["brand"] = values.productBrand;
      this.finalValue["ptype"] = values.productType;
      this.finalValue["itemName"] = values.itemName;
      this.finalValue["start_date"] = values.startDate;
      this.finalValue["end_date"] = values.endDate;
      this.finalValue["itemId"] = values.itemId;
      this.finalValue["er_id"] = values.erId;

      this.getBadStocksPage["selectedStore"] = this.selectedStore;
      this.getBadStocksPage["filterValues"] = this.finalValue;
      this.updateBadStocksPage(this.getBadStocksPage);

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    async refreshFilters() {
      let res = api.fetchBadStockPTypeAndL0List().then((response) => {
        let pTypeList = response.data?.data?.find((listItem) => listItem?.field === 'ptype')?.filtered_values
        let l0List = response.data?.data?.find((listItem) => listItem?.field === 'l0_category')?.filtered_values
        this.updateBadStockPTypeList(pTypeList);
        this.updateBadStockL0CategoryList(l0List);
      })
      await Promise.all([res]);

      fetchStoreToCityMapping();
    },

    async fetchCityStoreMapping() {
      let storeToCityMapping = this.getStoreToCityMapping;
      let userAccessMapping = getUserAccessMapping();
      this.isLoading = false;
      this.userAllowedStoresMapping = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
      let storeList = this.userAllowedStoresMapping;
      storeList[PanIndiaConstants.PAN_INDIA.code] = [];
      for (let cityName in storeList) {
        if (storeList.hasOwnProperty(cityName)) {
          let cityStores = storeList[cityName];

          if ((this.getStoreToCityMapping && this.getStoreToCityMapping?.find((item) =>
            item?.city === this.getStoreCityList?.find((item) => item?.code === cityName)?.name)
          )?.data?.length <= cityStores?.length || cityName === PanIndiaConstants.PAN_INDIA.code
          ) {
            let panIndiaStore = {
              frontend_merchant_id: "",
              frontend_merchant_name: "Overall",
              backend_merchant_id: "",
              name: cityName,
              merchant_name_id: "Overall",
            };
            cityStores.unshift(panIndiaStore);
          }
        }
      }
      this.selectedCityStores = storeList[this.selectedCity];

      if (isEmpty(this.selectedStore)) {
        this.selectedStore = this.selectedCityStores[0];
      }

      this.appliedStore = this.selectedStore;

      this.isLoading = true;
      this.fetchAllMetrics();
    },

    async fetchAllMetrics() {
      this.cancelPreviousRequests();
      try {
        const badStocksImagesCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens = [badStocksImagesCancelTokenSource];

        let badStocksData = api.fetchBadStocksImages({
          ...this.finalValue,
          cancelToken: badStocksImagesCancelTokenSource.token
        })

        let [badStocksDataResponse] = await Promise.all([
          badStocksData
        ]);

        if (badStocksDataResponse) {
          this.badStocksData = badStocksDataResponse.data.bad_stock_images
        }

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          this.isLoading = false;
          this.isError = true;
          let status = error?.response?.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      };
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    resetAllMetrics() {
      this.allMetrics = []
      this.allComplaints = []
      this.cityMetrics = []
    },

    showComplaint(data) {
      this.visible = true;
      this.badStockItemData = data;
    },

    ...mapActions(useUserStore, ["logout"]),
    ...mapActions(useCityStore, ["updateStoreToCityMapping", "updateBadStockPTypeList", "updateBadStockL0CategoryList", "updateBadStocksPage"]),
  },

  data() {
    return {
      showHeader: false,
      isGlobalUser: false,
      isCityUser: false,
      userAllowedStoresMapping: {},
      isLoading: false,
      isError: false,
      selectedCity: null,
      selectedStore: null,
      selectedCityStores: [],
      finalValue: {},
      cancelTokens: [],
      appliedCity: null,
      appliedStore: null,
      badStocksData: [],
      badStockItemData: {},
      visible: false,
      currIdx: 0,
      canAccessPage: false
    };
  },

  watch: {
    $route() {
      this.checkRoute();
    },
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[19]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
    this.isCityUser = isCityUser(this.userAccessMapping);

    if (!this.getStoreToCityMapping.length || isEmpty(this.getBadStockPTypeList) || isEmpty(this.getBadStockL0CategoryList)) {
      this.refreshFilters();
    }
  },

  mounted() {
    if (this.canAccessPage) {
      this.isLoading = true;
      this.isError = false;

      if (this.isGlobalUser) {
        this.selectedCity = isEmpty(this.getBadStocksPage?.selectedStore) ? PanIndiaConstants.PAN_INDIA.code : this.getBadStocksPage.selectedStore?.name;
      } else {
        this.selectedCity = isEmpty(this.getBadStocksPage?.selectedStore) ? Object.keys(this.getStorePageCityMapping)[0] : this.getBadStocksPage.selectedStore?.name
      }

      this.selectedStore = isEmpty(this.getBadStocksPage?.selectedStore) ? null : this.getBadStocksPage.selectedStore;

      this.finalValue = isEmpty(this.getBadStocksPage?.filterValues) ? {}
        : { ...this.getBadStocksPage.filterValues, l1_category: null, l2_category: null, brand: null, itemName: null, itemId: null };

      this.appliedCity = this.computedCityName;

      this.fetchCityStoreMapping();
      this.checkRoute();
    }
  },

  beforeUnmount() {
    this.cancelPreviousRequests();
  },
};
</script>

<style scoped>
:deep(.paginator .p-paginator) {
  background: inherit;
}
</style>
