<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">Emergency Services</div>
        </div>
    </header>

    <div
        class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10 [&>div]:mt-2 [&>div]:md:mt-0">
        <div>
            <div class="text-left pb-2 font-semibold italic">Date :</div>
            <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single" :maxDate="new Date()"
                :minDate="computedMinDate" showButtonBar inputClass="w-full" panelClass="p-1" class="w-full"
                :inputStyle="{ height: '36px', marginBottom: '4px' }" @update:model-value="onDateChange($event)"
                dateFormat="yy-mm-dd" />
        </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Some internal error
            occured.<br />Please retry in a while or reach out to data
            <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>

    <main v-else>
        <WaterMark></WaterMark>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <div v-if="computedBaseMetrics.length > 0"
                class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
                <div v-for="metric in computedBaseMetrics" :key="metric.name"
                    class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow">
                    <div class="flex justify-between px-1 md:px-2">
                        <span
                            class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                            {{ metric.name }}
                            <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                        </span>
                        <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                            :type="metric.curr.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs font-light gray-900"></span>
                        <div class="flex justify-between">
                            <div v-for="diff in metric.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                    </template>
                                    <template v-else>-</template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric"> »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else>
                <div class="text-center mt-10">No data available for the selected date.</div>
            </div>
        </div>
    </main>
</template>


<script>
import { getUserAccessMapping, noPermissionRoute, formatDateToString } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities';
import isEmpty from "lodash.isempty";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { api } from "../../api/index.js";
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import { MetricChange } from "../../interfaces/index.js";
import { ABSOLUTE_METRIC_DELTA, EMERGENCY_SERVICES_METRIC_LIST, metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import axios from "axios";
import InfoDialog from "../../components/InfoDialog.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import Calendar from "primevue/calendar";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {
    components: {
        Loading,
        WaterMark,
        InfoDialog,
        AnimatedNumber,
        Calendar
    },

    data() {
        return {
            showHeader: false,
            canAccessPage: false,
            isLoading: false,
            isError: false,
            selectedDate: null,
            pageLocalStorage: {},
            cancelTokens: [],
            baseMetrics: [],
        }
    },

    computed: {
        yesterdayOptions() {
            return [
                { label: 'Today', value: false },
                { label: 'Yesterday', value: true }
            ]
        },

        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA
        },

        computedBaseMetrics() {
            let sortedBaseMetrics = sortedAllMetrics(this.baseMetrics, undefined, true, 5, this.computedDate)
            return this.calculatedMetrics(sortedBaseMetrics)
        },

        computedDate() {
            return formatDateToString(this.selectedDate);
        },

        computedMinDate() {
            var day = new Date();
            day.setDate(day.getDate() - 25);
            return day;
        },

        ...mapState(useUserStore, ['getAllowedNavs']),
        ...mapState(useCityStore, ['getEmergencyServicePage']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        onDateChange(event) {
            this.isLoading = true;
            this.isError = false;

            this.pageLocalStorage["selectedDate"] = this.selectedDate;
            this.updateEmergencyServicePage(this.pageLocalStorage);

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        filterMetricsWithNoData(metrics) {
            const parsedMetrics = JSON.parse(JSON.stringify(metrics))
            let filteredMetrics = parsedMetrics.filter(metric => metric.data.length > 0);
            return filteredMetrics;
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const baseMetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [baseMetricsCancelTokenSource];

                let baseMetrics = api.fetchAllEmergencyBaseMetrics({ metrics: EMERGENCY_SERVICES_METRIC_LIST, date_str: this.computedDate, cancelToken: baseMetricsCancelTokenSource.token })

                let [
                    baseMetricsResponse,
                ] = await Promise.all([
                    baseMetrics,
                ]);

                if (baseMetricsResponse) this.baseMetrics = this.filterMetricsWithNoData(baseMetricsResponse.data.metrics);

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        calculatedMetrics(metrics) {
            return metrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },

        resetAllMetrics() {
            this.baseMetrics = []
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateEmergencyServicePage']),
    },

    watch: {
        $route() {
            this.checkRoute();
        },
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[20]);
        if (!this.canAccessPage) noPermissionRoute()
    },

    mounted() {
        if (this.canAccessPage) {
            this.pageLocalStorage = this.getEmergencyServicePage

            let currentDate = (new Date()).getDate()
            if (this.pageLocalStorage && this.pageLocalStorage.currentDate !== currentDate) {
                this.pageLocalStorage["selectedDate"] = null;
                this.pageLocalStorage["currentDate"] = (new Date()).getDate();
                this.updateEmergencyServicePage(this.pageLocalStorage);
                window.location.reload()
            } else {
                this.selectedDate = isEmpty(this.pageLocalStorage?.selectedDate) ? new Date() : new Date(this.pageLocalStorage?.selectedDate)
            }

            this.fetchAllMetrics();
            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    }
}
</script>
