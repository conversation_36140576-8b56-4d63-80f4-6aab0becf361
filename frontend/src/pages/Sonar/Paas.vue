<template>
    <main class="bg-gray-50 min-h-screen">
        <WaterMark />

        <header
            class="bg-gradient-to-br from-white via-gray-50 to-gray-100 shadow-xl sticky top-0 backdrop-blur-sm z-20"
            v-if="showHeader">
            <div class="max-w-7xl mx-auto py-3">
                <div class="flex flex-col items-start space-y-4 h-full">
                    <div class="flex items-center space-x-3 h-full">
                        <div
                            class="w-1.5 min-h-12 bg-gradient-to-b from-yellow-400 via-orange-400 to-orange-500 rounded-full shadow-md">
                        </div>
                        <div class="flex flex-col">
                            <div
                                class="text-2xl font-black tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
                                {{ computedCityName && computedCityName.displayName ? computedCityName.displayName :
                                    finalValue.city }}
                            </div>
                            <div v-if="finalValue?.store?.merchant_name_id !== 'Overall'"
                                class="text-gray-600 font-semibold mt-1 tracking-wide">
                                {{ finalValue.store.merchant_name_id }}
                            </div>
                            <div v-if="computedDate" class="text-gray-600 font-semibold mt-1 tracking-wide">
                                {{ computedDate }}
                            </div>
                            <div v-if="computedPrintTypeName !== 'All'"
                                class="text-gray-600 font-semibold mt-1 tracking-wide">
                                {{ computedPrintTypeName }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mobile Filter Bottom Sheet -->
        <MobileFilterBottomSheet ref="mobileFilterBottomSheet" v-if="isMobile && assetsLoaded"
            :filterSummary="mobileFilterSummary" :activeFiltersCount="activeFiltersCount"
            :hasActiveFilters="hasActiveFilters" :deferredMode="hasPendingMobileChanges" @open="onMobileFilterOpen"
            @close="onMobileFilterClose" @apply="applyFilters" @clear-all="onMobileFilterClearAll">

            <template #filters>
                <!-- City Filter -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-yellow-600">
                        <i class="pi pi-map-marker mr-2"></i>City
                    </label>
                    <v-select :reduce="city => city.code" v-model="selectedCity" :options="computedCityList"
                        label="name" @update:model-value="onCityChange($event)" :clearable="false"
                        placeholder="Select city" class="w-full filter-select bg-white rounded-xl shadow-md"
                        :styles="mobileSelectStyles" />
                </div>

                <!-- Store Filter -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-yellow-600">
                        <i class="pi pi-shopping-cart mr-2"></i>Store
                    </label>
                    <v-select v-model="selectedStore" :options="computedStoreList" label="merchant_name_id"
                        :disabled="selectedCity === computedPanIndiaConstants.PAN_INDIA.code" :clearable="false"
                        placeholder="Select store" class="w-full filter-select bg-white rounded-xl shadow-md"
                        :class="{ 'opacity-50': selectedCity === computedPanIndiaConstants.PAN_INDIA.code }"
                        :styles="mobileSelectStyles" />
                </div>

                <!-- Print Type Filter -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-yellow-600">
                        <i class="pi pi-print mr-2"></i>Print Type
                    </label>
                    <v-select v-model="selectedPrintType" :reduce="print => print.code" :options="computedPrintTypeList"
                        label="name" :clearable="false" placeholder="Select print type"
                        class="w-full filter-select bg-white rounded-xl shadow-md" :styles="mobileSelectStyles" />
                </div>

                <!-- Date Filter -->
                <div class="space-y-3">
                    <label class="block text-sm font-semibold text-yellow-600">
                        <i class="pi pi-calendar mr-2"></i>Date
                    </label>
                    <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single"
                        :maxDate="new Date()" :minDate="computedMinDate" showButtonBar inputClass="w-full"
                        panelClass="p-1" class="w-full" :inputStyle="{ height: '36px', marginBottom: '4px' }"
                        dateFormat="yy-mm-dd" />
                </div>
            </template>
        </MobileFilterBottomSheet>

        <!-- Desktop Filters -->
        <div v-if="!isMobile && assetsLoaded" class="max-w-7xl mx-auto px-4 py-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- City Filter -->
                <div>
                    <label class="block text-sm font-semibold text-yellow-600 mb-2">
                        <i class="pi pi-map-marker mr-2"></i>City
                    </label>
                    <v-select :reduce="city => city.code" v-model="selectedCity" :options="computedCityList"
                        label="name" @update:model-value="onCityChange($event)" :clearable="false"
                        placeholder="Select city" class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                            control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                            option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
                        }" />
                </div>

                <!-- Store Filter -->
                <div>
                    <label class="block text-sm font-semibold text-yellow-600 mb-2">
                        <i class="pi pi-shopping-cart mr-2"></i>Store
                    </label>
                    <v-select v-model="selectedStore" :options="computedStoreList" label="merchant_name_id"
                        :disabled="this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code" :clearable="false"
                        placeholder="Select store" class="w-full filter-select bg-white rounded-xl shadow-md"
                        :class="{ 'opacity-50': this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code }"
                        :styles="{
                            control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                            option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
                        }" />
                </div>

                <!-- Print Type Filter -->
                <div>
                    <label class="block text-sm font-semibold text-yellow-600 mb-2">
                        <i class="pi pi-print mr-2"></i>Print Type
                    </label>
                    <v-select v-model="selectedPrintType" :reduce="print => print.code" :options="computedPrintTypeList"
                        label="name" :clearable="false" placeholder="Select print type"
                        class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                            control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                            option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
                        }" />
                </div>

                <!-- Date Filter -->
                <div>
                    <label class="block text-sm font-semibold text-yellow-600 mb-2">
                        <i class="pi pi-calendar mr-2"></i>Date
                    </label>
                    <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single"
                        :maxDate="new Date()" :minDate="computedMinDate" showButtonBar class="w-full blinkit-calendar"
                        :inputStyle="{ height: '40px', borderRadius: '0.75rem', borderColor: '#975A16', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }"
                        dateFormat="yy-mm-dd" />
                </div>
            </div>

            <!-- Apply Button for Desktop -->
            <div class="flex justify-center mt-4">
                <button @click="applyFilters"
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-8 rounded-xl transition-colors">
                    Apply Filters
                </button>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="!assetsLoaded">
            <Loading />
        </div>

        <!-- PAAS Metrics Section -->
        <div v-if="assetsLoaded" class="max-w-7xl mx-auto px-2 py-1">
            <!-- Use the PaasMetricGroup component with metric groups from constants -->
            <MetricGroup v-for="(metricGroup, key, idx) in PAAS_METRIC_GROUPS" :metricGroup="metricGroup"
                :key="metricGroup.key" :cityCode="computedCityName.cityName" :storeObject="finalValue.store"
                :printType="finalValue.printType" :date="computedDate" :showHeader="showHeader" />

            <div class="acc mt-8">
                <div v-if="overallStoresSearched" class="mb-2">
                    <Accordion :activeIndex="activeCitiesData" @tab-open="handleTabOpen()" @tab-close="handleTabClose()"
                        class="mt-4">
                        <AccordionTab :pt="{
                            header: { class: 'shadow-md hover:shadow-lg rounded-lg' },
                            headerAction: { class: 'bg-blue-50 rounded-lg' },
                            content: { class: 'bg-blue-50/40' },
                        }">
                            <template #header>
                                <div class="flex justify-between items-center w-full">
                                    <div class="flex items-center gap-2 md:gap-3">
                                        <i class="pi pi-chart-line text-violet-600" style="color: black;"></i>
                                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">
                                            {{ computedIsPanIndiaView ? 'Cities' : 'Stores' }} Data
                                        </div>
                                    </div>
                                </div>
                            </template>

                            <div v-if="isCitiesDataLoading">
                                <Loading />
                            </div>

                            <div v-else-if="isCitiesDataError" class="text-center p-4">
                                <span class="text-sm md:text-md font-bold text-red-700">
                                    Failed to load metrics. Please try again.
                                </span>
                            </div>

                            <div v-else>
                                <TabView class="mt-2 md:mt-4">
                                    <TabPanel header="Order Cancellation">
                                        <MetricTable :metrics="computedCityMetrics.orderCancellationMetrics"
                                            :isPanIndiaView="computedIsPanIndiaView" class="mb-4" isReverse />
                                    </TabPanel>
                                    <TabPanel header="Cart Volume">
                                        <MetricTable :metrics="computedCityMetrics.cartMetrics"
                                            :isPanIndiaView="computedIsPanIndiaView" class="mb-4" />
                                    </TabPanel>
                                </TabView>
                            </div>
                        </AccordionTab>
                    </Accordion>
                </div>
            </div>
        </div>

        <!-- Mobile Floating Buttons -->
        <MobileFloatingButtons v-if="isMobile" :isMobile="isMobile" :hasActiveFilters="hasActiveFilters"
            :activeFiltersCount="activeFiltersCount" @openFilters="openMobileFilterFromFloatingButton" />
    </main>
</template>

<script>
import { getUserAccessMapping, noPermissionRoute, formatDateToString, getUserAllowedStoresMapping, isGlobalUser, isCityUser, getPanIndiaStoreCityList, AddMissingStores } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities';
import isEmpty from "lodash.isempty";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import MobileFilterBottomSheet from "../../components/Common/MobileFilterBottomSheet.vue";
import MobileFloatingButtons from "../../components/Common/MobileFloatingButtons.vue";
import Calendar from "primevue/calendar";
import vSelect from 'vue-select';
import { useScreenSize } from "../../composables/useScreenSize.js";
import { PanIndiaConstants } from "../../constants/index.js";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import { PAAS_METRIC_GROUPS } from "../../constants/sonar/paas.js";
import MetricGroup from "../../components/Paas/MetricGroup.vue";
import AccordionTab from "primevue/accordiontab";
import Accordion from "primevue/accordion";
import MetricsGrid from "../../components/MetricsGrid.vue";
import axios from "axios";
import { api } from "../../api/index.js";
import { cityMetrics, PASS_METRIC_LIST, storeMetrics } from "../../utils/metrics.js";
import { paasMetricGridMapping } from "../../configurations/Sonar/paasMetricGridMapping .js";
import MetricTable from "../../components/Paas/MetricTable.vue";
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';

export default {
    components: {
        Loading,
        WaterMark,
        MobileFilterBottomSheet,
        MobileFloatingButtons,
        Calendar,
        'v-select': vSelect,
        MetricGroup,
        Accordion,
        AccordionTab,
        MetricsGrid,
        MetricTable,
        TabView,
        TabPanel
    },

    data() {
        return {
            showHeader: false,
            selectedCity: "",
            mappedList: {},
            selectedCityStores: [],
            selectedStore: null,
            userAccessMapping: {},
            isGlobalUser: false,
            isCityUser: false,
            canAccessPage: false,
            visible: false,
            selectedDate: null,
            selectedPrintType: 'all',
            cancelTokens: [],
            pageLocalStorage: [],
            assetsLoaded: false,

            // Filter values tracking
            finalValue: {
                city: "",
                store: null,
                printType: 'all',
                date: null,
            },

            // Mobile filter properties
            hasPendingMobileChanges: false,
            PAAS_METRIC_GROUPS: PAAS_METRIC_GROUPS,

            // city metrics
            isCitiesDataLoading: false,
            isCitiesDataError: false,
            cityMetrics: [],
            activeCitiesData: null,
            intervalIdCities: null,
        }
    },

    computed: {
        computedPanIndiaConstants() {
            return PanIndiaConstants;
        },

        mobileSelectStyles() {
            return {
                control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
            };
        },

        computedPrintTypeList() {
            return [
                { code: "all", name: "All" },
                { code: "document", name: "Document Prints" },
                { code: "photo", name: "Photo Prints" },
                { code: "passport", name: "Passport Photos" }
            ];
        },

        computedNote() {
            return null
        },

        computedDate() {
            return formatDateToString(this.finalValue?.date || this.selectedDate);
        },

        computedMinDate() {
            var day = new Date();
            day.setDate(day.getDate() - 25);
            return day;
        },

        computedCityName() {
            if (!this.finalValue?.city) return {}

            let modifiedCityMapping = getPanIndiaStoreCityList(this.getStorePageCityMapping);
            let cityName = modifiedCityMapping[this.finalValue?.city]["name"];

            return {
                displayName: modifiedCityMapping[this.finalValue?.city]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));

            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });
            if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
            return cityList;
        },

        computedStoreList() {
            return this.selectedCityStores;
        },

        computedPrintTypeName() {
            return this.computedPrintTypeList?.find((p) => p.code === this.finalValue.printType)?.name
        },

        // Mobile filter computed properties
        mobileFilterSummary() {
            const summary = [];

            // Use finalValue instead of selected values for filter summary
            if (this.finalValue?.city && this.finalValue.city !== PanIndiaConstants.PAN_INDIA.code) {
                summary.push({
                    key: 'city',
                    label: 'City',
                    value: this.computedCityName.displayName || this.finalValue.city
                });
            }

            if (this.finalValue?.store) {
                summary.push({
                    key: 'store',
                    label: 'Store',
                    value: this.finalValue.store.merchant_name_id
                });
            }

            if (this.finalValue?.printType && this.finalValue.printType !== 'all') {
                summary.push({
                    key: 'printType',
                    label: 'Print Type',
                    value: this.computedPrintTypeName
                });
            }

            if (this.finalValue?.date) {
                const today = new Date();
                const selected = new Date(this.finalValue.date);
                if (selected.toDateString() !== today.toDateString()) {
                    summary.push({
                        key: 'date',
                        label: 'Date',
                        value: formatDateToString(this.finalValue.date)
                    });
                } else {
                    summary.push({
                        key: 'date',
                        label: 'Date',
                        value: "Today"
                    });
                }
            }

            return summary;
        },

        activeFiltersCount() {
            return this.mobileFilterSummary.length;
        },

        hasActiveFilters() {
            return this.activeFiltersCount > 0;
        },

        overallStoresSearched() {
            return this.finalValue?.store?.merchant_name_id === 'Overall';
        },

        computedIsPanIndiaView() {
            return this.finalValue?.city === PanIndiaConstants.PAN_INDIA.code
        },

        computedMetricConfig() {
            return paasMetricGridMapping(this);
        },

        computedCityMetrics() {
            let metricsFunc = this.computedIsPanIndiaView ? cityMetrics : storeMetrics;
            let metricType = this.computedIsPanIndiaView ? "city" : undefined;
            let cartMetrics = metricsFunc(this.cityMetrics, "Cart Volume", metricType);
            let orderCancellationMetrics = metricsFunc(this.cityMetrics, "Order Cancellation", metricType);

            let metricKey = this.computedIsPanIndiaView ? 'city' : 'frontend_merchant_id';

            // only showing top 30
            return {
                orderCancellationMetrics: this.sortMetricsByCartIndex(orderCancellationMetrics, cartMetrics, metricKey)?.slice(0, 30),
                cartMetrics: cartMetrics?.slice(0, 30),
            };
        },

        ...mapState(useUserStore, ['getAllowedNavs']),
        ...mapState(useCityStore, ['getPaasPage', 'getStoreToCityMapping', 'getCityMapping', 'getStorePageCityMapping', 'getStoreCityList']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return { isMobile };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        onCityChange(event) {
            this.selectedCityStores = this.mappedList[this.selectedCity] || [];
            this.selectedStore = this.selectedCityStores.length > 0 ? this.selectedCityStores[0] : null;
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            let storeList = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            this.mappedList = storeList;
            this.isGlobalUser = isGlobalUser(userAccessMapping);
            this.isCityUser = isCityUser(userAccessMapping);

            if (this.pageLocalStorage?.selectedStore) this.selectedStore = this.pageLocalStorage?.selectedStore;
            if (this.pageLocalStorage?.selectedCity) this.selectedCity = this.pageLocalStorage?.selectedCity;
            else if (this.isGlobalUser) this.selectedCity = PanIndiaConstants.PAN_INDIA.code;
            else this.selectedCity = Object.keys(this.getStorePageCityMapping)[0];

            if (this.pageLocalStorage?.selectedPrintType) this.selectedPrintType = this.pageLocalStorage?.selectedPrintType;

            // Add "Overall" store to each city
            if (this.isGlobalUser || this.isCityUser) {
                this.mappedList[PanIndiaConstants.PAN_INDIA.code] = [];
                for (let cityName in this.mappedList) {
                    if (this.mappedList.hasOwnProperty(cityName)) {
                        let cityStores = this.mappedList[cityName];

                        const actualStoreCountOfCity = this.getStoreToCityMapping?.find((item) =>
                            item?.city === (this.getStoreCityList?.find((item) => item?.code === cityName)?.name)
                        )?.data?.length;

                        if ((cityStores?.length >= actualStoreCountOfCity || cityName === PanIndiaConstants.PAN_INDIA.code)) {
                            let overallStore = {
                                frontend_merchant_id: '',
                                frontend_merchant_name: 'Overall',
                                backend_merchant_id: '',
                                name: cityName,
                                merchant_name_id: 'Overall'
                            };

                            if (!cityStores.some(store => store.merchant_name_id === 'Overall')) {
                                cityStores.unshift(overallStore);
                            }
                        }
                    }
                }
            }

            this.selectedCityStores = this.mappedList[this.selectedCity];
            if (!this.selectedStore && this.selectedCityStores?.length > 0) {
                this.selectedStore = this.selectedCityStores[0];
            }

            // Initialize the finalValue with the current selections
            this.finalValue = {
                city: this.selectedCity,
                store: this.selectedStore,
                printType: this.selectedPrintType,
                date: this.selectedDate
            };

            this.assetsLoaded = true;
            // PaasMetricGroup component will fetch metrics automatically when mounted
        },

        refreshFiltersList() {
            fetchStoreToCityMapping(false, false);
        },

        // Mobile filter methods
        onMobileFilterOpen() {
            // No deferred mode for PAAS page - apply changes immediately
        },

        onMobileFilterClose() {
            // No special handling needed
        },

        // Common filter apply method for both mobile and desktop
        applyFilters() {
            // Update finalValue with current selections
            this.finalValue = {
                city: this.selectedCity,
                store: this.selectedStore,
                printType: this.selectedPrintType,
                date: this.selectedDate
            };

            // Close bottom sheet if in mobile view
            if (this.isMobile && this.$refs.mobileFilterBottomSheet) {
                this.$refs.mobileFilterBottomSheet.closeBottomSheet();
            }

            this.pageLocalStorage = {
                selectedCity: this.selectedCity,
                selectedStore: this.selectedStore,
                selectedPrintType: this.selectedPrintType,
                selectedDate: this.selectedDate
            };

            this.updatePaasPage(this.pageLocalStorage);

            this.handleTabOpen();

            //for metric groups : No need to fetch metrics directly, they will be fetched by the PaasMetricGroup component
        },

        onMobileFilterClearAll() {
            // Reset all filters to default values
            this.selectedCity = this.isGlobalUser ? PanIndiaConstants.PAN_INDIA.code : Object.keys(this.getStorePageCityMapping)[0];
            this.selectedStore = null;
            this.selectedPrintType = 'all';
            this.selectedDate = new Date();

            // Update localStorage
            this.pageLocalStorage = {
                selectedCity: this.selectedCity,
                selectedStore: this.selectedStore,
                selectedPrintType: this.selectedPrintType,
                selectedDate: this.selectedDate
            };
            this.updatePaasPage(this.pageLocalStorage);

            // Apply changes using common apply filters method
            this.applyFilters();
        },

        openMobileFilterFromFloatingButton() {
            // Trigger mobile filter opening from floating button
            this.$refs.mobileFilterBottomSheet?.openBottomSheet();
        },

        handleTabClose() {
            this.isCitiesDataLoading = false;
            this.isCitiesDataError = false;

            this.activeCitiesData = null
            this.cancelPreviousRequests()
        },

        handleTabOpen() {
            this.isCitiesDataLoading = true;
            this.isCitiesDataError = false;

            // Clear any previous data to ensure a fresh load
            this.cityMetrics = [];

            this.activeCitiesData = 0
            this.fetchCitiesMetrics();

            this.intervalIdCities = window.setInterval(() => {
                this.fetchCitiesMetrics();
            }, 120000);
        },


        async fetchCitiesMetrics() {
            this.cancelPreviousRequests();
            this.isCitiesDataLoading = true;
            this.isCitiesDataError = false;

            try {
                const citiesMetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [citiesMetricsCancelTokenSource];

                let apiParams = {
                    city: this.computedCityName?.cityName,
                    // store: this.finalValue.store?.frontend_merchant_id ? [this.finalValue.store?.frontend_merchant_id] : null,
                    print_type: this.printType && this.printType !== 'all' ? this.printType : null,
                    date_str: this.computedDate,
                };

                let citiesMetricsResponse = await api.fetchPaasCityMetrics({
                    metrics: PASS_METRIC_LIST,
                    ...apiParams,
                    cancelToken: citiesMetricsCancelTokenSource.token,
                });

                if (citiesMetricsResponse) {
                    if (this.computedIsPanIndiaView) {
                        this.cityMetrics = citiesMetricsResponse.data.metrics;
                    } else {
                        let storeMappingObj = this.getStoreToCityMapping.filter(cityObj => cityObj.city === this.computedCityName.cityName);

                        let storeMapping = storeMappingObj.reduce((initial, acc) => {
                            return [...initial, ...acc?.data];
                        }, []);

                        this.cityMetrics = AddMissingStores(storeMapping, citiesMetricsResponse.data.metrics);
                    }
                }
            } catch (error) {
                if (!axios.isCancel(error)) {
                    this.isCitiesDataError = true;
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            } finally {
                this.isCitiesDataLoading = false;
            }
        },


        cancelPreviousRequests() {
            this.cancelTokens?.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        sortMetricsByCartIndex(metrics, cartMetrics, metricKey) {
            return metrics.sort((a, b) => {
                let indexOfA = cartMetrics.findIndex(metric => metric[metricKey] === a[metricKey]);
                let indexOfB = cartMetrics.findIndex(metric => metric[metricKey] === b[metricKey]);
                return indexOfA - indexOfB;
            });
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updatePaasPage']),
    },

    watch: {
        $route() {
            this.checkRoute();
        },
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[25]);
        if (!this.canAccessPage) noPermissionRoute();
    },

    mounted() {
        if (this.canAccessPage) {
            this.pageLocalStorage = this.getPaasPage || {};

            let currentDate = (new Date()).getDate()
            if (this.pageLocalStorage && currentDate !== this.pageLocalStorage?.currentDate) {
                this.pageLocalStorage["selectedDate"] = null;
                this.pageLocalStorage["currentDate"] = (new Date()).getDate();
                this.updatePaasPage(this.pageLocalStorage);
                window.location.reload()
            } else {
                this.selectedDate = isEmpty(this.pageLocalStorage?.selectedDate) ? new Date() : new Date(this.pageLocalStorage?.selectedDate)
            }

            this.selectedCity = this.pageLocalStorage?.selectedCity ? this.pageLocalStorage?.selectedCity : PanIndiaConstants.PAN_INDIA.code;
            this.selectedPrintType = isEmpty(this.pageLocalStorage?.selectedPrintType) ? "all" : this.pageLocalStorage?.selectedPrintType;

            // Initializing the finalValue in "fetchCityStoreMapping"

            this.fetchCityStoreMapping();
            this.checkRoute();
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalIdCities);
    }
}
</script>


<style scoped>
:deep(.acc .p-accordion .p-accordion-content) {
    padding: 0.5rem;
}
</style>
