<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedCityName && computedCityName.displayName ?
                computedCityName.displayName : selectedCity }}</div>
            <div class="text-3xl font-bold text-gray-900" v-if="insightsPage">Store: {{
                appliedFilters?.store?.merchant_name_id || insightsPage?.merchant_name_id }} </div>
        </div>
    </header>

    <div v-if="showHeader" class="lg:bg-slate-50">
        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 text-center">
            <div class="lg:flex lg:flex-row justify-center items-center sm:mx-auto">
                <div class="flex flex-row lg:mr-4">
                    <p for="citySelect" class="lg:mr-2 lg:text-center lg:m-auto">City: </p>
                    <v-select id="citySelect" :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                        :options="computedCityList" label="name" @update:model-value="onCityChange($event)"
                        :clearable="false" placeholder="click to select city"
                        class="bg-gray-50 px-2 py-1 text-gray-900 text-sm rounded-lg focus:border-blue-500 block w-full lg:w-80 lg:mx-auto text-center" />
                </div>
                <div class="flex flex-row lg:mr-4">
                    <p for="citySelect" class="lg:mr-2v lg:m-auto">Store:</p>
                    <v-select id="storeSelect" v-model="insightsPage" :options="computedStoreList"
                        label="merchant_name_id" :clearable="false" placeholder="click to select store"
                        :disabled="selectedCity == '#Pan-India'"
                        class="bg-gray-50 px-2 py-1 text-gray-900 text-sm rounded-lg focus:border-blue-500 block w-full lg:w-96 lg:mx-auto lg:py-2 text-center p-2.5" />
                </div>
                <div class="flex flex-row lg:mr-4">
                    <p for="citySelect" class="lg:mr-2 lg:m-auto">Hour:</p>
                    <HourList id="hourSelect" v-model="selectedHour" :startHour="5" :isYesterday="isYesterday"
                        :styles="['bg-gray-50', 'px-2', 'py-1', 'text-gray-900', 'text-sm', 'rounded-lg', 'focus:border-blue-500', 'block', 'w-full', 'lg:w-80', 'lg:mx-auto', 'lg:py-2', 'text-center', 'p-2.5']" />
                </div>
            </div>
            <div class="flex flex-row justify-center items-center sm:mx-auto mt-3">
                <div class="flex flex-row">
                    <div class="text-left text-gray-900 flex items-center">
                        <VueToggle class="max-w-md m-auto" title="" name="VueToggle" :toggled="computedToggleState"
                            @toggle="onToggleYesterday" />
                        <p class="ml-2">Yesterday</p>
                    </div>
                </div>
                <div class="flex flex-row mx-8">
                    <Button label="Apply" severity="contrast" @click="loadMetrics" class="px-10 py-3" />
                </div>
                <Button text @click="refreshList" class="pi pi-refresh text-black"
                    v-tooltip.top="{ value: 'Refresh Filters List' }" />
            </div>
        </div>
    </div>

    <div v-else class="m-2">
        <HeaderFilters :selectedCity="selectedCity" @update:selectedCity="handleCityChange" :insightsPage="insightsPage"
            @update:insightsPage="handleStoreChange" :selectedHour="selectedHour"
            @update:selectedHour="handleHourChange" :computedCityList="computedCityList"
            :computedStoreList="computedStoreList" :isYesterday="isYesterday" @update:isYesterday="isYesterday = $event"
            @toggle="onToggleYesterday" @loadMetrics="loadMetrics" @refreshList="refreshList"
            :appliedFilters="appliedFilters" />
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark :isGreyBackground="true"></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{ this.selectedCity }}
            city is invalid or some internal error occured.<br />Please select another city or reach out to data team at
            <EMAIL> or #bl-data-support slack channel.</span>
    </div>


    <main v-else class="mt-2 md:mt-6 mb-20">
        <WaterMark :isGreyBackground="true"></WaterMark>
        <div v-if="(isGlobalUser)" class="max-w-7xl mx-auto px-3 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="cities" :data="computedPanIndiaCityMetrics" :nonMetricColumns="{ key: 'City' }"
                :includesOverall="isGlobalUser || appliedFilters?.city !== computedPanIndiaConstants.PAN_INDIA.code"
                :isCityLevelView="true" :showHeader="showHeader" :reversedMetrics="computedReversalColumns"
                :defaultRowCount="showHeader ? 12 : 10" :onSelectId="onCitySelect" :hideAccordion="true"
                :hasSearchFilter="true" :isPanIndia="true" />
        </div>

        <div v-if="(isGlobalUser || isCityUser) && fetchAllMetric" class="max-w-7xl mx-auto px-3 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="cities" :data="computedCityMetrics" :nonMetricColumns="{ key: 'City' }"
                :includesOverall="(isGlobalUser && appliedFilters?.city === computedPanIndiaConstants.PAN_INDIA.code) || (!this.isGlobalUser && this.isCityUser)"
                :showOverallMetric="(!this.isGlobalUser && this.isCityUser)" :isCityLevelView="true"
                :showHeader="showHeader" :reversedMetrics="computedReversalColumns"
                :defaultRowCount="showHeader ? 12 : 10" :onSelectId="onCitySelect"
                :hideAccordion="computedHideCityAccordion" :hasSearchFilter="true" />
        </div>

        <div class="max-w-7xl mx-auto px-3 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="stores" :data="computedStoreMetrics" :nonMetricColumns="{ key: 'Store' }"
                :isCityLevelView="false" :showHeader="showHeader" :reversedMetrics="computedReversalColumns"
                :defaultRowCount="showHeader ? 12 : 10" :onSelectId="onStoreSelect"
                :includesOverall="!(isGlobalUser || isCityUser)" :showOverallMetric="!(isGlobalUser || isCityUser)"
                :hideAccordion="computedHideStoreAccordion" :hasSearchFilter="true" />
        </div>

        <div class="max-w-7xl mx-auto px-3 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="p-types" :data="computedPtypeMetrics"
                :nonMetricColumns="{ key: computedStoreId === '' ? 'City' : 'Store', grain: 'P Type' }"
                :isCityLevelView="false" :showHeader="showHeader" :defaultRowCount="showHeader ? 12 : 10"
                :hasSearchFilter="true" @onPtypeSelect="onPtypeSelect" :currentFilters="computedCurrentFilters"
                :selectedHour="selectedHour" />
        </div>

        <div class="max-w-7xl mx-auto px-3 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="p-types-conv" :data="computedPtypeConvDropMetrics"
                :nonMetricColumns="{ key: computedStoreId === '' ? 'City' : 'Store', grain: 'P Type' }"
                :isCityLevelView="false" :showHeader="showHeader" :defaultRowCount="showHeader ? 12 : 10"
                :hasSearchFilter="true" @onPtypeSelect="onPtypeSelect" :currentFilters="computedCurrentFilters" />
        </div>
    </main>
</template>

<script>

import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import HourList from "../../components/HourList.vue";
import { api } from "../../api/index.js";
import { MetricChange } from "../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import { getUserAccessMapping, getUserAllowedStoresMapping, formatStoreName, getPanIndiaStoreCityList, isGlobalUser, isCityUser, noPermissionRoute } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import { PanIndiaConstants, insights } from "../../constants/index.js";
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import VueToggle from 'vue-toggle-component';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import Accordions from "../../components/Insights/Accordions.vue";
import SelectButton from 'primevue/selectbutton';
import HeaderFilters from "../../components/Insights/HeaderFilters.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import Tooltip from 'primevue/tooltip';
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

    components: {
        Loading,
        AnimatedNumber,
        WaterMark,
        'v-select': vSelect,
        VueToggle,
        HourList,
        Button,
        Checkbox,
        Accordions,
        SelectButton,
        HeaderFilters
    },

    directives: {
        tooltip: Tooltip
    },

    computed: {
        computedReversalColumns() {
            return insights.REVERSE_COLUMNS;
        },
        computedPanIndiaConstants() {
            return PanIndiaConstants;
        },
        computedToggleState() {
            if (this.getInsightsPage == null || !this.getInsightsPage.hasOwnProperty('yesterday')) return false;
            return this.getInsightsPage['yesterday'];
        },

        computedCityList() {
            let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
            cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) {
                let staticList = [PanIndiaConstants.PAN_INDIA];
                cityList = staticList.concat(cityList);
            }
            return cityList;
        },

        computedDayDiffList() {
            return [{ name: "T-7", code: 7 }, { name: "T-28", code: 28 }];
        },

        computedStoreList() {
            return this.selectedCityStores;
        },

        computedCityName() {
            let modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;
            if (modifiedCityMapping[this.appliedFilters?.city] == undefined) return;
            let cityName = modifiedCityMapping[this.appliedFilters?.city]["name"];

            return {
                displayName: modifiedCityMapping[this.appliedFilters?.city]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        computedStoreId() {
            if (this.getStorePageCityMapping[this.selectedCity] == undefined) return;
            return this.insightsPage["frontend_merchant_id"];
        },

        computedPanIndiaCityMetrics() {
            return this.calculatedMetrics(this.panIndiaInsightsMetrics);
        },

        computedCityMetrics() {
            return this.calculatedMetrics(this.cityInsightsMetrics);
        },

        computedStoreMetrics() {
            return this.calculatedMetrics(this.storeInsightsMetrics);
        },

        computedPtypeMetrics() {
            return this.calculatedMetrics(this.ptypeInsightsMetrics);
        },

        computedHideCityAccordion() {
            return (!this.isGlobalUser && this.isCityUser);
        },
        computedHideStoreAccordion() {
            return !(this.isGlobalUser || this.isCityUser);
        },

        toggleYesterdayOptions() {
            return [{ name: "Today", value: false }, { name: "Yesterday", value: true }]
        },

        computedCurrentFilters() {
            return {
                cityName: this.computedCityName?.displayName,
                storeName: this.appliedFilters?.store?.merchant_name_id || this.insightsPage?.merchant_name_id,
                storeCode: this.appliedFilters?.store?.frontend_merchant_id || this.insightsPage?.frontend_merchant_id,
                isYesterday: this.isYesterday
            }
        },

        computedPtypeConvDropMetrics() {
            return this.calculatedMetrics(this.ptypeConvDropInsightsMetrics);
        },

        ...mapState(useCityStore, ['getStorePageCityMapping', 'getStoreCityList', 'getStoreToCityMapping', 'getInsightsPage']),
        ...mapState(useUserStore, ['user', 'isLoggedIn', 'getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        onCitySelect(city, isPtype, ptype_value) {
            const cityKey = this.computedCityList.find((item) => item.name === city).code;
            if (!cityKey) return;

            if (isPtype) {
                this.$router.push({ name: 'P-Type Insights', query: { city, cityCode: cityKey } })
            }
            else {
                this.$router.push({ name: 'City Insights', query: { city, cityCode: cityKey } })
            }
        },

        onStoreSelect(storeName, isPtype, ptype_value) {
            let cityCode = ''
            let storeObj = {}

            Object.keys(this.userAllowedStoresMapping).forEach((city) => {
                if (cityCode) return;

                const cityStores = this.userAllowedStoresMapping[city];
                const obj = cityStores.find(({ frontend_merchant_name }) => frontend_merchant_name.includes(storeName));

                if (!obj || !Object.keys(obj).length) return;

                cityCode = city;
                storeObj = obj;
            })

            const cityName = this.computedCityList.find((item) => item.code === cityCode).name;

            if (!cityCode || !cityName || !storeObj || !Object.keys(storeObj).length) return;

            if (isPtype) {
                this.$router.push({
                    name: 'P-Type Insights', query: {
                        city: cityName, cityCode,
                        store: storeName, storeCode: storeObj['frontend_merchant_id']
                    }
                })
            } else {
                this.$router.push({
                    name: 'Store Insights', query: {
                        city: cityName, cityCode,
                        store: storeName, storeCode: storeObj['frontend_merchant_id']
                    }
                })
            }
        },

        calculatedMetrics(insightsMetrics) {
            let res = insightsMetrics.map(cityMetric => {
                let filteredMetrics = cityMetric.metric.map(metric => {
                    let { data, name } = metric;
                    let curr = data[data.length - 1];
                    let diffs = data.slice(0, -1).map((prev) => new MetricChange(curr, prev));
                    return {
                        name,
                        reverseStyle: metricsForStyleReversal.includes(name),
                        curr,
                        diffs,
                    };
                });

                let { metric, ...metricObject } = cityMetric;

                // to remove prefix from store names
                metricObject.key = formatStoreName(metricObject.key)

                return {
                    ...metricObject,
                    metrics: filteredMetrics
                };
            });
            return res;
        },

        onCityChange() {
            this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
            this.insightsPage = this.selectedCityStores[0];
        },


        onToggleYesterday(isToggled) {
            this.isLoading = true;
            this.isError = false;
            this.isYesterday = isToggled;
            this.insightsPage["yesterday"] = this.isYesterday;
            this.insightsPage["selectedHour"] = this.selectedHour;
            this.insightsPage["dayDiff"] = this.selectedDayDiff;

            this.appliedFilters = {
                ...this.appliedFilters,
                yesterday: this.isYesterday
            }

            this.updateInsightsPage(this.insightsPage);

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },


        toFetchAllMetric() {
            this.fetchAllMetric = (this.isGlobalUser || (this.isCityUser && this.getStoreToCityMapping?.find((cityStores) =>
                cityStores?.city === this.computedCityName?.cityName)?.data?.length <= this.selectedCityStores?.length));
        },

        handleCityChange(city) {
            this.selectedCity = city;
            this.onCityChange();
        },

        handleStoreChange(store) {
            this.insightsPage = store;
        },

        handleHourChange(hour) {
            this.selectedHour = hour;
        },

        loadMetrics() {
            this.isLoading = true;
            this.isError = false;

            this.insightsPage["yesterday"] = this.isYesterday;
            this.insightsPage["selectedHour"] = this.selectedHour;
            this.insightsPage["dayDiff"] = this.selectedDayDiff;

            this.appliedFilters = {
                ...this.appliedFilters,
                city: this.selectedCity,
                store: this.insightsPage,
                hour: this.selectedHour,
            }

            this.updateInsightsPage(this.insightsPage);
            this.toFetchAllMetric();

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        refreshList() {
            fetchStoreToCityMapping()
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            this.isLoading = false;
            this.userAllowedStoresMapping = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            let storeList = this.userAllowedStoresMapping;
            storeList[PanIndiaConstants.PAN_INDIA.code] = [];
            for (let cityName in storeList) {
                if (storeList.hasOwnProperty(cityName)) {
                    let cityStores = storeList[cityName];

                    if (((this.getStoreToCityMapping && this.getStoreToCityMapping?.find((item) => item?.city === (this.getStoreCityList?.find((item) =>
                        // item?.code === cityName)?.name)))?.data?.length === cityStores?.length # CHANGE TO THIS AFTER MAPPING GETS CORRECTED
                        item?.code === cityName)?.name)))?.data?.length <= cityStores?.length
                        || cityName === PanIndiaConstants.PAN_INDIA.code)) {
                        let panIndiaStore = {
                            frontend_merchant_id: '',
                            frontend_merchant_name: 'Overall',
                            backend_merchant_id: '',
                            name: cityName,
                            merchant_name_id: 'Overall'
                        };
                        cityStores.unshift(panIndiaStore);
                    }
                }
            }
            this.selectedCityStores = storeList[this.selectedCity];
            if (this.insightsPage == null) {
                this.insightsPage = this.selectedCityStores[0];
            }

            this.toFetchAllMetric();
            this.isLoading = true;
            this.fetchAllMetrics();
        },


        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const panIndiaMetricsCancelTokenSource = axios.CancelToken.source();
                const cityMetricsCancelTokenSource = axios.CancelToken.source();
                const storeMetricsCancelTokenSource = axios.CancelToken.source();
                const ptypeMetricsCancelTokenSource = axios.CancelToken.source();
                const ptypeConvDropMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [panIndiaMetricsCancelTokenSource, cityMetricsCancelTokenSource, storeMetricsCancelTokenSource, ptypeMetricsCancelTokenSource, ptypeConvDropMetricsCancelTokenSource];

                let panIndiaMetrics = (this.isGlobalUser && this.fetchAllMetric) ? api.fetchCityInsightsMetrics([], this.isYesterday, '', '', this.selectedHour, this.selectedDayDiff, false, panIndiaMetricsCancelTokenSource.token)
                    : Promise.resolve(null);

                let cityMetrics = this.fetchAllMetric ? api.fetchCityInsightsMetrics([], this.isYesterday, this.computedCityName.cityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, false, cityMetricsCancelTokenSource.token)
                    : Promise.resolve(null);

                let storeMetrics = api.fetchCityInsightsMetrics([], this.isYesterday, this.computedCityName.cityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, true, storeMetricsCancelTokenSource.token)

                let ptypeMetrics = api.fetchPtypeInsightsMetrics([], this.isYesterday, this.computedCityName.cityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, 'contributed_conv_drop', ptypeMetricsCancelTokenSource.token)

                let ptypeConvDropMetrics = api.fetchPtypeInsightsMetrics([], this.isYesterday, this.computedCityName.cityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, 'absolute_conv_drop', ptypeConvDropMetricsCancelTokenSource.token)

                let [
                    panIndiaMetricsResponse,
                    cityMetricsResponse,
                    storeMetricsResponse,
                    ptypeMetricsResponse,
                    ptypeConvDropMetricsResponse
                ] = await Promise.all([
                    panIndiaMetrics,
                    cityMetrics,
                    storeMetrics,
                    ptypeMetrics,
                    ptypeConvDropMetrics
                ]);

                if (panIndiaMetricsResponse) {
                    let metrics = panIndiaMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.panIndiaInsightsMetrics = metrics;
                }

                if (cityMetricsResponse) {
                    let metrics = cityMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.cityInsightsMetrics = metrics;
                }

                if (storeMetricsResponse) {
                    let metrics = storeMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.storeInsightsMetrics = metrics;
                }

                if (ptypeMetricsResponse) {
                    let metrics = ptypeMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.ptypeInsightsMetrics = metrics;
                }

                if (ptypeConvDropMetricsResponse) {
                    let metrics = ptypeConvDropMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.ptypeConvDropInsightsMetrics = metrics;
                }

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error.response?.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.panIndiaInsightsMetrics = []
            this.cityInsightsMetrics = []
            this.storeInsightsMetrics = []
            this.ptypeInsightsMetrics = []
        },

        onPtypeSelect(ptype) {
            if (!ptype) return;

            this.$router.push({
                name: 'Item Insights', query: {
                    city: this.computedCityName?.displayName,
                    store: this.appliedFilters?.store?.merchant_name_id || this.insightsPage?.merchant_name_id,
                    storeCode: this.appliedFilters?.store?.frontend_merchant_id || this.insightsPage?.frontend_merchant_id,
                    ptype: ptype?.data,
                    yesterday: this.isYesterday,
                    selectedHour: this.selectedHour,
                    availability: ptype?.availability
                }
            })
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateInsightsPage', 'updateStoreToCityMapping']),
    },

    data() {
        return {
            showHeader: false,
            selectedCity: "",
            selectedDayDiff: 7,
            isLoading: true,
            cityInsightsMetrics: [],
            storeInsightsMetrics: [],
            ptypeInsightsMetrics: [],
            userAllowedStoresMapping: {},
            selectedCityStores: [],
            insightsPage: null,
            isError: false,
            intervalId: null,
            isYesterday: false,
            selectedHour: null,
            selectedFields: {},
            isGlobalUser: false,
            isCityUser: false,
            fetchAllMetric: false,
            panIndiaInsightsMetrics: [],
            cancelTokens: [],
            canAccessPage: false,
            ptypeConvDropInsightsMetrics: [],
            appliedFilters: {}
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        },

    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[9]);
        if (!this.canAccessPage) noPermissionRoute()

        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (!this.getStoreToCityMapping.length) {
            this.refreshList();
        }
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = true;
            this.isError = false;
            this.insightsPage = this.getInsightsPage;

            if (this.isGlobalUser) {
                this.selectedCity = !isEmpty(this.insightsPage) ? this.insightsPage.name : PanIndiaConstants.PAN_INDIA.code;
            } else {
                this.selectedCity = !isEmpty(this.insightsPage) ? this.insightsPage.name : Object.keys(this.getStorePageCityMapping)[0];
            }

            this.selectedDayDiff = this.insightsPage?.dayDiff ? this.insightsPage.dayDiff : 7;
            this.isYesterday = this.insightsPage?.yesterday ? this.insightsPage.yesterday : false;
            this.selectedHour = this.insightsPage?.selectedHour ? this.insightsPage.selectedHour : -1;

            this.appliedFilters = {
                city: this.selectedCity,
                store: this.insightsPage,
                hour: this.selectedHour,
                yesterday: this.isYesterday
            }

            this.fetchCityStoreMapping();
            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },
    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    },
}

</script>
