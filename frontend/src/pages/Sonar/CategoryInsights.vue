<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedCityName && computedCityName.displayName ?
                computedCityName.displayName : this.selectedCity }}</div>
        </div>
    </header>

    <div
        class="max-w-md mx-auto px-2 py-1 mt-2 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-2 lg:gap-10">
        <div class="flex items-center">
            <div class="hidden md:block text-left pr-2 font-semibold italic">City:</div>
            <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
                placeholder="click to select city" class="w-full" />
        </div>

        <div class="flex items-end justify-center mt-2 md:mt-0 md:justify-end">
            <SelectButton v-model="yesterday" :options="yesterdayOptions" optionLabel="label" optionValue="value"
                aria-labelledby="basic" @update:modelValue="toggled" />
        </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>
    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{
            this.selectedCity }}
            city is invalid or some internal error occured.<br />Please select another city or reach out to data
            <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>
    <main v-else>
        <WaterMark></WaterMark>

        <div class="max-w-full mx-auto p-2 lg:px-10 lg:py-4">
            <div v-for="metricValues in computedMetrics" :key="metricValues.metricName" class="mb-10">
                <div class="text-lg font-bold text-blue-900 flex items-center">{{ metricValues.metricName }}
                    <InfoDialog :metrickey="metricValues.metricName" :showHeader="showHeader"
                        :customWOW="['WOW4', 'WOW1']" />
                </div>

                <div class="mt-1.5 grid grid-cols-2 md:grid-cols-2 lg:grid-cols-6 gap-2 lg:gap-3">
                    <div v-for="metric in metricValues.metrics" :key="metric.name"
                        class="bg-gray-100 p-1 md:p-2 border-b md:border-b-0 md:rounded-lg group"
                        :class="showL1DataPopUp.includes(metricValues.metricName) ? 'hover:shadow-md' : null"
                        @click.prevent="handleMetricClick(metricValues.metricName, metric.name)">
                        <div class="flex justify-between items-center">
                            <div class="text-xs md:text-sm text-gray-500">{{ metric.name }}</div>

                            <i v-if="showL1DataPopUp.includes(metricValues.metricName)"
                                class="pi pi-external-link text-xs text-gray-400 group-hover:text-gray-600 transition-colors"></i>
                        </div>

                        <div class="mt-2"
                            :class="metricValues.metricName === 'GMV' && !showHeader ? '' : 'flex justify-between items-center'">
                            <AnimatedNumber :styles="['text-sm', 'font-semibold', 'md:text-md']"
                                :value="metric?.curr?.value" :type="metric?.curr?.type" />

                            <div class="flex justify-between items-center">
                                <div class="flex justify-between">
                                    <div v-if="isNotEmpty(metric.diffs)" v-for="diff in metric.diffs" :key="diff.date">
                                        <span class="text-xs font-[650] mr-0.25 md:mr-0.5"
                                            v-bind:class="diff.style(metric.reverseStyle)">
                                            <template v-if="diff.change() !== '-'">
                                                {{ diff.change() }}
                                                {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%'
                                                }}
                                            </template>
                                            <template v-else>
                                                -
                                            </template>
                                        </span>
                                        <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                            class="text-xs font-bold mr-1 text-positive-metric"> »
                                        </span>
                                    </div>
                                    <div v-else>
                                        -
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <Dialog v-model:visible="showL1PopUp" modal
        :header="`${selectedPopUpMeta?.metricName} - ${selectedPopUpMeta?.l0Category} `"
        :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
        <div class="w-80 md:w-full">
            <L1DataPopUp :city="computedCityName?.cityName" :isYesterday="yesterday"
                :metricName="selectedPopUpMeta?.metricName" :l0Category="selectedPopUpMeta?.l0Category" />
        </div>
    </Dialog>
</template>

<script>

import Loading from "../../components/Loading.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import MetricBox from "../../components/MetricBox.vue";
import { getCombinedCityNames, getPanIndiaStoreCityList, getUserAccessMapping, isCityUser, isGlobalUser, noPermissionRoute } from "../../utils/utils.js";
import WaterMark from "../../components/WaterMark.vue";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import 'vue-select/dist/vue-select.css';
import isEmpty from "lodash.isempty";
import axios from "axios";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import SelectButton from "primevue/selectbutton";
import { useCityStore } from "../../stores/cities.js";
import { api } from "../../api/index.js";
import InfoDialog from '../../components/InfoDialog.vue';
import { ABSOLUTE_METRIC_DELTA, metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import { MetricChange } from "../../interfaces/index.js";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import vSelect from 'vue-select';
import { CitiesToExcludeFromCityMapping, MergedCityStoreConstants, PanIndiaConstants } from "../../constants/index.js";
import { useScreenSize } from "../../composables/useScreenSize.js";
import Dialog from 'primevue/dialog'
import { defineAsyncComponent } from "vue";

export default {

    components: {
        Loading,
        AnimatedNumber,
        MetricBox,
        WaterMark,
        SelectButton,
        InfoDialog,
        'v-select': vSelect,
        L1DataPopUp: defineAsyncComponent(() => import("../../components/CategoryInsights/L1DataPopUp.vue")),
        Dialog
    },

    computed: {
        computedPanIndiaConstants() {
            return PanIndiaConstants;
        },

        showL1DataPopUp() {
            return ['AOV Contribution', 'GMV', 'Cart Pen', 'Total Items Sold']
        },

        yesterdayOptions() {
            return [
                { label: 'Today', value: false },
                { label: 'Yesterday', value: true }
            ]
        },

        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        computedBlockMetrics() {
            return this.allMetrics?.map(({ category_name, data }) => {
                let metrics = sortedAllMetrics(data)

                let insightMetricsData = this.insightMetrics?.find((insightMetric) => insightMetric?.grain === category_name)?.metric

                let allSortedMetrics = []
                if (insightMetricsData) {
                    let insightMetrics = sortedAllMetrics(insightMetricsData, [], true, 2)
                    allSortedMetrics = [...metrics, ...insightMetrics]
                }
                else allSortedMetrics = [...metrics]

                metrics = allSortedMetrics?.map(metric => {
                    let data = metric?.data;
                    let curr = data?.[data?.length - 1];
                    let diffs = [];
                    for (let j = 0; j < data?.length - 1; j++) {
                        if (!this.NO_SKIP_DIFFS.includes(metric?.name) && [1, 2].includes(j)) continue // hide t-3, t-2

                        let prev = data[j];
                        diffs.push(new MetricChange(curr, prev, metric?.name));
                    }

                    return {
                        name: metric?.name,
                        reverseStyle: metricsForStyleReversal.includes(metric?.name),
                        curr: curr,
                        diffs: diffs,
                    }
                }).filter(metric => metric.curr && metric.curr?.value !== "-");

                if (isEmpty(metrics)) return

                return { category_name, metrics }
            }).filter(Boolean)
        },


        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
            if ((this.isGlobalUser || this.isCityUser)) {
                cityList.forEach((cityItem) => {
                    if (Object.keys(MergedCityStoreConstants).includes(cityItem.code)) {
                        cityItem.name = getCombinedCityNames(cityItem.code);
                    }
                });
            }

            Object.keys(CitiesToExcludeFromCityMapping).forEach((item) => {
                if (cityList.filter((city) => city.code === item).length > 0) {
                    cityList = cityList.filter((city) => !CitiesToExcludeFromCityMapping[item].includes(city.name));
                }
            })

            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
            return cityList;
        },

        computedCityName() {
            let modifiedCityMapping = getPanIndiaStoreCityList(this.getStorePageCityMapping);
            let cityName = modifiedCityMapping?.[this.selectedCity]?.["name"];

            if (!cityName) return

            if ((this.isGlobalUser || this.isCityUser) &&
                Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.selectedCity]["code"])
            ) {
                cityName = MergedCityStoreConstants[this.selectedCity].join("&city=");
            }

            return {
                displayName: (Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.selectedCity]["code"]) && (this.isGlobalUser || this.isCityUser))
                    ? getCombinedCityNames(this.selectedCity)
                    : modifiedCityMapping[this.selectedCity]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        computedMetrics() {
            // 1. first aggregated all data w.r.t. category items
            // 2. sorted them w.r.t. AOV
            // 3. picking metrics separately

            let aovContributionMetrics = this.getMetricValues(this.computedBlockMetrics, "AOV Contribution");
            let gmvMetrics = this.getMetricValues(this.computedBlockMetrics, "GMV");
            let totalItemsSoldMetrics = this.getMetricValues(this.computedBlockMetrics, "Total Items Sold");
            let cartPenMetrics = this.getMetricValues(this.computedBlockMetrics, "Cart Pen");
            let uniqueImpressionsMetrics = this.getMetricValues(this.computedBlockMetrics, "Unique Impressions");
            let searchConvMetrics = this.getMetricValues(this.computedBlockMetrics, "Search Conv %");
            let searchImpressionsMetrics = this.getMetricValues(this.computedBlockMetrics, "Search Impressions");
            let feAvailabilityMetrics = this.getMetricValues(this.computedBlockMetrics, "FE Availability");
            let feInventoryMetrics = this.getMetricValues(this.computedBlockMetrics, "FE Inventory");

            return {
                aovContributionMetrics,
                gmvMetrics,
                totalItemsSoldMetrics,
                cartPenMetrics,
                uniqueImpressionsMetrics,
                searchConvMetrics,
                searchImpressionsMetrics,
                feAvailabilityMetrics,
                feInventoryMetrics
            }
        },

        ...mapState(useCityStore, ['getCategoryPage', 'getStoreToCityMapping', 'getStoreCityList', 'getStorePageCityMapping']),
        ...mapState(useUserStore, ['getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        toggled(val) {
            this.yesterday = val
            this.isLoading = true
            this.isError = false
            this.fetchAllMetrics()
            this.categoryPage['yesterday'] = this.yesterday
            this.updateCategoryPage(this.categoryPage)
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const categoryMetricsCancelTokenSource = axios.CancelToken.source();
                const insightMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [categoryMetricsCancelTokenSource, insightMetricsCancelTokenSource];

                const baseFunc = this.yesterday ? api.fetchYesterdayCategoryMetrics : api.fetchCategoryMetrics
                let categoryMetrics = ["gmv", "aov_contribution", "total_items_sold", "unique_carts"].map((metric) =>
                    baseFunc({
                        metric,
                        city: this.computedCityName.cityName,
                        cancelToken: categoryMetricsCancelTokenSource.token
                    })
                )

                let insightMetrics = api.fetchCategoryInsightMetrics({ city: this.computedCityName.cityName, yesterday_metric: this.yesterday, cancelToken: insightMetricsCancelTokenSource.token })

                let [categoryMetricsResponse, insightsMetricsResponse] = await Promise.all([
                    Promise.all(categoryMetrics),
                    insightMetrics
                ]);

                if (categoryMetricsResponse) {
                    let metricData = {}
                    categoryMetricsResponse.map((response) => {
                        let data = response.data

                        for (const [key, value] of Object.entries(data)) {
                            if (isEmpty(value?.[0]?.data)) return
                            if (metricData.hasOwnProperty(key)) {
                                metricData[key] = [
                                    ...metricData[key],
                                    ...value
                                ]
                            } else {
                                metricData[key] = [...value]
                            }
                        }
                    })

                    let allMetrics = Object.entries(metricData).map(([key, value]) => {
                        return { category_name: key, data: value }
                    })

                    this.allMetrics = this.sortviaAOV(allMetrics)
                }

                if (insightsMetricsResponse) this.insightMetrics = insightsMetricsResponse.data;

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        refreshFiltersList() {
            fetchStoreToCityMapping();
        },


        onCityChange(event) {
            this.isLoading = true;
            this.isError = false;
            this.resetAllMetrics();
            this.fetchAllMetrics();
            this.categoryPage['selectedCity'] = this.selectedCity
            this.updateCategoryPage(this.categoryPage)
        },

        resetAllMetrics() {
            this.allMetrics = {}
            this.insightMetrics = []
        },

        sortviaAOV(metrics) {
            const today = new Date(new Date().getTime()).toISOString().split('T')[0];
            const yesterday = new Date(new Date().getTime() - 864e5).toISOString().split('T')[0];

            const selectedDate = this.yesterday ? yesterday : today

            return metrics.sort((a, b) => {
                const { data: aData = [] } = a || {}
                const { data: bData = [] } = b || {}

                const metricA = aData?.find((item) => item?.name === 'AOV Contribution')?.data?.find((itemData) => itemData?.date === selectedDate)?.metric ?? 0
                const metricB = bData?.find((item) => item?.name === 'AOV Contribution')?.data?.find((itemData) => itemData?.date === selectedDate)?.metric ?? 0

                if (metricA > metricB) return -1
                if (metricA < metricB) return 1
                return 0
            })
        },

        isNotEmpty(val) {
            return !isEmpty(val)
        },

        getMetricValues(data, metricName) {
            let calculatedMetrics = data.map(({ category_name = '', metrics = [] }) => {
                const metricValue = metrics.find((metric) => metric.name === metricName)
                return { ...metricValue, name: category_name }
            })
            return { metricName, metrics: calculatedMetrics }
        },

        handleMetricClick(metricName, l0Category) {
            if (!this.showL1DataPopUp.includes(metricName)) return
            this.selectedPopUpMeta = { metricName, l0Category }
            this.showL1PopUp = true
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateCategoryPage']),
    },

    data() {
        return {
            showHeader: false,
            isLoading: true,
            isError: false,
            intervalId: null,
            cancelTokens: [],
            canAccessPage: false,
            yesterday: false,
            categoryPage: {},
            selectedCity: null,
            mappedList: {},
            isGlobalUser: false,
            isCityUser: false,
            allMetrics: {},
            insightMetrics: [],
            NO_SKIP_DIFFS: ["FE Inventory", "FE Availability", "Search Impressions", "Search Conv %", "Unique Impressions"],
            showL1PopUp: false,
            selectedPopUpMeta: {
                metricName: "",
                l0Category: ""
            }
        }
    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[18]);
        if (!this.canAccessPage) noPermissionRoute(true)

        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (!this.getStoreToCityMapping.length) {
            this.refreshFiltersList();
        }
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = true;
            this.isError = false;

            this.categoryPage = this.getCategoryPage
            this.yesterday = this.categoryPage?.yesterday || false


            if (this.categoryPage?.selectedCity) this.selectedCity = this.categoryPage?.selectedCity
            else if (this.isGlobalUser) this.selectedCity = PanIndiaConstants.PAN_INDIA.code;
            else this.selectedCity = Object.keys(this.getStorePageCityMapping)[0]

            this.fetchAllMetrics();
            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}

</script>