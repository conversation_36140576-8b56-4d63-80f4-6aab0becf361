<template>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
            <div class="text-3xl font-bold text-gray-900">City: {{ computedAppliedCityName && computedAppliedCityName.displayName ?
                computedAppliedCityName.displayName : this.selectedCity }}</div>
            <div class="text-3xl font-bold text-gray-900" v-if="computedAppliedStore">Store: {{
                computedAppliedStore.merchant_name_id }} </div>
        </div>
    </header>


    <!-- Mobile Filter Bottom Sheet -->
    <div class = "max-w-7xl mx-auto px-4 py-6">
    <MobileFilterBottomSheet ref="mobileFilterBottomSheet" v-if="isMobile" :filterSummary="mobileFilterSummary"
      :activeFiltersCount="activeFiltersCount" :hasActiveFilters="hasActiveFilters"
     @open="onMobileFilterOpen" @close="onMobileFilterClose"
      @apply="onMobileFilterApply" @clear-all="onMobileFilterClearAll">

      <template #filters>

        <!-- City/Zone/Store Filters (Mobile) -->
          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-map-marker mr-2"></i>City
            </label>
            <v-select :reduce="city => city.code" v-model="selectedCity" :options="computedCityList"
              label="name" :clearable="false" placeholder="Select city" @update:model-value="onMobileCityChange($event)"
              class="w-full filter-select bg-white rounded-xl shadow-md" :styles="mobileSelectStyles" />
          </div>

          <!-- Store Type Filter (Mobile) -->
          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
                <i class="pi pi-building mr-2"></i>Store Type
            </label>
            <v-select v-model="selectedStoresType" :options="storeTypeOptions" 
                @update:model-value="onMobileStoreTypeChange($event)"
                :clearable="false" placeholder="Select store type"
                class="w-full filter-select bg-white rounded-xl shadow-md" :styles="mobileSelectStyles" />
        </div>

         <!-- Store Filter (Mobile) -->
         <div class="space-y-3">
          <label class="block text-sm font-semibold text-yellow-600">
            <i class="pi pi-shopping-cart mr-2"></i>Store
          </label>
          <v-select v-model="selectedStore" :options="computedStoreList" label = "merchant_name_id"
            @update:model-value="onMobileStoreChange($event)"
             :clearable="false" placeholder="Select store"
            class="w-full filter-select bg-white rounded-xl shadow-md" :styles="mobileSelectStyles" />
        </div>

        <!-- Date Filter (Mobile) -->
          <div class="space-y-3">
          <label class="block text-sm font-semibold text-yellow-600">
            <i class="pi pi-calendar mr-2"></i>Date
          </label>
          <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single"
            :maxDate="new Date()" :minDate="computedMinDate" showButtonBar class="w-full blinkit-calendar"
            :inputStyle="{ height: '40px', borderRadius: '0.75rem', borderColor: '#975A16', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }"
            dateFormat="yy-mm-dd" />
        </div>
      </template>
    </MobileFilterBottomSheet>
    </div>

    <!-- Desktop Filter Grid -->

    <div v-if="!isMobile"
        class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10">
        <div>
            <div class="text-left pb-2 font-semibold italic">City :</div>
            <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
                placeholder="click to select city" class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Store :</div>
            <v-select v-model="selectedStoresType" :options="storeTypeOptions"
                @update:model-value="storeTypeChanged($event)" :clearable="false"
                placeholder="click to select store type" class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Store :</div>
            <v-select v-model="selectedStore" :options="computedStoreList" label="merchant_name_id"
                @update:model-value="onStoreChange($event)" :clearable="false" placeholder="click to select store"
                class="w-full bg-white" />
        </div>
        <div>
            <div class="text-left pb-2 font-semibold italic">Date :</div>
            <Calendar v-model="selectedDate" showIcon iconDisplay="input" selectionMode="single" :maxDate="new Date()"
                :minDate="computedMinDate" showButtonBar inputClass="w-full" panelClass="p-1" class="w-full"
                :inputStyle="{ height: '36px', marginBottom: '4px' }" @update:model-value="onDateChange($event)"
                dateFormat="yy-mm-dd" />
        </div>
            <div class="flex flex-row justify-center items-center sm:mx-auto mt-3 mb-4">
        <div class="flex flex-row mx-8">
            <Button label="Apply" severity="contrast" @click="loadMetrics" class="px-10 py-3" />
        </div>
        <Button text @click="refreshList" class="pi pi-refresh text-black"
            v-tooltip.top="{ value: 'Refresh Filters List' }" />
    </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>
    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{ this.selectedCity }}
            city is invalid or some internal error occured.<br />Please select another city or reach out to data team at
            <EMAIL> or #bl-data-support slack channel.</span>
    </div>
    <main v-else>
        <WaterMark></WaterMark>
        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
        <MetricGroup v-for="(metricGroup, key, idx) in computedInstoreMetricBreakdown"
            :key="metricGroup.key" :isDefaultOpen="metricGroup.isDefaultOpen" :title="metricGroup.title"
            :icon="metricGroup?.icon" :metricGroup="metricGroup"
            :cityCode="computedAppliedCityName?.cityName"
            :storeCode="computedAppliedStore?.frontend_merchant_id"
            :storeType="computedAppliedStoreType"
            :selectedDate="computedAppliedDate"
            :showHeader="showHeader" :idx="idx"  :openAccordions="metricGroupOpenAccordions"
            :extraMetricsMapping="computedExtraMetricsMapping"
            @showExtraMetrics="showExtraMetrics" @metricsUpdated="updateMetricGroupMetrics"
            @updateOpenAccordion="updateOpenMetricGroupAccordion"
            @updateClosedAccordion="updateClosedMetricGroupAccordion" />

                <Dialog v-model:visible="visible" modal :header="extraMetricKey" :style="{ width: '45rem' }"
                    :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :dismissableMask="true">
                    <div>
                        <div v-for="metric in computedExtraMetrics" :key="metric.name"
                            class="md:bg-white py-1.5 md:py-4 border-b md:border-b-0 md:rounded ">
            <div class="flex justify-between">
              <span
                                    class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
                                        metric.name }}
                                    <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
              </span>
                                <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                                    :type="metric.curr.type" />
            </div>
            <div class="flex justify-between px-2">
              <span class="text-xs font-light gray-900"></span>
              <div class="flex justify-between">
                <div v-for="diff in metric.diffs" :key="diff.date">
                                        <span class="text-xs font-[650] mr-0.5"
                                            v-bind:class="diff.style(metric.reverseStyle)">
                    <template v-if="diff.change() !== '-'">
                      {{ diff.change() }} %
                    </template>
                                            <template v-else>
                                                -
                                            </template>
                  </span>
                                        <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                            class="text-xs font-bold mr-1 text-positive-metric"> »
                  </span>
                </div>
              </div>
            </div>
          </div>
          </div>
        </Dialog>
        </div>
        
        <CriticalMetrics 
            :cityCode="appliedFilters.city"
            :storeCode="computedAppliedStore?.frontend_merchant_name"
            :cityName="computedAppliedCityName.cityName"
            :storeType="computedAppliedStoreType"
            :selectedDate="computedAppliedDate"
            :isNotToday="computedAppliedIsNotToday"

        />
        <div class="gap-3 mt-8 max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <div class="mt-2 md:mt-4 text-center md:text-left">
                <h2 class="text-sm md:text-lg font-bold text-gray-900 flex items-center gap-2">
                <i class="pi pi-chart-line"></i>
                Hourly Trends
                </h2>
            </div>

            <HourlyMetricsV2 
            :cityCode="computedAppliedCityName?.cityName"
            :storeCode="computedAppliedStore?.frontend_merchant_id" 
            :storeType="computedAppliedStoreType" 
            :selectedDate="computedAppliedDate"
            :showHeader="showHeader" :openAccordions="hourlyOpenAccordions" 
            @updateOpenAccordion="updateOpenHourlyAccordion"
            @updateClosedAccordion="updateClosedHourlyAccordion" />
        </div>
    </main>

    <MobileFloatingButtons :isMobile="isMobile" :hasActiveFilters="hasActiveFilters"
    :activeFiltersCount="activeFiltersCount" @openFilters="openMobileFilterFromFloatingButton" />
</template>

<script>

import Button from 'primevue/button';
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import Table from "../../components/Table.vue";
import { MetricChange } from "../../interfaces/";
import {
    cityMetrics, hourlyMetrics,
} from "../../utils/metrics.js";
import { getUserAccessMapping, getUserAllowedStoresMapping, getPanIndiaStoreCityList, isCityUser, isGlobalUser, noPermissionRoute, formatDateToString, isDateToday, cancelPreviousRequests } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users';
import { useCityStore } from '../../stores/cities';
import vSelect from 'vue-select';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import { MERCHANT_TYPES, PanIndiaConstants } from "../../constants/index.js";
import MetricsGrid from "../../components/MetricsGrid.vue";
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { inStoreMetricGridMapping } from '../../configurations/Sonar/inStoreMetricGridMapping.js';
import Dialog from "primevue/dialog";
import Calendar from "primevue/calendar";
import { useScreenSize } from "../../composables/useScreenSize.js";
import MetricGroup from "../../components/Instore/MetricGroup.vue";
import { INSTORE_METRIC_BREAKDOWN, FLATTENED_INSTORE_HOURLY_METRICS } from "../../constants/sonar/instore.js";
import HourlyMetricsV2 from "../../components/Instore/HourlyMetricsV2.vue";
import CriticalMetrics from "../../components/Instore/CriticialMetrics.vue";
import Tooltip from 'primevue/tooltip';
import MobileFilterBottomSheet from '../../components/Common/MobileFilterBottomSheet.vue';
import MobileFloatingButtons from '../../components/Common/MobileFloatingButtons.vue';
import { metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
export default {

    components: {
        Loading,
        AnimatedNumber,
        WaterMark,
        'v-select': vSelect,
        Table,
        TabView,
        TabPanel,
        MetricsGrid,
        InfoDialog,
        Button,
        Dialog,
        Calendar,
        MetricGroup,
        HourlyMetricsV2,
        CriticalMetrics,
        MobileFilterBottomSheet,
        MobileFloatingButtons
    },

    directives: {
        tooltip: Tooltip
    },

    computed: {
        computedCityList() {
            let staticList = [PanIndiaConstants.PAN_INDIA];
            let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
            cityList = cityList.sort(function (a, b) {
                if (a.name < b.name) { return -1; }
                if (a.name > b.name) { return 1; }
                return 0;
            });

            if (this.isGlobalUser) cityList = staticList.concat(cityList);
            return cityList;
        },

        computedCityName() {
            let modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;
            if (modifiedCityMapping[this.selectedCity] == undefined) return;
            let cityName = modifiedCityMapping[this.selectedCity]["name"];
            return {
                displayName: modifiedCityMapping[this.selectedCity]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },
        computedAppliedCityName() {
            if (isEmpty(this.appliedFilters) || !this.appliedFilters.city) return null;
            
            let modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;
            if (modifiedCityMapping[this.appliedFilters.city] == undefined) return null;
            let cityName = modifiedCityMapping[this.appliedFilters.city]["name"];
            
            return {
                displayName: modifiedCityMapping[this.appliedFilters.city]["name"],
                cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
            }
        },

        computedInstoreMetricBreakdown() {
            return INSTORE_METRIC_BREAKDOWN;
        },

        computedStore() {
            let modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;
            if (modifiedCityMapping[this.selectedCity] == undefined) return;
            return this.selectedStore;
        },

        computedAppliedStore() {
            let modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;
            if (modifiedCityMapping[this.appliedFilters.city] == undefined) return;
            return this.appliedFilters.store;
        },
        computedAppliedStoreType() {        
            return this.appliedFilters.storeType === MERCHANT_TYPES.all ? null : this.appliedFilters.storeType;
        },


        computedCityMetrics() {
            return cityMetrics(this.cityMetrics, "Picking Time Per Item");
        },

        computedStoreMetrics() {
            return cityMetrics(this.storeMetrics, "Picking Time Per Item");
        },

        computedCityActivityMetrics() {
            return cityMetrics(this.cityActivityMetrics, "OPH");
        },

        computedStoreActivityMetrics() {
            return cityMetrics(this.storeActivityMetrics, "OPH");
        },

        computedHourlyOrderMetrics() {
            let orderCountMetric = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Cart Volume", this.computedDate);
            let percentInstoreSla = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Instore SLA % < 2.5 mins", this.computedDate);
            let percentPPI = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Picking Time Per Item", this.computedDate);
            let percentC2AMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "%Picker assigned 10 secs", this.computedDate);
            let orderCountPickerSurge = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Picker Surge Orders %", this.computedDate);
            let orderCountPickerPlusRiderSurge = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Both Surge Orders %", this.computedDate);
            let fillRate = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Fill Rate %", this.computedDate);
            let qtyPicked = hourlyMetrics(this.hourlyOrderMetrics, true, [], "IPO", this.computedDate);
            let percentA2SMetrics = hourlyMetrics(this.hourlyOrderMetrics, true, [], "Picking Start % (5 secs)", this.computedDate);
            return {
                "orderCountMetric": orderCountMetric,
                "percentInstoreSla": percentInstoreSla,
                "percentPPI": percentPPI,
                "percentC2AMetrics": percentC2AMetrics,
                "orderCountPickerSurge": orderCountPickerSurge,
                "orderCountPickerPlusRiderSurge": orderCountPickerPlusRiderSurge,
                "fillRate": fillRate,
                "qtyPicked": qtyPicked,
                "percentA2SMetrics": percentA2SMetrics
            }
        },

        computedHourlyActiveTimeMetrics() {
            let totalActiveHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "Total Active Hrs", this.computedDate);
            let pickerActiveHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "Picker Active Time %", this.computedDate);
            let putterActiveHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "Putter Active Time %", this.computedDate);
            let auditorActiveHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "Auditor Active Time %", this.computedDate);
            let fnvActiveHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "FNV Active Time %", this.computedDate);
            let ophHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "OPH", this.computedDate);
            let iphHourMetric = hourlyMetrics(this.hourlyActiveTimeMetrics, true, [], "IPH", this.computedDate);
            return {
                "totalActiveHourMetric": totalActiveHourMetric,
                "pickerActiveHourMetric": pickerActiveHourMetric,
                "putterActiveHourMetric": putterActiveHourMetric,
                "auditorActiveHourMetric": auditorActiveHourMetric,
                "fnvActiveHourMetric": fnvActiveHourMetric,
                "ophHourMetric": ophHourMetric,
                "iphHourMetric": iphHourMetric
            }
        },

        computedHourlyComplaintsMetrics() {
            let totalComplaintsMetric = hourlyMetrics(this.hourlyCompaintsMetrics, true, [], "Complaints %", this.computedDate);
            return {
                "totalComplaintsMetric": totalComplaintsMetric
            }
        },


        computedMetricConfig() {
            return inStoreMetricGridMapping(this);
        },

        storeTypeOptions() {
            return [MERCHANT_TYPES.all, MERCHANT_TYPES.blinkit, MERCHANT_TYPES.merchant]
        },

        computedAllStoreList() {
            return this.selectedCityStores;
        },

        computedStoreList() {
            if (this.selectedStoresType === MERCHANT_TYPES.blinkit) {
                return this.computedAllStoreList?.filter((store) => store.merchant_type === MERCHANT_TYPES.blinkit)
            }
            else if (this.selectedStoresType === MERCHANT_TYPES.merchant) {
                return this.computedAllStoreList?.filter((store) => store.merchant_type === MERCHANT_TYPES.merchant)
            }
            return this.computedAllStoreList;
        },

        computedStoreType() {
            return this.selectedStore.merchant_type === MERCHANT_TYPES.all ? null : this.selectedStore.merchant_type;
        },

        computedExtraMetricsMapping() {
            return { "Total Active Hrs": ["On-Demand Active Hours"] };
        },

        computedExtraMetrics() {
            const metricsToShow = this.computedExtraMetricsMapping[this.extraMetricKey];
            const metrics = this.allMetricGroupMetrics.filter((metric) => metricsToShow.includes(metric.name))
            return this.calculatedBlockMetrics(metrics)
        },


        computedDate() {
            return formatDateToString(this.selectedDate);
        },

        computedAppliedDate() {
            return formatDateToString(this.appliedFilters.date);
        },

        computedIsNotToday() {
            return !isDateToday(this.selectedDate);
        },

        computedAppliedIsNotToday() {
            return isEmpty(this.appliedFilters) ? true : !isDateToday(this.appliedFilters.date);
        },

        computedMinDate() {
            var day = new Date();
            day.setDate(day.getDate() - 25);
            return day;
        },

        computedInvalidDate() {
            return isEmpty(this.selectedDate)
        },

        mobileFilterSummary() {
            const summary = [];

            const currentSelectedCity = this.appliedFilters.city;
            const currentSelectedStoresType = this.appliedFilters.storeType;
            const currentSelectedStore = this.appliedFilters.store;
            const currentSelectedDate = this.appliedFilters.date;

            if (currentSelectedCity) {
                const modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;
                const cityMapping = modifiedCityMapping?.[currentSelectedCity];
                const cityName = cityMapping?.name || currentSelectedCity;
                summary.push({
                    key: 'city',
                    label: 'City',
                    value: cityName
                });
            }

            if (currentSelectedStoresType && currentSelectedStoresType !== MERCHANT_TYPES.all) {
                summary.push({
                    key: 'storeType',
                    label: 'Store Type',
                    value: currentSelectedStoresType
                });
            }

            if (currentSelectedStore && currentSelectedStore.merchant_name_id) {
                summary.push({
                    key: 'store',
                    label: 'Store',
                    value: currentSelectedStore.merchant_name_id
                });
            }
            if (currentSelectedDate) {
                // Format date without timezone conversion to avoid T-1 issues
                const year = currentSelectedDate.getFullYear();
                const month = String(currentSelectedDate.getMonth() + 1).padStart(2, '0');
                const day = String(currentSelectedDate.getDate()).padStart(2, '0');
                const dateStr = `${year}-${month}-${day}`;
                summary.push({
                    key: 'date',
                    label: 'Date',
                    value: dateStr
                });
            }

            return summary;
        },


        mobileSelectStyles() {
        return {
            control: (styles) => ({
            ...styles,
            borderColor: '#975A16',
            borderRadius: '0.75rem',
            padding: '4px',
            boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
            zIndex: 10001
            }),
            option: (styles, { isFocused }) => ({
            ...styles,
            backgroundColor: isFocused ? '#FDF6B2' : null,
            padding: '8px',
            zIndex: 10002
            }),
            menu: (styles) => ({
            ...styles,
            zIndex: 10002
            })
        }
        },

        activeFiltersCount() {
            return this.mobileFilterSummary.length;
        },

        hasActiveFilters() {
            return this.activeFiltersCount > 0;
        },



        ...mapState(useCityStore, ['getInstore', 'getStorePageCityMapping', 'getStoreCityList', 'getStoreToCityMapping']),
        ...mapState(useUserStore, ['user', 'isLoggedIn', 'getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {

        calculatedBlockMetrics(metrics) {
            const sortedMetrics = sortedAllMetrics(metrics, undefined, true, 5, this.computedDate);
            return sortedMetrics.map(metric => {
                let data = metric.data;

                // Normalize data structure - convert 'metric' to 'value' and add 'type'
                let normalizedData = data;
                if (data.length > 0 && data[0].metric !== undefined && data[0].value === undefined) {
                    normalizedData = data.map(item => ({
                        ...item,
                        value: item.metric,
                        type: metric.type || 'number'
                    }));
                }
                let curr = normalizedData[normalizedData.length - 1];

                let diffs = [];
                for (let j = 0; j < normalizedData.length - 1; j++) {
                    let prev = normalizedData[j];
                    diffs.push(new MetricChange(curr, prev));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },

        checkRoute() {
            this.showHeader = !this.isMobile;
        },
        
        loadMetrics() {
            this.isLoading = false;
            this.isError = false;

            this.appliedFilters = {
                city: this.selectedCity,
                store: this.selectedStore,
                date: this.selectedDate,
                storeType: this.selectedStore?.merchant_type || this.selectedStoresType
            };
            this.selectedStore["selectedDate"] = this.selectedDate;
            this.updateInstore({
                ...this.selectedStore,
                metricGroupOpenAccordions: this.metricGroupOpenAccordions, hourlyOpenAccordions: this.hourlyOpenAccordions
            });

            this.resetAllMetrics();
        },

        onCityChange(event) {
            this.isError = false;
            this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
            this.selectedStore = this.computedStoreList[0];
            this.selectedStore["selectedDate"] = this.selectedDate;
            this.updateInstore(this.selectedStore);
        },

        onStoreChange(event) {
            this.isError = false;
            this.selectedStore["selectedDate"] = this.selectedDate;
            this.updateInstore(this.selectedStore);
        },

        onDateChange(event) {
            this.isError = false;
            this.selectedStore["selectedDate"] = this.selectedDate;
            this.updateInstore(this.selectedStore);
        },

        onMobileFilterOpen() {
            // Ensure city stores are loaded for the current city
            if (this.selectedCity && this.userAllowedStoresMapping[this.selectedCity]) {
                this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
            }
            
            // Ensure we have valid defaults
            if (!this.selectedDate) {
                this.selectedDate = new Date();
            }
            if (!this.selectedStoresType) {
                this.selectedStoresType = MERCHANT_TYPES.all;
            }
        },

        onMobileFilterApply() {
            this.loadMetrics();
        },


        onMobileFilterClose() {
           // Reset form state to applied filters when user closes without applying
           if (this.appliedFilters?.city) {
                this.selectedCity = this.appliedFilters.city;
            }
            if (this.appliedFilters?.storeType) {
                this.selectedStoresType = this.appliedFilters.storeType;
            }
            if (this.appliedFilters?.store) {
                this.selectedStore = this.appliedFilters.store;
            }
            if (this.appliedFilters?.date) {
                this.selectedDate = this.appliedFilters.date;
            }

            // Update city stores based on reverted city
            if (this.selectedCity && this.userAllowedStoresMapping[this.selectedCity]) {
                this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
            }
        }, 

        onMobileCityChange(event) {
            this.onCityChange(event);
        },

        onMobileStoreChange(event) {
            this.onStoreChange(event);
        },

        onMobileStoreTypeChange(event) {
            this.storeTypeChanged(event);
        },

        onMobileFilterClearAll() {
            // Reset form state to defaults
            this.selectedCity = this.isGlobalUser ? PanIndiaConstants.PAN_INDIA.code : Object.keys(this.getStorePageCityMapping)[0];
            this.selectedStoresType = MERCHANT_TYPES.all;
            this.selectedDate = new Date();
            
            // Update city stores and reset store
            if (this.userAllowedStoresMapping[this.selectedCity]) {
                this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
                this.selectedStore = this.selectedCityStores[0];
            }
        },

        filterMetricsWithNoData(metrics) {
            return metrics.filter(metric => metric.data.length > 0);
        },

        refreshList() {
            fetchStoreToCityMapping();
        },

        handleCancelPreviousRequests() {
            cancelPreviousRequests({ cancelTokens: this.cancelTokens })
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            this.isLoading = false;
            this.userAllowedStoresMapping = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            // Iterate over each key in userAllowedStoresMapping to add #Pan-India in dropdown
            let storeList = this.userAllowedStoresMapping;
            storeList[PanIndiaConstants.PAN_INDIA.code] = [];

            let storeTypeDict = { 'Overall': MERCHANT_TYPES.all, 'Overall Blinkit': MERCHANT_TYPES.blinkit, 'Overall Merchant': MERCHANT_TYPES.merchant }

            for (let cityName in storeList) {
                if (storeList.hasOwnProperty(cityName)) {
                    let cityStores = storeList[cityName];

                    if (((this.getStoreToCityMapping && this.getStoreToCityMapping?.filter((item) => item?.city === (this.getStoreCityList?.filter((item) =>
                        // item?.code === cityName)?.[0]?.name)))?.[0]?.data?.length === cityStores?.length # CHANGE TO THIS AFTER MAPPING GETS CORRECTED
                        item?.code === cityName)?.[0]?.name)))?.[0]?.data?.length <= cityStores?.length
                        || cityName === PanIndiaConstants.PAN_INDIA.code)) {

                        ['Overall Merchant', 'Overall Blinkit', 'Overall'].map((storeType) => {
                            let panIndiaStore = {
                                frontend_merchant_id: '',
                                frontend_merchant_name: storeType,
                                backend_merchant_id: '',
                                name: cityName,
                                merchant_name_id: storeType,
                                merchant_type: storeTypeDict[storeType]
                            }

                            cityStores.unshift(panIndiaStore);
                        })
                    }
                }
            }
            this.selectedCityStores = storeList[this.selectedCity];
            if (this.selectedStore == null) {
                this.selectedStore = this.selectedCityStores[0];
            }
            this.fetchAllMetrics();
        },


        async fetchAllMetrics() {
            this.handleCancelPreviousRequests();

            try {
                const allOrderMetricsCancelTokenSource = axios.CancelToken.source();
                const allTrueFillRateMetricsCancelTokenSource = axios.CancelToken.source();
                const allDauMetricsCancelTokenSource = axios.CancelToken.source();
                const allActiveTimeMetricsCancelTokenSource = axios.CancelToken.source();
                const allComplaintsMetricsCancelTokenSource = axios.CancelToken.source();
                const hourlyMetricsCancelTokenSource = axios.CancelToken.source();
                const ActiveTimehourlyMetricsCancelTokenSource = axios.CancelToken.source();
                const complaintsHourlyMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [
                    allOrderMetricsCancelTokenSource, allTrueFillRateMetricsCancelTokenSource, allDauMetricsCancelTokenSource, allActiveTimeMetricsCancelTokenSource,
                    allComplaintsMetricsCancelTokenSource, hourlyMetricsCancelTokenSource, ActiveTimehourlyMetricsCancelTokenSource, complaintsHourlyMetricsCancelTokenSource
                ];

                if (this.computedIsNotToday && !this.computedDate) {
                    this.isError = true;
                    throw new Error("Invalid date");
                }

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                            let status = error.response.status;
                            if (status === 403) {
                                this.logout();
                        console.log("Unauthenticated")
                                this.$router.replace('/login');
                        }
                }
            };
        },

        async getGroupsAndCitiesList() {
            this.isGroupLoading = true;

            await api.fetchCitiesAndGroups().then((response) => {
                this.groupsAndCitiesList = response.data;
            }).catch(e => {
                console.log("Error fetching groups: ", e);
                this.isGroupLoading = false;
            })
            this.isGroupLoading = false;
        },

        updateMetricGroupMetrics(metrics) {
             const newMetrics = [...metrics];
             const metricNames = newMetrics.map(m => m.name);
             this.allMetricGroupMetrics = this.allMetricGroupMetrics.filter(m => !metricNames.includes(m.name));
             this.allMetricGroupMetrics = [...this.allMetricGroupMetrics, ...newMetrics];
        },

        updateOpenMetricGroupAccordion(key) {
            this.metricGroupOpenAccordions.push(key)

            this.pageLocalStorage["metricGroupOpenAccordions"] = this.metricGroupOpenAccordions;
            const currentStore = this.getInstore || {};
            this.updateInstore({
                ...currentStore,
                metricGroupOpenAccordions: this.metricGroupOpenAccordions,
                hourlyOpenAccordions: this.hourlyOpenAccordions
            });
        },
        updateClosedMetricGroupAccordion(key) {
            this.metricGroupOpenAccordions = this.metricGroupOpenAccordions.filter((item) => item !== key);

            this.pageLocalStorage["metricGroupOpenAccordions"] = this.metricGroupOpenAccordions;
            const currentStore = this.getInstore || {};
            this.updateInstore({
                ...currentStore,
                metricGroupOpenAccordions: this.metricGroupOpenAccordions,
                hourlyOpenAccordions: this.hourlyOpenAccordions
            });
        },

        updateOpenHourlyAccordion(key) {
            if (!this.hourlyOpenAccordions.includes(key)) {
                this.hourlyOpenAccordions.push(key);
            }
            this.pageLocalStorage["hourlyOpenAccordions"] = this.hourlyOpenAccordions;
            const currentStore = this.getInstore || {};
            this.updateInstore({
                ...currentStore,
                metricGroupOpenAccordions: this.metricGroupOpenAccordions,
                hourlyOpenAccordions: this.hourlyOpenAccordions
            });
        },

        updateClosedHourlyAccordion(key) {
            this.hourlyOpenAccordions = this.hourlyOpenAccordions.filter((item) => item !== key);
            this.pageLocalStorage["hourlyOpenAccordions"] = this.hourlyOpenAccordions; 
            const currentStore = this.getInstore || {};
            this.updateInstore({
                ...currentStore,
                metricGroupOpenAccordions: this.metricGroupOpenAccordions,
                hourlyOpenAccordions: this.hourlyOpenAccordions
            });
        },

        resetAllMetrics() {
            this.collectAllMetrics = []
            this.hourlyOrderMetrics = []
            this.hourlyActiveTimeMetrics = []
            this.hourlyCompaintsMetrics = []
        },

        storeTypeChanged() {
            this.isError = false;

            if (this.selectedStoresType === MERCHANT_TYPES.blinkit) {
                this.selectedStore = this.computedAllStoreList.find((store) => store.merchant_type === MERCHANT_TYPES.blinkit)
            }
            else if (this.selectedStoresType === MERCHANT_TYPES.merchant) {
                this.selectedStore = this.computedAllStoreList.find((store) => store.merchant_type === MERCHANT_TYPES.merchant)
            }
            else this.selectedStore = this.computedAllStoreList.find((store) => store.merchant_type === MERCHANT_TYPES.all);

            this.updateInstore(this.selectedStore);
        },


        hasExtraMetrics(metricName) {
            let keysWithExtraMetrics = Object.keys(this.computedExtraMetricsMapping).map((metric) => metric)
            if (keysWithExtraMetrics.includes(metricName)) return true;
            return false;
        },

        showExtraMetrics(metricName) {
            if (this.hasExtraMetrics(metricName)) {
                this.extraMetricKey = metricName;
                this.visible = true;
            }
            return;
        },

        // Floating button methods
        openMobileFilterFromFloatingButton() {
        // Call the mobile filter bottom sheet's openBottomSheet method directly
        if (this.$refs.mobileFilterBottomSheet) {
            this.$refs.mobileFilterBottomSheet.openBottomSheet();
        }
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateInstore', 'updateStoreToCityMapping']),
    },

    data() {
        return {
            showHeader: false,
            selectedCity: "",
            isLoading: true,
            collectAllMetrics: [],
            hourlyOrderMetrics: [],
            hourlyActiveTimeMetrics: [],
            hourlyCompaintsMetrics: [],
            userAllowedStoresMapping: {},
            selectedCityStores: [],
            selectedStore: null,
            isError: false,
            intervalId: null,
            cityMetrics: [],
            storeMetrics: [],
            cityActivityMetrics: [],
            storeActivityMetrics: [],
            isGlobalUser: false,
            isCityUser: false,
            cancelTokens: [],
            canAccessPage: false,
            selectedStoresType: null,
            extraMetricKey: "",
            visible: false,
            selectedDate: null,
            metricGroupOpenAccordions: [],
            allMetricGroupMetrics: [],
            pageLocalStorage: [],
            extraMetricKey: "",
            visible: false,
            hourlyOpenAccordions: [],
            appliedFilters: {},
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        },

    },

    beforeMount() {
        this.userAccessMapping = getUserAccessMapping();
        if (isEmpty(this.userAccessMapping)) {
            this.$router.replace('/login');
        }

        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[4]);
        if (!this.canAccessPage) noPermissionRoute()

        this.isGlobalUser = isGlobalUser(this.userAccessMapping);
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (!this.getStoreToCityMapping.length) {
            this.refreshList();
        }
    },

    mounted() {
        if (this.canAccessPage) {
            this.isLoading = false;
            this.isError = false;
            this.selectedStore = this.getInstore;
            this.pageLocalStorage = this.getInstore || [];

            // Restore filter values from the stored state
            if (this.selectedStore) {
                this.selectedDate = isEmpty(this.selectedStore?.selectedDate) ? new Date() : new Date(this.selectedStore?.selectedDate);
                
                if (this.isGlobalUser) {
                    this.selectedCity = this.selectedStore["name"] || PanIndiaConstants.PAN_INDIA.code;
                } else {
                    this.selectedCity = this.selectedStore["name"] || Object.keys(this.getStorePageCityMapping)[0];
                }
                this.selectedStoresType = this.selectedStore?.merchant_type || MERCHANT_TYPES.all;
            } else {
                // Set defaults if no stored state
                this.selectedDate = new Date();
                if (this.isGlobalUser) {
                    this.selectedCity = PanIndiaConstants.PAN_INDIA.code;
                } else {
                    this.selectedCity = Object.keys(this.getStorePageCityMapping)[0];
                }
                this.selectedStoresType = MERCHANT_TYPES.all;
            }
            this.fetchCityStoreMapping();
            this.checkRoute();

            this.appliedFilters = {
                city: this.selectedCity,
                store: this.selectedStore,
                date: this.selectedDate,
                storeType: this.selectedStoresType
            };

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })

            // Initialize accordion states
            this.metricGroupOpenAccordions = isEmpty(this.pageLocalStorage?.metricGroupOpenAccordions) ? [Object.values(INSTORE_METRIC_BREAKDOWN).find((item) => item?.isDefaultOpen)?.title] : this.pageLocalStorage?.metricGroupOpenAccordions;
            this.hourlyOpenAccordions = isEmpty(this.pageLocalStorage?.hourlyOpenAccordions) ? FLATTENED_INSTORE_HOURLY_METRICS.filter((item) => item?.isDefaultOpen).map((item) => item?.metricKey) : this.pageLocalStorage?.hourlyOpenAccordions;
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.handleCancelPreviousRequests();
    }
}

</script>
