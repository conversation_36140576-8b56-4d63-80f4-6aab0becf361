<template>
<div v-if="!isGlobalUser" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
  <WaterMark></WaterMark>
  <span class="text-1xl font-bold text-red-700 m-auto">YOU DO NOT HAVE ACCESS TO AVAILABILITY PAGE</span>
</div>
<div v-else="isGlobalUser">
  <div>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
        <div class="text-3xl font-bold text-gray-900">Availability</div>
      </div>
    </header>
    <div v-if="isLoading">
      <Loading />
    </div>
    <main v-else>
      <WaterMark></WaterMark>
      <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mb-24 mt-8">
        <span class="text-lg font-bold text-blue-900">Availability Metrics - Cities</span>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 mt-4">
          <MetricBox v-for="(hourlyMetric, index) in computedAvailabilityMetrics" :key="index" :selected="index ==  selected" :name="hourlyMetric.city" :metrics="hourlyMetric.metrics" />
        </div>
        <div v-if="selected >= 0">
          <div class="flex text-gray-200 my-3 justify-center">
            <hr width="80%" />
          </div>
          <div class="border w-full h-full my-3 p-2 bg-gray-50 md:bg-gray-100 border-gray-200">
            <span class="text-lg">Selected City: <strong class="underline">{{computedAvailabilityMetrics[selected].city}}</strong></span>
            <div class="flex justify-center">
              <table class="table-fixed border-collapse border border-gray-200 px-2 py-1">
                <thead>
                  <tr>
                    <th class="border border-gray-300 px-2 py-1">Name</th>
                    <th class="border border-gray-300 px-2 py-1">Current Cum. Availability</th>
                    <th class="border border-gray-300 px-2 py-1">Previous Cum. Availability</th>
                    <th class="border border-gray-300 px-2 py-1">Change %</th>
                    <th class="border border-gray-300 px-2 py-1">Today's Availability Trend %</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(metricData, index) in computedAvailabilityMetrics[selected].metrics" :key="index">
                    <td class="border border-gray-300 px-2 py-1">{{ metricData.type }}</td>
                    <td class="border border-gray-300 px-2 py-1 text-center font-medium text-sm md:text-base">{{ metricData?.curr?.value }}</td>
                    <td class="border border-gray-300 px-2 py-1 text-center font-medium text-sm md:text-base">{{ metricData?.prev?.value }}</td>
                    <td class="border border-gray-300 px-2 py-1 text-center"><AnimatedNumber :styles="getStyle(metricData.prev, metricData.curr)" :value="diff(metricData?.curr,metricData?.prev)" type="percentage"/></td>
                    <td class="border border-gray-300 px-2 py-1 text-center font-medium text-sm md:text-base">
                        <thead>
                          <tr>
                            <th class="border border-gray-300 px-2 py-1">Hour</th>
                            <th class="border border-gray-300 px-2 py-1">Weighted Availability</th>
                            <th class="border border-gray-300 px-2 py-1">Cum. Availability</th>
                          </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(metricData1, index) in metricData.today " :key="index">
                         <td class="border border-gray-300 px-2 py-1 text-center font-medium text-sm md:text-base">{{ metricData1.hour }}</td>
                         <td class="border border-gray-300 px-2 py-1 text-center font-medium text-sm md:text-base"> {{ metricData1.data[1].metric }}</td>
                         <td class="border border-gray-300 px-2 py-1 text-center font-medium text-sm md:text-base"> {{ metricData1.data[0].metric }}</td>
                         </tr>
                          </tbody>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
  </div>
</template>

<script>

import Loading from "../../components/Loading.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import MetricBox from "../../components/AvailabilityMetricBox.vue";
import { api } from "../../api";
import { Metric, MetricChange } from "../../interfaces/";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users';
import WaterMark from "../../components/WaterMark.vue";
import { getUserAccessMapping, isGlobalUser, noPermissionRoute } from "../../utils/utils.js";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

  components: {
    Loading,
    AnimatedNumber,
    MetricBox,
    WaterMark
  },

  computed: {

    computedAvailabilityMetrics() {
      let currentDayMetric = this.availabilityMetrics.find(metric => metric.date_diff == 0)
      let max_hour = Math.max.apply(Math, currentDayMetric?.data.map(function(o) { return o.hour; }))
      currentDayMetric = currentDayMetric?.data.filter((item) => {return (item.hour == max_hour) });
      let previousDayMetric = this.availabilityMetrics.find(metric => metric.date_diff == 7)?.data.filter((item) => {
        return (item.hour == max_hour)
      });

      if (!currentDayMetric && !previousDayMetric) {
        return [];
      }

      let results = [];
      currentDayMetric.map(availabilityMetric => {
        let metricsToTrack = this.availabilityMetricsToTrack(availabilityMetric.data.filter((item) => {
            return (item.name=="Cumulative Weighted Availability")
        }), availabilityMetric.type);
        if (metricsToTrack) {
            let availabilityMetricDict = results.find(metric => metric.city == availabilityMetric.city );
            if (availabilityMetricDict) {
                availabilityMetricDict.metrics.push(metricsToTrack[0])
            } else {
                results.push({
                  city: availabilityMetric.city,
                  metrics: metricsToTrack,
                  selected: false,
                });
            };
        };
      });

      if (previousDayMetric) {
          previousDayMetric.map(availabilityMetric => {
            let metricsToTrack = this.availabilityMetricsToTrack(availabilityMetric.data.filter((item) => {
                return (item.name=="Cumulative Weighted Availability")
            }), availabilityMetric.type);
            let availabilityMetricDict = results.find(metric => metric.city == availabilityMetric.city)?.metrics.find(metric => metric.type == availabilityMetric.type);
            if (availabilityMetricDict) {
                availabilityMetricDict.prev = metricsToTrack[0].curr;
                availabilityMetricDict.today = this.availabilityMetrics.find(metric => metric.date_diff == 0)?.data.filter(metric => metric.city == availabilityMetric.city  && metric.type == availabilityMetric.type)
            }
          });
      }

      /*let cartMetrics = cityMetrics(this.cityOrderMetrics, "Cart Volume");
      let sortedMetrics = results.sort((a, b) => {
        let indexOfA = cartMetrics.findIndex(metric => metric.city === a.city);
        let indexOfB = cartMetrics.findIndex(metric => metric.city === b.city);
        return indexOfA - indexOfB;
      });*/
      return results;
    },

    style() {
      return [
        new MetricChange(this.computedAvailabilityMetrics[this.selected].curr, this.computedAvailabilityMetrics[this.selected].prev).style()
      ];
    },

    ...mapState(useUserStore, ['getAllowedNavs']),
  },

  methods: {
    availabilityMetricsToTrack(metricData, type){
          return metricData.map(metric => {
            return {
              name: metric.name,
              metric: metric.metric,
              type: type,
              curr: new Metric(metric.metric*100, metric.type)
            };
          });
    },

    resetSelection() {
      this.computedAvailabilityMetrics.forEach(metrix => {
        metrix.selected = false;
      });
      this.selected = -1;
    },

    setSelected(index) {
      if (index == this.selected) {
        this.resetSelection();
        return;
      }
      this.selected = index;
    },

    diff(curr, prev) {
      if (prev?.value != 0){
        return parseFloat(new MetricChange(curr, prev).change());
      }
      return 'NaN';
    },

    getStyle(prev, curr) {
      return [
        'font-semibold',
        'text-sm',
        '',
        new MetricChange(curr, prev).style()];
    },

    fetchAllMetrics() {
      const CancelTokenSource = axios.CancelToken.source();
      this.cancelTokens = [CancelTokenSource]

       api.fetchAvailabilityMetrics(CancelTokenSource.token)
         .then(response => {
           this.availabilityMetrics = response.data.metrics;
           this.isLoading = false;
           // return api.fetchCityMetrics();
         })
         /*.then(response => {
           this.isLoading = false;
           this.cityOrderMetrics = response.data.metrics;
         })*/
         .catch(error => {
           let status = error.response.status;
           if (status === 403) {
             this.logout();
             console.log("Unauthenticated")
             this.$router.replace('/login');
           }
         });
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    ...mapActions(useUserStore, ['logout'])
  },

  data() {
    return {
      showHeader: false,
      isLoading: true,
      availabilityMetrics: [],
      cityOrderMetrics: [],
      selected: -1,
      userAccessMapping: {},
      isGlobalUser: false,
      cancelTokens: [],
      canAccessPage: false
    }
  },
  
  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[6]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  mounted() {
    if (this.canAccessPage) {
      this.isLoading = true;
      this.showHeader = !this.isMobile
      this.fetchAllMetrics();
    }
  },

  beforeUnmount() {
    this.cancelPreviousRequests();
  }
}

</script>
