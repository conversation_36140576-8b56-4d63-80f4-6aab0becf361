<template>
  <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
      <div class="text-3xl font-bold text-gray-900">
        City: {{ appliedCity && appliedCity.displayName ? appliedCity.displayName : finalValue.city }}
      </div>
      <div class="text-3xl font-bold text-gray-900" v-if="this.selectedStore">
        Store: {{ appliedStore.merchant_name_id }}
      </div>
    </div>
  </header>

  <div>
    <HeaderFilters :showHeader="showHeader" :selectedCity="selectedCity" :computedCityList="computedCityList"
      @update:selectedCity="handleCityChange" :selectedStore="selectedStore" :computedStoreList="computedStoreList"
      @update:selectedStore="handleStoreChange" :isYesterday="isYesterday" @update:isYesterday="isYesterday = $event"
      @toggle="onToggleYesterday" :finalValue="finalValue" @finalClick="applyAllFilter"
      @refreshFilters="refreshFilters" />
  </div>

  <main>
    <WaterMark class="absolute inset-0 pointer-events-none z-10"></WaterMark>
    <div class="max-w-7xl mx-auto p-1 lg:px-8 lg:py-4">
      <div class="text-xs font-extralight mb-4 text-justify md:text-right w-full">
        Click on a metric to see detailed view.
      </div>

      <MetricGroup v-if="assetsLoaded" v-for="(metricGroup, key, idx) in computedOrderMetricBreakdown"
        :key="metricGroup.key" :isDefaultOpen="metricGroup.isDefaultOpen" :title="metricGroup.title"
        :icon="metricGroup?.icon" :metricGroup="metricGroup" :finalValue="finalValue" :isYesterday="isYesterday"
        :showHeader="showHeader" :idx="idx" :openAccordions="metricGroupOpenAccordions"
        @updateOpenAccordion="updateOpenMetricGroupAccordion"
        @updateClosedAccordion="updateClosedMetricGroupAccordion" />


      <div class="acc">
        <Accordion :activeIndex="activeInsights" @tab-open="handleTabOpen(typeConstants.insights)"
          @tab-close="handleTabClose(typeConstants.insights)" class="mt-4">
          <AccordionTab :pt="{
            header: { class: 'shadow-md hover:shadow-lg rounded-lg' },
            headerAction: { class: 'bg-green-100 rounded-lg' },
            content: { class: 'bg-green-50/40' },
          }">
            <template #header>
              <div class="flex justify-between items-center w-full">
                <div class="flex items-center gap-2 md:gap-3">
                  <i class="pi pi-chart-line text-violet-600" style="color: black;"></i>
                  <div class="text-md md:text-lg font-bold lg:font-normal text-black">PType Insights</div>
                </div>
              </div>
            </template>

            <div v-if="isInsightsLoading">
              <Loading />
            </div>

            <div v-else-if="isInsightsError" class="text-center p-4">
              <span class="text-sm md:text-md font-bold text-red-700">
                Failed to load metrics. Please try again.
              </span>
            </div>

            <div v-else>
              <div v-for="ptypeMetrics in computedPtypeMetrics" v-if="hasOnlyPtypeFilter" class="mt-2 md:mt-8">
                <span class="text-sm md:text-lg font-bold text-blue-900">
                  PType Details - {{ ptypeMetrics.grain }}
                </span>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3 mt-4">
                  <div v-for="metric in ptypeMetrics?.metrics" :key="metric.name"
                    class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow">
                    <div class="flex justify-between px-1 md:px-2">
                      <span
                        class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
                          computedPTypeMetricNameMapping[metric.name] || metric.name }}
                        <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                      </span>
                      <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr?.value"
                        :type="metric?.curr?.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                      <span class="text-xs font-light gray-900"></span>
                      <div class="flex justify-between">
                        <div v-for="diff in metric.diffs" :key="diff.date">
                          <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                            <template v-if="diff.change() !== '-'">
                              {{ diff.change() }}
                              {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                            </template>
                            <template v-else>
                              -
                            </template>
                          </span>
                          <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                            class="text-xs font-bold mr-1 text-positive-metric"> »
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else>
                <Accordions conversionType="p-types-spike" :data="computedPtypeSpikeMetrics"
                  :nonMetricColumns="{ key: computedStoreId === '' ? 'City' : 'Store', grain: 'P Type' }"
                  :isCityLevelView="false" :showHeader="showHeader" :defaultRowCount="3" :hasSearchFilter="true"
                  @onPtypeSelect="onPtypeSelect" :currentFilters="computedCurrentFilters" containerClass="mt-0"
                  titleClass="!text-sm md:!text-base" />

                <Accordions conversionType="p-types" :data="computedPtypeConvMetrics"
                  :nonMetricColumns="{ key: computedStoreId === '' ? 'City' : 'Store', grain: 'P Type' }"
                  :isCityLevelView="false" :showHeader="showHeader" :defaultRowCount="3" :hasSearchFilter="true"
                  @onPtypeSelect="onPtypeSelect" :currentFilters="computedCurrentFilters"
                  titleClass="!text-sm md:!text-base" />
              </div>
            </div>
          </AccordionTab>
        </Accordion>

        <div v-if="overallStoresSearched" class="mb-2">
          <Accordion :activeIndex="activeCitiesData" @tab-open="handleTabOpen(typeConstants.cities)"
            @tab-close="handleTabClose(typeConstants.cities)" class="mt-4">
            <AccordionTab :pt="{
              header: { class: 'shadow-md hover:shadow-lg rounded-lg' },
              headerAction: { class: 'bg-blue-50 rounded-lg' },
              content: { class: 'bg-blue-50/40' },
            }">
              <template #header>
                <div class="flex justify-between items-center w-full">
                  <div class="flex items-center gap-2 md:gap-3">
                    <i class="pi pi-chart-line text-violet-600" style="color: black;"></i>
                    <div class="text-md md:text-lg font-bold lg:font-normal text-black">
                      Cities Data
                    </div>
                  </div>
                </div>
              </template>

              <div v-if="isCitiesDataLoading">
                <Loading />
              </div>

              <div v-else-if="isCitiesDataError" class="text-center p-4">
                <span class="text-sm md:text-md font-bold text-red-700">
                  Failed to load metrics. Please try again.
                </span>
              </div>

              <MetricsGrid v-else v-for="metricGridItem in computedMetricConfig"
                :shouldRender="metricGridItem.shouldRender" :metrics="metricGridItem.metrics"
                :metricItemName="metricGridItem.metricItemName" :title="metricGridItem.title"
                :isReverse="metricGridItem.isReverse" :showHeader="showHeader"
                :containerClass="metricGridItem.containerClass" :titleClass="metricGridItem.titleClass"
                :gridClass="metricGridItem.gridClass" :isShortenedNumber="metricGridItem.isShortenedNumber" />
            </AccordionTab>
          </Accordion>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import { api } from "../../api/index.js";
import { getUserAccessMapping, getUserAllowedStoresMapping, isGlobalUser, isCityUser, getPanIndiaStoreCityList, AddMissingStores, formatStoreName, noPermissionRoute } from "../../utils/utils.js";
import { mapActions, mapState } from "pinia";
import { useUserStore } from "../../stores/users.js";
import { useCityStore } from "../../stores/cities.js";
import { PanIndiaConstants, PTypeMetricNameMapping, CITY_METRICS_LIST } from "../../constants/index.js";
import HeaderFilters from "../../components/Festives/HeaderFilters.vue";
import { MetricChange } from "../../interfaces/index.js";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import InfoDialog from '../../components/InfoDialog.vue';
import { ABSOLUTE_METRIC_DELTA, metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import MetricsGrid from "../../components/MetricsGrid.vue";
import { festivesMetricGridMapping } from "../../configurations/Sonar/festivesMetricGridMapping.js";
import { cityMetrics, storeMetrics } from "../../utils/metrics.js";
import MetricTrend from "../../components/Festives/MetricTrend.vue";
import Dialog from 'primevue/dialog';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import isEmpty from 'lodash.isempty';
import axios from "axios";
import { fetchStoreToCityMapping } from '../../utils/storeMappings.js'
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import Accordions from "../../components/Insights/Accordions.vue";
import MetricGroup from "../../components/Festives/MetricGroup.vue";
import { ORDER_METRIC_BREAKDOWN } from "../../constants/sonar/productDetails.js";
import AccordionTab from "primevue/accordiontab";
import Accordion from "primevue/accordion";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {
  components: {
    Loading,
    WaterMark,
    HeaderFilters,
    AnimatedNumber,
    InfoDialog,
    MetricsGrid,
    MetricTrend,
    Dialog,
    DataTable,
    Column,
    Accordions,
    MetricGroup,
    Accordion,
    AccordionTab
  },

  computed: {
    computedAbsoluteMetricDeltaConstant() {
      return ABSOLUTE_METRIC_DELTA;
    },

    computedOrderMetricBreakdown() {
      return ORDER_METRIC_BREAKDOWN;
    },

    computedCityList() {
      let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
      cityList.sort(function (a, b) {
        if (a.name < b.name) {
          return -1;
        }
        if (a.name > b.name) {
          return 1;
        }
        return 0;
      });

      if (this.isGlobalUser) {
        let staticList = [PanIndiaConstants.PAN_INDIA];
        cityList = staticList.concat(cityList);
      }
      return cityList;
    },

    computedStoreList() {
      return this.selectedCityStores;
    },

    computedCityName() {
      let modifiedCityMapping = this.isGlobalUser
        ? getPanIndiaStoreCityList(this.getStorePageCityMapping)
        : this.getStorePageCityMapping;

      if (isEmpty(modifiedCityMapping[this.selectedCity])) return;
      let cityName = modifiedCityMapping[this.selectedCity]["name"];

      return {
        displayName: modifiedCityMapping[this.selectedCity]["name"],
        cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : "",
      };
    },

    computedIsPanIndiaView() {
      return isEmpty(this.appliedCity) || isEmpty(this.appliedCity.cityName);
    },

    computedStoreId() {
      if (isEmpty(this.getStorePageCityMapping[this.selectedCity])) return;
      return this.selectedStore["frontend_merchant_id"];
    },

    computedMetricConfig() {
      return festivesMetricGridMapping(this);
    },

    computedCityMetrics() {
      let metricsFunc = this.computedIsPanIndiaView ? cityMetrics : storeMetrics;
      let metricType = this.computedIsPanIndiaView ? "city" : undefined;
      let cartMetrics = metricsFunc(this.cityMetrics, "Cart Volume", metricType);
      let gmvMetrics = metricsFunc(this.cityMetrics, "GMV", metricType);
      let totalItemsSoldMetrics = metricsFunc(this.cityMetrics, "Total Items Sold", metricType);
      let cartPenMetrics = metricsFunc(this.cityMetrics, "Cart Pen", metricType);

      let metricKey = this.computedIsPanIndiaView ? 'city' : 'frontend_merchant_id';

      return {
        gmvMetrics: this.sortMetricsByCartIndex(gmvMetrics, cartMetrics, metricKey),
        totalItemsSoldMetrics: this.sortMetricsByCartIndex(totalItemsSoldMetrics, cartMetrics, metricKey),
        cartPenMetrics: this.sortMetricsByCartIndex(cartPenMetrics, cartMetrics, metricKey),
        cartMetrics: cartMetrics
      };
    },

    showComplaintsPanel() {
      return !isEmpty(this.allComplaints);
    },

    overallStoresSearched() {
      return this.appliedCity !== PanIndiaConstants.PAN_INDIA.code && this.appliedStore?.merchant_name_id === 'Overall';
    },

    hasOnlyPtypeFilter() {
      return !isEmpty(this.finalValue?.ptype);
    },

    computedPtypeMetrics() {
      return this.calculatedMetrics(this.ptypeMetrics);
    },

    computedPtypeSpikeMetrics() {
      return this.calculatedMetrics(this.ptypeSpikeInsightsMetrics);
    },

    computedPtypeConvMetrics() {
      return this.calculatedMetrics(this.ptypeInsightsMetrics);
    },

    computedPTypeMetricNameMapping() {
      return PTypeMetricNameMapping;
    },

    computedCurrentFilters() {
      return {
        cityName: this.computedCityName?.displayName,
        storeName: this.appliedStore?.merchant_name_id || this.selectedStore?.merchant_name_id,
        storeCode: this.appliedStore?.frontend_merchant_id || this.selectedStore?.frontend_merchant_id,
        isYesterday: this.isYesterday,
      }
    },

    hasOnlyCityStoreFilter() {
      // playing on ptype and l0_category, as one would need to choose either of them to apply other filters...
      if (isEmpty(this.finalValue["l0_category"]) && isEmpty(this.finalValue["ptype"])) return true
      return false
    },

    ...mapState(useCityStore, ["getStorePageCityMapping", "getStoreCityList", "getStoreToCityMapping", "getL0CategoryList", "getFestivesPage"]),
    ...mapState(useUserStore, ["user", "isLoggedIn", "getAllowedNavs"]),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },


  methods: {
    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    handleCityChange(city) {
      this.selectedCity = city;
      this.onCityChange();
    },

    handleStoreChange(store) {
      this.selectedStore = store;
    },

    onCityChange() {
      this.selectedCityStores = this.userAllowedStoresMapping[this.selectedCity];
      this.selectedStore = this.selectedCityStores[0];
    },

    onToggleYesterday(isToggled) {
      this.isInsightsLoading = true;
      this.isInsightsError = false;

      this.isCitiesDataLoading = true;
      this.isCitiesDataError = false;

      this.isYesterday = isToggled;

      this.getFestivesPage["isYesterday"] = this.isYesterday;
      this.updateFestivesPages(this.getFestivesPage);

      this.resetAllMetrics();

      if (this.activeCitiesData === 0) this.fetchCitiesMetrics();
      if (this.activeInsights === 0) this.fetchInsightMetrics();
    },

    applyAllFilter(values) {
      this.isInsightsLoading = true;
      this.isInsightsError = false;

      this.isCitiesDataLoading = true;
      this.isCitiesDataError = false;

      this.appliedStore = this.selectedStore;
      this.appliedCity = this.computedCityName;

      this.finalValue["city"] = this.computedCityName.cityName;
      this.finalValue["store"] = this.computedStoreId;
      this.finalValue["l0_category"] = values.productL0;
      this.finalValue["l1_category"] = values.productL1;
      this.finalValue["l2_category"] = values.productL2;
      this.finalValue["brand"] = values.productBrand;
      this.finalValue["ptype"] = values.productType;
      this.finalValue["pname"] = values.productName;
      this.finalValue["pid"] = values.productId;

      this.getFestivesPage["selectedStore"] = this.selectedStore;
      this.getFestivesPage["isYesterday"] = this.isYesterday;
      this.getFestivesPage["filterValues"] = this.finalValue;
      this.updateFestivesPages(this.getFestivesPage);

      this.resetAllMetrics();

      if (this.activeCitiesData === 0) this.fetchCitiesMetrics();
      if (this.activeInsights === 0) this.fetchInsightMetrics();
    },

    filterMetricsWithNoData(metrics) {
      return metrics.filter(metric => metric.data.length > 0);
    },

    async refreshFilters() {
      let res = api.fetchPTypeAndL0List().then((response) => {
        let pTypeList = response.data?.data?.find((listItem) => listItem?.field === 'ptype')?.filtered_values
        let l0List = response.data?.data?.find((listItem) => listItem?.field === 'l0_category')?.filtered_values
        this.updatePTypeList(pTypeList);
        this.updateL0CategoryList(l0List);
      })
      await Promise.all([res]);

      fetchStoreToCityMapping();
    },

    async fetchCityStoreMapping() {
      let storeToCityMapping = this.getStoreToCityMapping;
      let userAccessMapping = getUserAccessMapping();

      this.isInsightsLoading = true;
      this.isCitiesDataLoading = true;

      this.userAllowedStoresMapping = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
      let storeList = this.userAllowedStoresMapping;
      storeList[PanIndiaConstants.PAN_INDIA.code] = [];
      for (let cityName in storeList) {
        if (storeList.hasOwnProperty(cityName)) {
          let cityStores = storeList[cityName];

          if ((this.getStoreToCityMapping && this.getStoreToCityMapping?.find((item) =>
            item?.city === this.getStoreCityList?.find((item) => item?.code === cityName)?.name)
            // )?.data?.length === cityStores?.length || cityName === PanIndiaConstants.PAN_INDIA.code  # revert after store mapping is corrected
          )?.data?.length <= cityStores?.length || cityName === PanIndiaConstants.PAN_INDIA.code
          ) {
            let panIndiaStore = {
              frontend_merchant_id: "",
              frontend_merchant_name: "Overall",
              backend_merchant_id: "",
              name: cityName,
              merchant_name_id: "Overall",
            };
            cityStores.unshift(panIndiaStore);
          }
        }
      }
      this.selectedCityStores = storeList[this.selectedCity];

      if (isEmpty(this.selectedStore)) {
        this.selectedStore = this.selectedCityStores[0];
      }

      this.appliedStore = this.selectedStore;

      this.isInsightsLoading = true;
      this.isCitiesDataLoading = true;

      if (this.activeCitiesData === 0) this.fetchCitiesMetrics();
      if (this.activeInsights === 0) this.fetchInsightMetrics();
    },

    async fetchCitiesMetrics() {
      this.cancelPreviousRequests(this.typeConstants.cities);

      try {
        const citiesMetricsCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens[this.typeConstants.cities] = [citiesMetricsCancelTokenSource];

        let citiesMetrics = this.overallStoresSearched ? api.fetchCitiesProductMetrics({
          metrics: CITY_METRICS_LIST, ...this.finalValue,
          yesterday_metric: this.isYesterday, cancelToken: citiesMetricsCancelTokenSource.token,
        }) : Promise.resolve(null);

        let [citiesMetricsResponse] = await Promise.all([citiesMetrics]);

        if (citiesMetricsResponse) {
          if (this.computedIsPanIndiaView) {
            this.cityMetrics = citiesMetricsResponse.data.metrics;
          } else {
            let storeMappingObj = this.getStoreToCityMapping.filter(cityObj => cityObj.city === this.computedCityName.cityName);

            let storeMapping = storeMappingObj.reduce((initial, acc) => {
              return [...initial, ...acc?.data];
            }, []);

            this.cityMetrics = AddMissingStores(storeMapping, citiesMetricsResponse.data.metrics);
          }
        }
      } catch (error) {
        if (!axios.isCancel(error)) {
          this.isCitiesDataError = true;
          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      } finally {
        this.isCitiesDataLoading = false;
      }
    },

    async fetchInsightMetrics() {
      this.cancelPreviousRequests(this.typeConstants.insights);

      try {
        const ptypeMetricsCancelTokenSource = axios.CancelToken.source();
        const ptypeSpikeMetricsCancelTokenSource = axios.CancelToken.source();
        const ptypeConvMetricsCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens[this.typeConstants.insights] = [ptypeMetricsCancelTokenSource, ptypeSpikeMetricsCancelTokenSource, ptypeConvMetricsCancelTokenSource];

        let ptypeMetrics = this.hasOnlyPtypeFilter ? api.fetchCitiesProductPtypeMetrics({
          ...this.finalValue, yesterday_metric: this.isYesterday, cancelToken: ptypeMetricsCancelTokenSource.token,
        }) : Promise.resolve(null);

        let ptypeSpikeMetrics = this.hasOnlyPtypeFilter ? Promise.resolve(null) : api.fetchPtypeProductSpikeMetrics({
          ...this.finalValue, yesterday_metric: this.isYesterday, metrics_type: 'search_spike', cancelToken: ptypeSpikeMetricsCancelTokenSource.token
        })

        let ptypeContributionMetrics = this.hasOnlyPtypeFilter ? Promise.resolve(null) : api.fetchPtypeProductSpikeMetrics({
          ...this.finalValue, yesterday_metric: this.isYesterday, metrics_type: 'contributed_conv_drop', cancelToken: ptypeConvMetricsCancelTokenSource.token
        })

        let [
          ptypeMetricsResponse, ptypeSpikeMetricsResponse, ptypeContributionMetricsResponse,
        ] = await Promise.all([
          ptypeMetrics,
          ptypeSpikeMetrics,
          ptypeContributionMetrics,
        ]);

        if (ptypeMetricsResponse) {
          let metrics = ptypeMetricsResponse.data.map(item => {
            item.metric = sortedAllMetrics(item.metric, ['Unique Impressions', 'ATC %', 'Search Impressions', 'Search Conv %', 'Merchandising Page DAUs', 'Merchandising Page ATC%',
              'Cat Grid PLP DAUs', 'Cat Grid PLP ATC%', 'FE Availability', 'BE Inventory', 'FE Inventory', 'Stores with Unavailability'], true, 2);
            return item;
          });
          this.ptypeMetrics = metrics;
        }

        if (ptypeSpikeMetricsResponse) {
          let metrics = ptypeSpikeMetricsResponse.data.map(item => {
            item.metric = sortedAllMetrics(item.metric, [], true, 2);
            return item;
          });
          this.ptypeSpikeInsightsMetrics = metrics;
        }

        if (ptypeContributionMetricsResponse) {
          let metrics = ptypeContributionMetricsResponse.data.map(item => {
            item.metric = sortedAllMetrics(item.metric, [], true, 2);
            return item;
          });
          this.ptypeInsightsMetrics = metrics;
        }
      } catch (error) {
        if (!axios.isCancel(error)) {
          this.isInsightsError = true;

          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      } finally {
        this.isInsightsLoading = false;
      };
    },

    cancelPreviousRequests(type) {
      if (type) {
        this.cancelTokens?.[type].forEach((source) => {
          source.cancel('Operation canceled due to new request.');
        });
        this.cancelTokens[type] = []; // Clear the tokens
      } else {
        this.cancelTokens = Object.values(this.cancelTokens)?.map(([key, values]) => {
          values?.forEach((source) => {
            source.cancel('Operation canceled due to new request.');
          })
          return key
        })?.reduce((acc, it) => {
          acc[it] = []
          return acc
        }, {})

        console.log("this.cancelTokens :", this.cancelTokens);
      }
    },

    resetAllMetrics() {
      this.cityMetrics = []
      this.ptypeMetrics = []
    },

    calculatedMetrics(metrics) {
      let res = metrics?.map(cityMetric => {
        let filteredMetrics = cityMetric.metric.map(metric => {
          let { data, name } = metric;
          let curr = data[data.length - 1];
          let diffs = data.slice(0, -1).map((prev) => new MetricChange(curr, prev));
          return {
            name,
            reverseStyle: metricsForStyleReversal.includes(name),
            curr,
            diffs,
          };
        });

        let { metric, ...metricObject } = cityMetric;

        // to remove prefix from store names
        metricObject.key = formatStoreName(metricObject.key)

        return {
          ...metricObject,
          metrics: filteredMetrics
        };
      });
      return res;
    },

    sortMetricsByCartIndex(metrics, cartMetrics, metricKey) {
      return metrics.sort((a, b) => {
        let indexOfA = cartMetrics.findIndex(metric => metric[metricKey] === a[metricKey]);
        let indexOfB = cartMetrics.findIndex(metric => metric[metricKey] === b[metricKey]);
        return indexOfA - indexOfB;
      });
    },

    onPtypeSelect(ptype) {
      if (!ptype) return;

      this.$router.push({
        name: 'Item Insights', query: {
          city: this.computedCityName?.displayName,
          store: this.appliedStore?.merchant_name_id || this.selectedStore?.merchant_name_id,
          storeCode: this.appliedStore?.frontend_merchant_id || this.selectedStore?.frontend_merchant_id,
          ptype: ptype?.data,
          yesterday: this.isYesterday,
          availability: ptype?.availability
        }
      })
    },

    updateOpenMetricGroupAccordion(key) {
      this.metricGroupOpenAccordions.push(key)

      this.getFestivesPage["metricGroupOpenAccordions"] = this.metricGroupOpenAccordions;
      this.updateFestivesPages(this.getFestivesPage);
    },

    updateClosedMetricGroupAccordion(key) {
      this.metricGroupOpenAccordions = this.metricGroupOpenAccordions.filter((item) => item !== key);

      this.getFestivesPage["metricGroupOpenAccordions"] = this.metricGroupOpenAccordions;
      this.updateFestivesPages(this.getFestivesPage);
    },

    handleTabClose(type) {
      if (type === this.typeConstants.insights) {
        this.isInsightsLoading = false;
        this.isInsightsError = false;

        this.activeInsights = null
        this.cancelPreviousRequests(this.typeConstants.insights)
      } else {
        this.isCitiesDataLoading = false;
        this.isCitiesDataError = false;

        this.activeCitiesData = null
        this.cancelPreviousRequests(this.typeConstants.cities)
      }
    },

    handleTabOpen(type) {
      if (type === this.typeConstants.insights) {
        this.isInsightsLoading = true;
        this.isInsightsError = false;

        this.activeInsights = 0
        this.fetchInsightMetrics()

        this.intervalIdInsights = window.setInterval(() => {
          this.fetchInsightMetrics();
        }, 120000);
      } else {
        this.isCitiesDataLoading = true;
        this.isCitiesDataError = false;

        this.activeCitiesData = 0
        this.fetchCitiesMetrics()

        this.intervalIdCities = window.setInterval(() => {
          this.fetchCitiesMetrics();
        }, 120000);
      }
    },

    midnightTask() {
      this.getFestivesPage["currentDate"] = (new Date()).getDate();
      this.getFestivesPage["metricGroupOpenAccordions"] = null;
      this.updateFestivesPages(this.getFestivesPage);
      window.location.reload()
    },

    ...mapActions(useUserStore, ["logout"]),
    ...mapActions(useCityStore, ["updateStoreToCityMapping", "updatePTypeList", "updateL0CategoryList", "updateFestivesPages"]),
  },

  data() {
    return {
      showHeader: false,
      assetsLoaded: false,
      isInsightsLoading: false,
      isInsightsError: false,
      isCitiesDataLoading: false,
      isCitiesDataError: false,
      canAccessPage: false,

      intervalIdInsights: null,
      intervalIdCities: null,
      isGlobalUser: false,
      isCityUser: false,
      userAllowedStoresMapping: {},

      isYesterday: false,
      selectedCity: null,
      selectedStore: null,
      selectedCityStores: [],
      finalValue: {},
      appliedCity: null,
      appliedStore: null,

      typeConstants: {
        insights: 'insights',
        cities: 'cities'
      },
      cancelTokens: {
        insights: [],
        cities: []
      },
      cityMetrics: [],
      ptypeMetrics: [],
      ptypeSpikeInsightsMetrics: [],
      ptypeInsightsMetrics: [],

      metricGroupOpenAccordions: [],
      activeInsights: null,
      activeCitiesData: null,
    };
  },

  watch: {
    $route() {
      this.checkRoute();
    },
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[10]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
    this.isCityUser = isCityUser(this.userAccessMapping);

    if (!this.getStoreToCityMapping.length || !this.getL0CategoryList.length) {
      this.refreshFilters();
    }

    if (this.isGlobalUser) {
      this.selectedCity = isEmpty(this.getFestivesPage?.selectedStore) ? PanIndiaConstants.PAN_INDIA.code : this.getFestivesPage.selectedStore?.name;
    } else {
      this.selectedCity = isEmpty(this.getFestivesPage?.selectedStore) ? Object.keys(this.getStorePageCityMapping)[0] : this.getFestivesPage.selectedStore?.name
    }

    this.selectedStore = isEmpty(this.getFestivesPage?.selectedStore) ? null : this.getFestivesPage.selectedStore;
    this.isYesterday = isEmpty(this.getFestivesPage?.isYesterday) ? false : this.getFestivesPage.isYesterday;

    // revert the comment once issue is fixed > async values to reflect on the ui as well...
    // this.finalValue = isEmpty(this.getFestivesPage?.filterValues) ? {} : this.getFestivesPage.filterValues;
    this.finalValue = isEmpty(this.getFestivesPage?.filterValues) ? {}
      : { ...this.getFestivesPage.filterValues, l1_category: null, l2_category: null, brand: null, pname: null };

    this.appliedCity = this.computedCityName;

    let currentDate = (new Date()).getDate()
    if (this.getFestivesPage && currentDate !== this.getFestivesPage?.currentDate) {
      this.midnightTask()
    } else {
      this.metricGroupOpenAccordions = isEmpty(this.getFestivesPage?.metricGroupOpenAccordions) ? [Object.values(ORDER_METRIC_BREAKDOWN).find((item) => item?.isDefaultOpen)?.title] : this.getFestivesPage?.metricGroupOpenAccordions;
    }

    this.assetsLoaded = true
  },

  mounted() {
    if (this.canAccessPage) {
      this.fetchCityStoreMapping();
      this.checkRoute();
    }
  },

  beforeUnmount() {
    clearInterval(this.intervalIdInsights);
    clearInterval(this.intervalIdCities);
    this.cancelPreviousRequests();
  },
};
</script>


<style scoped>
:deep(.acc .p-accordion .p-accordion-content) {
  padding: 0.5rem;
}
</style>