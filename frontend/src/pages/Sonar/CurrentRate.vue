<template>
  <div>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
        <div class="text-3xl font-bold text-gray-900">Projection Metrics</div>
      </div>
    </header>


    <div
      class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10">
      <div>
        <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity" :options="computedCityList"
          label="name" @update:model-value="onCityChange($event)" :clearable="false" placeholder="click to select city"
          class="w-full bg-white" />
      </div>
    </div>

    <div v-if="isLoading">
      <Loading />
    </div>

    <main v-else>
      <WaterMark></WaterMark>
      <div v-for="computedHeaderMetric in computedHeaderMetrics" class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
        <span class="text-sm md:text-lg font-bold text-blue-900 inline-flex items-center">
          {{ computedHeaderMetric.heading }}
          <InfoDialog :metrickey="computedHeaderMetric.heading" :showHeader="this.showHeader"
            class="p-0 ml-2 border border-black" />
        </span>
        <div class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
          <div v-for="metric in computedHeaderMetric.metrics" :key="metric.name"
            class="md:bg-white px-1 py-1.5 md:py-4 border-b md:border-b-0 md:rounded md:shadow">
            <div class="flex justify-between px-2">
              <span class="text-lg md:text-xl font-[450]">{{ metric.name }}</span>
              <AnimatedNumber :styles="['text-lg', 'md:text-xl', 'font-semibold']" :value="metric.curr.value"
                :type="metric.curr.type" />
            </div>
            <div class="flex justify-between px-2">
              <span class="text-xs font-light gray-900"></span>
              <span class="text-xs font-light gray-900"></span>
              <div class="flex justify-between">
                <div v-for="diff in metric.diffs" :key="diff.date">
                  <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">{{
                    diff.change() }}%</span>
                  <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                    class="text-xs font-bold mr-1 text-positive-metric"> » </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="computedHourlyUserMetrics?.projectedCartVolumeMetrics"
        class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 md:mt-8">
        <span class="text-sm md:text-lg font-bold text-blue-900 inline-flex items-center">
          PROJECTED CART VOLUME - HOURLY
          <InfoDialog metrickey="PROJECTED CART VOLUME - HOURLY" :showHeader="this.showHeader"
            class="p-0 ml-2 border border-black" />
        </span>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-2 lg:gap-4 mt-2 md:mt-4">
          <ProjectionMetricBox v-for="hourlyMetric in computedHourlyUserMetrics.projectedCartVolumeMetrics"
            :name="hourlyMetric.displayHour" :curr="hourlyMetric.curr" :prev="hourlyMetric.prev" metricLabel="P"
            diffLabel="WoW" :hasCurrentHourProperty="true" :currentHour="computedCurrentHour" />
        </div>
      </div>

      <div v-if="computedHourlyUserMetrics?.actualCartVolumeMetrics"
        class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 md:mt-8">
        <span class="text-sm md:text-lg font-bold text-blue-900 inline-flex items-center">
          ACTUAL CART VOLUME - HOURLY
        </span>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-2 lg:gap-4 mt-2 md:mt-4">
          <ProjectionMetricBox v-for="hourlyMetric in computedHourlyUserMetrics.actualCartVolumeMetrics"
            :name="hourlyMetric.displayHour" :curr="hourlyMetric.curr" :prev="hourlyMetric.prev" metricLabel="A"
            diffLabel="P vs A" />
        </div>
      </div>

      <ProjectedCalendar :metricData="this.projectionMetrics" :eventData="this.eventsData" />

      <!-- bottom spacing -->
      <div class="h-20"></div>
    </main>
  </div>
</template>

<script>

import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import ProjectionMetrics from "../../components/ProjectionMetrics.vue";
import { api, apiV2 } from "../../api";
import { MetricChange } from "../../interfaces";
import { hourlyMetrics, metricsForStyleReversal, transformProjectionMetricsDatewise, sortedAllMetrics } from "../../utils/metrics.js";
import { getPanIndiaStoreCityList, getUserAccessMapping, isGlobalUser, noPermissionRoute } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users';
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import ProjectionMetricBox from "../../components/Projections/ProjectionMetricBox.vue";
import { PanIndiaConstants } from "../../constants/index.js";
import { useCityStore } from "../../stores/cities.js";
import vSelect from 'vue-select';
import ProjectedCalendar from "../../components/Projections/ProjectedCalendar.vue";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

  components: {
    Loading,
    AnimatedNumber,
    ProjectionMetricBox,
    WaterMark,
    ProjectionMetrics,
    InfoDialog,
    'v-select': vSelect,
    ProjectedCalendar
  },

  computed: {
    computedPanIndiaConstants() {
      return PanIndiaConstants;
    },

    computedCityList() {
      let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
      cityList.sort(function (a, b) {
        if (a.name < b.name) {
          return -1;
        }
        if (a.name > b.name) {
          return 1;
        }
        return 0;
      });

      if (this.isGlobalUser) {
        let staticList = [PanIndiaConstants.PAN_INDIA];
        cityList = staticList.concat(cityList);
      }
      return cityList;
    },

    computedCityName() {
      let modifiedCityMapping = this.isGlobalUser
        ? getPanIndiaStoreCityList(this.getStorePageCityMapping)
        : this.getStorePageCityMapping;

      if (isEmpty(modifiedCityMapping[this.selectedCity])) return;
      let cityName = modifiedCityMapping[this.selectedCity]["name"];

      return {
        displayName: modifiedCityMapping[this.selectedCity]["name"],
        cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : "",
      };
    },


    computedAllMetrics() {
      const processedMetrics = this.allMetrics.map(metric => {
        let data = metric.data;
        let curr = data[data.length - 1];
        let diffs = [];
        for (let j = 0; j < data.length - 1; j++) {
          let prev = data[j];
          diffs.push(new MetricChange(curr, prev));
        }
        return {
          name: metric.name,
          reverseStyle: metricsForStyleReversal.includes(metric.name),
          curr: curr,
          diffs: diffs,
        };
      });

      // Separating projected and current rate metrics
      const todayProjectedMetrics = processedMetrics.filter(metric => metric.name.startsWith('Projected'));
      const currentRateMetrics = processedMetrics.filter(metric => !metric.name.startsWith('Projected'));
      return { todayProjectedMetrics, currentRateMetrics };
    },

    computedHeaderMetrics() {
      return [
        {
          heading: 'CURRENT RATE PROJECTION (FROM 7 AM)',
          metrics: this.computedAllMetrics.currentRateMetrics
        },
        {
          heading: 'PROJECTED CART METRICS- TODAY',
          metrics: this.computedAllMetrics.todayProjectedMetrics
        }
      ]
    },

    computedHourlyUserMetrics() {
      if (isEmpty(this.hourlyUserMetrics)) return

      let projectedCartVolumeMetrics = hourlyMetrics(this.hourlyUserMetrics, false, [], "Cart Volume");
      let actualCartVolumeMetrics = hourlyMetrics(this.actualTodayMetrics, false, [], "Cart Volume");

      return {
        projectedCartVolumeMetrics,
        actualCartVolumeMetrics
      };
    },

    computedCurrentHour() {
      return new Date().getHours()
    },

    ...mapState(useUserStore, ['getAllowedNavs']),
    ...mapState(useCityStore, ['getStoreCityList', 'getProjectionPage', 'getStorePageCityMapping']),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  methods: {
    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    onCityChange(event) {
      this.isLoading = true;
      this.isError = false;
      this.updateProjectionPage(this.selectedCity);
      this.resetAllMetrices();
      this.fetchAllMetrics();
    },

    filterMetricsWithNoData(metrics) {
      return metrics.filter(metric => metric.data.length > 0);
    },

    async fetchAllMetrics() {
      try {
        const currentRateMetricsCancelTokenSource = axios.CancelToken.source();
        const eventsCancelTokenSource = axios.CancelToken.source();
        const projectedMetricsCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens = [currentRateMetricsCancelTokenSource, projectedMetricsCancelTokenSource, eventsCancelTokenSource]

        let fetchCurrentRateMetrics = apiV2.fetchCurrentRateAllMetrics(this.computedCityName.cityName, currentRateMetricsCancelTokenSource.token)
        let projectedMetrics = api.fetchBiweeklyProjectionMetrics(this.computedCityName.cityName, projectedMetricsCancelTokenSource.token)
        let events = api.fetchCurrentRateDailyMetrics(this.computedCityName.cityName, eventsCancelTokenSource.token)

        const [fetchCurrentRateMetricsResponse, projectedMetricsResponse, eventsResponse] = await Promise.all([
          fetchCurrentRateMetrics,
          projectedMetrics,
          events
        ]);

        if (fetchCurrentRateMetricsResponse) {
          const { daily_metrics, hourly_metrics } = fetchCurrentRateMetricsResponse?.data || {}
          let dailyMetrics = this.filterMetricsWithNoData(daily_metrics?.metrics);
          this.allMetrics = sortedAllMetrics(dailyMetrics);

          const cartVolumeProjectedToday = hourly_metrics?.find(({ type = '' }) => type === 'Cart Volume Projected Today')?.metrics
          const cartVolumeWoW = hourly_metrics?.find(({ type = '' }) => type === 'Cart Volume WoW')?.metrics
          const cartVolumeActualToday = hourly_metrics?.find(({ type = '' }) => type === 'Cart Volume Actual Today')?.metrics

          this.hourlyUserMetrics = [
            cartVolumeProjectedToday?.[0],
            cartVolumeWoW?.[0]
          ]

          // this is done to make accomodate the comparison between actual and projection in the function already being used (by just introducing dummy date_diff)
          const convertedProjectedMetricToDiff = {
            data: cartVolumeProjectedToday?.[0]?.data,
            date: cartVolumeWoW?.[0]?.date,
            date_diff: cartVolumeWoW?.[0]?.date_diff,
          }

          this.actualTodayMetrics = [
            cartVolumeActualToday?.[0],
            convertedProjectedMetricToDiff
          ]
        }

        if (projectedMetricsResponse) this.projectionMetrics = projectedMetricsResponse.data.metrics;

        if (eventsResponse) this.eventsData = eventsResponse.data;

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        this.isLoading = false;
        this.isError = true;
        let status = error?.response?.status;
        if (status === 403) {
          this.logout();
          console.log("Unauthenticated")
          this.$router.replace('/login');
        }
      };
    },

    resetAllMetrices() {
      this.allMetrics = []
      this.projectionMetrics = []
      this.actualTodayMetrics = []
      this.eventsData = []
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    ...mapActions(useUserStore, ['logout']),
    ...mapActions(useCityStore, ['updateProjectionPage']),
  },

  data() {
    return {
      showHeader: false,
      isLoading: true,
      userAccessMapping: {},
      isGlobalUser: false,
      intervalId: null,
      selectedCity: null,
      canAccessPage: false,
      cancelTokens: [],
      allMetrics: [],
      projectionMetrics: [],
      actualTodayMetrics: [],
      eventsData: [],
      calendarData: []
    }
  },

  watch: {
    $route() {
      this.checkRoute();
    },
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[8]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
  },

  mounted() {
    if (this.canAccessPage) {
      this.isLoading = true;
      this.isError = false;

      this.selectedCity = isEmpty(this.getProjectionPage) ? PanIndiaConstants.PAN_INDIA.code : this.getProjectionPage

      this.fetchAllMetrics();
      this.checkRoute();

      this.$nextTick(function () {
        this.intervalId = window.setInterval(() => {
          this.fetchAllMetrics();
        }, 120000);
      })
    }
  },

  beforeUnmount() {
    clearInterval(this.intervalId);
    this.cancelPreviousRequests();
  }
}

</script>
