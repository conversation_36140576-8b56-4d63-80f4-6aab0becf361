<template>
    <header class="bg-white shadow flex items-center justify-between p-4 sticky top-0 z-10">
        <i class="pi pi-chevron-left" @click="goBack" />
        <div class="font-bold text-lg">{{ computedCityName }}</div>
        <div class="w-8" />
    </header>

    <div v-if="showHeader" class="lg:bg-slate-50">
        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 text-center">
            <div class="lg:flex lg:flex-row justify-center items-center sm:mx-auto">
                <div class="flex flex-row lg:mr-4">
                    <p for="citySelect" class="lg:mr-2v lg:m-auto">Store:</p>
                    <v-select id="storeSelect" v-model="insightsPage" :options="computedStoreList"
                        label="merchant_name_id" :clearable="false" placeholder="click to select store"
                        :disabled="selectedCity == '#Pan-India'"
                        class="bg-gray-50 px-2 py-1 text-gray-900 text-sm rounded-lg focus:border-blue-500 block w-full lg:w-96 lg:mx-auto lg:py-2 text-center p-2.5" />
                </div>
                <div class="flex flex-row lg:mr-4">
                    <p for="citySelect" class="lg:mr-2 lg:m-auto">Hour:</p>
                    <HourList id="hourSelect" v-model="selectedHour" :startHour="5" :isYesterday="isYesterday"
                        :styles="['bg-gray-50', 'px-2', 'py-1', 'text-gray-900', 'text-sm', 'rounded-lg', 'focus:border-blue-500', 'block', 'w-full', 'lg:w-80', 'lg:mx-auto', 'lg:py-2', 'text-center', 'p-2.5']" />
                </div>
            </div>
            <div class="flex flex-row justify-center items-center sm:mx-auto mt-3">
                <div class="flex flex-row">
                    <div class="text-left text-gray-900 flex items-center">
                        <VueToggle class="max-w-md m-auto" title="" name="VueToggle" :toggled="computedToggleState"
                            @toggle="onToggleYesterday" />
                        <p class="ml-2">Yesterday</p>
                    </div>
                </div>
                <div class="flex flex-row mx-8">
                    <Button label="Apply" severity="contrast" @click="loadMetrics" class="px-10 py-3" />
                </div>
            </div>
        </div>
    </div>

    <div v-else class="m-2">
        <HeaderFilters :filters="['isYesterday', 'store', 'hour']" :insightsPage="insightsPage"
            @update:insightsPage="handleStoreChange" :selectedHour="selectedHour"
            @update:selectedHour="handleHourChange" :computedStoreList="computedStoreList" :isYesterday="isYesterday"
            @update:isYesterday="isYesterday = $event" @toggle="onToggleYesterday" @loadMetrics="loadMetrics"
            :appliedFilters="appliedFilters" />
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark :isGreyBackground="true"></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{
            this.computedCityName }}
            city is invalid or some internal error occured.<br />Please select another city or reach out to data
            <NAME_EMAIL> or #bl-data-support slack channel.</span>
    </div>

    <main v-else class="mt-2 md:mt-6 mb-20">
        <WaterMark :isGreyBackground="true"></WaterMark>
        <div v-if="isCityUser" class="max-w-7xl mx-auto px-3 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="cities" :data="computedCityMetrics" :nonMetricColumns="{ key: 'City' }"
                :includesOverall="true" :isCityLevelView="true" :showHeader="showHeader"
                :reversedMetrics="computedReversalColumns" :defaultRowCount="10" :hideAccordion="true" />
        </div>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="stores" :data="computedStoreMetrics" :nonMetricColumns="{ key: 'Store' }"
                :isCityLevelView="false" :showHeader="showHeader" :reversedMetrics="computedReversalColumns"
                :defaultRowCount="10" :onSelectId="onStoreSelect"
                :includesOverall="!isCityUser && (appliedFilters?.store?.['frontend_merchant_name'] !== 'Overall')"
                :showOverallMetric="false" :hideAccordion="computedHideStoreAccordion" :hasSearchFilter="true" />
        </div>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="p-types" :data="computedPtypeMetrics"
                :nonMetricColumns="{ key: computedStoreId === '' ? 'City' : 'Store', grain: 'P Type' }"
                :isCityLevelView="false" :showHeader="showHeader" :defaultRowCount="10" :hasSearchFilter="true"
                @onPtypeSelect="onPtypeSelect" />
        </div>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="p-types-conv" :data="computedPtypeConvDropMetrics"
                :nonMetricColumns="{ key: computedStoreId === '' ? 'City' : 'Store', grain: 'P Type' }"
                :isCityLevelView="false" :showHeader="showHeader" :defaultRowCount="10" :hasSearchFilter="true"
                @onPtypeSelect="onPtypeSelect" :currentFilters="computedCurrentFilters" />
        </div>
    </main>
</template>

<script>

import Loading from "../../../components/Loading.vue";
import WaterMark from "../../../components/WaterMark.vue";
import AnimatedNumber from "../../../components/AnimatedNumber.vue";
import HourList from "../../../components/HourList.vue";
import { api } from "../../../api/index.js";
import { MetricChange } from "../../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics } from "../../../utils/metrics.js";
import { getUserAccessMapping, getUserAllowedStoresMapping, formatStoreName, isCityUser, noPermissionRoute } from "../../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../../stores/users.js';
import { useCityStore } from '../../../stores/cities.js';
import { insights } from "../../../constants/index.js";
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import VueToggle from 'vue-toggle-component';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import Accordions from "../../../components/Insights/Accordions.vue";
import SelectButton from 'primevue/selectbutton';
import HeaderFilters from "../../../components/Insights/HeaderFilters.vue";
import axios from "axios";
import { PAGE_ID_ROUTE_MAPPING } from "../../../constants/pages.js";
import { useScreenSize } from "../../../composables/useScreenSize.js";

export default {

    components: {
        Loading,
        AnimatedNumber,
        WaterMark,
        'v-select': vSelect,
        VueToggle,
        HourList,
        Button,
        Checkbox,
        Accordions,
        SelectButton,
        HeaderFilters
    },

    computed: {
        computedCityName() {
            return this.$route.query.city || '';
        },

        computedCityCode() {
            return this.$route.query.cityCode || '';
        },

        computedReversalColumns() {
            return insights.REVERSE_COLUMNS;
        },

        computedToggleState() {
            if (this.getInsightsPage == null || !this.getInsightsPage.hasOwnProperty('yesterday')) return false;
            return this.getInsightsPage['yesterday'];
        },

        computedDayDiffList() {
            return [{ name: "T-7", code: 7 }, { name: "T-28", code: 28 }];
        },

        computedStoreList() {
            return this.selectedCityStores;
        },

        computedStoreId() {
            if (this.getStorePageCityMapping[this.computedCityCode] == undefined) return;
            return this.insightsPage["frontend_merchant_id"];
        },

        computedCityMetrics() {
            return this.calculatedMetrics(this.cityInsightsMetrics);
        },

        computedStoreMetrics() {
            return this.calculatedMetrics(this.storeInsightsMetrics);
        },

        computedPtypeMetrics() {
            return this.calculatedMetrics(this.ptypeInsightsMetrics);
        },

        computedHideStoreAccordion() {
            return !this.isCityUser;
        },

        toggleYesterdayOptions() {
            return [{ name: "Today", value: false }, { name: "Yesterday", value: true }]
        },

        computedCurrentFilters() {
            return {
                cityName: this.computedCityName,
                isYesterday: this.isYesterday
            }
        },

        computedPtypeConvDropMetrics() {
            return this.calculatedMetrics(this.ptypeConvDropInsightsMetrics);
        },

        ...mapState(useCityStore, ['getStorePageCityMapping', 'getStoreCityList', 'getStoreToCityMapping', 'getInsightsPage']),
        ...mapState(useUserStore, ['user', 'isLoggedIn', 'getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        onStoreSelect(storeName, isPtype) {
            const storeObj = this.computedStoreList.find(({ frontend_merchant_name }) => frontend_merchant_name.includes(storeName));
            if (!storeObj || !Object.keys(storeObj).length) return;

            if (isPtype) {
                this.$router.push({
                    name: 'P-Type Insights', query: {
                        city: this.computedCityName, cityCode: this.computedCityCode,
                        store: storeName, storeCode: storeObj['frontend_merchant_id']
                    }
                })
            } else {
                this.$router.push({
                    name: 'Store Insights', query: {
                        city: this.computedCityName, cityCode: this.computedCityCode,
                        store: storeName, storeCode: storeObj['frontend_merchant_id']
                    }
                })
            }
        },

        calculatedMetrics(insightsMetrics) {
            let res = insightsMetrics.map(cityMetric => {
                let filteredMetrics = cityMetric.metric.map(metric => {
                    let { data, name } = metric;
                    let curr = data[data.length - 1];
                    let diffs = data.slice(0, -1).map((prev) => new MetricChange(curr, prev));
                    return {
                        name,
                        reverseStyle: metricsForStyleReversal.includes(name),
                        curr,
                        diffs,
                    };
                });

                let { metric, ...metricObject } = cityMetric;

                // to remove prefix from store names
                metricObject.key = formatStoreName(metricObject.key)

                return {
                    ...metricObject,
                    metrics: filteredMetrics
                };
            });
            return res;
        },

        checkRoute() {
            this.showHeader = this.isHeaderVisible(this.$route.query);
        },

        onToggleYesterday(isToggled) {
            this.isLoading = true;
            this.isError = false;
            this.isYesterday = isToggled;

            this.appliedFilters = {
                ...this.appliedFilters,
                yesterday: isToggled
            }

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        handleStoreChange(newInsightsPage) {
            this.insightsPage = newInsightsPage;
        },

        handleHourChange(newHour) {
            this.selectedHour = newHour;
        },

        loadMetrics() {
            this.isLoading = true;
            this.isError = false;

            this.appliedFilters = {
                ...this.appliedFilters,
                store: this.insightsPage,
                hour: this.selectedHour,
            }

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        goBack() {
            this.$router.go(-1);
        },

        async fetchStoreToCityMapping() {
            let res = api.fetchStoreCityMapping().then(response => {
                this.updateStoreToCityMapping(response.data.filters);
                window.location.reload();
            });
            await Promise.all([
                res
            ]);
        },

        async fetchCityStoreMapping() {
            let storeToCityMapping = this.getStoreToCityMapping;
            let userAccessMapping = getUserAccessMapping();
            this.isLoading = false;
            this.userAllowedStoresMapping = getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping);
            let storeList = this.userAllowedStoresMapping;

            for (let cityName in storeList) {
                if (storeList.hasOwnProperty(cityName)) {
                    let cityStores = storeList[cityName];

                    if (((this.getStoreToCityMapping && this.getStoreToCityMapping?.find((item) => item?.city === (this.getStoreCityList?.find((item) =>
                        // item?.code === cityName)?.name)))?.data?.length === cityStores?.length)) { # CHANGE TO THIS AFTER MAPPING GETS CORRECTED
                        item?.code === cityName)?.name)))?.data?.length <= cityStores?.length)) {
                        let overallStore = {
                            frontend_merchant_id: '',
                            frontend_merchant_name: 'Overall',
                            backend_merchant_id: '',
                            name: cityName,
                            merchant_name_id: 'Overall'
                        };
                        cityStores.unshift(overallStore);
                    }
                }
            }

            this.selectedCityStores = storeList[this.computedCityCode];
            if (this.insightsPage == null) {
                this.insightsPage = this.selectedCityStores[0];
            }

            this.isLoading = true;
            this.fetchAllMetrics();
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const cityMetricsCancelTokenSource = axios.CancelToken.source();
                const storeMetricsCancelTokenSource = axios.CancelToken.source();
                const ptypeMetricsCancelTokenSource = axios.CancelToken.source();
                const ptypeConvDropMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [cityMetricsCancelTokenSource, storeMetricsCancelTokenSource, ptypeMetricsCancelTokenSource, ptypeConvDropMetricsCancelTokenSource];

                let cityMetrics = api.fetchCityInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, false, cityMetricsCancelTokenSource.token)

                let storeMetrics = api.fetchCityInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, true, storeMetricsCancelTokenSource.token)

                let ptypeMetrics = api.fetchPtypeInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, 'contributed_conv_drop', ptypeMetricsCancelTokenSource.token)

                let ptypeConvDropMetrics = api.fetchPtypeInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreId, this.selectedHour, this.selectedDayDiff, 'absolute_conv_drop', ptypeConvDropMetricsCancelTokenSource.token)

                let [cityMetricsResponse, storeMetricsResponse, ptypeMetricsResponse, ptypeConvDropMetricsResponse] = await Promise.all([
                    cityMetrics,
                    storeMetrics,
                    ptypeMetrics,
                    ptypeConvDropMetrics
                ]);

                if (cityMetricsResponse) {
                    let metrics = cityMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.cityInsightsMetrics = metrics;
                }

                if (storeMetricsResponse) {
                    let metrics = storeMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.storeInsightsMetrics = metrics;
                }

                if (ptypeMetricsResponse) {
                    let metrics = ptypeMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.ptypeInsightsMetrics = metrics;
                }

                if (ptypeConvDropMetricsResponse) {
                    let metrics = ptypeConvDropMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.ptypeConvDropInsightsMetrics = metrics;
                }


                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        scrollToTop() {
            window.scrollTo({ top: 0 });
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.cityInsightsMetrics = []
            this.storeInsightsMetrics = []
            this.ptypeInsightsMetrics = []
        },

        onPtypeSelect(ptype) {
            if (!ptype) return;

            this.$router.push({
                name: 'Item Insights', query: {
                    city: this.computedCityName,
                    ptype: ptype?.data,
                    yesterday: this.isYesterday,
                    availability: ptype?.availability
                }
            })
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateInsightsPage', 'updateStoreToCityMapping']),
    },

    data() {
        return {
            showHeader: false,
            selectedDayDiff: 7,
            isLoading: true,
            cityInsightsMetrics: [],
            storeInsightsMetrics: [],
            ptypeInsightsMetrics: [],
            userAllowedStoresMapping: {},
            selectedCityStores: [],
            insightsPage: null,
            isError: false,
            intervalId: null,
            isYesterday: false,
            selectedHour: -1,
            isCityUser: false,
            appliedFilters: {},
            cancelTokens: [],
            canAccessPage: false,
            ptypeConvDropInsightsMetrics: []
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        },

    },

    beforeMount() {
        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[11]);
        if (!this.canAccessPage) noPermissionRoute()

        this.userAccessMapping = getUserAccessMapping();
        this.isCityUser = isCityUser(this.userAccessMapping);

        if (!this.getStoreToCityMapping.length) {
            this.fetchStoreToCityMapping();
        }
    },

    mounted() {
        if (this.canAccessPage) {
            this.scrollToTop();

            this.isLoading = true;
            this.isError = false;
            this.selectedDayDiff = 7;
            this.isYesterday = false;
            this.selectedHour = -1;

            this.appliedFilters = {
                store: {},
                hour: -1,
                yesterday: false
            }


            this.fetchCityStoreMapping();
            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
    },
}

</script>
