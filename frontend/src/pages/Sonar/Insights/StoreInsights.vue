<template>
    <header class="bg-white shadow flex items-center justify-between p-4 sticky top-0 z-10">
        <i class="pi pi-chevron-left" @click="goBack" />
        <div class="font-bold text-lg">{{ computedStoreName }}</div>
        <div class="w-2" />
    </header>

    <div v-if="showHeader" class="lg:bg-slate-50">
        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 text-center">
            <div class="lg:flex lg:flex-row justify-center items-center sm:mx-auto">
                <div class="flex flex-row lg:mr-4">
                    <p for="citySelect" class="lg:mr-2 lg:m-auto">Hour:</p>
                    <HourList id="hourSelect" v-model="selectedHour" :startHour="5" :isYesterday="isYesterday"
                        :styles="['bg-gray-50', 'px-2', 'py-1', 'text-gray-900', 'text-sm', 'rounded-lg', 'focus:border-blue-500', 'block', 'w-full', 'lg:w-80', 'lg:mx-auto', 'lg:py-2', 'text-center', 'p-2.5']" />
                </div>
            </div>
            <div class="flex flex-row justify-center items-center sm:mx-auto mt-3">
                <div class="flex flex-row">
                    <div class="text-left text-gray-900 flex items-center">
                        <VueToggle class="max-w-md m-auto" title="" name="VueToggle" :toggled="computedToggleState"
                            @toggle="onToggleYesterday" />
                        <p class="ml-2">Yesterday</p>
                    </div>
                </div>
                <div class="flex flex-row mx-8">
                    <Button label="Apply" severity="contrast" @click="loadMetrics" class="px-10 py-3" />
                </div>
            </div>
        </div>
    </div>

    <div v-else class="m-2">
        <HeaderFilters :filters="['isYesterday', 'hour']" :selectedHour="selectedHour"
            @update:selectedHour="handleHourChange" :isYesterday="isYesterday"
            @update:isYesterday="isYesterday = $event" @toggle="onToggleYesterday" @loadMetrics="loadMetrics"
            :appliedFilters="appliedFilters" />
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <WaterMark :isGreyBackground="true"></WaterMark>
        <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{
            this.computedCityName }}
            city is invalid or some internal error occured.<br />Please select another city or reach out to data team at
            <EMAIL> or #bl-data-support slack channel.</span>
    </div>

    <main v-else class="mt-2 md:mt-6 mb-20">
        <WaterMark :isGreyBackground="true"></WaterMark>
        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="stores" :data="computedStoreMetrics" :nonMetricColumns="{ key: 'Store' }"
                :isCityLevelView="false" :showHeader="showHeader" :reversedMetrics="computedReversalColumns"
                :defaultRowCount="10" :includesOverall="true" :showOverallMetric="true" :hideAccordion="true" />
        </div>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="p-types" :data="computedPtypeMetrics"
                :nonMetricColumns="{ key: 'Store', grain: 'P Type' }" :isCityLevelView="false" :showHeader="showHeader"
                :defaultRowCount="10" :hasSearchFilter="true" @onPtypeSelect="onPtypeSelect" />
        </div>

        <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <Accordions conversionType="p-types-spike" :data="computedPtypeConvDropMetrics"
                :nonMetricColumns="{ key: 'Store', grain: 'P Type' }" :isCityLevelView="false" :showHeader="showHeader"
                :defaultRowCount="10" :hasSearchFilter="true" @onPtypeSelect="onPtypeSelect"
                :currentFilters="computedCurrentFilters" />
        </div>
    </main>
</template>

<script>

import Loading from "../../../components/Loading.vue";
import WaterMark from "../../../components/WaterMark.vue";
import AnimatedNumber from "../../../components/AnimatedNumber.vue";
import HourList from "../../../components/HourList.vue";
import { api } from "../../../api/index.js";
import { MetricChange } from "../../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics } from "../../../utils/metrics.js";
import { getUserAccessMapping, formatStoreName, noPermissionRoute } from "../../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../../stores/users.js';
import { useCityStore } from '../../../stores/cities.js';
import { insights } from "../../../constants/index.js";
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import VueToggle from 'vue-toggle-component';
import Button from 'primevue/button';
import Checkbox from 'primevue/checkbox';
import Accordions from "../../../components/Insights/Accordions.vue";
import SelectButton from 'primevue/selectbutton';
import HeaderFilters from "../../../components/Insights/HeaderFilters.vue";
import axios from "axios";
import { PAGE_ID_ROUTE_MAPPING } from "../../../constants/pages.js";
import { useScreenSize } from "../../../composables/useScreenSize.js";

export default {

    components: {
        Loading,
        AnimatedNumber,
        WaterMark,
        'v-select': vSelect,
        VueToggle,
        HourList,
        Button,
        Checkbox,
        Accordions,
        SelectButton,
        HeaderFilters
    },

    computed: {
        computedCityName() {
            return this.$route.query.city || '';
        },

        computedCityCode() {
            return this.$route.query.cityCode || '';
        },

        computedStoreName() {
            return this.$route.query.store || '';
        },

        computedStoreCode() {
            return this.$route.query.storeCode || '';
        },

        computedReversalColumns() {
            return insights.REVERSE_COLUMNS;
        },

        computedToggleState() {
            if (this.getInsightsPage == null || !this.getInsightsPage.hasOwnProperty('yesterday')) return false;
            return this.getInsightsPage['yesterday'];
        },

        computedDayDiffList() {
            return [{ name: "T-7", code: 7 }, { name: "T-28", code: 28 }];
        },

        computedStoreMetrics() {
            return this.calculatedMetrics(this.storeInsightsMetrics);
        },

        computedPtypeMetrics() {
            return this.calculatedMetrics(this.ptypeInsightsMetrics);
        },

        toggleYesterdayOptions() {
            return [{ name: "Today", value: false }, { name: "Yesterday", value: true }]
        },

        computedCurrentFilters() {
            return {
                cityName: this.computedCityName,
                storeName: this.computedStoreName,
                storeCode: this.computedStoreCode,
                isYesterday: this.isYesterday
            }
        },

        computedPtypeConvDropMetrics() {
            return this.calculatedMetrics(this.ptypeConvDropInsightsMetrics);
        },

        ...mapState(useCityStore, ['getStorePageCityMapping', 'getStoreCityList', 'getStoreToCityMapping', 'getInsightsPage']),
        ...mapState(useUserStore, ['user', 'isLoggedIn', 'getAllowedNavs']),
    },

    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        calculatedMetrics(insightsMetrics) {
            let res = insightsMetrics.map(cityMetric => {
                let filteredMetrics = cityMetric.metric.map(metric => {
                    let { data, name } = metric;
                    let curr = data[data.length - 1];
                    let diffs = data.slice(0, -1).map((prev) => new MetricChange(curr, prev));
                    return {
                        name,
                        reverseStyle: metricsForStyleReversal.includes(name),
                        curr,
                        diffs,
                    };
                });

                let { metric, ...metricObject } = cityMetric;

                // to remove prefix from store names
                metricObject.key = formatStoreName(metricObject.key)

                return {
                    ...metricObject,
                    metrics: filteredMetrics
                };
            });
            return res;
        },

        checkRoute() {
            this.showHeader = this.isHeaderVisible(this.$route.query);
        },

        onToggleYesterday(isToggled) {
            this.isLoading = true;
            this.isError = false;
            this.isYesterday = isToggled;

            this.appliedFilters = {
                ...this.appliedFilters,
                yesterday: isToggled
            }

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        handleHourChange(newHour) {
            this.selectedHour = newHour;
        },

        loadMetrics() {
            this.isLoading = true;
            this.isError = false;

            this.appliedFilters = {
                ...this.appliedFilters,
                hour: this.selectedHour
            }

            this.resetAllMetrics();
            this.fetchAllMetrics();
        },

        goBack() {
            this.$router.go(-1);
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const storeMetricsCancelTokenSource = axios.CancelToken.source();
                const ptypeMetricsCancelTokenSource = axios.CancelToken.source();
                const ptypeConvDropMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [storeMetricsCancelTokenSource, ptypeMetricsCancelTokenSource, ptypeConvDropMetricsCancelTokenSource];

                let storeMetrics = api.fetchCityInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreCode, this.selectedHour, this.selectedDayDiff, true, storeMetricsCancelTokenSource.token)

                let ptypeMetrics = api.fetchPtypeInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreCode, this.selectedHour, this.selectedDayDiff, 'contributed_conv_drop', ptypeMetricsCancelTokenSource.token)

                let ptypeConvDropMetrics = api.fetchPtypeInsightsMetrics([], this.isYesterday, this.computedCityName, this.computedStoreCode, this.selectedHour, this.selectedDayDiff, 'absolute_conv_drop', ptypeConvDropMetricsCancelTokenSource.token)

                let [storeMetricsResponse, ptypeMetricsResponse, ptypeConvDropMetricsResponse] = await Promise.all([
                    storeMetrics,
                    ptypeMetrics,
                    ptypeConvDropMetrics
                ]);

                if (storeMetricsResponse) {
                    let metrics = storeMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.storeInsightsMetrics = metrics;
                }

                if (ptypeMetricsResponse) {
                    let metrics = ptypeMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.ptypeInsightsMetrics = metrics;
                }

                if (ptypeConvDropMetricsResponse) {
                    let metrics = ptypeConvDropMetricsResponse.data.map(item => {
                        item.metric = sortedAllMetrics(item.metric, [], true, 2);
                        return item;
                    });
                    this.ptypeConvDropInsightsMetrics = metrics;
                }


                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated")
                        this.$router.replace('/login');
                    }
                }
            };
        },

        scrollToTop() {
            window.scrollTo({ top: 0 });
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.storeInsightsMetrics = []
            this.ptypeInsightsMetrics = []
        },

        onPtypeSelect(ptype) {
            if (!ptype) return;

            this.$router.push({
                name: 'Item Insights', query: {
                    store: this.computedStoreName,
                    storeCode: this.computedStoreCode,
                    ptype: ptype?.data,
                    yesterday: this.isYesterday,
                    availability: ptype?.availability
                }
            })
        },

        ...mapActions(useUserStore, ['logout']),
        ...mapActions(useCityStore, ['updateStoreToCityMapping']),
    },

    data() {
        return {
            showHeader: false,
            selectedDayDiff: 7,
            isLoading: true,
            storeInsightsMetrics: [],
            ptypeInsightsMetrics: [],
            isError: false,
            intervalId: null,
            isYesterday: false,
            selectedHour: -1,
            cancelTokens: [],
            canAccessPage: false,
            ptypeConvDropInsightsMetrics: [],
            appliedFilters: {}
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        },

    },

    beforeMount() {
        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[12]);
        if (!this.canAccessPage) noPermissionRoute()

        this.userAccessMapping = getUserAccessMapping();
    },

    mounted() {
        if (this.canAccessPage) {
            this.scrollToTop();

            this.isLoading = true;
            this.isError = false;
            this.selectedDayDiff = 7;
            this.isYesterday = false;
            this.selectedHour = -1;

            this.appliedFilters = {
                yesterday: false,
                hour: -1
            }

            this.isLoading = true;
            this.fetchAllMetrics();

            this.checkRoute();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchAllMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
    },
}

</script>
