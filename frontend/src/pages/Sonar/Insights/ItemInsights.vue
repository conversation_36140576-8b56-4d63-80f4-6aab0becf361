<template>
    <header class="bg-white shadow flex items-center justify-between p-4 sticky top-0 z-10">
        <i class="pi pi-chevron-left" @click="goBack" />
        <div class="font-bold text-lg">
            {{ computedPtype }}
            <Chip class="font-extralight text-sm ml-2" style="border-radius: 0.5rem;">
                Availability: <span class="font-normal ml-2">{{ computedAvailability }}%</span>
            </Chip>
        </div>
        <div class="w-8" />
    </header>

    <div class="mb-20">
        <WaterMark :isGreyBackground="true"></WaterMark>

        <div class="flex items-center m-2 mx-4">
            <Chip v-if="computedCityName" :label="computedCityName" class="mr-2 bg-blue-100 font-light text-sm"
                style="border-radius: 0.5rem;" />
            <Chip v-if="computedStoreName" :label="computedStoreName" class="mr-2 bg-blue-100 font-light text-sm"
                style="border-radius: 0.5rem;" />
        </div>

        <div class="px-1 flex justify-end m-2 mx-4">
            <SelectButton v-model="yesterday" :options="yesterdayOptions" optionLabel="label" optionValue="value"
                aria-labelledby="basic" @update:modelValue="toggled" />
        </div>

        <div class="px-4">
            <ItemInsightsDetail :cityName="computedCityName" :storeName="computedStoreName"
                :storeCode="computedStoreCode" :ptype="computedPtype" :isYesterday="computedYesterdayValue"
                :showHeader="false" :selectedHour="computedSelectedHour" />
        </div>
    </div>
</template>

<script>
import ItemInsightsDetail from '../../../components/Insights/ItemInsightsDetail.vue';
import Chip from 'primevue/chip';
import SelectButton from 'primevue/selectbutton';
import WaterMark from '../../../components/WaterMark.vue';
import { mapState } from 'pinia';
import { useUserStore } from '../../../stores/users.js';
import { PAGE_ID_ROUTE_MAPPING } from '../../../constants/pages';
import { noPermissionRoute } from '../../../utils/utils.js';

export default {
    components: {
        Chip,
        SelectButton,
        ItemInsightsDetail,
        WaterMark
    },

    data() {
        return {
            yesterday: false,
            canAccessPage: false
        }
    },

    watch: {
        computedIsYesterday(val) {
            if (val) this.yesterday = true
        }
    },

    computed: {
        computedCityName() {
            return this.$route.query.city || '';
        },

        computedStoreName() {
            return this.$route.query.store || '';
        },

        computedStoreCode() {
            return this.$route.query.storeCode || '';
        },

        computedPtype() {
            return this.$route.query.ptype || '';
        },

        computedIsYesterday() {
            return this.$route.query.yesterday || false;
        },

        yesterdayOptions() {
            return [
                { label: 'Today', value: false },
                { label: 'Yesterday', value: true }
            ]
        },

        computedYesterdayValue() {
            return this.yesterday;
        },

        computedSelectedHour() {
            return this.$route.query.selectedHour || -1;
        },

        computedAvailability() {
            return this.$route.query.availability || 0;
        },

        ...mapState(useUserStore, ['getAllowedNavs']),
    },

    methods: {
        goBack() {
            this.$router.go(-1);
        },

        scrollToTop() {
            window.scrollTo({ top: 0 });
        },

        toggled(val) {
            this.yesterday = val;
        },
    },

    beforeMount() {
        this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[15]);
        if (!this.canAccessPage) noPermissionRoute()
    },

    mounted() {
        this.scrollToTop();
    }

}
</script>

<style scoped>
:deep(.p-button) {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

:deep(.p-button-label) {
    padding: 0.25rem;
}
</style>