<template>
  <div v-if="!isCityUser" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
    <WaterMark></WaterMark>
    <span class="text-1xl font-bold text-red-700 m-auto">YOU DO NOT HAVE ACCESS TO STORES-DAILYVIEW PAGE</span>
  </div>

  <div v-else="isCityUser">
    <header class="bg-gradient-to-br from-white via-gray-50 to-gray-100 shadow-xl sticky top-0 z-10 backdrop-blur-sm"
      v-if="showHeader">
      <div class="max-w-7xl mx-auto py-3">
        <div class="flex flex-col items-start space-y-4">
          <div class="flex items-center space-x-3">
            <div
              class="w-1.5 min-h-12 bg-gradient-to-b from-yellow-400 via-orange-400 to-orange-500 rounded-full shadow-md">
            </div>
            <div v-if="!appliedGroupView" class="flex flex-col">
              <div
                class="text-3xl font-black tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
                {{ computedCityName && computedCityName.displayName ? computedCityName.displayName : this.appliedCity
                }}
              </div>
              <div v-if="!computedZoneState" class="text-lg text-gray-600 font-semibold mt-1 tracking-wide">
                Zone: {{ computedZoneName }}
              </div>
            </div>
            <div v-else class="flex items-center space-x-4">
              <div
                class="text-3xl font-black tracking-tight bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
                {{ appliedGroup?.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 py-6">

      <!-- Mobile Filter Bottom Sheet -->
      <MobileFilterBottomSheet ref="mobileFilterBottomSheet" v-if="isMobile" :filterSummary="mobileFilterSummary"
        :activeFiltersCount="activeFiltersCount" :hasActiveFilters="hasActiveFilters" :deferredMode="false"
        @open="onMobileFilterOpen" @close="onMobileFilterClose" @apply="onMobileFilterApply"
        @clear-all="onMobileFilterClearAll">

        <template #filters>
          <!-- Group Filter (Mobile) -->
          <div v-if="groupView" class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-users mr-2"></i>Group
            </label>
            <v-select :loading="isGroupLoading" v-model="selectedGroup" :options="computedGroupsAndCitiesList"
              label="name" :clearable="false" class="w-full filter-select bg-white rounded-xl shadow-md"
              :class="{ 'opacity-75': isGroupLoading }" :styles="{
                control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
              }" />
          </div>

          <!-- City/Zone Filters (Mobile) -->
          <template v-else>
            <div class="space-y-3">
              <label class="block text-sm font-semibold text-yellow-600">
                <i class="pi pi-map-marker mr-2"></i>City
              </label>
              <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
                :options="computedCityList" label="name" :clearable="false" placeholder="Select city"
                @change="onMobileCityChange" class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                  control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                  option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
                }" />
            </div>

            <div class="space-y-3">
              <label class="block text-sm font-semibold text-yellow-600">
                <i class="pi pi-shopping-cart mr-2"></i>Zone
              </label>
              <v-select v-model="selectedZone" :reduce="computedZoneList => computedZoneList.code" label="name"
                :options="computedZoneList" :clearable="false" placeholder="Select zone"
                class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                  control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                  option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
                }" />
            </div>
          </template>

          <!-- Hour Filter (Mobile) -->
          <div class="space-y-3">
            <label class="block text-sm font-semibold text-yellow-600">
              <i class="pi pi-clock mr-2"></i>Hour
            </label>
            <v-select :reduce="computedHourList => computedHourList.value" v-model="selectedHour"
              :options="computedHourList" label="key" :clearable="false" placeholder="Select hour"
              class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
              }" />
          </div>

          <!-- View Toggles (Mobile) -->
          <div class="space-y-4">
            <div v-if="isGlobalUser" class="space-y-3">
              <label class="block text-sm font-semibold text-yellow-600">
                <i class="pi pi-eye mr-2"></i>View Mode
              </label>
              <SelectButton v-model="groupView" :options="groupViewOptions" optionLabel="label" optionValue="value"
                aria-labelledby="basic" @update:modelValue="(value) => toggleGroupView(value, true)"
                class="rounded-xl overflow-hidden w-full" :class="{
                  'bg-yellow-50': groupView,
                  'text-yellow-600': groupView
                }" />
            </div>

            <div class="space-y-3">
              <label class="block text-sm font-semibold text-yellow-600">
                <i class="pi pi-calendar mr-2"></i>Time Period
              </label>
              <SelectButton v-model="yesterday" :options="yesterdayOptions" optionLabel="label" optionValue="value"
                aria-labelledby="basic" @update:modelValue="(value) => toggleYesterday(value, true)"
                class="rounded-xl overflow-hidden w-full" :class="{
                  'bg-yellow-50': yesterday,
                  'text-yellow-600': yesterday
                }" />
            </div>
          </div>
        </template>
      </MobileFilterBottomSheet>

      <!-- Desktop Filter Grid -->
      <div v-if="!isMobile" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-if="groupView" class="col-span-1 md:col-span-2">
          <label class="block text-sm font-semibold text-yellow-600 mb-2">
            <i class="pi pi-users mr-2"></i>Group
          </label>
          <v-select :loading="isGroupLoading" v-model="selectedGroup" :options="computedGroupsAndCitiesList"
            label="name" @update:model-value="onGroupChange($event)" :clearable="false"
            class="w-full filter-select bg-white rounded-xl shadow-md" :class="{ 'opacity-75': isGroupLoading }"
            :styles="{
              control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
              option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
            }" />
        </div>
        <template v-else>
          <div>
            <label class="block text-sm font-semibold text-yellow-600 mb-2">
              <i class="pi pi-map-marker mr-2"></i>City
            </label>
            <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity"
              :options="computedCityList" label="name" @update:model-value="onCityChange($event)" :clearable="false"
              placeholder="Select city" class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
              }" />
          </div>
          <div>
            <label class="block text-sm font-semibold text-yellow-600 mb-2">
              <i class="pi pi-shopping-cart mr-2"></i>Zone
            </label>
            <v-select v-model="selectedZone" :reduce="computedZoneList => computedZoneList.code" label="name"
              :options="computedZoneList" @update:model-value="onZoneChange($event)" :clearable="false"
              placeholder="Select zone" class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
                control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
                option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
              }" />
          </div>
        </template>
        <div>
          <label class="block text-sm font-semibold text-yellow-600 mb-2">
            <i class="pi pi-clock mr-2"></i>Hour
          </label>
          <v-select :reduce="computedHourList => computedHourList.value" v-model="selectedHour"
            :options="computedHourList" label="key" @update:model-value="onHourChange($event)" :clearable="false"
            placeholder="Select hour" class="w-full filter-select bg-white rounded-xl shadow-md" :styles="{
              control: (styles) => ({ ...styles, borderColor: '#975A16', borderRadius: '0.75rem', padding: '4px', boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)' }),
              option: (styles, { isFocused }) => ({ ...styles, backgroundColor: isFocused ? '#FDF6B2' : null, padding: '8px' })
            }" />
        </div>
      </div>

      <!-- Desktop View Toggles -->
      <div v-if="!isMobile" class="flex flex-wrap gap-4 justify-end pt-4">
        <SelectButton v-if="isGlobalUser" v-model="groupView" :options="groupViewOptions" optionLabel="label"
          optionValue="value" aria-labelledby="basic" @update:modelValue="toggleGroupView"
          class="shadow-md rounded-xl overflow-hidden" :class="{
            'bg-yellow-50': groupView,
            'text-yellow-600': groupView
          }" />
        <SelectButton v-model="yesterday" :options="yesterdayOptions" optionLabel="label" optionValue="value"
          aria-labelledby="basic" @update:modelValue="toggleYesterday" class="shadow-md rounded-xl overflow-hidden"
          :class="{
            'bg-yellow-50': yesterday,
            'text-yellow-600': yesterday
          }" />
      </div>
    </div>

    <div v-if="!assetsLoaded || isGroupLoading">
      <Loading />
    </div>

    <main v-else>
      <WaterMark class="absolute inset-0 pointer-events-none z-10"></WaterMark>

      <div class="gap-3 mt-0 md:mt-8 max-w-7xl mx-auto px-2 py-1">
        <HourlyMetricsV2 ref="hourlyMetricsRef" :cityCode="stableCityCode" :zoneCode="stableZoneCode"
          :groupId="stableGroupId" :groupView="appliedGroupView" :selectedDate="selectedUserDate"
          :selectedHour="appliedHour" :showHeader="showHeader" :fetchCityMetrics="isFetchCityMetrics"
          :isPanIndia="stablePanIndiaState" :computedGroup="stableComputedGroup" :openAccordions="openAccordions"
          @updateOpenAccordion="updateOpenAccordion" @updateClosedAccordion="updateClosedAccordion"
          @drillDown="handleDrillDown" />
      </div>

      <!-- bottom spacing -->
      <div class="h-44"></div>
    </main>

    <!-- Drill-Down Popup -->
    <DrillDownPopup :visible="drillDownPopupVisible" :metricName="drillDownMetricName" :entityName="drillDownEntityName"
      :currentSelection="{
        city: computedCityName.cityName,
        zone: computedZoneName,
        enable_group_view: appliedGroupView ? 'true' : null,
        group_id: appliedGroup?.id,
        hour: appliedHour
      }" :selectedDate="formatDateToString(selectedUserDate)"
      :computedGroupsAndCitiesList="computedGroupsAndCitiesList" @close="closeDrillDownPopup" />

    <!-- Mobile Floating Action Buttons -->
    <MobileFloatingButtons :isMobile="isMobile" :hasActiveFilters="hasActiveFilters"
      :activeFiltersCount="activeFiltersCount" @openFilters="openMobileFilterFromFloatingButton" />
  </div>
</template>

<script>

import Loading from "../../components/Loading.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import MetricBox from "../../components/MetricBox.vue";
import { api } from "../../api/index.js";
import { isCityUser, getUserAccessMapping, isGlobalUser, getCombinedCityNames, getHourList, getCurrentHour, noPermissionRoute, formatDateToString } from "../../utils/utils.js";
import WaterMark from "../../components/WaterMark.vue";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js';
import vSelect from 'vue-select'
import VueToggle from 'vue-toggle-component';
import 'vue-select/dist/vue-select.css';
import { MergedCityStoreConstants, PanIndiaConstants } from "../../constants/index.js";
import isEmpty from "lodash.isempty";
import Button from "primevue/button";
import Tooltip from "primevue/tooltip";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import SelectButton from "primevue/selectbutton";
import HourlyMetricsV2 from "../../components/Cities_Stores/HourlyMetricsV2/index.vue";
import MobileFilterBottomSheet from "../../components/Common/MobileFilterBottomSheet.vue";
import MobileFloatingButtons from "../../components/Common/MobileFloatingButtons.vue";
import { useScreenSize } from "../../composables/useScreenSize.js";
import { defineAsyncComponent } from "vue";

export default {

  components: {
    Loading,
    AnimatedNumber,
    MetricBox,
    WaterMark,
    'v-select': vSelect,
    VueToggle,
    Button,
    SelectButton,
    HourlyMetricsV2,
    MobileFilterBottomSheet,
    MobileFloatingButtons,
    DrillDownPopup: defineAsyncComponent(() => import("../../components/Cities_Stores/DrillDownPopup.vue")),
  },

  directives: {
    tooltip: Tooltip
  },

  setup() {
    const { isMobile, isTablet, isDesktop, screenWidth } = useScreenSize();
    return {
      isMobile,
      isTablet,
      isDesktop,
      screenWidth
    };
  },

  computed: {
    // Mobile filter computed properties
    mobileFilterSummary() {
      const summary = [];

      if (this.appliedGroupView && this.appliedGroup) {
        summary.push({
          key: 'group',
          label: 'Group',
          value: this.appliedGroup.name
        });
      } else {
        if (this.appliedCity) {
          const cityName = this.appliedCity === PanIndiaConstants.PAN_INDIA.code ? PanIndiaConstants.PAN_INDIA.name : this.computedCityName?.displayName || this.appliedCity;
          summary.push({
            key: 'city',
            label: 'City',
            value: cityName
          });
        }

        if (this.appliedZone && this.appliedZone !== PanIndiaConstants.OVERALL.code) {
          summary.push({
            key: 'zone',
            label: 'Zone',
            value: this.computedZoneName
          });
        }
      }

      if (this.appliedHour !== undefined) {
        const hourOption = this.computedHourList.find(h => h.value === this.appliedHour);
        summary.push({
          key: 'hour',
          label: 'Hour',
          value: hourOption?.key || 'Full Day'
        });
      }

      summary.push({
        key: 'period',
        label: 'Period',
        value: this.appliedYesterday ? 'Yesterday' : 'Today'
      });

      return summary;
    },

    activeFiltersCount() {
      return this.mobileFilterSummary.length;
    },

    hasActiveFilters() {
      return this.activeFiltersCount > 0;
    },

    computedZoneState() {
      return this.appliedZone === PanIndiaConstants.OVERALL.code;
    },

    yesterdayOptions() {
      return [
        { label: 'Today', value: false },
        { label: 'Yesterday', value: true }
      ]
    },

    groupViewOptions() {
      return [
        { label: 'Normal View', value: false },
        { label: 'Group View', value: true }
      ]
    },

    computedCityList() {
      let staticList = [PanIndiaConstants.PAN_INDIA];
      let cityList = JSON.parse(JSON.stringify(this.getCityList));

      cityList.forEach((cityItem) => {
        if (Object.keys(MergedCityStoreConstants).includes(cityItem.code)) {
          cityItem.name = getCombinedCityNames(cityItem.code);
        }
      });

      cityList = cityList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });
      if (this.isGlobalUser) { cityList = staticList.concat(cityList); }
      return cityList;
    },

    computedGroup() {
      if (!this.selectedGroup) {
        return { isPanIndia: true }; // Default fallback
      }

      if (this.selectedGroup.type === PanIndiaConstants.PAN_INDIA_GROUP.type) {
        return { isPanIndia: true };
      } else if (this.selectedGroup.type === "city") {
        return { city: this.selectedGroup.name };
      } else {
        return { group_id: this.selectedGroup.id };
      }
    },

    computedGroupsAndCitiesList() {
      if (this.isGroupLoading) return [PanIndiaConstants.PAN_INDIA_GROUP];
      return [PanIndiaConstants.PAN_INDIA_GROUP, ...this.groupsAndCitiesList]
    },

    computedHourList() {
      return getHourList(null, !!this.yesterday) || [];
    },

    computedCityName() {
      let staticList = { [PanIndiaConstants.PAN_INDIA.code]: PanIndiaConstants.PAN_INDIA };

      let modifiedCityMapping = Object.assign(staticList, this.getCityMapping);
      let cityName = modifiedCityMapping[this.appliedCity]["name"];
      if (Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.appliedCity]["code"])
      ) {
        cityName = MergedCityStoreConstants[this.appliedCity].join("&city=");
      }
      return {
        displayName: (Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.appliedCity]["code"]))
          ? getCombinedCityNames(this.appliedCity)
          : modifiedCityMapping[this.appliedCity]["name"],
        cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
      }
    },

    zoneList() {
      let zoneList = this.getZoneList?.filter(zoneList => zoneList.name !== "null")
      zoneList = zoneList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });
      return zoneList;
    },

    computedZoneList() {
      let staticList = [PanIndiaConstants.OVERALL];
      if (this.selectedCity === PanIndiaConstants.PAN_INDIA.code) return staticList

      let cityName = this.getStorePageCityMapping?.[this.selectedCity]?.name
      let zoneList = this.zoneList?.filter((zone) => zone.city === cityName)

      if (zoneList?.length === 1) return zoneList

      zoneList = staticList.concat(zoneList);
      return zoneList
    },

    appliedZoneList() {
      let staticList = [PanIndiaConstants.OVERALL];
      if (this.appliedCity === PanIndiaConstants.PAN_INDIA.code) return staticList

      let cityName = this.getStorePageCityMapping?.[this.appliedCity]?.name
      let zoneList = this.zoneList?.filter((zone) => zone.city === cityName)

      if (zoneList?.length === 1) return zoneList

      zoneList = staticList.concat(zoneList);
      return zoneList
    },

    computedZoneName() {
      return this.appliedZoneList?.find((zone) => zone.code === this.appliedZone)?.name
    },

    isFetchCityMetrics() {
      return this.appliedGroupView ? this.stableComputedGroup?.isPanIndia : this.stablePanIndiaState
    },

    // Stable computed properties for metrics display (use applied values)
    stableCityCode() {
      // For group view, use the group's city
      if (this.appliedGroupView) {
        return this.stableComputedGroup?.city;
      }

      // For non-group view, temporarily use the current computedCityName logic
      // but we need to make it work with appliedCity instead of selectedCity
      let staticList = { [PanIndiaConstants.PAN_INDIA.code]: PanIndiaConstants.PAN_INDIA };
      let modifiedCityMapping = Object.assign(staticList, this.getCityMapping);

      if (!modifiedCityMapping[this.appliedCity]) {
        return this.appliedCity;
      }

      let cityName = modifiedCityMapping[this.appliedCity]["name"];
      if (Object.keys(MergedCityStoreConstants).includes(modifiedCityMapping[this.appliedCity]["code"])) {
        cityName = MergedCityStoreConstants[this.appliedCity].join("&city=");
      }

      return cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : '';
    },

    stableZoneCode() {
      return this.appliedGroupView ? null : this.stableZoneState ? null : this.stableZoneName;
    },

    stableGroupId() {
      return this.appliedGroupView ? this.stableComputedGroup?.group_id : null;
    },

    stableComputedGroup() {
      if (!this.appliedGroup) {
        return { isPanIndia: true };
      }

      if (this.appliedGroup.type === PanIndiaConstants.PAN_INDIA_GROUP.type) {
        return { isPanIndia: true };
      } else if (this.appliedGroup.type === "city") {
        return { city: this.appliedGroup.name };
      } else {
        return { group_id: this.appliedGroup.id };
      }
    },

    stableZoneName() {
      // Use the same logic as computedZoneName but with appliedZone
      // Backend expects zone names, not zone codes
      return this.computedZoneList?.find((zone) => zone.code === this.appliedZone)?.name || this.appliedZone;
    },

    stableZoneState() {
      return this.appliedZone === PanIndiaConstants.OVERALL.code;
    },

    stablePanIndiaState() {
      return this.appliedCity === PanIndiaConstants.PAN_INDIA.code;
    },

    ...mapState(useCityStore, ['getCityList', 'getCityMapping', 'getStoresDayViewCity', 'getStoresDayViewDate', 'getStoreToCityMapping', 'getStoresTrendPage', 'getZoneList', 'getStorePageCityMapping']),
    ...mapState(useUserStore, ['getAllowedNavs']),
  },

  methods: {
    formatDateToString,

    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    // Floating button methods
    openMobileFilterFromFloatingButton() {
      // Call the mobile filter bottom sheet's openBottomSheet method directly
      if (this.$refs.mobileFilterBottomSheet) {
        this.$refs.mobileFilterBottomSheet.openBottomSheet();
      }
    },

    // Mobile filter methods
    onMobileFilterOpen() {
      // Initialize temporary state with current values
      this.selectedCity = this.appliedCity;
      this.selectedZone = this.appliedZone;
      this.selectedGroup = this.appliedGroup;
      this.selectedHour = this.appliedHour;
      this.groupView = this.appliedGroupView;
      this.yesterday = this.appliedYesterday;

      if (this.isGlobalUser && !this.isGroupLoading && this.groupsAndCitiesList.length === 0) {
        this.getGroupsAndCitiesList();
      }
    },

    onMobileFilterClose() {
      // Optional: Track analytics or perform actions when filter closes
    },

    onMobileFilterApply() {
      // Update stable filter values for metrics display BEFORE applying changes
      this.appliedCity = this.selectedCity;
      this.appliedZone = this.selectedZone;
      this.appliedGroup = this.selectedGroup;
      this.appliedHour = this.selectedHour;
      this.appliedGroupView = this.groupView;
      this.appliedYesterday = this.yesterday;

      // Apply group view toggle changes
      this.storesTrendPage['groupView'] = this.groupView;
      this.storesTrendPage['yesterday'] = this.yesterday;
      this.updateStoresTrendPage(this.storesTrendPage);

      // Load groups if switching to group view
      if (this.groupView && this.groupsAndCitiesList.length === 0) {
        this.getGroupsAndCitiesList();
      }

      // Apply all filter changes when user clicks Apply button
      if (this.groupView) {
        this.onGroupChange();
      } else {
        // For mobile filters, handle city and zone changes more carefully
        // Update store state without automatic zone reset
        this.storesTrendPage['zone'] = this.selectedZone;
        this.updateStoresDayViewCity(this.selectedCity);
        this.updateStoresTrendPage(this.storesTrendPage);
      }

      this.onHourChange();

      // Manually trigger metrics refresh
      if (this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    onMobileFilterClearAll() {
      // Reset all filters to default values WITHOUT triggering API calls
      // Only reset the reactive values - APIs will be called when Apply is clicked

      if (this.groupView) {
        this.selectedGroup = this.computedGroupsAndCitiesList[0] || PanIndiaConstants.PAN_INDIA_GROUP;
      } else {
        this.selectedCity = PanIndiaConstants.PAN_INDIA.code;
        this.selectedZone = PanIndiaConstants.OVERALL.code;
      }

      // Reset hour to full day
      this.selectedHour = -1;

      // Reset to today
      this.yesterday = false;
      this.toggleYesterday(false, true);

      // Note: No API calls here - they will be triggered when user clicks "Apply Filters"
    },

    toggleYesterday(isToggled, fromMobile = false) {
      let dateToSet = isToggled ? this.getPreviousISTDate() : this.getCurrentISTDate();
      this.selectedUserDate = dateToSet;

      this.yesterday = isToggled;

      if (!this.yesterday && !getHourList(null, this.yesterday)
        .some((item) => item.value === this.selectedHour)) {
        this.selectedHour = -1; //full day
      }

      this.storesTrendPage['yesterday'] = this.yesterday;
      this.updateStoresTrendPage(this.storesTrendPage);

      // Only trigger metrics refresh for desktop filters, not mobile
      if (!fromMobile && this.$refs.hourlyMetricsRef) {
        this.appliedYesterday = this.yesterday
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    toggleGroupView(isToggled, fromMobile = false) {
      this.groupView = isToggled;

      if (!fromMobile) {
        this.storesTrendPage['groupView'] = this.groupView;
        this.updateStoresTrendPage(this.storesTrendPage);

        this.appliedGroupView = this.groupView;

        // When switching to normal view, ensure stable city/zone values are set
        if (!isToggled) {
          this.appliedCity = this.selectedCity;
          this.appliedZone = this.selectedZone;
          this.appliedGroup = null;
        } else {
          // When switching to group view, ensure stable group value is set
          this.appliedGroup = this.selectedGroup;
        }
      }

      if (isToggled) this.getGroupsAndCitiesList();

      // Only trigger metrics refresh for desktop filters, not mobile
      if (!fromMobile && this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    getCurrentDate() {
      // Return Date object instead of string to match component expectations
      return new Date();
    },

    getPreviousDate() {
      // Return Date object for yesterday instead of string
      let yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return yesterday;
    },

    onCityChange() {
      this.selectedZone = this.computedZoneList?.[0]?.code || PanIndiaConstants.OVERALL.code
      this.storesTrendPage['zone'] = this.selectedZone;
      this.updateStoresDayViewCity(this.selectedCity)
      this.updateStoresTrendPage(this.storesTrendPage);

      // Update stable values for desktop filters (immediate effect)
      this.appliedCity = this.selectedCity;
      this.appliedZone = this.selectedZone;

      // Trigger metrics refresh for desktop filters
      if (this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    onMobileCityChange() {
      this.selectedZone = this.computedZoneList?.[0]?.code || PanIndiaConstants.OVERALL.code
    },

    onZoneChange(event) {
      this.storesTrendPage['zone'] = this.selectedZone;
      this.updateStoresTrendPage(this.storesTrendPage);

      // Update stable values for desktop filters (immediate effect)
      this.appliedZone = this.selectedZone;

      // Trigger metrics refresh for desktop filters
      if (this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    onGroupChange() {
      this.storesTrendPage['group'] = this.selectedGroup;
      this.updateStoresTrendPage(this.storesTrendPage);

      // Update stable values for desktop filters (immediate effect)
      this.appliedGroup = this.selectedGroup;

      // Trigger metrics refresh for desktop filters
      if (this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    onHourChange() {
      this.storesTrendPage['hour'] = this.selectedHour;
      this.updateStoresTrendPage(this.storesTrendPage);

      // Update stable values for desktop filters (immediate effect)
      this.appliedHour = this.selectedHour;

      // Trigger metrics refresh for desktop filters
      if (this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    },

    async fetchStores() {
      this.cityStoreMapping = this.getStoreToCityMapping;
    },

    refreshList() {
      fetchStoreToCityMapping();
    },

    async getGroupsAndCitiesList() {
      this.isGroupLoading = true;

      await api.fetchCitiesAndGroups().then((response) => {
        this.groupsAndCitiesList = response.data;
      }).catch(e => {
        console.log("Error fetching groups (CitiesStoresTrend): ", e);
        this.isGroupLoading = false;
      })
      this.isGroupLoading = false;
    },

    getCurrentISTDate() {
      // Return Date object instead of string to match component expectations
      return new Date();
    },

    getPreviousISTDate() {
      // Return Date object for yesterday instead of string
      let yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return yesterday;
    },

    changeCity(val) {
      this.selectedCity = val;
      this.onCityChange();
    },

    updateOpenAccordion(key) {
      this.openAccordions.push(key)

      this.storesTrendPage['openAccordions'] = this.openAccordions;
      this.updateStoresTrendPage(this.storesTrendPage);
    },

    updateClosedAccordion(key) {
      this.openAccordions = this.openAccordions.filter((item) => item !== key);

      this.storesTrendPage['openAccordions'] = this.openAccordions;
      this.updateStoresTrendPage(this.storesTrendPage);
    },

    // Drill-down popup methods
    handleDrillDown(drillDownData) {
      this.drillDownMetricName = drillDownData.metricName;
      this.drillDownEntityName = drillDownData.entityName; // Store the clicked entity name
      this.drillDownPopupVisible = true;
    },
    closeDrillDownPopup() {
      this.drillDownPopupVisible = false;
      this.drillDownMetricName = "";
      this.drillDownEntityName = "";
    },

    ...mapActions(useUserStore, ['logout']),
    ...mapActions(useCityStore, ['updateStoresDayViewCity', 'updateStoresDayViewDate', 'updateStoreToCityMapping', 'updateStoresTrendPage']),
  },

  data() {
    return {
      assetsLoaded: false,
      showHeader: false,
      canAccessPage: false,
      isGroupLoading: false,
      isGlobalUser: false,
      isCityUser: false,
      groupView: false,
      yesterday: false,
      selectedCity: "",
      selectedGroup: null,
      selectedZone: null,
      selectedHour: null,
      selectedUserDate: null,
      storesTrendPage: null,
      userAccessMapping: {},
      cityStoreMapping: {},
      groupsAndCitiesList: [],
      // sortingOrder: [],
      openAccordions: [],

      // Stable filter values for metrics display (only update on Apply)
      appliedCity: "",
      appliedZone: null,
      appliedGroup: null,
      appliedHour: null,
      appliedGroupView: false,

      // Drill-down popup state
      drillDownPopupVisible: false,
      drillDownMetricName: "",
      drillDownEntityName: "",
    }
  },

  watch: {
    $route() {
      this.checkRoute();
    },

    isGroupLoading(newval, oldval) {
      if (!newval) {
        this.selectedGroup = isEmpty(this.storesTrendPage?.group) ? PanIndiaConstants.PAN_INDIA_GROUP : this.storesTrendPage.group
        this.appliedGroup = this.selectedGroup;
      }
    }
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[2]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
    this.isCityUser = isCityUser(this.userAccessMapping);

    if (!this.getStoreToCityMapping.length) this.refreshList();
    if (isGlobalUser) this.getGroupsAndCitiesList();

    if (this.isGlobalUser) {
      this.selectedCity = this.getStoresDayViewCity != null ? this.getStoresDayViewCity : PanIndiaConstants.PAN_INDIA.code;
    } else {
      this.selectedCity = this.getStoresDayViewCity != null ? this.getStoresDayViewCity : Object.keys(this.getCityMapping)[0];
    }

    this.storesTrendPage = this.getStoresTrendPage;
    this.yesterday = this.storesTrendPage?.hasOwnProperty('yesterday') ? this.storesTrendPage['yesterday'] : false;
    this.groupView = this.storesTrendPage?.hasOwnProperty('groupView') ? this.storesTrendPage['groupView'] : false;

    this.selectedHour = this.storesTrendPage?.hasOwnProperty('hour') ? this.storesTrendPage['hour'] : -1;
    this.selectedZone = isEmpty(this.storesTrendPage?.zone) ? PanIndiaConstants.OVERALL.code : this.storesTrendPage['zone'];
    if (!this.yesterday && this.selectedHour > getCurrentHour()) this.selectedHour = -1;

    // this.selectedGroup =  stays null till isGroupLoading is false 

    if (!this.storesTrendPage) this.storesTrendPage = {};

    this.yesterday = this.storesTrendPage?.yesterday ? this.storesTrendPage?.yesterday : false;
    this.selectedUserDate = this.yesterday ? this.getPreviousDate() : this.getCurrentDate();

    this.openAccordions = isEmpty(this.storesTrendPage?.openAccordions) ? [] : this.storesTrendPage?.openAccordions;

    // Initialize stable filter values for metrics display
    this.appliedCity = this.selectedCity;
    this.appliedZone = this.selectedZone;
    // this.appliedGroup =  stays null till isGroupLoading is false 
    this.appliedHour = this.selectedHour;
    this.appliedGroupView = this.groupView;
    this.appliedYesterday = this.yesterday

    this.assetsLoaded = true
  },

  mounted() {
    // Trigger initial metrics load after component is mounted
    this.$nextTick(() => {
      if (this.$refs.hourlyMetricsRef) {
        this.$refs.hourlyMetricsRef.refreshMetrics();
      }
    });
  },

  mounted() {
    if (this.canAccessPage) {
      this.fetchStores();
      this.checkRoute();
    }
  }
}

</script>