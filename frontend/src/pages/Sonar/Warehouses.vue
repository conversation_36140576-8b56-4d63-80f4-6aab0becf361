<template>
  <div>
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
        <div class="text-3xl font-bold text-gray-900">Warehouses</div>
      </div>
    </header>

    <div v-if="isLoading">
      <Loading />
    </div>

    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
      <WaterMark></WaterMark>
      <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">
        Either city is invalid or some internal error occured.
        <br />
        Please select another city or reach out to data <NAME_EMAIL> or #bl-data-support slack channel.
      </span>
    </div>

    <main v-else>
      <WaterMark></WaterMark>

      <div class="max-w-8xl px-2 py-1 lg:px-8 lg:py-4 mt-8">
        <FC v-if="calculatedAllFcData" :data="calculatedAllFcData" />
      </div>
    </main>
  </div>
</template>

<script>

import Loading from "../../components/Loading.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import DispatchMetricBox from "../../components/DispatchMetricBox.vue";
import { api } from "../../api/index.js";
import { getUserAccessMapping, noPermissionRoute } from "../../utils/utils.js";
import WaterMark from "../../components/WaterMark.vue";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import Button from "primevue/button";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import FC from "../../components/Warehouse/FC.vue";
import { Metric, MetricChange } from "../../interfaces/index.js";
import { formatDate, metricsForStyleReversal } from "../../utils/metrics.js";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

  components: {
    Loading,
    AnimatedNumber,
    DispatchMetricBox,
    WaterMark,
    InfoDialog,
    Button,
    FC,
  },

  computed: {
    computedAllFcData() {
      // sort with order of warehouseOutbound
      return this.warehouseOutbound.map((metrics) => {
        const { outlet_id, data } = metrics || {}

        const availaibilityData = this.warehouseAvailability?.[outlet_id]
        if (isEmpty(availaibilityData)) return

        let allData = [...data, ...availaibilityData]

        allData = allData.map(metric => {
          let data = metric.data
          data.sort((a, b) => {
            let dateA = new Date(a.date);
            let dateB = new Date(b.date);
            return dateA - dateB;
          });
          return {
            name: metric.name,
            data: data.map(x => new Metric(x.metric, metric.type, { date: x.date })),
            ...(new Date(metric.etl_snapshot_ts_ist).toString() !== 'Invalid Date' ? { updated_at: formatDate(metric.etl_snapshot_ts_ist) } : {}),
          }
        })

        const FeData = allData.find((it) => it.name === 'FE %')?.data
        const highlightMetric = { ...(FeData?.at(-1) || {}) }

        return { ...metrics, data: allData, highlightMetric }
      }).filter(Boolean)
    },

    calculatedAllFcData() {
      const all = this.computedAllFcData?.map((fcData) => {
        const metricData = fcData?.data?.map(metric => {

          let data = metric.data;

          let curr = data[data.length - 1];

          if (data.length <= 1) {
            return { name: metric.name, curr: curr }
          }

          let diffs = [];
          for (let j = 0; j < data.length - 1; j++) {
            let prev = data[j];
            diffs.push(new MetricChange(curr, prev, metric.name));
          }

          return {
            name: metric.name,
            reverseStyle: metricsForStyleReversal.includes(metric.name),
            curr: curr,
            diffs: diffs,
            type: metric.type
          }
        }).filter(metric => metric.curr && metric.curr.value !== "-");

        return { ...fcData, data: metricData }
      })

      const allMetrics = this.sortViaFEAvailability(all)
      return allMetrics
    },

    ...mapState(useUserStore, ['getAllowedNavs']),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  methods: {
    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    async fetchAllMetrics() {
      this.cancelPreviousRequests();

      try {
        const warehouseAvailabilityMetricsCancelTokenSource = axios.CancelToken.source();
        const warehouseOutboundMetricsCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens = [warehouseAvailabilityMetricsCancelTokenSource, warehouseOutboundMetricsCancelTokenSource]

        let warehouseAvailabilityMetrics = api.fetchWarehouseAvailabilityMetrics({ cancelToken: warehouseAvailabilityMetricsCancelTokenSource.token })
        let warehouseOutboundMetrics = api.fetchWarehouseOutboundMetrics({ cancelToken: warehouseOutboundMetricsCancelTokenSource.token })

        let [warehouseAvailabilityMetricsResponse, warehouseOutboundMetricsResponse,
        ] = await Promise.all([
          warehouseAvailabilityMetrics,
          warehouseOutboundMetrics,
        ]);

        if (warehouseAvailabilityMetricsResponse) this.warehouseAvailability = warehouseAvailabilityMetricsResponse.data

        if (warehouseOutboundMetricsResponse) this.warehouseOutbound = warehouseOutboundMetricsResponse.data.metrics

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          this.isLoading = false;
          this.isError = true;
          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      };
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    resetAllMetrics() {
      this.warehouseAvailability = []
      this.warehouseOutbound = []
    },

    sortViaFEAvailability(data) {
      // highlightMetric is FE Availability...
      return data.sort((a, b) => {
        const metricA = a?.highlightMetric?.value
        const metricB = b?.highlightMetric?.value

        if (metricA > metricB) return 1
        if (metricA < metricB) return -1
        return 0
      })
    },

    ...mapActions(useUserStore, ['logout']),
  },

  data() {
    return {
      showHeader: false,
      isLoading: true,
      userAccessMapping: {},
      isError: false,
      intervalId: null,
      graphToggle: false,
      cancelTokens: [],
      canAccessPage: false,
      warehouseAvailability: [],
      warehouseOutbound: []
    }
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[5]);
    if (!this.canAccessPage) noPermissionRoute()
  },

  mounted() {
    if (this.canAccessPage) {
      this.graphToggle = false;
      this.isLoading = true;
      this.isError = false;
      this.checkRoute();
      this.fetchAllMetrics();

      this.$nextTick(function () {
        this.intervalId = window.setInterval(() => {
          this.fetchAllMetrics();
        }, 120000);
      })
    }
  },

  beforeUnmount() {
    clearInterval(this.intervalId);
    this.cancelPreviousRequests();
  }
}

</script>