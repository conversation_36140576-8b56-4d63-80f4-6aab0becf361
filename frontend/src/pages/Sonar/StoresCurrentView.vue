<template>
  <div v-if="!isCityUser" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
    <WaterMark></WaterMark>
    <span class="text-1xl font-bold text-red-700 m-auto">YOU DO NOT HAVE ACCESS TO STORES PAGE</span>
  </div>
  <div v-else="isCityUser">
    <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
      <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
        <div class="text-3xl font-bold text-gray-900">City : {{ computedCityName && computedCityName.displayName ?
          computedCityName.displayName : this.selectedCity }}</div>
        <div class="text-gray-900 text-xs md:text-xs font-medium">Last 15 mins status of stores</div>
      </div>
    </header>
    <div class="max-w-md mx-auto px-2 py-1 lg:px-8 lg:py-4 text-center flex items-center">
      <v-select :reduce="computedCityList => computedCityList.code" v-model="selectedCity" :options="computedCityList"
        label="name" @update:model-value="onCityChange($event)" :clearable="false" placeholder="click to select city"
        class="bg-gray-50 px-2 py-1 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-72 lg:px-4 lg:py-2 text-center p-2.5" />
    </div>
    <div v-if="isLoading">
      <Loading />
    </div>
    <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
      <WaterMark></WaterMark>
      <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{ this.selectedCity }}
        city is invalid or some internal error occured.<br />Please select another city or reach out to data team at
        <EMAIL> or #bl-data-support slack channel.</span>
    </div>
    <main v-else>
      <WaterMark></WaterMark>

      <div class="mx-auto px-2 py-1 lg:px-8 lg:py-4">
        <div class="flex p-2 md:p-0">
          <div class="flex-auto">
            <div v-if="this.surgeReasonCounts['Total Surged Stores']"
              class="text-gray-900 text-xs md:text-xs font-medium">
              Stores in Surge: {{ this.surgeReasonCounts['Total Surged Stores'] }}</div>
            <div class="text-gray-900 text-xs md:text-xs font-medium">
              <template v-if="this.surgeReasonCounts['Total Surged Stores']">
                [<template v-for="(count, reason, index) in surgeReasonCounts">
                  <span v-if="reason !== 'Total Surged Stores'">
                    {{ index > 0 ? ', ' : '' }}{{ reason }}: {{ count }}
                  </span>
                </template>]
              </template>
            </div>
          </div>
          <div class="flex-auto text-right text-bottom">
            <div class="text-gray-900 text-xs md:text-xs font-medium">Active Stores: {{ this.activeStores }}</div>
          </div>
        </div>

        <div class="block md:flex justify-end items-center py-2 md:p-0 md:m-0">
          <v-select v-model="selectedSurgeReason" :options="surgeReasons" label="Surge Reason" clearable
            placeholder="Select surge reason"
            class="bg-gray-50 px-2 py-1 lg:p-0 text-gray-900 text-sm rounded-lg block mx-auto md:mx-0 max-w-md mt-1 md:min-w-96 text-center p-2.5" />
        </div>

        <div class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6">
          <div v-for="(metricsArray, store) in computedBlockMetrics" :key="store"
            :class="[getBorderStyle(metricsArray.find(metric => metric.name === 'Surge Seen %')), 'm-1.5 border-2 md:bg-white px-1 py-1.5 md:py-4 rounded shadow']">
            <div class="flex justify-between px-2">
              <span class="text-sm md:text-sm font-semibold">{{ store }}</span>
            </div>
            <div class="mt-1">
              <div v-for="metric in metricsArray" :key="metric.name" class="flex justify-between px-2">
                <div class="flex items-center">
                  <span class="text-xs md:text-xs font-medium">{{ metric.name }}</span>
                </div>
                <div class="flex items-center">
                  <AnimatedNumber :styles="['text-xs', 'md:text-xs', 'font-normal']" :value="metric.curr.value"
                    :type="metric.curr.type" />
                  <div v-for="diff in metric.diffs" :key="diff.date" class="ml-1 text-[10px]">
                    <span class="text-[10px] font-medium">
                      <template v-if="diff.change() !== '-'">
                        ( <span v-bind:class="diff.style(metric.reverseStyle)">{{ diff.change() }} %</span>)
                      </template>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </main>
  </div>
</template>

<script>

import Loading from "../../components/Loading.vue";
import WaterMark from "../../components/WaterMark.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import MetricBox from "../../components/MetricBox.vue";
import { api } from "../../api";
import { MetricChange } from "../../interfaces/";
import { metricsForStyleReversal, sortedStoresMetrics, UNAVAILABLE_METRIC_VALUE, SORT_STORES_PAGE_MERTICS_NAME } from "../../utils/metrics.js";
import { isCityUser, isGlobalUser, getUserAccessMapping, formatStoreName, noPermissionRoute } from "../../utils/utils.js";
import { mapActions, mapState } from 'pinia';
import { useUserStore } from '../../stores/users';
import { useCityStore } from '../../stores/cities';
import vSelect from 'vue-select'
import 'vue-select/dist/vue-select.css';
import isEmpty from "lodash.isempty";
import axios from "axios";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

  components: {
    Loading,
    AnimatedNumber,
    MetricBox,
    WaterMark,
    'v-select': vSelect,
  },

  computed: {
    computedCityList() {
      let staticList = [
        { name: "All", code: "all" }
      ]
      let cityList = Object.entries(this.getStoreCityList).map(([key, value]) => ({ name: value.name, code: value.code }));
      cityList = cityList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });
      if (this.isGlobalUser) { cityList = cityList.concat(staticList); }
      return cityList;
    },

    computedCityName() {
      let staticList = {
        all: { name: "All", code: "all" },
      }
      let newGetCityMapping = Object.assign(staticList, this.getStorePageCityMapping);
      let cityName = newGetCityMapping[this.selectedCity]["name"];
      return {
        displayName: newGetCityMapping[this.selectedCity]["name"],
        cityName: cityName
      }
      //return this.getCityMapping[this.selectedCity]["name"];
    },

    computedAllMetrics() {
      let result = {};

      for (let store in this.allMetrics) {
        let metricsArray = this.allMetrics[store];
        result[store] = metricsArray.map(metric => {
          let data = metric.data;
          let curr = data[data.length - 1];
          let diffs = [];

          for (let j = 0; j < data.length - 1; j++) {
            let prev = data[j];
            diffs.push(new MetricChange(curr, prev));
          }

          return {
            name: metric.name,
            reverseStyle: metricsForStyleReversal.includes(metric.name),
            curr: curr,
            diffs: diffs,
          };
        }).filter(metric => metric.curr && metric.curr.value !== '-');
      }
      return result;
    },

    computedBlockMetrics() {
      if (!this.selectedSurgeReason) return this.computedAllMetrics;

      return Object.fromEntries(
        Object.entries(this.computedAllMetrics).filter(([_, storeMetric]) =>
          storeMetric?.some(
            (metric) => metric.name === "Surge Reason" && metric.curr?.value.includes(this.selectedSurgeReason)
          )
        )
      );
    },

    ...mapState(useCityStore, ['getStoreCityList', 'getStorePageCityMapping', 'getStoresCurrentViewCity']),
    ...mapState(useUserStore, ['getAllowedNavs']),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  methods: {

    getBorderStyle(metric) {
      if (metric.name === SORT_STORES_PAGE_MERTICS_NAME) {
        let surge_shown = Math.ceil(metric.curr.value);
        if (surge_shown > 0) {
          return 'lg:border-red-500 bg-red-500 lg:bg-red-500 md:bg-red-500 lg:bg-opacity-10 md:bg-opacity-10 bg-opacity-10';
        }
        else if (surge_shown == 0) {
          return 'lg:border-green-500 bg-green-500 lg:bg-green-500 md:bg-green-500 lg:bg-opacity-10 md:bg-opacity-10 bg-opacity-10';
        }
      }
    },

    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    onCityChange(event) {
      //this.selectedCity = event.target.value;
      this.isLoading = true;
      this.isError = false;

      this.resetAllMetrices();
      this.fetchAllMetrics();

      this.updateStoresCurrentViewCity(this.selectedCity);
    },

    filterMetricsWithNoData(metrics) {
      return metrics.filter(metric => metric.metrics[2].data.length > 0);
    },

    refreshList() {
      fetchStoreToCityMapping();
    },

    async fetchAllMetrics() {
      this.selectedSurgeReason = ""; //reset on every fetch
      this.cancelPreviousRequests();

      try {
        const allMetricsForStoreCancelTokenSource = axios.CancelToken.source();
        const activeStoresCountCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens = [allMetricsForStoreCancelTokenSource, activeStoresCountCancelTokenSource];

        let allMetricsForStore = api.fetchStoresMetrics(this.computedCityName.cityName, allMetricsForStoreCancelTokenSource.token)

        let activeStoresCount = api.fetchActiveStoresCount(this.computedCityName.cityName, activeStoresCountCancelTokenSource.token)

        let [allMetricsForStoreResponse, activeStoresCountResponse] = await Promise.all([
          allMetricsForStore,
          activeStoresCount
        ]);

        if (allMetricsForStoreResponse) {
          this.surgeReasonCounts = {};
          let ResponseMetrics = allMetricsForStoreResponse.data.metrics;
          //let today = new Date().toISOString().split("T")[0];
          let currentDate = new Date();
          // 5.5 * 60 * 60 * 1000 is IST offset in milliseconds (5 hours 30 minutes)
          let targetDate = new Date(currentDate.getTime() + (5.5 * 60 * 60 * 1000));
          let today = targetDate.toISOString().split('T')[0];
          let metrics = ResponseMetrics.map(item => {
            let surgeValue = item.metrics.find(metric => metric.name === "Surge Seen %").data.find(data => data.date === today);
            if (item.surge_reason) {
              item.surge_reason.forEach(reason => {
                this.surgeReasonCounts[reason] = (this.surgeReasonCounts[reason] || 0) + 1;
              });
            }
            if (surgeValue && surgeValue.metric !== 0) {
              this.surgeReasonCounts["Total Surged Stores"] = (this.surgeReasonCounts["Total Surged Stores"] || 0) + 1;
            }
            let surge_reason = item.surge_reason.length > 0 ? item.surge_reason.join(', ') : UNAVAILABLE_METRIC_VALUE;
            // Iterate through each metric and handle empty data arrays
            if (typeof surgeValue !== "undefined" && surgeValue.metric == 0) {
              surge_reason = "None";
            }
            let updatedMetrics = item.metrics.map(metric => {
              let data = metric.data.length > 0 ? metric.data : [{
                date: today,
                metric: UNAVAILABLE_METRIC_VALUE
              }];

              return {
                ...metric,
                data: data
              };
            });
            // Add Surge Reason entry to updatedMetrics
            updatedMetrics.push({
              name: "Surge Reason",
              data: [{
                date: today,
                metric: surge_reason
              }],
              type: "string"
            });

            let frontend_merchant_name = formatStoreName(item.frontend_merchant_name);
            return {
              store: `${frontend_merchant_name} (${item.frontend_merchant_id})`,
              frontend_merchant_id: `${item.frontend_merchant_id}`,
              metrics: updatedMetrics
            };
          });

          this.allMetrics = sortedStoresMetrics(metrics, false);

          this.surgeReasons = [...new Set(ResponseMetrics?.flatMap((metric) => metric?.surge_reason))].filter(Boolean)
        }

        if (activeStoresCountResponse) this.activeStores = activeStoresCountResponse.data;

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          this.isLoading = false;
          this.isError = true;
          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      };
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    resetAllMetrices() {
      this.allMetrics = []
      this.activeStores = 0
    },

    ...mapActions(useUserStore, ['logout']),
    ...mapActions(useCityStore, ['updateStoresCurrentViewCity']),
  },

  data() {
    return {
      showHeader: false,
      selectedCity: "",
      isLoading: true,
      allMetrics: [],
      userAccessMapping: {},
      isCityUser: false,
      isGlobalUser: false,
      surgeReasonCounts: {},
      activeStores: 0,
      isError: false,
      intervalId: null,
      cancelTokens: [],
      canAccessPage: false,
      surgeReasons: [],
      selectedSurgeReason: ""
    }
  },

  watch: {
    $route() {
      this.checkRoute();
    },
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[3]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isCityUser = isCityUser(this.userAccessMapping);
    this.isGlobalUser = isGlobalUser(this.userAccessMapping);

    if (isEmpty(this.getStorePageCityMapping)) this.refreshList()
  },

  mounted() {
    if (this.canAccessPage) {
      this.isLoading = true;
      this.isError = false;
      this.selectedCity = this.getStoresCurrentViewCity != null ? this.getStoresCurrentViewCity : Object.keys(this.getStorePageCityMapping)[0];
      this.fetchAllMetrics();
      this.checkRoute();

      this.$nextTick(function () {
        this.intervalId = window.setInterval(() => {
          this.fetchAllMetrics();
        }, 120000);
      })
    }
  },

  beforeUnmount() {
    clearInterval(this.intervalId);
    this.cancelPreviousRequests();
  }
}

</script>