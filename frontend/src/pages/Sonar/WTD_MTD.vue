<template>
  <header class="bg-white shadow sticky top-0 z-10" v-if="showHeader">
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 min-h-32 flex flex-col justify-center">
      <div class="text-3xl font-bold text-gray-900">City:
        {{ computedCityName && computedCityName.displayName ?
          computedCityName.displayName : this.selectedCity }}
      </div>
      <div class="text-3xl font-bold text-gray-900" v-if="this.selectedZone">Zone: {{ this.computedZoneName }} </div>
    </div>
  </header>

  <div
    class="max-w-md mx-auto px-2 py-1 lg:max-w-7xl lg:px-8 lg:py-4 text-center mb-4 lg:grid lg:grid-cols-3 lg:gap-10 [&>div]:mt-2 [&>div]:md:mt-0">
    <div>
      <div class="text-left pb-2 font-semibold italic">City :</div>
      <v-select v-model="selectedCity" :reduce="computedCityList => computedCityList.code" :options="computedCityList"
        label="name" @update:model-value="onCityChange($event)" :clearable="false" placeholder="click to select city"
        class="w-full bg-white" />
    </div>
    <div>
      <div class="text-left pb-2 font-semibold italic">Zone :</div>
      <v-select v-model="selectedZone" :reduce="computedZoneList => computedZoneList.code" label="name"
        :options="computedZoneList" @update:model-value="onZoneChange($event)" :clearable="false"
        placeholder="click to select zone" class="w-full bg-white" />
    </div>
    <div class="flex items-end justify-center md:justify-end">
      <SelectButton v-model="isMTD" :options="toggleOptions" optionLabel="label" optionValue="value"
        aria-labelledby="basic" @update:modelValue="toggleView" />
    </div>
  </div>

  <div v-if="isLoading">
    <Loading />
  </div>
  <div v-else-if="!isLoading && isError" class="flex h-screen max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8">
    <WaterMark />
    <span class="text-xs md:text-lg font-bold text-red-700 mx-auto mb-70 text-center">Either {{ this.selectedCity }}
      city is invalid or some internal error occured.<br />Please select another city or reach out to data team at
      <EMAIL> or #bl-data-support slack channel.</span>
  </div>
  <main v-else>
    <WaterMark />
    <div class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
      <div v-if="projectedCartCount > 0" class="text-left font-bold text-gray-900 mt-2 sm:mt-0">
        Projected Cart Volume :
        <AnimatedNumber :value="projectedCartCount" :type="'number'" />
      </div>
      <div v-if="projectedGMV > 0" class="text-left font-bold text-gray-900 mb-2 sm:mb-0">
        Projected GMV :
        <AnimatedNumber :value="projectedGMV" :type="'currency'" />
      </div>
      <div class="text-right text-gray-900  mb-3 sm:mb-0">Data last refreshed on {{
        this.computedAllMetrics?.[0]?.updated_at }}</div>
      <div class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
        <div v-for="metric in computedAllMetrics" :key="metric.name"
          class="md:bg-white px-1 py-1.5 md:py-3 border-b md:border-b-0 md:rounded md:shadow">
          <div class="flex justify-between px-2">
            <span class="text-md md:text-base font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
              metric.name }}
              <InfoDialog :metrickey="metric.name" :showHeader="showHeader" sourcePage="WTD_MTD" />
            </span>
            <AnimatedNumber :styles="['text-lg', 'md:text-lg', 'font-semibold']" :value="metric?.curr?.value"
              :type="metric?.curr?.type" />
          </div>
          <div class="flex justify-between px-2">
            <span class="text-xs font-light gray-900"></span>
            <div class="flex justify-between">
              <div v-for="diff in metric.diffs" :key="diff.date">
                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style()">
                  {{ diff.change() }}%</span>
                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                  class="text-xs font-bold mr-1 text-positive-metric"> » </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-8">
        <div class="lg:grid lg:grid-cols-3 lg:gap-10 my-4 md:my-6">
          <div class="col-start-2">
            <div class="text-left pb-2 font-semibold italic">Metric:</div>
            <v-select v-model="selectedMetric" :options="computedMetricList" label="name"
              @update:model-value="onMetricChange($event)" :clearable="false" placeholder="Cart Volume"
              class="w-full bg-white" />
          </div>
        </div>

        <span class="text-lg font-bold text-blue-900 inline-flex items-center">
          {{ this.selectedMetric?.toUpperCase() }} TRENDS
          <InfoDialog metrickey="ORDERS - CITIES TRENDS" :showHeader="this.showHeader"
            class="p-0 ml-2 border border-black" />
        </span>
        <div
          class="grid grid-cols-1 sm:grid-cols-2 md:rounded md:grid-cols-3 lg:grid-cols-4 gap-2 lg:gap-4 mt-4 border-b">
          <MetricBox class="sm:rounded md:rounded lg:rounded" v-for="hourlyMetric in computedZoneMetrics"
            :name="hourlyMetric.city" :curr="hourlyMetric.curr" :prev="hourlyMetric.prev" />
        </div>
      </div>
    </div>
  </main>
</template>


<script>
import Button from "primevue/button";
import Loading from "../../components/Loading.vue";
import AnimatedNumber from "../../components/AnimatedNumber.vue";
import WaterMark from "../../components/WaterMark.vue";
import MetricBox from "../../components/MetricBox.vue";
import { api } from "../../api/index.js";
import { cityMetrics, sortedAllMetrics, formatDate } from "../../utils/metrics.js";
import { isGlobalUser, getUserAccessMapping, noPermissionRoute, getPanIndiaStoreCityList } from "../../utils/utils.js";
import { MetricChange } from "../../interfaces/index.js";
import { MTD_METRIC_LIST, WTD_METRIC_LIST } from "../../utils/metrics.js";
import { mapState } from 'pinia';
import { mapActions } from 'pinia';
import { useUserStore } from '../../stores/users.js';
import { useCityStore } from '../../stores/cities.js'
import vSelect from 'vue-select'
import 'vue-select/dist/vue-select.css';
import VueToggle from 'vue-toggle-component';
import { PanIndiaConstants } from "../../constants/index.js";
import InfoDialog from "../../components/InfoDialog.vue";
import isEmpty from "lodash.isempty";
import axios from "axios";
import { fetchStoreToCityMapping } from "../../utils/storeMappings.js";
import Tooltip from 'primevue/tooltip';
import { PAGE_ID_ROUTE_MAPPING } from "../../constants/pages.js";
import SelectButton from "primevue/selectbutton";
import { useScreenSize } from "../../composables/useScreenSize.js";

export default {

  components: {
    Loading,
    AnimatedNumber,
    MetricBox,
    WaterMark,
    'v-select': vSelect,
    VueToggle,
    InfoDialog,
    Button,
    SelectButton
  },

  directives: {
    tooltip: Tooltip
  },

  computed: {
    toggleOptions() {
      return [
        { label: 'WTD', value: false },
        { label: 'MTD', value: true }
      ]
    },

    computedToggleState() {
      if (this.getWTDMTDPage == null || !this.getWTDMTDPage.hasOwnProperty("mtd")) return false;
      return this.getWTDMTDPage.mtd;
    },

    computedCityList() {
      let cityList = JSON.parse(JSON.stringify(this.getStoreCityList));
      cityList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });

      if (this.isGlobalUser) {
        let staticList = [PanIndiaConstants.PAN_INDIA];
        cityList = staticList.concat(cityList);
      }
      return cityList;
    },

    zoneList() {
      let zoneList = this.getZoneList?.filter(zoneList => zoneList.name !== "null")
      zoneList = zoneList.sort(function (a, b) {
        if (a.name < b.name) { return -1; }
        if (a.name > b.name) { return 1; }
        return 0;
      });
      return zoneList;
    },

    computedZoneList() {
      let staticList = [PanIndiaConstants.OVERALL];
      if (this.selectedCity === PanIndiaConstants.PAN_INDIA.code) return staticList

      let cityName = this.getStorePageCityMapping?.[this.selectedCity]?.name
      let zoneList = this.zoneList?.filter((zone) => zone.city === cityName)

      // If there's only one zone for this city, auto-select it
      if (zoneList?.length === 1) {
        this.selectedZone = zoneList[0]?.code
        return zoneList
      }

      zoneList = staticList.concat(zoneList);
      return zoneList
    },

    computedCityName() {
      let modifiedCityMapping = this.isGlobalUser ? getPanIndiaStoreCityList(this.getStorePageCityMapping) : this.getStorePageCityMapping;

      if (modifiedCityMapping[this.selectedCity] == undefined) return;
      let cityName = modifiedCityMapping[this.selectedCity]["name"];

      return {
        displayName: modifiedCityMapping[this.selectedCity]["name"],
        cityName: cityName !== PanIndiaConstants.PAN_INDIA.name ? cityName : ''
      }
    },

    computedZoneName() {
      return this.computedZoneList?.find((zone) => zone.code === this.selectedZone).name
    },

    computedAllMetrics() {
      return this.allMetrics.map(metric => {
        let data = metric.data;
        let updated_at = metric?.updated_at;
        let curr = data[data.length - 1];
        let diffs = [];
        for (let j = 0; j < data.length - 1; j++) {
          let prev = data[j];
          diffs.push(new MetricChange(curr, prev));
        }

        return {
          name: metric.name,
          updated_at: updated_at,
          curr: curr,
          diffs: diffs,
        }
      })
    },

    computedMetricParams() {
      let city = "", zone = "";
      if (this.selectedCity === PanIndiaConstants.PAN_INDIA.code) return { city, zone }
      city = this.computedCityName.cityName
      if (this.selectedZone === PanIndiaConstants.OVERALL.code) return { city, zone }
      zone = this.computedZoneName
      return { city, zone }
    },

    computedMetricList() {
      const uniqueNames = [...new Set(this.zoneMetrics[0]?.data[0]?.data.map(metric => metric.name))];
      return uniqueNames;
    },

    computedZoneMetrics() {
      this.zoneMetrics.map(metric => {
        metric.etl_snapshot_ts_ist = formatDate(metric.etl_snapshot_ts_ist);
      })
      return cityMetrics(this.zoneMetrics, this.selectedMetric)
        .filter(metric =>
          metric.city !== "Overall");
    },


    ...mapState(useUserStore, ['user', 'getAllowedNavs']),
    ...mapState(useCityStore, ['getStoreCityList', 'getStorePageCityMapping', 'getStoreToCityMapping', 'getWTDMTDPage', 'getZoneList', 'getZoneMapping']),
  },

  setup() {
    const { isMobile } = useScreenSize();
    return {
      isMobile
    };
  },

  methods: {

    checkRoute() {
      this.showHeader = !this.isMobile;
    },

    toggleView(isToggled) {
      this.isMTD = isToggled;
      this.isLoading = true;
      this.isError = false;

      this.WTDMTDPage["mtd"] = this.isMTD;
      this.WTDMTDPage['zone'] = this.selectedZone;
      this.WTDMTDPage['city'] = this.selectedCity;
      this.WTDMTDPage['metric'] = this.selectedMetric;
      this.updateWTDMTDPage(this.WTDMTDPage);

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    onZoneChange(event) {
      this.isLoading = true;
      this.isError = false;

      this.WTDMTDPage['zone'] = this.selectedZone;
      this.WTDMTDPage['city'] = this.selectedCity;
      this.WTDMTDPage['metric'] = this.selectedMetric;
      this.updateWTDMTDPage(this.WTDMTDPage);

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },


    onCityChange(event) {
      this.isLoading = true;
      this.isError = false;

      this.selectedZone = PanIndiaConstants.OVERALL.code
      this.WTDMTDPage['zone'] = this.selectedZone;
      this.WTDMTDPage['city'] = this.selectedCity;
      this.WTDMTDPage['metric'] = this.selectedMetric;
      this.updateWTDMTDPage(this.WTDMTDPage);

      this.resetAllMetrics();
      this.fetchAllMetrics();
    },

    onMetricChange(event) {
      this.WTDMTDPage['metric'] = this.selectedMetric;
      this.updateWTDMTDPage(this.WTDMTDPage);
    },

    async fetchAllMetrics() {
      this.cancelPreviousRequests();

      try {
        const metricsPromiseCancelTokenSource = axios.CancelToken.source();
        const MetricsCancelTokenSource = axios.CancelToken.source();
        const ProjectionMetricsCancelTokenSource = axios.CancelToken.source();

        this.cancelTokens = [metricsPromiseCancelTokenSource, MetricsCancelTokenSource, ProjectionMetricsCancelTokenSource]

        let blockMetrics = this.isMTD
          ? api.fetchMTDMetrics(MTD_METRIC_LIST, this.computedMetricParams.city, this.computedMetricParams.zone, metricsPromiseCancelTokenSource.token)
          : api.fetchWTDMetrics(WTD_METRIC_LIST, this.computedMetricParams.city, this.computedMetricParams.zone, metricsPromiseCancelTokenSource.token)

        let zoneMetrics = this.isMTD
          ? api.fetchMTDAllMetrics(MTD_METRIC_LIST, this.computedMetricParams.city, this.computedMetricParams.zone, MetricsCancelTokenSource.token)
          : api.fetchWTDAllMetrics(WTD_METRIC_LIST, this.computedMetricParams.city, this.computedMetricParams.zone, MetricsCancelTokenSource.token)

        let projectionMetrics = this.isMTD
          ? api.fetchBucketProjectionMetrics('monthly', this.computedMetricParams.city, this.computedMetricParams.zone, '', ProjectionMetricsCancelTokenSource.token)
          : api.fetchBucketProjectionMetrics('weekly', this.computedMetricParams.city, this.computedMetricParams.zone, '', ProjectionMetricsCancelTokenSource.token)

        let [blockMetricsResponse,
          zoneMetricsResponse,
          projectionMetricsResponse
        ] = await Promise.all([
          blockMetrics,
          zoneMetrics,
          projectionMetrics
        ])

        if (blockMetricsResponse) this.allMetrics = sortedAllMetrics(blockMetricsResponse.data, [], false);

        if (zoneMetricsResponse) this.zoneMetrics = zoneMetricsResponse.data.metrics;

        if (projectionMetricsResponse) {
          this.projectedCartCount = projectionMetricsResponse.data.metrics[0].metric;
          this.projectedGMV = projectionMetricsResponse.data.metrics[1].metric;
        }

        this.isLoading = false;
        this.isError = false;
      } catch (error) {
        if (axios.isCancel(error)) {
          console.log("Request canceled", error.message);
        } else {
          this.isLoading = false;
          this.isError = true;
          let status = error.response.status;
          if (status === 403) {
            this.logout();
            console.log("Unauthenticated")
            this.$router.replace('/login');
          }
        }
      };
    },

    refreshMapping() {
      fetchStoreToCityMapping();
    },

    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    resetAllMetrics() {
      this.projectedCartCount = 0
      this.projectedGMV = 0
      this.allMetrics = []
      this.zoneMetrics = []
    },

    ...mapActions(useUserStore, ['logout']),
    ...mapActions(useCityStore, ['updateWTDMTDPage']),
  },

  data() {
    return {
      selectedZone: "",
      selectedCity: "",
      selectedMetric: "Cart Volume",
      WTDMTDPage: {},
      isMTD: null,
      isGlobalUser: false,
      showHeader: false,
      isError: false,
      isLoading: true,
      userAccessMapping: {},
      projectedCartCount: 0,
      projectedGMV: 0,
      cancelTokens: [],
      allMetrics: [],
      zoneMetrics: [],
    }
  },

  watch: {
    $route() {
      this.checkRoute();
    },

    computedMetricList(newVal) {
      if (isEmpty(newVal)) return;

      this.selectedMetric = newVal[0];
      this.WTDMTDPage['metric'] = newVal[0];
      this.updateWTDMTDPage(this.WTDMTDPage);
    }
  },

  beforeMount() {
    this.userAccessMapping = getUserAccessMapping();
    if (isEmpty(this.userAccessMapping)) {
      this.$router.replace('/login');
    }

    this.canAccessPage = this.getAllowedNavs.includes(PAGE_ID_ROUTE_MAPPING[7]);
    if (!this.canAccessPage) noPermissionRoute()

    this.isGlobalUser = isGlobalUser(this.userAccessMapping);
    if (!Object.keys(this.getStoreToCityMapping).length || !Object.keys(this.getZoneMapping).length) {
      this.refreshMapping();
    }
  },

  mounted() {
    if (this.canAccessPage) {
      this.isLoading = true;
      this.isError = false;

      this.WTDMTDPage = this.getWTDMTDPage ? this.getWTDMTDPage : {};
      this.isMTD = this.WTDMTDPage?.mtd ? this.WTDMTDPage?.mtd : false;
      this.selectedCity = isEmpty(this.WTDMTDPage?.city) ? PanIndiaConstants.PAN_INDIA.code : this.WTDMTDPage['city'];
      this.selectedZone = isEmpty(this.WTDMTDPage?.zone) ? PanIndiaConstants.OVERALL.code : this.WTDMTDPage['zone'];
      this.selectedMetric = isEmpty(this.WTDMTDPage?.metric) ? "Cart Volume" : this.WTDMTDPage['metric']

      this.fetchAllMetrics();
      this.checkRoute();
    }
  },

  beforeUnmount() {
    this.cancelPreviousRequests();
  }
}


</script>