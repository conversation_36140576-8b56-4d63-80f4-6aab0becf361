<template>
  <Loading />
</template>

<script>

import { mapActions } from 'pinia'
import { useUserStore } from '../stores/users'
import Loading from "../components/Loading.vue";
import { TenantMapping } from "../constants/index.js";

export default {

  components: {
    Loading,
  },

  methods: {
    ...mapActions(useUserStore, ['logout', 'updateActiveTenant']),

    clearlocalStorage() {
      localStorage.clear();
    },

    redirectToLogin() {
      const path = this.$route.path;

      if (path.includes('bistro')) {
        this.updateActiveTenant(TenantMapping.bistro)
        this.$router.push({ path: '/bistro/login', replace: true });
      }
      else {
        this.updateActiveTenant(TenantMapping.blinkit)
        this.$router.push({ path: '/login', replace: true });
      }
    },
  },

  mounted() {
    this.logout();
    this.clearlocalStorage();
    this.redirectToLogin();
  }
}

</script>
