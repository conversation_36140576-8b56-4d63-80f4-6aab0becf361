<template>
  <!-- Black backdrop -->
  <div class="p-6">
    <div class="space-y-2">
      <router-link v-for="item in navigationItems" :key="item.name" :to="item.href" :class="[
        'flex items-center justify-between p-3 rounded-lg transition-colors',
        route.path === item.href
          ? 'bg-[#f8cb46] text-gray-900 font-medium'
          : 'hover:bg-gray-100 text-gray-700'
      ]">
        <span class="flex items-center gap-3">
          <component :is="item.Icon" class="h-6 w-6" />
          <span>{{ item.name }}</span>
        </span>
        <div class="flex items-center space-x-2">
          <span v-if="route.path === item.href" class="text-xs bg-gray-900 text-white px-2 py-1 rounded">
            Current
          </span>
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </router-link>
    </div>

    <div class="mt-2 pt-6 border-t border-gray-200">
      <div class="space-y-2">
        <button @click="goToHome"
          class="w-full flex items-center justify-between p-3 text-black rounded-lg transition-colors"
          :class="currTenant === TenantMapping.blinkit ? 'bg-[#f8cb46]' : 'bg-green-200'">
          <span class="font-medium">Go to Home</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        </button>

        <button @click="handleSignOut"
          class="w-full flex items-center justify-between p-3 bg-red-50 text-red-700 rounded-lg hover:bg-red-100 transition-colors">
          <span class="font-medium">Sign Out</span>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
        </button>
      </div>
    </div>
  </div>
  <!-- </div>
  </div> -->
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { BISTRO_NAVIGATION, SONAR_NAVIGATION } from '../constants/pages.js'
import { computed } from 'vue';
import { TenantMapping } from '../constants/index.js';

// Router
const route = useRoute()
const router = useRouter()

// computed
const currTenant = computed(() => {
  return route?.path?.includes('bistro') ? TenantMapping.bistro : TenantMapping.blinkit;
})

const navigationItems = computed(() => {
  return currTenant.value === TenantMapping.bistro ? BISTRO_NAVIGATION : SONAR_NAVIGATION;
})

// Methods
const goToHome = () => {
  if (currTenant.value === TenantMapping.bistro) {
    router.push('/bistro')
    return
  }
  router.push('/')
}

const handleSignOut = () => {
  if (currTenant.value === TenantMapping.bistro) {
    router.push('/bistro/logout')
    return
  }
  router.push('/logout')
}
</script>

<style scoped>
a {
  text-decoration: none;
  background-color: rgb(255 255 255 / 30%);
  backdrop-filter: blur(10px);
}

.shadow-custom {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
}
</style>