<template>
    <section class="py-12 md:py-20 min-h-screen flex justify-center items-center">
        <WaterMark></WaterMark>
        <div class="container mx-auto">
            <div class="flex justify-center">
                <div class="text-center">
                    <h2 class="flex justify-center items-center gap-0 mb-6">
                        <span class="text-7xl font-bold font-mono">4</span>
                        <ExclamationCircleIcon class="h-20 w-20 text-red-600" />
                        <span class="text-7xl font-bold transform scale-x-[-1] font-mono">4</span>
                    </h2>
                    <h3 class="text-3xl mb-4">Oops! You're lost.</h3>
                    <p class="mb-8">The page you are looking for was not found.</p>
                    <Button severity="contrast" rounded @click="sendToHome">Back to Home</Button>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import Button from "primevue/button";
import WaterMark from "../components/WaterMark.vue";
import { ExclamationCircleIcon } from '@heroicons/vue/24/solid'
import { mapState } from 'pinia';
import { useUserStore } from "../stores/users";
import { TenantMapping } from "../constants";
import isEmpty from "lodash.isempty";

export default {
    components: {
        WaterMark,
        Button,
        ExclamationCircleIcon
    },

    computed: {
        hasAccess() {
            return !isEmpty(this.getAllowedNavs);
        },
        ...mapState(useUserStore, ['getAllowedNavs', 'getActiveTenant']),
    },

    methods: {
        sendToHome() {
            let homeRoute = this.getActiveTenant === TenantMapping.bistro ? "/bistro" : "/"

            if (!this.hasAccess) {
                this.$router.replace(homeRoute);
                return;
            }

            homeRoute = this.getAllowedNavs?.includes(homeRoute) ? homeRoute : this.getAllowedNavs[0]
            this.$router.replace(homeRoute);
        }
    }
}
</script>