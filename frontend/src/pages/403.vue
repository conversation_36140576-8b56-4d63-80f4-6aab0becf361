<template>
    <section class="py-12 md:py-20 min-h-screen flex justify-center items-center">
        <WaterMark></WaterMark>
        <div class="container mx-auto">
            <div class="flex justify-center">
                <div class="text-center">
                    <h2 class="flex justify-center items-center gap-0 mb-6">
                        <span class="text-7xl font-bold font-mono">4</span>
                        <NoSymbolIcon class="h-20 w-20 text-blue-500" />
                        <span class="text-7xl font-bold font-mono">3</span>
                    </h2>
                    <h3 class="text-3xl mb-4">Access Forbidden</h3>
                    <p class="mb-8">You do not have permission to access this page.</p>
                    <Button class="bg-blue-500 border-0" rounded @click="sendToHome">
                        {{ hasAccess ? "Back to Home" : "Go to Login" }}
                    </Button>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import Button from "primevue/button";
import WaterMark from "../components/WaterMark.vue";
import { NoSymbolIcon } from '@heroicons/vue/24/solid'
import { mapState } from 'pinia';
import { useUserStore } from "../stores/users";
import isEmpty from 'lodash.isempty';
import { TenantMapping } from "../constants";
import { useScreenSize } from "../composables/useScreenSize.js";

export default {
    components: {
        WaterMark,
        Button,
        NoSymbolIcon
    },
    data() {
        return {
            showHeader: false,
        }
    },

    watch: {
        $route() {
            this.checkRoute();
        }
    },

    computed: {
        hasAccess() {
            return !isEmpty(this.getAllowedNavs);
        },
        ...mapState(useUserStore, ['getAllowedNavs', 'getActiveTenant']),
    },


    setup() {
        const { isMobile } = useScreenSize();
        return {
            isMobile
        };
    },

    methods: {
        checkRoute() {
            this.showHeader = !this.isMobile;
        },

        sendToLogin() {
            if (this.getActiveTenant === TenantMapping.bistro) {
                this.$router.replace("/bistro/login");
            } else {
                this.$router.replace("/login");
            }
            return;
        },

        sendToHome() {
            let homeRoute = this.getActiveTenant === TenantMapping.bistro ? "/bistro" : "/"

            if (!this.hasAccess || !this.getAllowedNavs?.includes(homeRoute)) {
                this.sendToLogin()
                return
            }

            this.$router.replace(homeRoute);
        }
    }
}
</script>