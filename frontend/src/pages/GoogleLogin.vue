<template>
  <Loading />
</template>

<script>

import { api, bistroApi } from "../api";
import { mapState, mapActions } from 'pinia';
import { useUserStore } from '../stores/users';
import { useCityStore } from "../stores/cities";
import Loading from "../components/Loading.vue";
import isEmpty from "lodash.isempty";
import { BISTRO_PAGE_ID_ROUTE_MAPPING, PAGE_ID_ROUTE_MAPPING } from "../constants/pages.js";
import { TenantMapping } from "../constants/index.js";
import { createBistroUserCityMapping, createUserCityMapping } from "../utils/storeMappings.js";

export default {

  components: {
    Loading,
  },

  computed: {
    ...mapState(useUserStore, ['getActiveTenant']),
  },

  methods: {
    fetchAccessToken() {
      api.fetchAccessToken(this.$route.query)
        .then((response) => {
          this.login(response.data.user);
          let jwtString = response.data.user.access_token.split('.');
          let accessMap = JSON.parse(atob(jwtString[1]))['user_access_mapping'];

          this.getAllowedNavs(accessMap);

          this.getStoreCityMapping();

          this.getPTypeAndL0List();
        })
        .catch((error) => {
          const path = (this.getActiveTenant === TenantMapping.bistro || this.$route.path.includes('bistro')) ? "/bistro/login" : "/login";
          this.$router.push({
            path,
            query: { message: 'Unauthorized ! For any query reach out to data <NAME_EMAIL> or #bl-data-support slack channel.' }
          });
        });
    },

    getAllowedNavs(accessMap) {
      let allowedTenants = Object.keys(accessMap)?.map((tenant) => {
        if (!isEmpty(accessMap[tenant]?.allowed_pages)) return tenant;
      })?.filter(Boolean)
      this.updateAllowedTenants(allowedTenants);

      let totalNavs = allowedTenants?.map((tenant) => accessMap[tenant].allowed_pages)?.flat()
      let allowedNavs = totalNavs?.map((navId) => PAGE_ID_ROUTE_MAPPING[navId] || BISTRO_PAGE_ID_ROUTE_MAPPING[navId]);
      this.updateAllowedNav(allowedNavs);

      if (this.getActiveTenant === TenantMapping.bistro || this.$route.path.includes('bistro')) {
        this.updateActiveTenant(TenantMapping.bistro)
        this.$router.push("/bistro");
      } else {
        this.updateActiveTenant(TenantMapping.blinkit)
        this.$router.push("/");
      }
    },

    getStoreCityMapping() {
      if (this.getActiveTenant === TenantMapping.bistro || this.$route.path.includes('bistro')) {
        bistroApi.fetchBistroStoreCityMapping().then(response => {
          let mappingResponse = response.data.filters;
          this.updateBistroStoreToCityMapping(mappingResponse);

          let cityList = mappingResponse.map((item) => {
            return { name: item.city };
          });

          createBistroUserCityMapping(cityList);
        });
      } else {
        api.fetchStoreCityMapping().then(response => {
          let mappingResponse = response.data.filters;
          this.updateStoreToCityMapping(mappingResponse);
          this.cityList = mappingResponse.map(item => { return { name: item.city, region: item.region }; });
          this.regionList = mappingResponse.map(item => { return { name: item.region }; });
          createUserCityMapping(this.cityList, this.regionList);
        });
      }
    },

    getPTypeAndL0List() {
      api.fetchPTypeAndL0List().then((response) => {
        let pTypeList = response.data?.data?.find((listItem) => listItem?.field === 'ptype')?.filtered_values;
        let l0List = response.data?.data?.find((listItem) => listItem?.field === 'l0_category')?.filtered_values;

        this.updatePTypeList(pTypeList);
        this.updateL0CategoryList(l0List);
      })
    },

    ...mapActions(useUserStore, ['login', 'updateAllowedNav', 'updateAllowedTenants', 'updateActiveTenant']),
    ...mapActions(useCityStore, ['updateStoresCurrentViewCity', 'updateStoresDayViewCity', 'updateYesterdayCity', 'updateCityMapping', 'updateRegionMapping', 'updateStorePageCityMapping', 'updateStoreToCityMapping', 'updatePTypeList', 'updateL0CategoryList', 'updateBistroStoreToCityMapping']),
  },

  mounted() {
    this.fetchAccessToken();
  },

  data() {
    return {
      cityList: [],
      regionList: [],
    }
  }
}

</script>
