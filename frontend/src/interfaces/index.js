import { ABSOLUTE_METRIC_DELTA, BPS_METRICS } from "../utils/metrics";

export class Metric {
    constructor(value, type, meta={}) {
        this.value = value;
        this.type = type;
        this.meta = meta;
    }

    static addMetric(metric1, metric2) {
        if (metric1.type !== metric2.type) {
            throw new Error('Cannot add metrics of different types');
        }

        if (metric1.type == 'percentage') {
            throw new Error('Cannot add metrics of percentage types');
        }

        return new Metric(metric1.value + metric2.value, metric1.type, {...metric1.meta, ...metric2.meta});
    }
}

export class MetricChange {
    constructor(current, previous, name) {
        this.current = current;
        this.previous = previous;
        this.name = name;
    }

    change() {
        if(this.previous == undefined || this.previous?.value == "-")
        {
            return "-";
        }

        if (this.current?.type && this.previous?.type && this.current.type !== this.previous?.type) {
            throw new Error('Metrics are of different types.');
        }

        if (this.current.type == 'percentage') {
            if (this.curent?.value == '-')
            {
                return (0 - this.previous?.value).toFixed(1);
            }

            if(BPS_METRICS.includes(this.name)){
                return (this.current?.value - this.previous?.value).toFixed(2);
            }

            return (this.current?.value - this.previous?.value).toFixed(1);
        }

        let diff = (this.current?.value == '-') ? -100 : ((this.current?.value - this.previous?.value) / this.previous?.value * 100);

        if(ABSOLUTE_METRIC_DELTA.includes(this.name)){
            diff = (this.current?.value - this.previous?.value);
        }

        if (isNaN(diff)) {
            return 0;
        }
        else if (!isFinite(diff)) {
            return "-";
        }

        if(ABSOLUTE_METRIC_DELTA.includes(this.name)){
            let m_diff = diff.toFixed(2);
            if(m_diff.endsWith('.00'))return diff.toFixed(0);
            return m_diff;
        }

        if(BPS_METRICS.includes(this.name)){
            return diff.toFixed(2);    
        }

        return diff.toFixed(1);
    }

    style(reverseStyle=false) {
        if (reverseStyle){
            return this.change() > 0 ? 'text-negative-metric' : 'text-positive-metric';
        } else {
            return this.change() >= 0 ? 'text-positive-metric' : 'text-negative-metric';
        }
    }
}
