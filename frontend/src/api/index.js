import axios from 'axios';
import { apiUrl } from '../env';
import { useUserStore } from '../stores/users';
import isEmpty from 'lodash.isempty';
import { BISTRO_PAGE_ID_ROUTE_MAPPING, PAGE_ID_ROUTE_MAPPING } from '../constants/pages';
import router from '../router';

function authHeaders(cancelToken) {
    let token = useUserStore().accessToken;
    return {
        headers: {
            Authorization: `Bearer ${token}`,
        },
        cancelToken: cancelToken,
    };
}

function refreshHeaders() {
    let token = useUserStore().refreshToken;
    return {
        headers: {
            Authorization: `Bearer ${token}`,
        },
    };
}

// Function to handle forced logout
function forceLogout() {
    const userStore = useUserStore();
    userStore.logout();
    localStorage.clear();

    // Determine the correct logout route based on current path
    const currentPath = window.location.pathname;
    const logoutPath = currentPath.includes('bistro') ? '/bistro/logout' : '/logout';

    // Navigate to logout page
    router.replace(logoutPath);
}

axios.interceptors.response.use(
    function (response) {
        return response;
    },
    async function (err) {
        if (axios.isCancel(err)) return Promise.reject(err);

        const originalConfig = err.config;
        if (originalConfig.url !== `${apiUrl}/api/v1/auth/refresh` && err.response) {
            // Handle both 401 and 403 responses for token refresh
            if ((err.response.status === 401 || err.response.status === 403) && !originalConfig._retry) {
                console.log(`${err.response.status} error detected, attempting token refresh`);
                originalConfig._retry = true;
                try {
                    console.log("Calling refresh token API");
                    const rs = await api.refreshToken();
                    console.log("Token refresh successful");
                    useUserStore().updateTokens(rs.data.access_token, rs.data.refresh_token);

                    let token = useUserStore().accessToken;

                    // Parse the JWT token payload
                    try {
                        let tokenPayload = token?.split('.')?.[1];
                        if (!tokenPayload) {
                            console.error("Invalid token format - no payload section");
                        } else {
                            let accessMap = JSON.parse(atob(tokenPayload))['user_access_mapping'];

                            if (!isEmpty(accessMap)) {
                                let allowedTenants = Object.keys(accessMap)?.map((tenant) => {
                                    if (!isEmpty(accessMap[tenant]?.allowed_pages)) return tenant;
                                })?.filter(Boolean)
                                useUserStore().updateAllowedTenants(allowedTenants)

                                let totalNavs = allowedTenants?.map((tenant) => accessMap[tenant].allowed_pages)?.flat()
                                let allowedNavs = totalNavs?.map((navId) => PAGE_ID_ROUTE_MAPPING[navId] || BISTRO_PAGE_ID_ROUTE_MAPPING[navId]);
                                useUserStore().updateAllowedNav(allowedNavs)
                            }
                        }
                    } catch (parseError) {
                        console.error("Error parsing token:", parseError);
                    }

                    // Update the original request with the new token
                    originalConfig.headers = {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    };

                    return axios.request(originalConfig);
                } catch (_error) {
                    console.error("Token refresh failed:", _error);
                    // If refresh token fails, force logout
                    forceLogout();
                    return Promise.reject(_error);
                }
            }
        }

        return Promise.reject(err);
    }
)

// Helper function to add metrics to URLSearchParams
function appendMetricsToParams(params, metrics) {
    metrics.forEach(metric => params.append('metric', metric));
    return params;
}

// Helper function to handle city code condition
function getCityCode(city_code) {
    let panIndiaCityNames = ["#Pan-India", "All", ""];
    return panIndiaCityNames.includes(city_code) ? "" : city_code;
}

function processParams(city_code = "", store_code = "", params = "", zone = "", merchant_type = "", start_date = "", date_str = "", group_id = "", enable_group_view = false) {

    let finalParams = "";
    if (zone) {
        if (zone.includes("&zone=")) {
            finalParams += encodeURIComponent(zone);
        } else {
            finalParams += zone ? `&zone=${encodeURIComponent(zone)}` : "";
        }
    }
    if (city_code) {
        if (city_code.includes("&city=")) {
            finalParams += `&city=${city_code}`;
        } else {
            let cityCode = getCityCode(city_code);
            finalParams += cityCode ? `&city=${cityCode}` : "";
        }
    }
    if (store_code) finalParams += `&store=${store_code}`;
    if (merchant_type) finalParams += `&merchant_type=${merchant_type}`;
    if (start_date) finalParams += `&start_date=${start_date}`;
    if (date_str) finalParams += `&date_str=${date_str}`;
    if (group_id) finalParams += `&group_id=${group_id}`;
    if (enable_group_view) finalParams += `&enable_group_view=${enable_group_view}`;

    if (!params) return finalParams;
    return `${params.toString()}${finalParams}`;
}

function getSearchedParam(key, value) {
    if (!value) return "";
    let param = "";
    if (typeof value === 'string' || typeof value === 'number') param = `&${key}=${encodeURIComponent(value)}`
    else value.forEach((val) => { if (val) { param += `&${key}=${encodeURIComponent(val)}` } });
    return param;
}

function getProductParams({
    params = "", city = "", store = "", l0_category = "", l1_category = "", l2_category = "",
    ptype = "", pname = "", brand = "", search_filter = "", yesterday_metric = false, is_daywise = false, current_hour = false,
    start_date = "", end_date = "", pid = "", metrics_type = "", itemId = "", itemName = "", group_id = "",
    date = "", hour = -1, enable_group_view = false, date_str = "", zone = "", station_id = "", enable_zone_view, merchant_type = "", print_type = "", er_id = "",
    metric = "",
}) {
    let pTypes = getSearchedParam('ptype', ptype);
    let l0Category = getSearchedParam('l0_category', l0_category);
    let l1Category = getSearchedParam('l1_category', l1_category);
    let pId = getSearchedParam('pid', pid);
    let pName = getSearchedParam('pname', pname);
    let Brand = getSearchedParam('brand', brand);
    let l2Category = getSearchedParam('l2_category', l2_category);

    let searchedParams = "";
    if (yesterday_metric) searchedParams += `&yesterday_metric=${yesterday_metric}`;
    if (is_daywise) searchedParams += `&is_daywise=${is_daywise}`;
    if (current_hour) searchedParams += `&current_hour=${current_hour}`;
    if (city) searchedParams += `&city=${city}`;
    if (store) searchedParams += `&store=${store}`;
    if (merchant_type) searchedParams += `&merchant_type=${merchant_type}`;
    if (!isEmpty(l0Category)) searchedParams += `${l0Category}`;
    if (!isEmpty(l1Category)) searchedParams += `${l1Category}`;
    if (!isEmpty(l2Category)) searchedParams += `${l2Category}`;
    if (!isEmpty(pTypes)) searchedParams += `${pTypes}`;
    if (!isEmpty(pName) || pName) searchedParams += `${pName}`;
    if (!isEmpty(Brand)) searchedParams += `${Brand}`;
    if (search_filter) searchedParams += `&search_filter=${search_filter}`;
    if (start_date) searchedParams += `&start_date=${start_date}`;
    if (end_date) searchedParams += `&end_date=${end_date}`;
    if (!isEmpty(pId)) searchedParams += `${pId}`;
    if (metrics_type) searchedParams += `&metrics_type=${metrics_type}`;
    if (itemId) searchedParams += `&item_id=${encodeURIComponent(itemId)}`;
    if (itemName) searchedParams += `&item_name=${encodeURIComponent(itemName)}`;
    if (group_id) searchedParams += `&group_id=${group_id}`;
    if (date) searchedParams += `&date=${date}`;
    if (hour !== -1) searchedParams += `&hour=${hour}`;
    if (enable_group_view) searchedParams += `&enable_group_view=${enable_group_view}`;
    if (date_str) searchedParams += `&date_str=${date_str}`;
    if (zone) searchedParams += `&zone=${zone}`;
    if (station_id) searchedParams += `&station=${station_id}`;
    if (enable_zone_view ) searchedParams += `&enable_zone_view=${enable_zone_view}`;
    if (print_type) searchedParams += `&print_type=${print_type}`;
    if (er_id) searchedParams += `&er_id=${er_id}`;
    if (metric) searchedParams += `&metric=${metric}`;
    if (!params) return searchedParams;
    return `${params.toString()}${searchedParams}`;
}


export const api = {

    refreshToken() {
        try {
            return axios.post(`${apiUrl}/api/v1/auth/refresh`, {}, refreshHeaders());
        } catch (error) {
            console.error("Error refreshing token:", error);
            throw error;
        }
    },

    fetchGoogleURL() {
        return axios.get(`${apiUrl}/api/v1/login/google`);
    },

    fetchAccessToken(params) {
        return axios.get(`${apiUrl}/api/v1/auth/google/callback`, { params });
    },



    // fetchAllMetrics(metrics, city_code, store_code, cancelToken) {
    //     let params = new URLSearchParams();
    //     appendMetricsToParams(params, metrics);
    //     let searchedParams = processParams(city_code, store_code, params);
    //     return axios.get(`${apiUrl}/api/v1/order-metrics/all?${searchedParams}`, authHeaders(cancelToken));
    // },

    fetchAllOrderMetricsForInstore({ metrics, yesterday_metric, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type, yesterday_metric});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/all/order?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllDauMetricsForInstore({ yesterday_metric, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = getProductParams({ city, store, params, merchant_type, yesterday_metric});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/all/dau?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllActiveTimeMetricsForInstore({ metrics, yesterday_metric, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type, yesterday_metric});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/all/active-time?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllComplaintsMetricsForInstore({ metrics, yesterday_metric, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type, yesterday_metric});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/all/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    // fetchAllYesterdayMetrics(metrics, city_code, store_code, cancelToken) {
    //     let params = new URLSearchParams();
    //     appendMetricsToParams(params, metrics);
    //     let searchedParams = processParams(city_code, store_code, params);
    //     return axios.get(`${apiUrl}/api/v1/order-metrics/yesterday/all?${searchedParams}`, authHeaders(cancelToken));
    // },

    fetchHourlyMetrics(metrics, yesterday_metric = false, city, zone, store, merchant_type, cancelToken, group_id, enable_group_view) {
        let params = new URLSearchParams({ yesterday_metric: yesterday_metric });
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, zone, merchant_type, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v1/order-metrics/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchInstoreHourlyMetrics(metrics, yesterday_metric, city, store, merchant_type, cancelToken) {
        let params = new URLSearchParams({ yesterday_metric: yesterday_metric });
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchComplaintsHourlyMetrics(metrics, yesterday_metric, city, store, merchant_type, cancelToken) {
        let params = new URLSearchParams({ yesterday_metric: yesterday_metric });
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, merchant_type});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/hourly/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlyFunnelMetrics(yesterday_metric = false, city_code, zone_code, store_code, group_id, enable_group_view, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v1/order-metrics/hourly/funnel?yesterday_metric=${yesterday_metric}${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityMetrics({ city, date, hour = -1, enable_group_view, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view});
        return axios.get(`${apiUrl}/api/v1/order-metrics/cities?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoreMetrics({ city, zone, date, hour = -1, group_id, enable_group_view, enable_zone_view = true, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view, group_id, zone, enable_zone_view:(enable_group_view?false:enable_zone_view) });
        return axios.get(`${apiUrl}/api/v1/order-metrics/cities/stores?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityDauMetrics({ city, date, hour = -1, enable_group_view, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view});
        return axios.get(`${apiUrl}/api/v1/order-metrics/cities/dau?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoreDauMetrics({ city, zone, date, hour = -1, group_id, enable_group_view, enable_zone_view = true, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view, group_id, zone, enable_zone_view:(enable_group_view?false:enable_zone_view) });
        return axios.get(`${apiUrl}/api/v1/order-metrics/cities/stores/dau?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityWiseMetrics(metrics = [], is_yesterday = false, city_wise = false, store_wise = false, is_critical_metrics = false, city_code, merchant_type, cancelToken) {
        let params = new URLSearchParams({ yesterday_metric: is_yesterday, city_wise: city_wise, store_wise: store_wise, is_critical_metrics: is_critical_metrics });
        appendMetricsToParams(params, metrics);
        let searchedParams = processParams(city_code, '', params, '', merchant_type);
        return axios.get(`${apiUrl}/api/v1/order-metrics/cities/base/comparision?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStressedStoreCityMetrics() {
        return axios.get(`${apiUrl}/api/v1/stores/stressed/cities/base/0/comparision/7`, authHeaders());
    },

    fetchStoreCityMapping() {
        return axios.get(`${apiUrl}/api/v1/stores/storecitymapping`, authHeaders());
    },

    fetchMTDMetrics(metrics, city_code, zone, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params, zone)
        return axios.get(`${apiUrl}/api/v1/mtd_metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchMTDAllMetrics(metrics, city_code, zone, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params, zone)
        return axios.get(`${apiUrl}/api/v1/mtd_metrics/trends?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchWTDMetrics(metrics, city_code, zone, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params, zone)
        return axios.get(`${apiUrl}/api/v1/wtd_metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchWTDAllMetrics(metrics, city_code, zone, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params, zone)
        return axios.get(`${apiUrl}/api/v1/wtd_metrics/trends?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAvailabilityMetrics(cancelToken) {
        return axios.get(`${apiUrl}/api/v1/availability_metrics/cities`, authHeaders(cancelToken));
    },

    fetchPanIndiaHAU() {
        return axios.get(`${apiUrl}/api/v1/hau/pan_india?past_days_diff=7`, authHeaders());
    },

    fetchPanIndiaYesterdayHAU(city_code, store_code, cancelToken) {
        let searchedParams = processParams(city_code, store_code);
        return axios.get(`${apiUrl}/api/v1/hau/pan_india/yesterday?${searchedParams}`, authHeaders(cancelToken));
    },

    // fetchPanIndiaSurgeSeen(yesterday_metric)  {
    //     return axios.get(`${apiUrl}/api/v1/surge_seen/hourly?yesterday_metric=${yesterday_metric}`, authHeaders());
    // },

    // fetchCitySurgeSeen(city_code)  {
    //     return axios.get(`${apiUrl}/api/v1/surge_seen/hourly?city=${city_code}`, authHeaders());
    // },

    // fetchStoreSurgeSeen(city_code, store_code)  {
    //     return axios.get(`${apiUrl}/api/v1/surge_seen/hourly?city=${city_code}&store=${store_code}`, authHeaders());
    // },

    // fetchCitiesSurgeSeen()  {
    //     return axios.get(`${apiUrl}/api/v1/surge_seen/cities`, authHeaders());
    // },

    fetchPanIndiaRiderMetrics(yesterday_metric = false, city_code, zone_code, store_code, group_id, enable_group_view, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v1/rider_metrics/hourly?yesterday_metric=${yesterday_metric}${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityRiderMetrics(city_code) {
        return axios.get(`${apiUrl}/api/v1/rider_metrics/hourly?city=${city_code}`, authHeaders());
    },

    fetchStoreRiderMetrics(city_code, store_code) {
        let searchedParams = processParams(city_code, store_code);
        return axios.get(`${apiUrl}/api/v1/rider_metrics/hourly?${searchedParams}`, authHeaders());
    },

    fetchCitiesRiderMetrics({ city, date, hour = -1, enable_group_view, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view});
        return axios.get(`${apiUrl}/api/v1/rider_metrics/cities?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoresRiderMetrics({ city, zone, date, hour = -1, enable_group_view, group_id, enable_zone_view = true,cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view, group_id, zone, enable_zone_view:(enable_group_view?false:enable_zone_view) });
        return axios.get(`${apiUrl}/api/v1/rider_metrics/cities/stores?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCitiesNewRiderMetrics({ city, date, hour = -1, enable_group_view, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view});
        return axios.get(`${apiUrl}/api/v1/rider_metrics/new/cities?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoresNewRiderMetrics({ city, zone, date, hour = -1, enable_group_view, group_id, enable_zone_view = true, cancelToken }) {
        let searchedParams = getProductParams({ city, date, hour, enable_group_view, group_id, zone , enable_zone_view:(enable_group_view?false:enable_zone_view)});
        return axios.get(`${apiUrl}/api/v1/rider_metrics/new/cities/stores?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityHAU(city_code) {
        return axios.get(`${apiUrl}/api/v1/hau/pan_india?past_days_diff=7&city=${city_code}`, authHeaders());
    },
    fetchStoreHAU(city_code, zone_code, store_code, group_id, enable_group_view, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v1/hau/pan_india?past_days_diff=7${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCurrentRateMetrics(city_code, store_code, cancelToken) {
        let searchedParams = processParams(city_code, store_code);
        return axios.get(`${apiUrl}/api/v1/current_rate_metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },
    fetchCurrentRateHAU(cancelToken) {
        return axios.get(`${apiUrl}/api/v1/current_rate_metrics/hau/pan_india?past_days_diff=7`, authHeaders(cancelToken));
    },
    fetchBiweeklyProjectionMetrics(city_code, cancelToken) {
        let searchedParams = processParams(city_code);
        return axios.get(`${apiUrl}/api/v1/current_rate_metrics/daily/projection_metrics?projection_days=15&${searchedParams}`, authHeaders(cancelToken));
    },
    fetchBucketProjectionMetrics(bucket, city_code, zone, store_code, cancelToken) {
        let projectionEndpoint = '';
        let searchedParams = processParams(city_code, store_code, '', zone);

        switch (bucket) {
            case 'yesterday':
                projectionEndpoint = 'yesterday_projection=true';
                break;
            case 'weekly':
                projectionEndpoint = 'weekly_projection=true';
                break;
            case 'monthly':
                projectionEndpoint = 'monthly_projection=true';
                break;
            default:
                throw new Error('Invalid projection type. Allowed values are "yesterday", "weekly", or "monthly".');
        }
        return axios.get(`${apiUrl}/api/v1/current_rate_metrics/bucket/projection_metrics?${projectionEndpoint}${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoresMetrics(city_code, cancelToken) {
        let searchedParams = processParams(city_code);
        return axios.get(`${apiUrl}/api/v1/stores/store_metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchActiveStoresCount(city_code, cancelToken) {
        let searchedParams = processParams(city_code);
        return axios.get(`${apiUrl}/api/v1/stores/active_stores?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCitiesAndGroups(cancelToken) {
        return axios.get(`${apiUrl}/api/v1/stores/cities_and_groups`, authHeaders(cancelToken));
    },

    fetchFunnelConversionMetrics(date = null, hour = null) {
        return axios.get(`${apiUrl}/api/v1/insights/conversion_funnel?filter_date=${date}&filter_hour=${hour}`, authHeaders())
    },

    fetchCityInsightsMetrics(metrics = [], is_yesterday = false, city_code = '', store_code = '', filter_hour = -1, day_diff = 7, store_response = false, cancelToken) {
        let params = new URLSearchParams({ yesterday_metric: is_yesterday, ...(metrics.length > 0 && { metric: metrics }), ...(city_code && { city: city_code }), ...(store_code && { store: store_code }), ...({ filter_hour: filter_hour }), ...(day_diff && { day_diff: day_diff }), ...(store_response && { store_response: store_response }) });
        return axios.get(`${apiUrl}/api/v1/insights/conversion_funnel?${params}`, authHeaders(cancelToken))
    },

    fetchPtypeInsightsMetrics(metrics = [], is_yesterday = false, city_code = '', store_code = '', filter_hour = -1, day_diff = 7, metrics_type, cancelToken) {
        let params = new URLSearchParams({ yesterday_metric: is_yesterday, ...(metrics.length > 0 && { metric: metrics }), ...(city_code && { city: city_code }), ...(store_code && { store: store_code }), ...({ filter_hour: filter_hour }), ...(day_diff && { day_diff: day_diff }), ...(metrics_type && { metrics_type: metrics_type }) });
        return axios.get(`${apiUrl}/api/v1/insights/ptype_metrics?${params}`, authHeaders(cancelToken))
    },

    fetchWarehouseMapping() {
        return axios.get(`${apiUrl}/api/v1/warehouses/warehousemapping`, authHeaders());
    },

    // fetchWarehouseDispatchMetrics(city_code, store_wise, outlet_id=null, cancelToken){
    //     let searchedParams = processParams(city_code);
    //     let searchedOutlet = outlet_id == null ?'' : `&outlet=${outlet_id}`;
    //     return axios.get(`${apiUrl}/api/v1/warehouses/dispatch_metrics?store_wise=${store_wise.toString()}${searchedParams}${searchedOutlet}`, authHeaders(cancelToken));
    // },

    fetchATHMetrics(city_code, store_code, group_id, enable_group_view, cancelToken) {
        // Todo: store level filter not added atm
        let searchedParams = processParams(city_code, store_code, "", "", "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v1/order-metrics/ath?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchProductFilters({ l0_category = '', l1_category = '', l2_category = '', ptype = '', pname = '', brand = '', pid = "", search_filter = '',  cancelToken }) {
        let searchedParams = getProductParams({ l0_category, l1_category, l2_category, ptype, pname, brand, pid, search_filter });
        return axios.get(`${apiUrl}/api/v1/product-metrics/filter?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchProductMetrics({ metrics = [], city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, is_daywise, current_hour, pid, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);

        let searchedParams = getProductParams({ params, city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, is_daywise, current_hour, pid });

        return axios.get(`${apiUrl}/api/v1/product-metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchProductComplaints({ city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, is_daywise, current_hour, pid, cancelToken }) {
        let searchedParams = getProductParams({ city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, is_daywise, current_hour, pid });
        return axios.get(`${apiUrl}/api/v1/product-metrics/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchPTypeAndL0List() {
        let searchedParams = "&search_filter=ptype&search_filter=l0_category";
        return axios.get(`${apiUrl}/api/v1/product-metrics/list/primary?${searchedParams}`, authHeaders());
    },

    fetchCitiesProductMetrics({ metrics = [], city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, pid, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);

        let searchedParams = getProductParams({ params, city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, pid });

        return axios.get(`${apiUrl}/api/v1/product-metrics/cities?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlyProductMetrics({ metrics = [], city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, pid, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);

        let searchedParams = getProductParams({ params, city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, pid });

        return axios.get(`${apiUrl}/api/v1/product-metrics/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchComplaintImages({ city, store, l0_category, l1_category, l2_category, ptype, pname, brand, start_date, end_date, pid, cancelToken }) {
        let searchedParams = getProductParams({ city, store, l0_category, l1_category, l2_category, ptype, pname, brand, start_date, end_date, pid });
        return axios.get(`${apiUrl}/api/v1/complaints/images?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlySurgeMetrics(city, store, zone, yesterday_metric, group_id, enable_group_view, cancelToken) {
        let searchedParams = getProductParams({ city, store, zone, yesterday_metric, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v1/order-metrics/hourly/surge?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCitiesProductPtypeMetrics({ city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric, cancelToken }) {
        let searchedParams = getProductParams({ city, store, l0_category, l1_category, l2_category, ptype, pname, brand, yesterday_metric });
        return axios.get(`${apiUrl}/api/v1/product-metrics/ptype_metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchItemInsightsMetrics(metrics = [], is_yesterday = false, city_code = '', store_code = '', filter_hour = -1, product_type = '', cancelToken) {
        let params = new URLSearchParams({ yesterday_metric: is_yesterday, ...(metrics.length > 0 && { metric: metrics }), ...(city_code && { city: city_code }), ...(store_code && { store: store_code }), ...({ filter_hour: filter_hour }), ...(product_type && { product_type: product_type }) });
        return axios.get(`${apiUrl}/api/v1/insights/item_metrics?${params}`, authHeaders(cancelToken))
    },

    // fetchWarehousesOutboundMetrics({metric_type = "", outlet = "", previous_shift = false, cancelToken}){
    //     let params = new URLSearchParams({previous_shift: previous_shift,...(metric_type && { metric_type: metric_type }),...(outlet && { outlet: outlet })});
    //     return axios.get(`${apiUrl}/api/v1/warehouses/outbound_metrics?${params}`, authHeaders(cancelToken))
    // },

    fetchPtypeProductSpikeMetrics({ city, store, ptype, yesterday_metric, metrics_type, l0_category, cancelToken }) {
        let searchedParams = getProductParams({ city, store, ptype, yesterday_metric, metrics_type, l0_category });
        return axios.get(`${apiUrl}/api/v1/insights/ptype_metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchFreebieMetrics({ city_code, store_code, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, ["freebie_order_percentage"]);
        let searchedParams = processParams(city_code, store_code, params, "", "", "", "", "", false);
        return axios.get(`${apiUrl}/api/v1/product-metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchYesterdayFreebieMetrics({ city_code, store_code, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, ["freebie_order_percentage"]);
        let searchedParams = processParams(city_code, store_code, params, "", "", "", "", "", false);
        searchedParams += "&yesterday_metric=true";
        return axios.get(`${apiUrl}/api/v1/product-metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchWarehouseAvailabilityMetrics({ cancelToken }) {
        return axios.get(`${apiUrl}/api/v1/warehouses/availability?`, authHeaders(cancelToken))
    },

    fetchWarehouseOutboundMetrics({ cancelToken }) {
        return axios.get(`${apiUrl}/api/v1/warehouses/outbound_metrics?`, authHeaders(cancelToken))
    },

    fetchWarehouseManPowerMetrics({ outlet_id, cancelToken }) {
        return axios.get(`${apiUrl}/api/v1/warehouses/manpower_metrics?outlet=${outlet_id}`, authHeaders(cancelToken))
    },

    fetchWarehouseSlotWiseMetrics({ outlet_id, cancelToken }) {
        return axios.get(`${apiUrl}/api/v1/warehouses/slot_wise_metrics?outlet=${outlet_id}`, authHeaders(cancelToken))
    },

    fetchWarehouseSlotMetrics({ outlet_id, slot, cancelToken }) {
        return axios.get(`${apiUrl}/api/v1/warehouses/availability?outlet=${outlet_id}&slot=${slot}&is_slot_wise=true`, authHeaders(cancelToken))
    },

    fetchCategoryMetrics({ metric, city, cancelToken }) {
        let searchedParams = getProductParams({ city });
        return axios.get(`${apiUrl}/api/v1/category-metrics/all?metric=${metric}${searchedParams}`, authHeaders(cancelToken));
    },

    fetchYesterdayCategoryMetrics({ metric, city, yesterday_metric = true, cancelToken }) {
        let searchedParams = getProductParams({ city, yesterday_metric });
        return axios.get(`${apiUrl}/api/v1/category-metrics/yesterday/all?metric=${metric}${searchedParams}`, authHeaders(cancelToken));
    },

    fetchTodaysProjectionMetrics(city_code, store_code, zone_code, cancelToken, group_id, enable_group_view) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v1/current_rate_metrics/all/projection_metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCategoryInsightMetrics({ city, yesterday_metric, cancelToken }) {
        let searchedParams = getProductParams({ city, yesterday_metric });
        return axios.get(`${apiUrl}/api/v1/category-metrics/insight?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchL1CategoryMetrics({ metric, city, l0_category, cancelToken }) {
        let searchedParams = getProductParams({ city, l0_category, metric });
        return axios.get(`${apiUrl}/api/v1/category-metrics/l1?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchYesterdayL1CategoryMetrics({ metric, city, l0_category, cancelToken }) {
        let searchedParams = getProductParams({ city, l0_category, metric });
        return axios.get(`${apiUrl}/api/v1/category-metrics/l1/yesterday?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchBadStocksImages({ city, store, l0_category, l1_category, l2_category, ptype, itemName, brand, start_date, end_date, itemId, er_id, cancelToken }) {
        let searchedParams = getProductParams({ city, store, l0_category, l1_category, l2_category, ptype, itemName, brand, start_date, end_date, itemId, er_id });
        return axios.get(`${apiUrl}/api/v1/bad_stock/images?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchBadStockFilters({ l0_category = '', l1_category = '', l2_category = '', ptype = '', item_name = '', brand = '', item_id = "", er_id = "", search_filter = '', cancelToken }) {
        let searchedParams = getProductParams({ l0_category, l1_category, l2_category, ptype, item_name, brand, item_id, er_id, search_filter });
        return axios.get(`${apiUrl}/api/v1/bad_stock/filter?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchBadStockPTypeAndL0List() {
        let searchedParams = "&search_filter=ptype&search_filter=l0_category";
        return axios.get(`${apiUrl}/api/v1/bad_stock/list/primary?${searchedParams}`, authHeaders());
    },

    fetchMagic() {
        return axios.get(`${apiUrl}/api/v1/product-metrics/magic?`, authHeaders());
    },

    fetchCurrentRateDailyMetrics(city_code, cancelToken) {
        let searchedParams = processParams(city_code);
        return axios.get(`${apiUrl}/api/v1/current_rate_metrics/daily/projection_metrics/events?projection_days=15&${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllEmergencyBaseMetrics({ metrics, date_str, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);

        let searchedParams = processParams("", "", params, "", "", "", date_str);
        return axios.get(`${apiUrl}/api/v1/emergency-service-metrics/all/base-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllAccessibleGroups(cancelToken) {
        return axios.get(`${apiUrl}/api/v1/groups/accessible-groups`, authHeaders(cancelToken));
    },

    createGroup(group, cancelToken) {
        return axios.post(`${apiUrl}/api/v1/groups/`, group, authHeaders(cancelToken));
    },

    getGroupById(groupId, cancelToken) {
        return axios.get(`${apiUrl}/api/v1/groups/${groupId}`, authHeaders(cancelToken));
    },

    editGroup(groupId, group, cancelToken) {
        return axios.put(`${apiUrl}/api/v1/groups/${groupId}`, group, authHeaders(cancelToken));
    },

    deleteGroup(groupId) {
        return axios.delete(`${apiUrl}/api/v1/groups/${groupId}`, authHeaders());
    },

    fetchPaasMetrics({ metrics, city, store, print_type, date_str, is_daywise, current_hour, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, print_type, date_str });
        return axios.get(`${apiUrl}/api/v1/paas-metrics/all/base-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchPaasHourlyMetrics({ metrics, city, store, date_str, print_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, date_str, print_type });
        return axios.get(`${apiUrl}/api/v1/paas-metrics/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchPaasComplaints({ city, store, print_type, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, store, print_type, date_str });
        return axios.get(`${apiUrl}/api/v1/paas-metrics/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchPaasCityMetrics({ city, store, print_type, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, store, print_type, date_str });
        return axios.get(`${apiUrl}/api/v1/paas-metrics/cities?${searchedParams}`, authHeaders(cancelToken));
    },
}

export const bistroApi = {
    baseUrl: `${apiUrl}/api/bistro/v1/`,
    fetchAllMetrics(metrics, city_code, store_code, date_str, cancelToken) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = processParams(city_code, store_code, params, "", "", "", date_str);
        return axios.get(`${this.baseUrl}order-metrics/all/base-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlyMetrics(metrics, date_str, city_code, store_code, cancelToken) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = processParams(city_code, store_code, params, "", "", "", date_str);
        return axios.get(`${this.baseUrl}order-metrics/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoreHAU(city_code, store_code, date_str, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", "", "", "", date_str);
        return axios.get(`${this.baseUrl}hau/pan_india?past_days_diff=7${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllDAUMetrics(city_code, store_code, date_str, cancelToken) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, "", "", "", date_str);
        return axios.get(`${this.baseUrl}order-metrics/all/dau-metrics?${searchedParams}`, authHeaders(cancelToken));
    },
    
    fetchProductFilters({ l0_category = '', l1_category = '', l2_category = '', ptype = '', pname = '', brand = '', pid = "", search_filter = '' , cancelToken }) {
        let searchedParams = getProductParams({ l0_category, l1_category, l2_category, ptype, pname, brand, pid, search_filter });
        return axios.get(`${this.baseUrl}product-metrics/filter?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchProductMetrics({ metrics = [], city, store, yesterday_metric, cancelToken, l0_category, pname, ptype, pid, brand, l1_category, l2_category }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, yesterday_metric, l0_category, pname, ptype, pid, brand, l1_category, l2_category });
        return axios.get(`${this.baseUrl}product-metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchBistroStoreCityMapping() {
        return axios.get(`${this.baseUrl}stores/storecitymapping`, authHeaders());
    },

    fetchStationMapping() {
        return axios.get(`${this.baseUrl}stations/stationmapping`, authHeaders());
    },

    // fetchNTUMetrics(city_code, store_code, date_str, cancelToken) {
    //     let params = new URLSearchParams();
    //     let searchedParams = processParams(city_code, store_code, params, "", "", "", date_str);
    //     return axios.get(`${this.baseUrl}order-metrics/all/ntu-metrics?${searchedParams}`, authHeaders(cancelToken));
    // },

    fetchRiderMetrics(city_code, store_code, date_str, cancelToken) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, "", "", "", date_str);
        return axios.get(`${this.baseUrl}rider-metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    // fetchYesterdayRiderMetrics(city_code, store_code, cancelToken) {
    //     let params = new URLSearchParams();
    //     let searchedParams = processParams(city_code, store_code, params);
    //     return axios.get(`${apiUrl}/api/bistro/v1/rider-metrics/yesterday/all?${searchedParams}`, authHeaders(cancelToken));
    // },

    fetchHourlyRiderMetrics(city_code, store_code, date_str, cancelToken) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, "", "", "", date_str);
        return axios.get(`${this.baseUrl}rider-metrics/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    // fetchYesterdayHourlyRiderMetrics(city_code, store_code, cancelToken) {
    //     let params = new URLSearchParams();
    //     let searchedParams = processParams(city_code, store_code, params);
    //     return axios.get(`${apiUrl}/api/bistro/v1/rider-metrics/yesterday/hourly?${searchedParams}`, authHeaders(cancelToken));
    // },

    fetchProductComplaints({ city, store, yesterday_metric, cancelToken, ptype, pname, pid, brand,l0_category, l1_category, l2_category } ) {
        let searchedParams = getProductParams({ city, store, yesterday_metric, pname, ptype, pid, brand,l0_category, l1_category, l2_category })
        return axios.get(`${this.baseUrl}product-metrics/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchOrderComplaints(city, store, date_str, cancelToken) {
        let searchedParams = getProductParams({ city, store, date_str })
        return axios.get(`${this.baseUrl}order-metrics/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchProductRatings({ city, store, yesterday_metric, cancelToken, ptype, pname, pid, brand, l0_category, l1_category, l2_category }) {
        let searchedParams = getProductParams({ city, store, yesterday_metric, pname, ptype, pid, brand, l0_category, l1_category, l2_category })
        return axios.get(`${this.baseUrl}rating-metrics/product?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchOrderMetricsRatings(city, store, date_str, cancelToken) {
        let searchedParams = getProductParams({ city, store, date_str })
        return axios.get(`${this.baseUrl}rating-metrics/order?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityMetrics(date, hour = -1, cancelToken) {
        let hourParams = hour !== -1 ? `&hour=${hour}` : '';
        return axios.get(`${this.baseUrl}order-metrics/cities?date=${date}${hourParams}`, authHeaders(cancelToken));
    },

    fetchStoreMetrics(city_code, date, hour = -1, cancelToken) {
        let hourParams = hour !== -1 ? `&hour=${hour}` : '';
        return axios.get(`${this.baseUrl}order-metrics/cities/stores?city=${city_code}&date=${date}${hourParams}`, authHeaders(cancelToken));
    },

    fetchCitiesRiderMetrics(date, hour = -1, cancelToken) {
        let hourParams = hour !== -1 ? `&hour=${hour}` : '';
        return axios.get(`${this.baseUrl}rider-metrics/cities?date=${date}${hourParams}`, authHeaders(cancelToken));
    },

    fetchStoresRiderMetrics(city_code, date, hour = -1, cancelToken) {
        let hourParams = hour !== -1 ? `&hour=${hour}` : '';
        return axios.get(`${this.baseUrl}rider-metrics/cities/stores?city=${city_code}&date=${date}${hourParams}`, authHeaders(cancelToken));
    },

    // fetchCitiesNewRiderMetrics(date, hour = -1, cancelToken)  {
    //     let hourParams = hour !== -1 ? `&hour=${hour}` : '';
    //     return axios.get(`${apiUrl}/api/bistro/v1/rider-metrics/new/cities?date=${date}${hourParams}`, authHeaders(cancelToken));
    // },

    // fetchStoresNewRiderMetrics(city_code, date, hour = -1, cancelToken)  {
    //     let hourParams = hour !== -1 ? `&hour=${hour}` : '';
    //     return axios.get(`${apiUrl}/api/bistro/v1/rider-metrics/new/cities/stores?city=${city_code}&date=${date}${hourParams}`, authHeaders(cancelToken));
    // },

    fetchMTDMetrics(metrics, city_code, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params)
        return axios.get(`${this.baseUrl}mtd_metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchMTDAllMetrics(metrics, city_code, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params)
        return axios.get(`${this.baseUrl}mtd_metrics/trends?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchWTDMetrics(metrics, city_code, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params)
        return axios.get(`${this.baseUrl}wtd_metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchWTDAllMetrics(metrics, city_code, cancelToken) {
        let params = new URLSearchParams()
        appendMetricsToParams(params, metrics)
        let searchedParams = processParams(city_code, '', params)
        return axios.get(`${this.baseUrl}wtd_metrics/trends?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCitiesRatingMetrics(date, hour = -1, cancelToken) {
        let hourParams = hour !== -1 ? `&hour=${hour}` : '';
        return axios.get(`${this.baseUrl}rating-metrics/cities?date=${date}${hourParams}`, authHeaders(cancelToken));
    },

    fetchStoresRatingMetrics(city_code, date, hour = -1, cancelToken) {
        let hourParams = hour !== -1 ? `&hour=${hour}` : '';
        return axios.get(`${this.baseUrl}rating-metrics/cities/stores?city=${city_code}&date=${date}${hourParams}`, authHeaders(cancelToken));
    },

    fetchATHMetricsBistro(city_code, store_code, date_str, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", "", "", "", date_str);
        return axios.get(`${this.baseUrl}order-metrics/ath?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStationMapping() {
        return axios.get(`${this.baseUrl}station-metrics/stationmapping`, authHeaders());
    },

    fetchStationsRatingMetrics({ city, store, station_id, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, store, date_str, station_id });
        return axios.get(`${this.baseUrl}rating-metrics/stations?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStationComplaints({ city, store, station_id, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, store, date_str, station_id });
        return axios.get(`${this.baseUrl}station-metrics/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStationBaseMetric({ metrics, city, store, station_id, date_str, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, date_str, station_id });
        return axios.get(`${this.baseUrl}station-metrics/base-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStationKptMetrics({ city, store, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, store, date_str });
        return axios.get(`${this.baseUrl}station-metrics/group-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchIPCMetrics({ city, store, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, store, date_str });
        return axios.get(`${this.baseUrl}product-metrics/ipc-metrics?${searchedParams}`, authHeaders(cancelToken));
    },
}


export const apiV2 = {
    fetchAllBaseMetrics({ metrics, city_code, zone_code, store_code, group_id, enable_group_view, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = processParams(city_code, store_code, params, zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/all/base-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllRiderMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/all/rider-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllDauMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/all/dau-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllFunnelMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/all/funnel-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllSurgeSeenMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = processParams(city_code, store_code, params, zone_code, "", "", "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/all/surge-seen-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCustomDateAllBaseMetrics({ metrics, city_code, zone_code, store_code, group_id, enable_group_view, start_date, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city: city_code, store: store_code, zone: zone_code, start_date, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/all/base-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCustomDateAllRiderMetrics({ city_code, zone_code, store_code, group_id, start_date, enable_group_view, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = getProductParams({ params, city: city_code, store: store_code, zone: zone_code, start_date, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/all/rider-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCustomDateAllDauMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, start_date, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = getProductParams({ params, city: city_code, store: store_code, zone: zone_code, start_date, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/all/dau-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCustomDateAllFunnelMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, start_date, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = getProductParams({ params, city: city_code, store: store_code, zone: zone_code, start_date, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/all/funnel-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCustomDateAllSurgeSeenMetrics({ city_code, zone_code, store_code, group_id, enable_group_view, start_date, cancelToken }) {
        let params = new URLSearchParams();
        let searchedParams = getProductParams({ params, city: city_code, store: store_code, zone: zone_code, start_date, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/all/surge-seen-metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlyMetrics(metrics, start_date, city, zone, store, merchant_type, cancelToken, group_id, enable_group_view) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, zone, store, merchant_type, start_date, group_id, enable_group_view });
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlyFunnelMetrics(start_date, city_code, zone_code, store_code, group_id, enable_group_view, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", start_date, "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/hourly/funnel?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchStoreHAU(city_code, zone_code, store_code, group_id, enable_group_view, start_date, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", start_date, "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/hau/custom/pan_india?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchPanIndiaRiderMetrics(start_date, city_code, zone_code, store_code, group_id, enable_group_view, cancelToken) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", start_date, "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/rider_metrics/custom/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCurrentRateMetrics(city_code, store_code, zone_code, start_date, cancelToken, group_id, enable_group_view) {
        let searchedParams = processParams(city_code, store_code, "", zone_code, "", start_date, "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/current_rate_metrics/custom/bucket/projection_metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchHourlySurgeMetrics(city, store, zone, group_id, enable_group_view, start_date, cancelToken) {
        let searchedParams = processParams(city, store, "", zone, "", start_date, "", group_id, enable_group_view);
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/hourly/surge?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCityWiseMetrics(metrics = [], start_date, city_wise = false, store_wise = false, is_critical_metrics = false, city_code, merchant_type, cancelToken) {
        let params = new URLSearchParams({ city_wise: city_wise, store_wise: store_wise, is_critical_metrics: is_critical_metrics });
        appendMetricsToParams(params, metrics);
        let searchedParams = processParams(city_code, '', params, '', merchant_type, start_date);
        return axios.get(`${apiUrl}/api/v2/order-metrics/custom/cities/base/comparision?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchCurrentRateAllMetrics(city_code, cancelToken) {
        let searchedParams = processParams(city_code);
        return axios.get(`${apiUrl}/api/v2/current_rate_metrics/all?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllOrderMetricsForInstore({ metrics, start_date, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type, start_date});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/custom/all/order?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllDauMetricsForInstore({ start_date, city, store, merchant_type, cancelToken }) {
        let searchedParams = getProductParams({ city, store, merchant_type, start_date});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/custom/all/dau?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllActiveTimeMetricsForInstore({ metrics, start_date, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type, start_date});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/custom/all/active-time?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchAllComplaintsMetricsForInstore({ metrics, start_date, city, store, merchant_type, cancelToken }) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ city, store, params, merchant_type, start_date});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/custom/all/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchInstoreHourlyMetrics(metrics, start_date, city, store, merchant_type, cancelToken) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, merchant_type, start_date});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/custom/hourly?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchComplaintsHourlyMetrics(metrics, start_date, city, store, merchant_type, cancelToken) {
        let params = new URLSearchParams();
        appendMetricsToParams(params, metrics);
        let searchedParams = getProductParams({ params, city, store, merchant_type, start_date});
        return axios.get(`${apiUrl}/api/v1/instore-metrics/custom/hourly/complaints?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchKeywordMetrics({ city, zone, store, start_date, cancelToken }) {
        let searchedParams = getProductParams({ city, zone, store, start_date});
        return axios.get(`${apiUrl}/api/v2/order-metrics/keyword_metrics?${searchedParams}`, authHeaders(cancelToken));
    },

    fetchOrdersPerMinute({ city, zone, store, group_id, enable_group_view, date_str, cancelToken }) {
        let searchedParams = getProductParams({ city, zone, store, group_id, enable_group_view, date_str });
        return axios.get(`${apiUrl}/api/v2/order-metrics/orders-per-minute?${searchedParams}`, authHeaders(cancelToken));
    }
}
