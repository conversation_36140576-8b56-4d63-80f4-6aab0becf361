import { createApp } from "vue";
import PrimeVue from "primevue/config";
import "primevue/resources/themes/aura-light-green/theme.css";
import "primeicons/primeicons.css";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import VueNumber from "vue-number-animation";
import App from "./App.vue";
import router from "./router";
import "./index.css";
import Vue<PERSON>on<PERSON>tti from "vue-confetti";
import ToastService from "primevue/toastservice";
import "vue-select/dist/vue-select.css";

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

createApp(App)
  .use(pinia)
  .use(router)
  .use(VueNumber)
  .use(PrimeVue)
  .use(VueConfetti)
  .use(ToastService)
  .mount("#app");
