import { ref, onMounted, onUnmounted } from 'vue';

export function useScreenSize() {
  const isMobile = ref(false);
  const isTablet = ref(false);
  const isDesktop = ref(false);
  const screenWidth = ref(0);

  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth;
    isMobile.value = window.innerWidth < 768; // md breakpoint
    isTablet.value = window.innerWidth >= 768 && window.innerWidth < 1024; // lg breakpoint
    isDesktop.value = window.innerWidth >= 1024;
  };

  onMounted(() => {
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenSize);
  });

  return {
    isMobile,
    isTablet,
    isDesktop,
    screenWidth
  };
}
