export const festivesMetricGridMapping = (context) => {
  const { computedCityMetrics = {}, computedIsPanIndiaView = false } =
    context || {};

  return [
    {
      title: `TOTAL ITEMS SOLD - ${
        computedIsPanIndiaView ? "CITIES" : "STORES"
      }  TRENDS`,
      shouldRender: computedCityMetrics.totalItemsSoldMetrics?.length > 0,
      metrics: computedCityMetrics.totalItemsSoldMetrics,
      metricItemName: computedIsPanIndiaView ? "city" : "frontend_merchant_name",
      containerClass:
        "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2",
      titleClass: "text-lg font-bold text-blue-900",
      gridClass:
        "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4",
    },
    {
      title: `GMV - ${computedIsPanIndiaView ? "CITIES" : "STORES"} TRENDS`,
      shouldRender: computedCityMetrics.gmvMetrics?.length > 0,
      metrics: computedCityMetrics.gmvMetrics,
      metricItemName: computedIsPanIndiaView ? "city" : "frontend_merchant_name",
      containerClass: "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2",
      titleClass: "text-lg font-bold text-blue-900",
      gridClass:
        "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4",
      isShortenedNumber: true,
    },
    {
      title: `CART PENETRATION - ${computedIsPanIndiaView ? "CITIES" : "STORES"} TRENDS`,
      shouldRender: computedCityMetrics.cartPenMetrics?.length > 0,
      metrics: computedCityMetrics.cartPenMetrics,
      metricItemName: computedIsPanIndiaView ? "city" : "frontend_merchant_name",
      containerClass: "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 mb-48",
      titleClass: "text-lg font-bold text-blue-900",
      gridClass:
        "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4",
    },
  ];
};
