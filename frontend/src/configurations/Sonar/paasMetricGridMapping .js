export const paasMetricGridMapping = (context) => {
  const { computedCityMetrics = {}, computedIsPanIndiaView = false } =
    context || {};

  return [
    {
      title: `Cart Volume - ${
        computedIsPanIndiaView ? "CITIES" : "STORES"
      }  TRENDS`,
      shouldRender: computedCityMetrics.cartMetrics?.length > 0,
      metrics: computedCityMetrics.cartMetrics,
      metricItemName: computedIsPanIndiaView
        ? "city"
        : "frontend_merchant_name",
      containerClass: "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2",
      titleClass: "text-lg font-bold text-blue-900",
      gridClass:
        "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4",
    },
    {
      title: `Order Cancellation - ${
        computedIsPanIndiaView ? "CITIES" : "STORES"
      } TRENDS`,
      shouldRender: computedCityMetrics.orderCancellationMetrics?.length > 0,
      metrics: computedCityMetrics.orderCancellationMetrics,
      metricItemName: computedIsPanIndiaView
        ? "city"
        : "frontend_merchant_name",
      containerClass: "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 mb-48",
      titleClass: "text-lg font-bold text-blue-900",
      gridClass:
        "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4",
    },
  ];
};
