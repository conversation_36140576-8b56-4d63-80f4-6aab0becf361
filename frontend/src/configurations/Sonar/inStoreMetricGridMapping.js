export const inStoreMetricGridMapping = (context) => {
  const {
    isLoading = false,
    computedHourlyOrderMetrics = {},
    computedHourlyActiveTimeMetrics = {},
    computedHourlyComplaintsMetrics = {},
  } = context || {};

  const containerClass =
    "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 md:mt-8";
  const titleClass = "text-sm md:text-lg font-bold text-blue-900";
  const gridClass =
    "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-2 md:mt-4";

  return [
    {
      title: "CART VOLUME - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.orderCountMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "INSTORE SLA % LESS THAN 2.5 MINS - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.percentInstoreSla,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "PICKING TIME PER ITEM - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.percentPPI,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
      isReverse: true,
    },
    {
      title: "PICKER ASSIGNMENT % (10 secs) - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.percentC2AMetrics,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "PICKING START % (5 secs) - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.percentA2SMetrics,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "PICKER SURGE ORDERS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.orderCountPickerSurge,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
      isReverse: true,
    },
    {
      title: "BOTH SURGE ORDERS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.orderCountPickerPlusRiderSurge,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
      isReverse: true,
    },
    {
      title: "FILL RATE % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.fillRate,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "IPO - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.qtyPicked,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "TOTAL ACTIVE HOURS - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.totalActiveHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "PICKER ACTIVE HOURS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.pickerActiveHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "PUTTER ACTIVE HOURS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.putterActiveHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "AUDITOR ACTIVE HOURS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.auditorActiveHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "FNV ACTIVE HOURS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.fnvActiveHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "OPH - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.ophHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "IPH - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyActiveTimeMetrics.iphHourMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
    },
    {
      title: "COMPLAINTS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyComplaintsMetrics.totalComplaintsMetric,
      metricItemName: "displayHour",
      containerClass,
      titleClass,
      gridClass,
      isReverse: true,
    },
  ];
};
