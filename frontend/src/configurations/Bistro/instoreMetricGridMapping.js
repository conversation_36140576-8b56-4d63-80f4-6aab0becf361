export const instoreMetricGridMapping = (context) => {
  const { isLoading = false, computedHourlyOrderMetrics = {} } = context || {};

  return [
    {
      title: "KPT - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.kptMetrics,
      isReverse: true,
    },
    {
      title: "WAIT TIME - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.avgWaitTimeMetrics,
      isReverse: true,
    },
    {
      title: "PREP TIME - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.avgPrepTimeMetrics,
      isReverse: true,
    },
    {
      title: "ASSEMBLY TIME - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.avgAssemblyTimeMetrics,
      isReverse: true,
    },
    {
      title: "ORDERS BREACHING WAIT+PREP TIME % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.breachingWaitPrepTimeMetrics,
      isReverse: true,
    },
    {
      title: "ORDERS BREACHING ASSEMBLY TIME % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.breachingAssemblyTimeMetrics,
      isReverse: true,
    },
  ];
};
