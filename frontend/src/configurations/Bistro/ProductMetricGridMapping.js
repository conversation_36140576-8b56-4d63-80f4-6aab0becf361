
export const ProductMetricGridMapping = (context) => {
    const { computedAllMetrics = {} } = context || {};
    
    const metricConfigs = [
        {
            key: 'orderCountMetrics',
            title: 'CART VOLUME',
            isReverse: false,
            isShortenedNumber: false
        },
        {
            key: 'gmvMetrics',
            title: 'GMV', 
            isReverse: false,
            isShortenedNumber: true
        },
        {
            key: 'totalItemsSoldMetrics',
            title: 'TOTAL ITEMS SOLD',
            isReverse: false,
            isShortenedNumber: false
        },
        {
            key: 'aovMetrics',
            title: 'AOV',
            isReverse: false,
            isShortenedNumber: true
        },
        {
            key: 'canceledQuantityMetrics',
            title: 'CANCELED QUANTITY',
            isReverse: true,
            isShortenedNumber: false
        },
        {
            key: 'uniqueCartsMetrics',
            title: 'CART PEN',
            isReverse: false,
            isShortenedNumber: false
        },
        {
            key: 'ratingsMetrics',
            title: 'RATINGS',
            isReverse: false,
            isShortenedNumber: false
        },
        {
            key: 'complaintsMetrics',
            title: 'ALL COMPLAINTS',
            isReverse: true,
            isShortenedNumber: false
        }
    ];
  
    return metricConfigs
        .filter(config => computedAllMetrics[config.key] && computedAllMetrics[config.key].length > 0)
        .map(config => ({
            title: config.title,
            shouldRender: true,
            metrics: computedAllMetrics[config.key] || [],
            metricItemName: "productName",
            containerClass: "max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-8",
            titleClass: "text-lg font-bold text-blue-900",
            gridClass: "grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-4",
            isReverse: config.isReverse,
            isShortenedNumber: config.isShortenedNumber,
            enableScrolling: true,
            maxHeight: 'max-h-80',
            scrollThreshold: 8
        }));
  };