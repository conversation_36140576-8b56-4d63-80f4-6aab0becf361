export const homeMetricGridMapping = (context) => {
  const {
    isLoading = false,
    computedHourlyOrderMetrics = {},
    computedHourlyUserMetrics = {},
    computedHourlyRiderLoginMetrics = {},
  } = context || {};

  return [
    {
      title: "ORDERS - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.orderCountMetric,
    },
    {
      title: "ACTIVE USERS - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyUserMetrics.hauMetrics,
    },
    {
      title: "ACTIVE USER CONVERSION - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyUserMetrics.hauConversionMetrics,
    },
    {
      title: "SURGE CHECKOUTS % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.percentSurgeShownCartsMetrics,
      isReverse: true,
    },
    {
      title: "Indirect Rider Handshake time % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.indirectHandoverWaitTimeMetrics,
      isReverse: true,
    },
    {
      title: "DIRECT HANDOVER % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.directHandoverMetrics,
    },
    {
      title: "CHECKOUT TO ENROUTE  - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.checkouttoEnrouteMetrics,
      isReverse: true,
    },
    {
      title: "RIDER LOGIN HOURS - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyRiderLoginMetrics,
    },
    {
      title: "RAIN ORDER % - HOURLY TRENDS",
      shouldRender: !isLoading,
      metrics: computedHourlyOrderMetrics.rainorderMetrics,
      isReverse: true,
    }
  ];
};
