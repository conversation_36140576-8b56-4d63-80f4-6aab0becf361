export const BISTRO_ORDER_METRIC_LIST = [
  "gmv",
  "order_count",
  "aov",
  "avg_checkout_to_enroute",
  "percentage_orders_delivered_in_15mins",
  "cancellation_percentage",
  "surge_shown_carts_percentage",
  "billed_to_assigned",
  "batched_orders",
  "enroute_to_billed",
  "percentage_orders_arrived_doorstep_in_15mins",
  "enroute_to_delivery",
  "enroute_to_doorstep",
  "percentage_cod_orders",
  "direct_handover_wait_time",
  "indirect_handover_wait_time",
  "rain_surge_carts_percentage",
];

export const BISTRO_PROMO_METRIC_LIST = [
  "promo_percentage",
  "promo_orders",
  "promo_charge",
  "avg_kpt",
  "avg_wait_time",
  "avg_prep_time",
  "avg_assembly_time",
  "percent_order_breaching_wait_prep_time",
  "percent_order_breaching_assembly_time",
];

export const BISTRO_HOURLY_ORDER_METRIC_LIST = [
  "order_count",
  "surge_shown_carts_percentage",
  "billed_to_assigned",
  "avg_checkout_to_enroute",
  "enroute_to_billed",
  "rain_surge_carts_percentage",
];

export const BISTRO_INSTORE_METRIC_LIST = [
  "order_volume",
  "billed_to_assigned",
  "direct_handover_wait_time",
  "indirect_handover_wait_time",
  "ipc",
  "avg_kpt",
  "avg_wait_time",
  "avg_prep_time",
  "avg_assembly_time",
  "percent_order_breaching_wait_prep_time",
  "percent_order_breaching_assembly_time",
];

export const BISTRO_INSTORE_HOURLY_METRIC_LIST = [
  "avg_kpt",
  "avg_wait_time",
  "avg_prep_time",
  "avg_assembly_time",
  "percent_order_breaching_wait_prep_time",
  "percent_order_breaching_assembly_time",
];
