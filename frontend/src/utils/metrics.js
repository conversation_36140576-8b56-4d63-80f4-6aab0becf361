import { Metric, MetricChange } from "../interfaces/";
import { formatStoreName } from "./utils";

export const metricsForStyleReversal = ["Order Cancellation", "Surge Checkouts %", "Surge Paid Checkouts %", "Rain Surge Orders %", "Surge Shown %", "Unserviceable DAU %", "Demand Based Block %", "Rain Order %", "Picking Time Per Item", "Picker Surge Orders %", "Both Surge Orders %", "Complaints %", "Canceled Quantity", "Surge Seen %", "Rider Surge Seen %", "Picker Surge Seen %", "Rain Surge Seen %", "Indirect Rider Handshake time", "Checkout to Enroute Time", "Express OOH Based Block %", "Express Manual Based Block %", "Batched Order %", "Rider Handshake time", "Direct Rider Handshake time", "Cancelled Cases", "Average ETA Shown", "Enroute to Delivery time", "Enroute to Doorstep time", "Average KPT", "Average Wait Time", "Average Preparation Time", "Average Assembly Time", "% Orders Breaching Wait+Prep Time", "% Orders Breaching Assembly Time", "Express Demand Based Block %", "Longtail Demand Based Block %", "rain_surge_carts_percentage", "All Complaints", "Station Average KPT", "Station Wait Time", "Station Preparation Time"];
export const HOURLY_ORDER_METRIC_LIST = ["order_count", "checkout_to_picker_assigned","rain_order_percentage","billed_to_assigned", "surge_shown_carts_percentage", "aov"]
export const ORDER_METRIC_LIST = ["gmv", "order_count", "aov", "delivery_cost", "surge_shown_carts_percentage", "surge_paid_carts_percentage", "rain_surge_carts_percentage", "cancellation_percentage", "percentage_orders_delivered_in_10mins", "percentage_orders_delivered_in_15mins", "percentage_orders_delivered_in_30mins", "checkout_to_picker_assigned", "billed_to_assigned", "rain_order_percentage", "platform_cost", "batched_orders", "free_delivery_percentage", "slot_charge", "handling_charge", "convenience_charge", "night_charge", "small_cart_charge"]
export const INSTORE_ORDER_METRIC_LIST = ["order_count", "avg_item_count", "percent_instore_sla_less_than_150_sec", "checkout_to_picker_assigned", "ppi_in_seconds", "picker_surge_orders", "rider_plus_picker_surge_orders", "total_procured_items_quantity", "picker_assigned_to_picking_start", "total_orders", "sm_fill_rate", "billed_to_assigned"]
export const INSTORE_ACTIVE_HOURS_METRICS_LIST = ["total_active_time", "picker_active_time_percentage", "putter_active_time_percentage", "auditor_active_time_percentage", "fnv_active_time_percentage", "items_putaway_per_hour", "orders_picked_per_hour"]
export const INSTORE_ALL_ACTIVE_TIME_METRICS_LIST = [...INSTORE_ACTIVE_HOURS_METRICS_LIST, "new_manpower", "total_od_active_time"]
export const UNAVAILABLE_METRIC_VALUE = "unavailable";
export const SORT_STORES_PAGE_MERTICS_NAME = "Surge Seen %";
export const MTD_METRIC_LIST = ["gmv", "order_count", "aov", "transacting_users_count", "delivered_order_count", "new_transacting_users_count", "rm", "rm_percentage", "mau", "order_conversion", "total_items_sold", "total_items_per_order", "avg_item_count", "order_per_store"]
export const WTD_METRIC_LIST = ["gmv", "order_count", "aov", "transacting_users_count", "delivered_order_count", "new_transacting_users_count", "rm", "rm_percentage", "wau", "order_conversion", "total_items_sold", "total_items_per_order", "avg_item_count", "order_per_store"]
// Todo: sending unique_carts to calculate cart pen, as cart pen is calculated after wards...
export const PRODUCT_ORDER_METRIC_LIST = ["gmv", "order_count", "total_items_sold", "canceled_quantity", "transacting_users_count", "asp", "ipc", "aov", "unique_carts"];
export const EMERGENCY_SERVICES_METRIC_LIST = ["total_cases", "approved_cases", "cancelled_cases", "completed_cases", "active_cases", "avg_eta_show"];
export const PROMO_METRIC_LIST = ["promo_percentage", "promo_orders", "promo_charge"];
export const PASS_METRIC_LIST = ["order_count", "cancellation_percentage"];

// for Home page (dynamic)
export const HOURLY_ORDER_METRIC_LIST_1 = ["order_count", "aov", "surge_shown_carts_percentage"]
export const HOURLY_ORDER_METRIC_LIST_2 = ["billed_to_assigned", "checkout_to_picker_assigned", "rain_order_percentage"]

export const priorityOrder = [
  "GMV",
  "Order Per Store",
  "Cart Volume",
  "Total Orders",
  "AOV",
  "Daily Active Users",
  "New Transacting Customers",
  "Transacting Customers",
  "DAU Conversion Percentage",
  "Unique SKU/Order",
  "Total Items Sold",
  "Surge Checkouts %",
  "Surge Paid Checkouts %",
  "Rain Surge Orders %",
  "Surge Seen %",
  "Surge Shown %",
  "Unserviceable DAU %",
  "Demand Based Block %",
  "Priority Customers %",
  "Priority Customers Demand Block %",
  "Priority Customers OOH Block %",
  "Priority Customers Manual Block %",
  "% ETA below 10mins",
  "<15 mins ETA%",
  "% ETA below 30mins",
  "% orders arrived in 10mins",
  "% orders arrived in 15mins",
  "%Delivery < 10mins",
  "%Delivery < 15mins",
  "%Delivery < 30mins",
  "Order Cancellation",
  "Delivery Charge",
  "Rider Login hrs",
  "New Riders",
  "Direct Handover %",
  "Rider Handshake time",
  "Indirect Rider Handshake time",
  "%Picker assigned 10 secs",
  "Picking Start % (5 secs)",
  "Rain Order %",
  "Batched Order %",
  "Customer Paid Charges",
  "ATC %",
  "C2Co %",
  "Instore SLA % < 2.5 mins",
  "Picking Time Per Item",
  "Picker Surge Orders %",
  "Both Surge Orders %",
  "Fill Rate %",
  "True Fill Rate %",
  "IPO",
  "Total Active Hrs",
  "Picker Active Time %",
  "Putter Active Time %",
  "Auditor Active Time %",
  "FNV Active Time %",
  "OPH",
  "IPH",
  "New Manpower",
  "Total Complaints",
  "Complaints %",
  "Active Stores Count",
  "OPD Per Store",
  "Canceled Quantity",
  "Average Selling Price",
  "IPC",
  "Cart Pen",
  "Checkout to Enroute Time",
  "Free DC %",
  "Freebie %",
  "% orders at doorstep in 15m",
  "Enroute to Delivery time",
  "Enroute to Doorstep time",
  "Ratings",
  "COD orders %",
  "Average KPT",
  "Average Wait Time",
  "Average Preparation Time",
  "Average Assembly Time",
  "% Orders Breaching Wait+Prep Time",
  "% Orders Breaching Assembly Time",
  "Promo %",
  "Promo Orders",
  "Promo Charge",
];

export const demandBasedPriorityOrder = [
    "Express Demand Based Block %",
    "Express OOH Based Block %",
    "Express Manual Based Block %",
    "Longtail Demand Based Block %",
    "Longtail OOH Based Block %",
    "Longtail Manual Based Block %",
]

export const productMetricNameMapping = {
    "GMV": "gmv",
    "Cart Volume": "order_count",
    "Total Items Sold": "total_items_sold",
    "Transacting Customers": "transacting_users_count",
    "New Transacting Customers": "new_transacting_users_count",
    "Canceled Quantity": "canceled_quantity",
    "Average Selling Price": "asp",
    "IPC": "ipc",
    "AOV": "aov",
    "Cart Pen": "unique_carts"
}


export const ABSOLUTE_METRIC_DELTA = [
    'Delivery Charge',
    'Customer Paid Charges',
    'Active Stores Count',
    'Express Outlet Count',
    'Longtail Outlet Count',
    'All Complaints', //hard coded metric,
    'Ratings', //hard coded metric
    'Surge Charge',
    'Handling Charge',
    'Convenience Charge',
    'Night Charge',
    'Small Cart Charge',
]

//? BPS is 1/100 of a unit
export const BPS_METRICS = [
    'Order Cancellation'
]

export function orderMetricToTrack(metricData, metricName, metricType) {
    let metricToTrack = metricData.data.find(metric => metric.name == metricName);
    if(!metricToTrack) return {name: metricName, metric: "-", type: metricType};
    return metricToTrack;
}

export function getLastNDaysDates(n) {
    const dates = [];
    let currentDate = new Date();
    for (let i = n; i >= 0; i--) {
        let targetDate = new Date(currentDate.getTime() - (86400000 * i) + (5.5 * 60 * 60 * 1000));
        dates.push(targetDate.toISOString().slice(0, 10));
    }
    return dates;
}

export function addDateToData(data, numDates, hourly = false, customDate = "") {
    if (!data.length) return data;
    let getDateDiff = (days) => {
        let currentDate = new Date();
        // 5.5 * 60 * 60 * 1000 is IST offset in milliseconds (5 hours 30 minutes)
        let targetDate = new Date(currentDate.getTime() - (86400000 * days) + (5.5 * 60 * 60 * 1000));
        return targetDate.toISOString().split('T')[0];
    };

    let today_diff = Array.from({ length: numDates }, (_, i) => getDateDiff(i * 7));
    let yesterday_diff = Array.from({ length: numDates }, (_, i) => getDateDiff(i * 7 + 1));

    let preffered_diff = yesterday_diff.includes(data[0]['date']) ? yesterday_diff : today_diff;

    if(customDate){
        const [year, month, day] = customDate.split('-').map(Number);
        const targetDate = new Date(year, month - 1, day);
        const diffInMs = targetDate - new Date();
        const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
        let day_diff = Array.from({ length: numDates }, (_, i) => getDateDiff(i * 7 - diffInDays));
        preffered_diff = day_diff
    }

    let presentKeys = preffered_diff.filter(key => data.some(obj => obj.date === key));
    let missingKeys = preffered_diff.filter(key => !presentKeys.includes(key));
    if(hourly){
        missingKeys.forEach(key => {
            key == getDateDiff(0) ? data.push({ date: key, date_diff: 0, data: [] }) : data.push({ date: key, date_diff: 7, data: [] });
        });
    }
    else{
        missingKeys.forEach(key => {
            data.push({ date: key, metric: "-" });
        });
    }
}

export function sortedAllMetrics(metrics, priorityArray = priorityOrder, addMissingDates = true, numDates = 5, customDate = ""){
    metrics.sort((a, b) => priorityArray.findIndex(x => x === a.name) - priorityArray.findIndex(x => x === b.name));
    return metrics.map(metric => {
        let data = metric.data
        if(addMissingDates)addDateToData(data, numDates, false, customDate);
        data.sort((a, b) => {
            let dateA = new Date(a.date);
            let dateB = new Date(b.date);
            return dateA - dateB;
        });
        return {
        name: metric.name,
        data: data.map(x => new Metric(x.metric, metric.type, {date: x.date})),
        ...(new Date(metric.etl_snapshot_ts_ist).toString() !== 'Invalid Date' ? { updated_at: formatDate(metric.etl_snapshot_ts_ist) } : {}),
        }
  })
}

export function cityMetrics(cityMetrics, metricName, cityMetricKey = 'name') {
    let date_diffs = cityMetrics.map(metric => metric.date_diff);
    let currentDayMetric = cityMetrics.find(metric => metric.date_diff == Math.min.apply(null, date_diffs));
    let previousDayMetric = cityMetrics.find(metric => metric.date_diff == Math.max.apply(null, date_diffs));
    cityMetrics = cityMetrics.map(metric =>{if(metric.etl_snapshot_ts_ist == null){delete metric.etl_snapshot_ts_ist}});
    if (!currentDayMetric || !previousDayMetric) {
        return [];
    }

    let results = [];

    currentDayMetric.data.forEach(cityMetric => {
        let metricToTrack = orderMetricToTrack(cityMetric, metricName);
        if (metricToTrack) {
            results.push({
                city: cityMetric[cityMetricKey],
                curr: new Metric(metricToTrack.metric, metricToTrack.type)
            });
        }
    });

    previousDayMetric.data.map(cityMetric => {
        let metricToTrack = orderMetricToTrack(cityMetric, metricName);
        let cityMetricDict = results.find(metric => metric.city == cityMetric[cityMetricKey]);
        if (cityMetricDict) {
            cityMetricDict.prev = new Metric(metricToTrack.metric, metricToTrack.type);
        }
    });

    return results.filter(metric => metric.curr).sort((a, b) => b.curr.value - a.curr.value);
}

export function convertWarehouseMetrics(warehouseMetrics) {
    let results = [];
    warehouseMetrics.map(warehouseMetric => {
        results.push({
            outlet_name: warehouseMetric.outlet_name,
            name: warehouseMetric.outlet_name,
            metrics: warehouseMetric.data.map(metric => {
                return({
                name: metric.name,
                value: new Metric(
                    metric.data?.[0]?.metric,
                    metric.type
                )
            })}),
            shift: warehouseMetric.shift
        });
    })
    
    return results
}

export function convertWarehouseDateMetrics(
    warehouseMetrics,
    isAvailabilityMetric = false
) {
    let results = [];

    if (isAvailabilityMetric) {
        const today_date = new Date(new Date().getTime() + (5.5 * 60 * 60 * 1000)).toISOString().split('T')[0];
        
        warehouseMetrics.map((warehouseMetric) => {
        results.push({
            outlet_name: warehouseMetric.outlet_name,
            name: warehouseMetric.outlet_name,
            metrics: warehouseMetric.data.map((metric) => {
            const currentDayMetric = metric?.data?.find(
                (it) => it?.date === today_date
            );
            return {
                name: metric.name,
                value: new Metric(currentDayMetric?.metric || '-', metric.type),
            };
            }),
            shift: warehouseMetric?.shift,
        });
        });
    } else {
        warehouseMetrics.map((warehouseMetric) => {
        results.push({
            outlet_name: warehouseMetric.outlet_name,
            name: warehouseMetric.outlet_name,
            metrics: warehouseMetric.data.map((metric) => {
            return {
                name: metric.name,
                value: new Metric(metric.metric, metric.type),
            };
            }),
            shift: warehouseMetric?.shift,
        });
        });
    }

    return results;
}

export function storeMetrics(storeMetrics, metricName, metricType) {
    let date_diffs = storeMetrics.map(metric => metric.date_diff);
    let currentDayMetric = storeMetrics.find(metric => metric.date_diff == Math.min.apply(null, date_diffs));
    let previousDayMetric = storeMetrics.find(metric => metric.date_diff == Math.max.apply(null, date_diffs));
    storeMetrics = storeMetrics.map(metric =>{if(metric.etl_snapshot_ts_ist == null){delete metric.etl_snapshot_ts_ist}});
    if (!currentDayMetric || !previousDayMetric) {
        return [];
    }

    let results = [];

    currentDayMetric.data.map(storeMetric => {
        let metricToTrack = orderMetricToTrack(storeMetric, metricName, metricType);
        results.push({
            frontend_merchant_id: storeMetric.frontend_merchant_id,
            frontend_merchant_name: storeMetric.frontend_merchant_name,
            curr: new Metric(metricToTrack.metric, metricToTrack.type)
        });
    });

    previousDayMetric.data.map(storeMetric => {
        let metricToTrack = orderMetricToTrack(storeMetric, metricName, metricType);
        let storeMetricDict = results.find(metric => metric.frontend_merchant_name == storeMetric.frontend_merchant_name);
        if (storeMetricDict) {
            storeMetricDict.prev = new Metric(metricToTrack.metric, metricToTrack.type);
        }
    });
    results = results.map(item => ({
        ...item,
        frontend_merchant_name: item?.frontend_merchant_name === item?.frontend_merchant_id ?
          item.frontend_merchant_name :
          `${formatStoreName(item.frontend_merchant_name).replace(/ PR$/, '')} (${item.frontend_merchant_id})`
      }));
    return results.filter(metric => metric.curr).sort((a, b) => b.curr.value - a.curr.value);
}


export function hourlyMetrics(hourlyMetrics, is_grouped=false, filter_hours=[], metricName="", customDate = "") {
    addDateToData(hourlyMetrics, 2, true, customDate);
    let date_diffs = hourlyMetrics.map(metric => metric.date_diff);
    let currentDayMetric = hourlyMetrics.find(metric => metric.date_diff == Math.min.apply(null, date_diffs));
    let previousDayMetric = hourlyMetrics.find(metric => metric.date_diff == Math.max.apply(null, date_diffs));

    if (!currentDayMetric || !previousDayMetric) {
        return [];
    }

    let results = [];

    currentDayMetric.data.map(hourlyMetric => {
        let metricToTrack = hourlyMetric.data.find(metric => metric.name == metricName);
        // Filter the hours which are not needed
        if (!(hourlyMetric.hour in filter_hours) && metricToTrack) {
            results.push({
                hour: hourlyMetric.hour,
                displayHour: hourlyMetric.hour.toString(),
                curr: new Metric(metricToTrack.metric, metricToTrack.type)
            });
        }
    });

     previousDayMetric.data.map(hourlyMetric => {
         let metricToTrack = hourlyMetric.data.find(metric => metric.name == metricName);
         let hourlyMetricDict = results.find(metric => metric.hour == hourlyMetric.hour);
         if (hourlyMetricDict && metricToTrack) {
             hourlyMetricDict.prev = new Metric(metricToTrack.metric, metricToTrack.type);
         }
     });

    if (is_grouped) {
        let groupedMetrics = [];
        results.map(metric => {
            if (metric.curr) {
                let [groupHour, sort_key] = [metric.hour.toString(), metric.hour];
                let groupedMetric = groupedMetrics.find(group => group.displayHour == groupHour);
                if (!groupedMetric) {
                    groupedMetrics.push({
                        displayHour: groupHour,
                        curr: metric.curr,
                        prev: metric.prev,
                        sort_key: sort_key,
                    });
                } else {
                    groupedMetric["curr"] = Metric.addMetric(groupedMetric.curr, metric.curr);
                    groupedMetric["prev"] = Metric.addMetric(groupedMetric.prev, metric.prev);
                }
            }
        });

        return groupedMetrics.sort((a, b) => a.sort_key - b.sort_key);
    } else {
        return results.filter(metric => metric.curr).sort((a, b) => a.hour - b.hour);
    }
}

export function transformProjectionMetricsDatewise(projectedMetric, metricName = "") {

    if (!Array.isArray(projectedMetric) || projectedMetric.length === 0) {
        return [];
    }
    let filteredMetrics = projectedMetric.filter(metric => metric.name === metricName);

    if (metricName && filteredMetrics.length === 0) {
        return [];
    }
    let result = filteredMetrics.map(metric => {
        return metric.data.map(data => {
            return { date: data.date, value: data.metric };
        });
    });
    return result.flat();
}

export function formatDate(dateStr) {
    const options = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric', 
      hour: 'numeric', 
      minute: 'numeric' 
    };
    return  new Date(dateStr).toLocaleString('en-US', options);
  }

function sortStoresBySurgeNow(metrics) {
    let dt = new Date();
    // 5.5 * 60 * 60 * 1000 is IST offset in milliseconds (5 hours 30 minutes)
    let targetDate = new Date(dt.getTime() + (5.5 * 60 * 60 * 1000));
    const currentDate = targetDate.toISOString().split('T')[0];
    //const currentDate = new Date().toISOString().split('T')[0]; // Get current date in 'YYYY-MM-DD' format
  
    let availableStores = [];
    let unavailableStores = [];
  
    metrics.forEach(store => {
      let surgeMetric = store.metrics.find(metric => metric.name === SORT_STORES_PAGE_MERTICS_NAME);
  
      if (surgeMetric) {
        let surgeNow = surgeMetric.data.find(data => data.date === currentDate);
  
        if (surgeNow) {
          if (surgeNow.metric === UNAVAILABLE_METRIC_VALUE) {
            unavailableStores.push(store);
          } else {
            availableStores.push(store);
          }
        }
      }
    });
  
    let sortedStores = availableStores.sort((storeA, storeB) => {
            let surgeMetricA = storeA.metrics.find(metric => metric.name === SORT_STORES_PAGE_MERTICS_NAME);
            let surgeMetricB = storeB.metrics.find(metric => metric.name === SORT_STORES_PAGE_MERTICS_NAME);
        
            if (surgeMetricA && surgeMetricB) {
                    let surgeNowA = surgeMetricA.data.find(data => data.date === currentDate);
                    let surgeNowB = surgeMetricB.data.find(data => data.date === currentDate);
        
                if (surgeNowA && surgeNowB) {
                    return surgeNowB.metric - surgeNowA.metric;
                }
            }
        
        return 0;
    });

    
    let finalSortedStores = sortedStores.concat(unavailableStores);
    return finalSortedStores;
    // return finaLSortedStores.map(store => store.store);
}


  export function sortedStoresMetrics(metrics, apply = true) {
    let SortedMetrics  = sortStoresBySurgeNow(metrics);
    // metrics.sort((a, b) => priorityArray.findIndex(x => x === a.name) - priorityArray.findIndex(x => x === b.name));
    let result = {};

    SortedMetrics.forEach(metric => {
        metric.metrics.forEach(metri => {
            let data = metri.data;
            addDateToData(data, 2, false);
            data.sort((a, b) => {
                let dateA = new Date(a.date);
                let dateB = new Date(b.date);
                return dateA - dateB;
            });
            let store = metric.store;
            if (!result[store]) {
                result[store] = [];
            }

            result[store].push({
                store: store,
                name: metri.name,
                data: data.map(x => new Metric(x.metric, metri.type, { date: x.date })),
                ...(new Date(metric.etl_snapshot_ts_ist).toString() !== 'Invalid Date' ? { updated_at: formatDate(metri.etl_snapshot_ts_ist) } : {}),
            });
        });
    });

    return result;
}
