import { api, bistroApi } from "../api";
import {
  getAllowedCityList,
  getCityMap,
  getZoneMap,
  getUserAccessMapping,
  isGlobalUser,
} from "./utils";
import { useCityStore } from "../stores/cities";
import { useUserStore } from "../stores/users";
import { TenantMapping } from "../constants";

//* onApp --> is being called on App.vue
export async function fetchStoreToCityMapping(onApp = false, isBistro = false) {
  const cityStore = useCityStore();
  const userStore = useUserStore();

  const isSonarView =
    !isBistro && userStore.getActiveTenant === TenantMapping.blinkit;

  let mapping = isSonarView
    ? api.fetchStoreCityMapping()
    : bistroApi.fetchBistroStoreCityMapping();

  let [response] = await Promise.all([mapping]);

  if (response) {
    if (isSonarView) {
      let mappingResponse = response.data.filters;
      await cityStore.updateStoreToCityMapping(mappingResponse);

      let cityList = mappingResponse.map((item) => {
        return { name: item.city };
      });

      let zoneList = mappingResponse.map((item) => {
        return { name: item.zone, city: item.city };
      });

      await createUserCityMapping(cityList, zoneList);
    } else {
      let mappingResponse = response.data.filters;
      await cityStore.updateBistroStoreToCityMapping(mappingResponse);

      let cityList = mappingResponse.map((item) => {
        return { name: item.city };
      });

      await createBistroUserCityMapping(cityList);
    }
  }

  if (!onApp) window.location.reload();
}

export async function createUserCityMapping(allCityList, zoneList) {
  const cityStore = useCityStore();

  let userAccessMapping = getUserAccessMapping();
  let storePageCityList = [];
  let cityList = [];

  if (isGlobalUser(userAccessMapping)) {
    storePageCityList = allCityList;
    cityList = allCityList;
  } else {
    storePageCityList = Object.keys(userAccessMapping).map((cityName) => ({
      name: cityName,
      code: cityName.toLowerCase(),
    }));
    cityList = getAllowedCityList(userAccessMapping);
  }

  let cityMapping = getCityMap(cityList);
  let zoneMapping = getZoneMap(zoneList);
  let storePageCityMapping = getCityMap(storePageCityList, false);

  await cityStore.updateCityMapping(cityMapping);
  await cityStore.updateStorePageCityMapping(storePageCityMapping);
  await cityStore.updateZoneMapping(zoneMapping);
}

export async function createBistroUserCityMapping(allCityList) {
  const cityStore = useCityStore();

  let userAccessMapping = getUserAccessMapping();
  let storePageCityList = [];
  let cityList = [];

  if (isGlobalUser(userAccessMapping)) {
    storePageCityList = allCityList;
    cityList = allCityList;
  } else {
    storePageCityList = Object.keys(userAccessMapping).map((cityName) => ({
      name: cityName,
      code: cityName.toLowerCase(),
    }));
    cityList = getAllowedCityList(userAccessMapping);
  }

  let cityMapping = getCityMap(cityList);
  let storePageCityMapping = getCityMap(storePageCityList, false);

  await cityStore.updateBistroCityMapping(cityMapping);
  await cityStore.updateBistroStorePageCityMapping(storePageCityMapping);
}
