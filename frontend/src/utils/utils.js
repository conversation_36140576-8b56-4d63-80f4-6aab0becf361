import { StoreNameConstants, MergedCityStoreConstants, PanIndiaConstants, HourList } from "../constants";
import isEmpty from 'lodash.isempty';
import router from "../router";

export function getUserTenantAccessMapping(){
  let response = JSON.parse(localStorage.getItem('userStore'));
    if(isEmpty(response))return;
    let arr = response?.['user']?.['accessToken']?.split('.');
    if(isEmpty(arr))return;
    let userAccessMapping = JSON.parse(atob(arr?.[1]))?.['user_access_mapping'];
    if(isEmpty(userAccessMapping))return userAccessMapping;

    const activeTenant = response?.activeTenant;
    const { allowed_pages, is_global_group_allowed, ...accessMapping } = userAccessMapping[activeTenant] || {};

    return {
      allowed_pages: allowed_pages || [],
      is_global_group_allowed: is_global_group_allowed || false,
      accessMapping: accessMapping || {}
    }
}

//** does not return the whole access token, only returns the city access mapping present in the access token
export function getUserAccessMapping(){
    const { accessMapping = {} } = getUserTenantAccessMapping() || {}
    return accessMapping
}

export function getGlobalGrpAllowed(){
  const { is_global_group_allowed = false } = getUserTenantAccessMapping() || {}
  return is_global_group_allowed
}

export function isGlobalUser(userAccessMapping){
  if(isEmpty(userAccessMapping))return;
  return Object.keys(userAccessMapping).includes("all") ? true : false;
}

export function isCityUser(userAccessMapping){
    if(isEmpty(userAccessMapping))return;
    let citiesWithKey = Object.entries(userAccessMapping)?.reduce((acc, [cityName, cityObject]) => {
        if (!isEmpty(cityObject) && cityObject.hasOwnProperty("-1")) {
              acc.push({ name: cityName , code: cityName?.toLowerCase()});
            }
            return acc;
    }, []);
    return citiesWithKey?.length > 0 ? true : false;
}

export function getCityMap(cityListResponse, excludeCities = true) {
    let dict = {};
    let excludedCities = ["Zirakpur", "Panchkula", "Mohali", "Kharar"];

    let filteredCityList = cityListResponse.filter((cityObj) => cityObj.region !== "null");
    let nullRegionCityList = cityListResponse.filter((cityObj) => cityObj.region === "null");

    filteredCityList.forEach(city => {
      let key = city.name.toLowerCase();
      if(excludeCities){
        if (!excludedCities.includes(city.name)) dict[key] = city;
      }
      else{
        dict[key] = city;
      }
    });

    nullRegionCityList.forEach(city => {
      let key = city.name.toLowerCase();
      if(dict.hasOwnProperty(key)) return;

      if(excludeCities){
        if (!excludedCities.includes(city.name)) dict[key] = city;
      }
      else{
        dict[key] = city;
      }
    });


    return dict;
}

export function getZoneMap(zoneListResponse) {
  let mapping = [];
  zoneListResponse.forEach(zone => {
    mapping.push({...zone, code:zone.name.toLowerCase() })
  });
  return mapping;
}

export function AddMissingStores(cityStoreMapping, ApiResponse) {
  let storeIds = new Set(cityStoreMapping.map(store => store.frontend_merchant_id));

  for (let [dateKey, dateEntry] of Object.entries(ApiResponse)) {
    let existingStoreIds = new Set(dateEntry.data.map(entry => entry.frontend_merchant_id));
    let missingStoreIds = [...storeIds].filter(id => !existingStoreIds.has(id));

    dateEntry.data.push(
      ...missingStoreIds.map(frontend_merchant_id => ({
        frontend_merchant_name: cityStoreMapping.find(store => store.frontend_merchant_id === frontend_merchant_id).frontend_merchant_name,
        frontend_merchant_id,
        data: []
      }))
    );
  }

  return ApiResponse;
}

export function getAllowedCityList(dict){
  let citiesWithKey = Object.entries(dict).reduce((acc, [cityName, cityObject]) => {
  if (cityObject.hasOwnProperty("-1")) {
        acc.push({ name: cityName , code: cityName.toLowerCase()});
      }
      return acc;
    }, []);

  return citiesWithKey;
}

export function getUserAllowedStoresMapping(userAccessMapping, storeToCityMapping){
  let userAllowedStoresMapping = {}
  // If global user then include all stores of all citites
    if (isGlobalUser(userAccessMapping)) {
        for (let cityData of storeToCityMapping) {
            let city = cityData?.city?.toLowerCase();
            let zone = cityData?.zone?.toLowerCase();
            if (!userAllowedStoresMapping[city]) {
              userAllowedStoresMapping[city] = [];
          }
            for (let data of cityData.data) {
                userAllowedStoresMapping[city].push({
                    frontend_merchant_id: data.frontend_merchant_id,
                    frontend_merchant_name: data.frontend_merchant_name,
                    backend_merchant_id: data.backend_merchant_id,
                    name: city,
                    zone: zone,
                    merchant_name_id: `${formatStoreName(data.frontend_merchant_name)} (${data.frontend_merchant_id})`,
                    merchant_type: data.merchant_type
                });
            }
        }
    }
    else {
        for (let cityData of storeToCityMapping) {
            let cityCap = cityData?.city;
            let city = cityData?.city?.toLowerCase();
            let zone = cityData?.zone?.toLowerCase();
            if (userAccessMapping.hasOwnProperty(cityCap)) {
              if (!userAllowedStoresMapping[city]) {
                userAllowedStoresMapping[city] = [];
                }
                if (userAccessMapping[cityCap].hasOwnProperty("-1")) {
                    // Include all stores for this city since the key is -1 in the allowed dict
                    for (let data of cityData.data) {
                        userAllowedStoresMapping[city].push({
                            frontend_merchant_id: data.frontend_merchant_id,
                            frontend_merchant_name: data.frontend_merchant_name,
                            backend_merchant_id: data.backend_merchant_id,
                            name: city,
                            zone: zone,
                            merchant_name_id: `${formatStoreName(data.frontend_merchant_name)} (${data.frontend_merchant_id})`,
                            merchant_type: data.merchant_type
                        });
                    }
                } else {
                    // Include only the allowed stores for this city
                    for (let data of cityData.data) {
                        let frontendMerchantId = data.frontend_merchant_id.toString();
                        if (
                            frontendMerchantId !== "-1" &&
                            userAccessMapping[cityCap].hasOwnProperty(frontendMerchantId)
                        ) {
                            userAllowedStoresMapping[city].push({
                                frontend_merchant_id: data.frontend_merchant_id,
                                frontend_merchant_name: data.frontend_merchant_name,
                                backend_merchant_id: data.backend_merchant_id,
                                name: city,
                                zone: zone,
                                merchant_name_id: `${formatStoreName(data.frontend_merchant_name)} (${data.frontend_merchant_id})`,
                                merchant_type: data.merchant_type
                            });
                        }
                    }
                }
            }
        }
  }

  return userAllowedStoresMapping;
}

export function formatStoreName(storeName) {
  return storeName.replace(new RegExp(`^(${StoreNameConstants.PREFIX.join('|')})`), '');
}

export function getModifiedStoreList(storeList) {
  Object.keys(MergedCityStoreConstants).forEach((cityName) => {
    let storeCityList = MergedCityStoreConstants[cityName];
    
    if (storeList[storeCityList[0].toLowerCase()] === undefined) return storeList;
    let newStoreList = [storeList[storeCityList[0].toLowerCase()]];

    storeCityList.slice(1).forEach((city) => {
      if (storeList[city.toLowerCase()])
        newStoreList.push(
          storeList[city.toLowerCase()].filter(
            (item) => item.frontend_merchant_name !== "Overall"
          )
        );
    });

    storeList[storeCityList[0].toLowerCase()] = newStoreList.flat();
  });

  return storeList;
}

export function getCombinedCityNames(cityName) {
  return MergedCityStoreConstants[cityName].join(", ");
}

export function getPanIndiaStoreCityList(cityStoreList = {}){
  let staticList = { [PanIndiaConstants.PAN_INDIA.code]: PanIndiaConstants.PAN_INDIA };
  return Object.assign(staticList, cityStoreList);
}

export function getCurrentHour() {
  let options = { timeZone: "Asia/Kolkata", hour12: false, hour: "numeric" };
  return parseInt(new Date().toLocaleString("en-US", options), 10) + 1;
}

/**
 * Format a Date object to YYYY-MM-DD string without timezone adjustments
 * This prevents T-1 date issues that occur with timezone conversions
 * @param {Date} date - The date object to format
 * @returns {string|null} - Formatted date string or null if date is invalid
 */
export function formatDateToString(input) {
   if (!input) return null;
  const date = new Date(input);

  // Validate the date
  if (isNaN(date.getTime())) return null;

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Check if a date is today without timezone adjustments
 * @param {Date} date - The date to check
 * @returns {boolean} - True if the date is today
 */
export function isDateToday(date) {
  if (!date) return false;

  const selectedDateStr = formatDateToString(date);
  const today = new Date();
  const todayStr = formatDateToString(today);
  return selectedDateStr === todayStr;
}

export function getHourList(fromHour = 0, isYesterday = false) {
  const fullDay = [{ key: "Full Day", value: -1 }];

  if (isYesterday) return [...fullDay, ...HourList];

  let currentHour = getCurrentHour();

  return [...fullDay, ...HourList.slice(fromHour, currentHour)];
}

export function noPermissionRoute(isBistroPage = false) {
  const baseRoute = isBistroPage ? "/bistro/403" : "/403";
  router.replace(baseRoute);
  return;
}

export function cancelPreviousRequests({ cancelTokens, apiKey, useApiKey = false}) {
  if (!cancelTokens) {
    console.warn('cancelPreviousRequests: cancelTokens is undefined');
    return;
  }

  if (!useApiKey) {
    cancelTokens.forEach((source) => {
      source.cancel('Operation canceled due to new request.');
    });
    cancelTokens = [];
  } else {
    if (apiKey) {
      if (cancelTokens[apiKey]) {
        cancelTokens[apiKey].forEach((source) => {
          source.cancel('Operation canceled due to new request.');
        });
        cancelTokens[apiKey] = [];
      }
    } else {
        Object.keys(cancelTokens).forEach((key) => {
          if (Array.isArray(cancelTokens[key])) {
            cancelTokens[key].forEach((source) => {
              source.cancel('Operation canceled due to new request.');
            });
          }
          cancelTokens[key] = [];
        });
     }
   }
}
