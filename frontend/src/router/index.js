import { createRouter, createWebHistory } from "vue-router";

// Keep only Home and BistroHome as static imports
import Home from "../pages/Sonar/Home.vue";
import BistroHome from "../pages/Bistro/Home.vue";

const sonarRoutes = [
  {
    path: "/",
    name: "Home",
    component: Home,
  },
  {
    path: "/cities-stores-trend",
    name: "Cities/Stores",
    component: () => import("../pages/Sonar/CitiesStoresTrend.vue"),
  },
  {
    path: "/stores",
    name: "Stores(Live)",
    component: () => import("../pages/Sonar/StoresCurrentView.vue"),
  },
  {
    path: "/instore",
    name: "Instore",
    component: () => import("../pages/Sonar/Instore.vue"),
  },
  {
    path: "/warehouses",
    name: "Warehouses",
    component: () => import("../pages/Sonar/Warehouses.vue"),
  },
  {
    path: "/availability",
    name: "Availability",
    component: () => import("../pages/Sonar/Availability.vue"),
  },
  {
    path: "/wtd-mtd",
    name: "WTD/MTD",
    component: () => import("../pages/Sonar/WTD_MTD.vue"),
  },
  {
    path: "/current-rate",
    name: "CurrentRate",
    component: () => import("../pages/Sonar/CurrentRate.vue"),
  },
  {
    path: "/insights",
    name: "Insights",
    component: () => import("../pages/Sonar/Insights.vue"),
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("../pages/Login.vue"),
  },
  {
    path: "/logout",
    name: "Logout",
    component: () => import("../pages/Logout.vue"),
  },
  {
    path: "/auth/google/callback",
    name: "Google SSO Callback",
    component: () => import("../pages/GoogleLogin.vue"),
  },
  {
    path: "/product-details",
    name: "Product Details",
    component: () => import("../pages/Sonar/Festives.vue"),
  },
  {
    path: "/city-insights",
    name: "City Insights",
    component: () => import("../pages/Sonar/Insights/CityInsights.vue"),
  },
  {
    path: "/store-insights",
    name: "Store Insights",
    component: () => import("../pages/Sonar/Insights/StoreInsights.vue"),
  },
  {
    path: "/ptype-insights",
    name: "P-Type Insights",
    component: () => import("../pages/Sonar/Insights/PTypeInsights.vue"),
  },
  {
    path: "/403",
    name: "403",
    component: () => import("../pages/403.vue"),
  },
  {
    path: "/:pathMatch(.*)*",
    name: "404",
    component: () => import("../pages/404.vue"),
  },
  {
    path: "/complaints",
    name: "Complaints",
    component: () => import("../pages/Sonar/Complaints.vue"),
  },
  {
    path: "/item-insights",
    name: "Item Insights",
    component: () => import("../pages/Sonar/Insights/ItemInsights.vue"),
  },
  {
    path: "/category-insights",
    name: "Category Insights",
    component: () => import("../pages/Sonar/CategoryInsights.vue"),
  },
  {
    path: "/bad-stocks",
    name: "Bad Stocks",
    component: () => import("../pages/Sonar/BadStocks.vue"),
  },
  {
    path: "/emergency-services",
    name: "Emergency Services",
    component: () => import("../pages/Sonar/EmergencyServices.vue"),
  },
  {
    path: "/paas",
    name: "PAAS",
    component: () => import("../pages/Sonar/Paas.vue"),
  },
  {
    path: "/group-maker",
    name: "Group Maker",
    component: () => import("../pages/Sonar/GroupMaker.vue"),
  },
  {
    path: "/navigation",
    name: "Navigation",
    component: () => import("../pages/NavigationPage.vue"),
  },
];

const bistroRoutes = [
  {
    path: "/bistro",
    name: "Bistro Home",
    component: BistroHome,
  },
  {
    path: "/bistro/login",
    name: "Bistro Login",
    component: () => import("../pages/Login.vue"),
  },
  {
    path: "/bistro/logout",
    name: "Bistro Logout",
    component: () => import("../pages/Logout.vue"),
  },
  {
    path: "/bistro/403",
    name: "Bistro 403",
    component: () => import("../pages/403.vue"),
  },
  {
    path: "/bistro/:pathMatch(.*)*",
    name: "Bistro 404",
    component: () => import("../pages/404.vue"),
  },
  {
    path: "/bistro/product-details",
    name: "Bistro Product Details",
    component: () => import("../pages/Bistro/Product/Product.vue"),
  },
  {
    path: "/bistro/cities-stores-trend",
    name: "Bistro Cities Stores Trend",
    component: () => import("../pages/Bistro/CitiesStoresTrend.vue"),
  },
  {
    path: "/bistro/wtd-mtd",
    name: "Bistro WTD/MTD",
    component: () => import("../pages/Bistro/WTD-MTD.vue"),
  },
  {
    path: "/bistro/inkitchen",
    name: "Bistro Inkitchen",
    component: () => import("../pages/Bistro/Inkitchen.vue"),
  },
  {
    path: "/bistro/navigation",
    name: "Bistro Navigation",
    component: () => import("../pages/NavigationPage.vue"),
  },
];

const routes = [...sonarRoutes, ...bistroRoutes];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    return { top: 0 };
  },
});

export default router;
