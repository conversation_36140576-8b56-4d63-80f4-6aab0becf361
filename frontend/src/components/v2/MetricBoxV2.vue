<template>
  <div class="px-2 py-2 md:px-3 rounded-lg modern-metric-box"
       :class="[
         hasHover ? 'hover:shadow-md' : '',
         isDrillDownEnabled ? 'cursor-pointer hover:bg-yellow-50 hover:border-yellow-200 transition-all duration-200' : ''
       ]"
       @click="handleClick">
    <div class="flex items-center justify-between">
      <span class="font-normal text-gray-500 text-xs md:text-sm">{{ name }}</span>
      <i v-if="isDrillDownEnabled" class="pi pi-external-link text-xs text-gray-400 hover:text-yellow-600 transition-colors"></i>
    </div>
    <div class="flex justify-between mt-1">
      <AnimatedNumber :styles="['font-medium', 'text-sm', 'md:text-base']" :value="curr.value" :type="curr.type"
        :isShortenedNumber="isShortenedNumber" />
      <AnimatedNumber :styles="style" :value="diff" type="percentage" />
    </div>
  </div>
</template>


<script>

import AnimatedNumber from "../AnimatedNumber.vue";
import { Metric, MetricChange } from "../../interfaces/";

export default {

  components: {
    AnimatedNumber,
  },

  computed: {
    diff() {
      let val = new MetricChange(this.curr, this.prev).change();
      if (val === "-") return val;
      return parseFloat(new MetricChange(this.curr, this.prev).change());
    },

    style() {
      var metricChange = new MetricChange(this.curr, this.prev);
      var diff_style = metricChange.style(this.isReverse);

      return [
        'font-semibold',
        'text-sm',
        'mt-auto',
        diff_style
      ];
    }
  },

  emits: ['onClick', 'onDrillDown'],

  methods: {
    handleClick() {
      if (this.isDrillDownEnabled) {
        this.$emit('onDrillDown', {
          metricName: this.name,
          currentValue: this.curr,
          previousValue: this.prev
        })
      } else {
        this.$emit('onClick', { val: this.name })
      }
    }
  },

  props: {
    name: String,
    curr: Metric,
    prev: Metric,
    styles: Array,
    isShortenedNumber: {
      type: Boolean,
      default: false,
    },
    isReverse: {
      type: Boolean,
      default: false,
    },
    hasHover: {
      type: Boolean,
      default: false,
    },
    isDrillDownEnabled: {
      type: Boolean,
      default: false,
    },
  },
}

</script>

<style scoped>
.modern-metric-box {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.6);
  box-shadow:
    2px 2px 4px rgba(0, 0, 0, 0.05),
    -2px -2px 4px rgba(255, 255, 255, 0.8);
}
</style>