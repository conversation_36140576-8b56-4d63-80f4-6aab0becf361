<template>
  <div class="border px-2 py-1 bg-gray-50 md:bg-gray-100 border-gray-200" v-bind:class="{ 'border-gray-400 border-4 duration-75 shadow scale-105': selected }">
      <div class="flex">
          <span class="font-normal text-gray-500 text-xs md:text-sm">{{name}}</span>
      </div>
      <div v-for="item in metrics" class="flex justify-between mt-1">
          <div class="font-normal text-sm">{{item.type}}</div>
          <div class="flex justify-between">
            <div class="font-normal text-sm lg:text-base w-10 lg:w-12">{{item.curr ? (item.curr?.value).toLocaleString() + '%' : ''}}</div>
            <div class="font-normal text-sm lg:text-base w-10 lg:w-12 text-right">(<AnimatedNumber class="mt-1 font-normal text-sm" :styles="style(item.curr,item.prev)" :value="diff(item.curr,item.prev)" type="percentage"/>)</div>
          </div>
      </div>
  </div>
</template>


<script>

import AnimatedNumber from "./AnimatedNumber.vue";
import { Metric, MetricChange} from "../interfaces/";

export default {

  components: {
    AnimatedNumber,
  },

  methods: {
    diff(curr,prev) {
      if (curr?.value && prev?.value){
        let number = parseFloat(new MetricChange(curr, prev).change())
        return number;
        //return Math[number < 0 ? 'ceil' : 'floor'](number);
      }
      return '-';
    },

    style(curr,prev) {
      return [
        'font-semibold',
        'text-sm',
        'mt-auto',
        new MetricChange(curr, prev).style()
      ];
    }
  },

  props: {
    name: String,
    styles: Array,
    selected: Boolean,
    metrics: Array
  },
}

</script>
