<template>
    <div v-for="warehouseMetric in computedWarehouseList" class="px-2 py-1 bg-gray-50 md:bg-gray-100 border">
        <Chip v-if="warehouseMetric.shift"
            class="bg-yellow-50 my-1 w-full flex justify-between text-xs italic font-semibold">
            Shift
            <span>{{ getShift(warehouseMetric.shift).date }}</span>
            <span>{{ getShift(warehouseMetric.shift).time }}</span>
        </Chip>

        <div class="flex">
            <span class="font-normal text-gray-500 text-xs md:text-sm">{{ warehouseMetric.name }}</span>
        </div>

        <div v-for="(metric, index) in warehouseMetric.metrics" :key="index"
            class="flex justify-between mt-1 mb-1 items-center">
            <span class="text-sm md:text-sm font-[350]">
                {{ metric.name }}
            </span>
            <AnimatedNumber :styles="this.getStyles(metric.value.type)" :value="metric.value.value"
                :type="metric.value.type" />
        </div>
    </div>

    <div v-if="showIcon" class="flex justify-center items-center">
        <Button :label="showMore ? 'view less' : 'view more'" plain text @click="updateShowMore" iconPos="right"
            class="text-xs md:text-base p-0 mt-2 md:mt-4" :icon="showMore ? 'pi pi-angle-up' : 'pi pi-angle-down'"
            style="color: green;" />
    </div>
</template>


<script>
import Button from "primevue/button";
import AnimatedNumber from "./AnimatedNumber.vue";
import Chip from 'primevue/chip';

export default {

    components: {
        AnimatedNumber,
        Chip,
        Button
    },

    props: {
        graphToggle: {
            type: Boolean,
            default: false
        },
        styles: Array,
        metricList: {
            type: Array,
            default: []
        },
        showHeader: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            showIcon: false,
            showMore: false
        }
    },

    computed: {
        computedMetricList() {
            return this.metricList
        },

        computedWarehouseList() {
            return (this.showMore || !this.showIcon) ? this.computedMetricList : this.computedMetricList?.slice(0, 3)
        },

        computedGraphToggle() {
            return this.graphToggle
        }
    },

    methods: {
        getStyles(type) {
            return ['font-medium', 'text-sm', 'md:text-base']
        },

        getShift(shift) {
            return {
                date: shift?.split(" ")?.[0],
                time: shift?.split(" ")?.[1]
            }
        },

        updateShowMore() {
            this.showMore = !this.showMore
        }
    },

    mounted() {
        if (!this.showHeader && this.computedMetricList.length > 3) this.showIcon = true;
    }
}
</script>



<style scoped>
.border {
    border: 1px solid #EFEFEF;
}

.chip {
    padding: 0.1rem 0.3rem;
}


@media only screen and (min-width: 600px) {
    .chip {
        padding: 0.3rem;
    }
}
</style>