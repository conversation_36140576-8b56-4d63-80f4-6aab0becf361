<template>
    <div v-if="shouldRender" :class="containerClass">
        <span :class="[titleClass, 'inline-flex items-center']">
            {{ title }}
            <InfoDialog :metrickey="title" :showHeader="this.showHeader" class="p-0 ml-2 border border-black" />
        </span>

        <div :class="gridClass">
            <MetricBox v-for="metric in metrics" :key="metric[metricItemName]" :name="metric[metricItemName]"
                :curr="metric.curr" :prev="metric.prev" :isReverse="isReverse" :isShortenedNumber="isShortenedNumber" />
        </div>
    </div>
</template>

<script>

import MetricBox from './MetricBox.vue';
import Button from 'primevue/button';
import InfoDialog from './InfoDialog.vue';

export default {
    name: "MetricsGrid",

    components: {
        MetricBox,
        Button,
        InfoDialog
    },

    props: {
        shouldRender: {
            type: <PERSON>olean,
            required: true
        },
        metrics: {
            type: Array,
            required: true
        },
        title: {
            type: String,
            required: true
        },
        metricItemName: {
            type: String,
            default: 'displayHour'
        },
        containerClass: {
            type: String,
            default: 'max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4 mt-2 md:mt-8'
        },
        titleClass: {
            type: String,
            default: 'text-sm md:text-lg font-bold text-blue-900'
        },
        gridClass: {
            type: String,
            default: 'grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 mt-2 md:mt-4'
        },
        isReverse: {
            type: Boolean,
            required: false
        },
        isShortenedNumber: {
            type: Boolean,
            default: false
        },
        showHeader: {
            type: Boolean,
            required: true
        }
    },
}

</script>