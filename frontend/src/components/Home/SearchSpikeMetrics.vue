<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="isError" class="text-center p-6">
        <div class="text-sm md:text-md font-semibold text-red-700 mb-3">
            Failed to load metrics. Please try again.
        </div>

        <button @click="fetchMetrics"
            class="px-3 py-1.5 text-sm rounded-md border border-red-300 text-red-700 hover:bg-red-50">
            Retry
        </button>
    </div>

    <div v-else>
        <div v-if="!keywordMetrics || !keywordMetrics.length" class="text-center text-slate-500 p-6">
            No search spikes found for {{ computedDate }}.
        </div>

        <div v-else class="space-y-4">
            <div class="flex items-end justify-between">
                <div class="text-slate-500">Date: {{ computedDate }}</div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-5">
                <div v-for="keywordMeta in keywordMetrics" :key="keywordMeta?.key || keywordMeta?.grain"
                    class="rounded-lg border border-slate-300 shadow-md transition-all duration-300 bg-white/70 backdrop-blur-sm">

                    <div class="bg-gradient-to-r from-slate-700 to-slate-600 px-4 py-2.5 rounded-t-md">
                        <div class="text-sm md:text-base truncate font-medium text-white z-10">
                            {{ keywordMeta?.key || keywordMeta?.grain }}
                        </div>
                    </div>

                    <div class="divide-y divide-slate-100 p-4">
                        <div v-for="metricMeta in keywordMeta?.metric" :key="metricMeta?.name"
                            class="flex items-baseline justify-between py-1 md:py-2">
                            <div class="text-slate-500 text-xs md:text-base">{{ metricMeta?.name }}</div>
                            <div :class="['text-right font-semibold']">
                                <AnimatedNumber :value="getValue(metricMeta)" :type="metricMeta?.type"
                                    :removeDecimals="metricMeta?.type === 'percentage'"
                                    :styles="getMetricStyles(metricMeta)" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Loading from "../Loading.vue";
import AnimatedNumber from "../AnimatedNumber.vue";
import { formatDateToString, isDateToday } from "../../utils/utils.js";
import { apiV2 } from "../../api/index.js";
import axios from "axios";

export default {
    name: "SearchSpikeMetrics",

    components: {
        Loading,
        AnimatedNumber,
    },

    props: {
        cityCode: {
            type: String,
            required: true
        },
        zoneCode: {
            type: String,
            default: null
        },
        storeCode: {
            type: String,
            default: null
        },
        groupId: {
            type: String,
            default: null
        },
        selectedDate: {
            type: Date,
            required: true
        },
        enableGroupView: {
            type: Boolean,
            default: false
        },
    },

    data() {
        return {
            isLoading: true,
            isError: false,
            metrics: [],
            cancelTokens: [],
            keywordMetrics: []
        };
    },

    computed: {
        computedDate() {
            return formatDateToString(this.selectedDate);
        },

        computedIsNotToday() {
            return !isDateToday(this.selectedDate);
        },

        triggerFetchApi() {
            return `${this.cityCode}-${this.zoneCode}-${this.storeCode}-${this.selectedDate}-${this.groupId}-${this.enableGroupView}`
        }
    },

    methods: {
        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = [];
        },

        async fetchMetrics() {
            this.cancelPreviousRequests();

            this.isLoading = true;
            this.isError = false;

            try {
                const keywordMetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [keywordMetricsCancelTokenSource]


                if (this.enableGroupView) {
                    this.isError = true;
                    throw new Error("Not available for group view");
                }

                const keywordMetricsResponse = await apiV2.fetchKeywordMetrics({
                    city: this.cityCode,
                    store: this.storeCode,
                    zone: this.zoneCode,
                    start_date: this.computedDate,
                    cancelToken: keywordMetricsCancelTokenSource.token
                })

                if (keywordMetricsResponse) this.keywordMetrics = keywordMetricsResponse.data

                this.isLoading = false
                this.isError = false

            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    console.error("Error fetching metrics:", error);
                    this.isError = true;
                }
                this.isLoading = false;
            }
        },

        getValue(metricMeta) {
            if (metricMeta.type === 'percentage') {
                const num = parseFloat(metricMeta?.data?.[0]?.metric)
                return Math.floor(num)
            }
            return metricMeta?.data?.[0]?.metric
        },

        getMetricStyles(metricMeta) {
            const baseStyles = ['text-xs', 'md:text-base', 'font-medium'];
            const value = this.getValue(metricMeta);
            // Show red if Search Conversion % is less than 60
            if (metricMeta?.name === 'Search Conversion %') {
                if (value < 60) {
                    baseStyles.push('text-negative-metric');
                } else {
                    baseStyles.push('text-positive-metric');
                }
            } else {
                // Default
                if (value > 0) {
                    baseStyles.push('text-positive-metric');
                } else {
                    baseStyles.push('text-negative-metric');
                }
            }
            
            return baseStyles;
        }
    },

    watch: {
        triggerFetchApi() {
            this.isLoading = true
            this.isError = false
            this.fetchMetrics()
        }
    },

    mounted() {
        this.fetchMetrics();
    },

    beforeUnmount() {
        this.cancelPreviousRequests();
    }
}
</script>
