<template>
    <Accordion :activeIndex="activeAccordions" @tab-open="handleTabOpen" @tab-close="handleTabClose" class="mt-4">
        <AccordionTab>
            <template #header>
                <div class="flex justify-between items-center w-full">
                    <div class="flex items-center gap-2 md:gap-3">
                        <i :class="icon" style="color: black;"></i>
                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">{{ title }}</div>
                    </div>

                    <div class="flex items-center gap-4 md:gap-6 mr-2 md:mr-4">
                        <i v-if="isLoading" class="pi pi-spin pi-spinner" style="color: blue;"></i>

                        <span v-tooltip.top="'View Top Spiking Keywords'">
                            <ArrowTrendingUpIcon @click.stop.prevent="toggleSearchSpike"
                                v-if="metricGroup?.showSearchSpike && !enableGroupView"
                                class="w-6 h-6 p-0.5 rounded-lg bg-gradient-to-r from-blue-400 to-indigo-500 text-white shadow-sm hover:shadow transition-all duration-200 transform hover:scale-105" />
                        </span>

                        <span v-tooltip.top="'View Orders Per Minute'">
                            <ShoppingCartIcon @click.stop.prevent="toggleOrdersPerMinute"
                                v-if="metricGroup?.showOrdersPerMinuteBtn"
                                class="w-6 h-6 p-0.5 rounded-lg bg-gradient-to-r from-lime-400 to-lime-500 text-white shadow-sm hover:shadow transition-all duration-200 transform hover:scale-105" />
                        </span>
                    </div>
                </div>
            </template>

            <div v-if="isLoading" class="flex justify-center items-center p-4">
                loading...
            </div>

            <div v-else-if="isError" class="text-center p-4">
                <span class="text-sm md:text-md font-bold text-red-700">
                    Failed to load metrics. Please try again.
                </span>
            </div>

            <div v-else>
                <div v-if="calculatedMetrics.length === 0" class="flex justify-center items-center p-4">
                    Data not present
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
                    <div v-for="metric in calculatedMetrics" :key="metric.name"
                        class="bg-white/80 backdrop-blur-sm px-4 py-3 md:py-5 rounded-xl shadow-lg border border-gray-100">
                        <div class="flex justify-between px-1 md:px-2">
                            <span
                                class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                                {{ metric.name }}
                                <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                                <i v-if="showCartVolTrendBtn(metric.name)" style="font-size: 0.7rem !important;"
                                    class="pi pi-chart-line ml-1 cursor-pointer transition-colors bg-green-500 text-white p-1 rounded-md hover:bg-green-600"
                                    @click="toggleCartVolTrend"></i>

                                <i v-if="hasExtraMetrics(metric.name) && !showMetricParamsBtn(metric.name)"
                                    style="font-size: 0.7rem !important;"
                                    class="pi pi-plus-circle ml-1 text-blue-500 hover:text-blue-600 cursor-pointer transition-colors"
                                    @click="$emit('showExtraMetrics', metric.name)"></i>

                                <i v-if="showMetricParamsBtn(metric.name)" style="font-size: 0.7rem !important;"
                                    class="pi pi-plus-circle ml-1 text-blue-500 hover:text-blue-600 cursor-pointer transition-colors"
                                    @click="toggleMetricParams(metric.name)"></i>
                            </span>
                            <AnimatedNumber
                                :styles="['text-lg', 'font-semibold', highlightATHMetrics(metric) ? 'linear-wipe' : '']"
                                :value="metric.curr.value" :type="metric.curr.type" />
                        </div>
                        <div class="flex justify-between items-center px-2">
                            <span class="text-xs font-light gray-900"></span>
                            <div class="flex justify-between">
                                <div v-for="diff in metric.diffs" :key="diff.date">
                                    <span class="text-xs font-[650] mr-0.5"
                                        v-bind:class="diff.style(metric.reverseStyle)">
                                        <template v-if="diff.change() !== '-'">
                                            {{ diff.change() }}
                                            {{ absoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </span>
                                    <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                        class="text-xs font-bold mr-1 text-positive-metric">
                                        »</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="anyFailure && failedMetrics.length > 0" class="col-span-1 md:col-span-2 lg:col-span-3">
                        <p class="text-sm md:text-md text-red-700 bg-red-50 p-2 rounded-md">
                            <strong>Failed to load these metrics:</strong> {{ failedMetrics }}
                        </p>
                    </div>
                </div>
            </div>
        </AccordionTab>
    </Accordion>

    <Dialog v-model:visible="showCartVolTrend" modal header="Cart Volume Hourly Trend"
        :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
        <HourlyMetricChart :city="cityCode" />
    </Dialog>

    <Dialog v-model:visible="showOrdersPerMinute" modal header="Orders Per Minute"
        :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
        <OrdersPerMinuteChart :city="cityCode" :zone="zoneCode" :store="storeCode" :groupId="groupId"
            :enableGroupView="enableGroupView" :date="computedDate" />
    </Dialog>

    <SplitMetricModal v-model:visible="showMetricParams" :metricName="selectedMetricForParams" :cityCode="cityCode"
        :zoneCode="zoneCode" :storeCode="storeCode" :groupId="groupId" :enableGroupView="enableGroupView"
        :selectedDate="selectedDate" :allMetrics="metrics" />

    <Dialog v-model:visible="showSearchSpike" modal header="Top Spiking keywords"
        :style="{ width: '75rem', margin: '1rem' }" :dismissableMask="true">
        <SearchSpikeMetrics :cityCode="cityCode" :zoneCode="zoneCode" :storeCode="storeCode" :groupId="groupId"
            :selectedDate="selectedDate" :enableGroupView="enableGroupView" />
    </Dialog>
</template>


<script>
import Loading from "../Loading.vue";
import AnimatedNumber from "../AnimatedNumber.vue";
import InfoDialog from "../InfoDialog.vue";
import { MetricChange } from "../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import { formatDateToString, isDateToday } from "../../utils/utils.js";
import { ASSORTED_TYPE_METRIC_MAPPING, ATHMetricRelation, HomeExtraMetricsMapping } from "../../constants/index.js";
import { ABSOLUTE_METRIC_DELTA } from "../../utils/metrics.js";
import axios from "axios";
import isEmpty from "lodash.isempty";
import Accordion from "primevue/accordion";
import AccordionTab from "primevue/accordiontab";
import Dialog from "primevue/dialog";
import Tooltip from 'primevue/tooltip';
import { ArrowTrendingUpIcon, ShoppingCartIcon } from '@heroicons/vue/24/outline';
import { defineAsyncComponent } from "vue";


export default {
    name: "MetricGroup",

    components: {
        Loading,
        AnimatedNumber,
        InfoDialog,
        Accordion,
        AccordionTab,
        Dialog,
        Tooltip,
        ArrowTrendingUpIcon,
        ShoppingCartIcon,
        HourlyMetricChart: defineAsyncComponent(() => import("../../components/Projections/HourlyMetricChart.vue")),
        SearchSpikeMetrics: defineAsyncComponent(() => import("./SearchSpikeMetrics.vue")),
        OrdersPerMinuteChart: defineAsyncComponent(() => import("./OrdersPerMinuteChart.vue")),
        SplitMetricModal: defineAsyncComponent(() => import("./SplitMetricModal.vue")),
    },

    directives: {
        tooltip: Tooltip
    },

    props: {
        isDefaultOpen: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            required: true
        },
        icon: {
            type: String,
            default: "pi pi-chart-line"
        },
        metricGroup: {
            type: Object,
            required: true
        },
        cityCode: {
            type: String,
            required: true
        },
        zoneCode: {
            type: String,
            default: null
        },
        storeCode: {
            type: String,
            default: null
        },
        groupId: {
            type: String,
            default: null
        },
        enableGroupView: {
            type: Boolean,
            default: false
        },
        selectedDate: {
            type: Date,
            required: true
        },
        showHeader: {
            type: Boolean,
            default: true
        },
        athMetrics: {
            type: Array,
            default: () => []
        },
        idx: {
            type: Number,
        },
        openAccordions: {
            type: Array,
            default: []
        }
    },

    emits: ['updateOpenAccordion', 'updateClosedAccordion', 'showExtraMetrics', 'athHit', 'metricsUpdated'],

    data() {
        return {
            activeAccordions: null,
            isLoading: false,
            isError: false,
            metrics: [],
            cancelTokens: [],
            absoluteMetricDeltaConstant: ABSOLUTE_METRIC_DELTA,
            intervalId: "",
            showCartVolTrend: false,
            showMetricParams: false,
            showSearchSpike: false,
            showOrdersPerMinute: false,
            selectedMetricForParams: ""
        };
    },

    computed: {
        computedDate() {
            return formatDateToString(this.selectedDate);
        },
        computedIsNotToday() {
            return !isDateToday(this.selectedDate);
        },

        calculatedMetrics() {
            const sortedMetrics = sortedAllMetrics(this.metrics, undefined, true, 5, this.computedDate);

            // removing extra metrics
            const filteredMetrics = sortedMetrics
                ?.filter(metric => !Object.values(HomeExtraMetricsMapping)
                    ?.flatMap(metric => metric)
                    ?.includes(metric?.name))

            return filteredMetrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];

                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            })
                ?.filter(metric => metric.curr && metric.curr.value !== "-")
                ?.filter((metric) => this.metricGroup?.metricsToShow?.includes(metric.name)) //* only show metrics that are in metricsToShow
        },

        triggerFetchApi() {
            return `${this.cityCode}-${this.storeCode}-${this.zoneCode}-${this.selectedDate}-${this.groupId}-${this.enableGroupView}`
        },

        failedMetrics() {
            const presentMetrics = this.metrics?.map(metric => metric.name);
            return this.metricGroup?.metricsToShow?.filter((m) => !presentMetrics.includes(m))?.join(', ');
        }
    },

    methods: {
        handleTabOpen(val) {
            this.activeAccordions = 0
            this.$emit('updateOpenAccordion', this.title)
            this.fetchMetrics()
            this.intervalId = window.setInterval(() => {
                this.fetchMetrics();
            }, 120000);
        },

        handleTabClose(val) {
            this.activeAccordions = null
            this.$emit('updateClosedAccordion', this.title)
            this.cancelPreviousRequests();
            clearInterval(this.intervalId);
        },

        hasExtraMetrics(metricName) {
            return Object.keys(HomeExtraMetricsMapping).includes(metricName);
        },

        highlightATHMetrics(metric) {
            const metricName = metric.name;
            const metricValue = metric.curr?.value;

            if (ATHMetricRelation[metricName]) {
                const metricDate = metric.curr?.meta?.date;
                let metricToCompare = this.athMetrics.find((athMetric) => athMetric.name === ATHMetricRelation[metricName])?.data?.[0];

                if (!isEmpty(metricToCompare)) {
                    const meetsCondition = metricDate === metricToCompare?.date
                        ? metricValue >= metricToCompare?.metric
                        : metricValue > metricToCompare?.metric;

                    if (meetsCondition) {
                        this.$emit('athHit');
                        return true;
                    }
                }
            }
            return false;
        },

        filterMetricsWithNoData(metrics) {
            const parsedMetrics = JSON.parse(JSON.stringify(metrics));
            let filteredMetrics = parsedMetrics.filter(metric => metric.data.length > 0);
            return filteredMetrics;
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = [];
        },

        async fetchMetrics() {
            this.isLoading = true;
            this.isError = false;
            this.metrics = [];
            this.cancelPreviousRequests();

            let commonParams = {
                city_code: this.cityCode,
                store_code: this.storeCode,
                zone_code: this.zoneCode,
                group_id: this.groupId,
                enable_group_view: this.enableGroupView
            };

            const metricsPromises = this.metricGroup?.metrics.map((metric) => {
                const { metricList, currDayFetchApi, previousDayFetchApi } = metric;
                const baseFunc = this.computedIsNotToday ? previousDayFetchApi : currDayFetchApi;

                if (metricList) {
                    let metricsToFetch = [];
                    for (let idx = 0; idx < metricList.length; idx += 3) {
                        metricsToFetch.push(metricList.slice(idx, idx + 3));
                    }

                    return metricsToFetch.map((metrics) => {
                        const cancelToken = axios.CancelToken.source();
                        this.cancelTokens.push(cancelToken);

                        let params = {
                            ...commonParams,
                            metrics,
                            cancelToken: cancelToken.token
                        };

                        if (this.computedIsNotToday) {
                            params['start_date'] = this.computedDate;
                        }

                        return baseFunc(params);
                    });
                } else {
                    const cancelToken = axios.CancelToken.source();
                    this.cancelTokens.push(cancelToken);

                    let params = {
                        ...commonParams,
                        cancelToken: cancelToken.token
                    };

                    if (this.computedIsNotToday) {
                        params['start_date'] = this.computedDate;
                    }

                    return baseFunc(params);
                }
            });

            const results = await Promise.allSettled(metricsPromises?.flat());

            let allMetrics = [];
            let anySuccess = false;
            let anyFailure = false;

            results?.forEach((result) => {
                if (result.status === "fulfilled") {
                    const response = result.value;
                    if (response?.data?.metrics) {
                        anySuccess = true;
                        const metrics = this.filterMetricsWithNoData(response.data.metrics);
                        allMetrics.push(...metrics);
                    }
                } else {
                    anyFailure = true;
                    const error = result.reason;
                    if (axios.isCancel(error)) {
                        console.log("Request canceled:", error.message);
                    } else {
                        console.error("Error fetching metrics batch:", error);
                    }
                }
            });

            this.metrics = allMetrics;
            this.isError = !anySuccess; // Only true if ALL requests failed
            this.isLoading = false;

            this.$emit('metricsUpdated', allMetrics);
        },

        showCartVolTrendBtn(metricName) {
            return metricName === "Cart Volume" && !this.computedIsNotToday && !this.storeCode && !this.zoneCode && !this.groupId && !this.enableGroupView;
        },

        toggleCartVolTrend() {
            this.showCartVolTrend = true;
        },

        showOrdersPerMinuteBtn(metricName) {
            return metricName === "Cart Volume" && !this.computedIsNotToday;
        },

        toggleOrdersPerMinute() {
            this.showOrdersPerMinute = true;
        },

        showMetricParamsBtn(metricName) {
            return Object.keys(ASSORTED_TYPE_METRIC_MAPPING).includes(metricName);
        },

        toggleMetricParams(metricName) {
            this.selectedMetricForParams = metricName;
            this.showMetricParams = true;
        },

        toggleSearchSpike() {
            this.showSearchSpike = true;
        }
    },

    watch: {
        triggerFetchApi() {
            this.cancelPreviousRequests();
            if (this.activeAccordions == 0) {
                this.isLoading = true
                this.isError = false
                this.fetchMetrics()
            }
        }
    },

    mounted() {
        if (this.openAccordions.includes(this.title)) {
            this.activeAccordions = 0
            this.fetchMetrics();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}
</script>


<style scoped>
/* Base accordion styles with enhanced glass effect */
:deep(.p-accordion-tab) {
    @apply mb-6 rounded-2xl overflow-hidden;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Header styles with Yellow theme */
:deep(.p-accordion-header) {
    background: rgba(255, 255, 255, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    border-left: 3px solid #FFD700;
    /* Yellow */
}

:deep(.p-accordion-header:not(.p-disabled)) {
    &:hover {
        background: rgba(255, 215, 0, 0.05);
        /* Yellow with low opacity */
        transform: translateY(-2px);
    }

    &:active {
        transform: translateY(0);
        background: rgba(255, 215, 0, 0.1);
    }
}

/* Header link styles */
:deep(.p-accordion-header-link) {
    @apply p-5 font-medium;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 255, 255, 0.05));
}

/* Content styles */
:deep(.p-accordion-content) {
    @apply p-6;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

/* Active state styles */
:deep(.p-accordion-tab-active) {
    .p-accordion-header {
        background: rgba(255, 215, 0, 0.08);
        border-bottom-color: rgba(255, 215, 0, 0.2);
    }
}

/* Animation effects */
:deep(.p-accordion-tab) {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 0 12px 40px 0 rgba(255, 215, 0, 0.15),
            inset 0 0 0 1px rgba(255, 215, 0, 0.1);
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    :deep(.p-accordion-tab) {
        @apply mb-4;
        backdrop-filter: blur(12px);
    }

    :deep(.p-accordion-header-link) {
        @apply p-4 min-h-[56px];
        -webkit-tap-highlight-color: transparent;
    }

    :deep(.p-accordion-content) {
        @apply p-4;
    }
}

/* Custom scrollbar for content */
:deep(.p-accordion-content)::-webkit-scrollbar {
    width: 6px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-track {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 3px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.2);
    border-radius: 3px;
}
</style>