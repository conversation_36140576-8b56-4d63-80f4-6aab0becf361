<template>
    <Dialog v-model:visible="visibleValue" modal :header="props.metricName" :style="{ width: '45rem' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :dismissableMask="true">
        <div v-if="isLoading" class="flex justify-center items-center p-4">
            <i class="pi pi-spin pi-spinner text-xl" style="color: blue;"></i>
            <span class="ml-3">Loading...</span>
        </div>

        <div v-else-if="isError" class="text-center p-4">
            <span class="text-sm md:text-md font-bold text-red-700">
                Failed to make API request. Please try again.
            </span>
        </div>

        <div v-else class="p-1">
            <div v-if="metricsData.length === 0" class="flex justify-center items-center p-4">
                Data not present
            </div>

            <div v-else>
                <div class="mb-3 text-xs md:text-sm italic">
                    <strong>Note : </strong>
                    The sum of Express, Longtail, Super Longtail, and Unicorn metrics gives the overall cart volume. The
                    Union metrics are separate and not part of this sum.
                </div>

                <div v-for="metric in metricsData" :key="metric.name"
                    class="md:bg-white py-1.5 md:py-4 border-b md:border-b-0 md:rounded ">
                    <div class="flex justify-between">
                        <span
                            class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                            {{ getMetricName(metric.name) }}
                            <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                        </span>
                        <AnimatedNumber :styles="['text-lg', 'font-semibold', 'text-black']" :value="metric.curr.value"
                            :type="metric.curr.type" />
                    </div>
                    <div class="flex justify-between items-center px-2">
                        <span class="text-xs font-light gray-900"></span>
                        <div class="flex justify-between">
                            <div v-for="diff in metric.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ ABSOLUTE_METRIC_DELTA.includes(metric.name) ? '' : '%' }}
                                    </template>
                                    <template v-else>
                                        -
                                    </template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric"> »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Dialog>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import Dialog from "primevue/dialog";
import axios from "axios";
import { formatDateToString, isDateToday } from "../../utils/utils.js";
import { apiV2 } from '../../api/index.js';
import { ABSOLUTE_METRIC_DELTA, metricsForStyleReversal, sortedAllMetrics } from '../../utils/metrics.js';
import { MetricChange } from '../../interfaces/index.js';
import InfoDialog from '../InfoDialog.vue';
import AnimatedNumber from '../AnimatedNumber.vue';
import { ASSORTED_TYPE_METRIC_MAPPING, HomeExtraMetricsMapping } from '../../constants/index.js';
import isEmpty from 'lodash.isempty';

const props = defineProps({
    visible: {
        type: Boolean,
        required: true
    },
    metricName: {
        type: String,
        required: true
    },
    cityCode: {
        type: String,
        required: true
    },
    zoneCode: {
        type: String,
        default: null
    },
    storeCode: {
        type: String,
        default: null
    },
    groupId: {
        type: String,
        default: null
    },
    enableGroupView: {
        type: Boolean,
        default: false
    },
    selectedDate: {
        type: Date,
        required: true
    },
    allMetrics: {
        type: Array,
        required: true
    }
});

const emit = defineEmits(['update:visible']);

const isLoading = ref(false);
const isError = ref(false);
const apiResponse = ref(null);
const cancelTokens = ref(null);

// This helps with v-model:visible binding
const visibleValue = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
});

const isNotToday = computed(() => {
    return !isDateToday(props.selectedDate);
});

const formattedDate = computed(() => {
    return formatDateToString(props.selectedDate);
});

const extraMetricsData = computed(() => {
    const metricsToShow = HomeExtraMetricsMapping?.[props.metricName];
    if (isEmpty(metricsToShow)) return null
    return props.allMetrics?.filter((m) => metricsToShow?.includes(m.name))
})

const params = computed(() => {
    let commonParams = {
        city_code: props.cityCode,
        store_code: props.storeCode,
        zone_code: props.zoneCode,
        group_id: props.groupId,
        enable_group_view: props.enableGroupView,
    };

    if (isNotToday.value) {
        commonParams['start_date'] = formattedDate.value
    }

    return commonParams
});

const metricsData = computed(() => {
    const sortedMetrics = sortedAllMetrics(apiResponse.value, undefined, true, 5, formattedDate.value);

    return sortedMetrics?.map(metric => {
        let data = metric.data;
        let curr = data[data.length - 1];
        let diffs = [];

        for (let j = 0; j < data.length - 1; j++) {
            let prev = data[j];
            diffs.push(new MetricChange(curr, prev, metric.name));
        }

        return {
            name: metric.name,
            reverseStyle: metricsForStyleReversal.includes(metric.name),
            curr: curr,
            diffs: diffs,
        }
    })?.filter(metric => metric.curr && metric.curr.value !== "-")
})

const cancelRequest = () => {
    cancelTokens.value?.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
    });
    cancelTokens.value = [];
};

const filterMetricsWithNoData = (metrics) => {
    const parsedMetrics = JSON.parse(JSON.stringify(metrics));
    let filteredMetrics = parsedMetrics.filter(metric => metric.data.length > 0);
    return filteredMetrics;
};



const fetchMetricData = async () => {
    isLoading.value = true;
    isError.value = false;
    apiResponse.value = null;
    cancelRequest();

    const metric = ASSORTED_TYPE_METRIC_MAPPING?.[props.metricName]
    const assortmentType = ['express', 'longtail', 'unicorn', 'super_longtail', 'union']
    const metrics = assortmentType.map((prefix) => `${prefix}_${metric}`)


    const baseFunc = isNotToday.value ? apiV2.fetchCustomDateAllBaseMetrics : apiV2.fetchAllBaseMetrics

    let metricsToFetch = [];
    for (let idx = 0; idx < metrics.length; idx += 3) {
        metricsToFetch.push(metrics.slice(idx, idx + 3));
    }

    const metricsPromises = metricsToFetch.map((metrics) => {
        const cancelToken = axios.CancelToken.source();
        cancelTokens.value.push(cancelToken);

        let apiParams = {
            ...(params.value || {}),
            metrics,
            cancelToken: cancelToken.token
        };

        return baseFunc(apiParams);
    });

    const results = await Promise.allSettled(metricsPromises?.flat());

    let finalResult = []
    let anySuccess = false;
    let anyFailure = false;

    results?.forEach((result) => {
        if (result.status === "fulfilled") {
            const response = result.value;
            if (response?.data?.metrics) {
                anySuccess = true;
                const metrics = filterMetricsWithNoData(response.data.metrics);
                finalResult.push(...metrics);
            }
        } else {
            anyFailure = true;
            const error = result.reason;
            if (axios.isCancel(error)) {
                console.log("Request canceled:", error.message);
            } else {
                console.error("Error fetching metrics batch:", error);
            }
        }
    });

    if (!isEmpty(extraMetricsData.value)) {
        finalResult = [
            ...finalResult,
            ...extraMetricsData.value
        ]
    }

    apiResponse.value = finalResult
    isError.value = !anySuccess; // Only true if ALL requests failed
    isLoading.value = false;
};


const getMetricName = (metric) => {
    return metric?.split(props.metricName)?.[0] || metric
}

// Watch for visibility changes
watch(() => props.visible, (newValue) => {
    if (newValue) {
        fetchMetricData();
    } else {
        cancelRequest();
    }
});

// Cleanup on component unmount
onUnmounted(() => {
    cancelRequest();
});
</script>