<template>
    <MetricCollapse v-for="(metricData, accIdx) in FLATTENED_HOURLY_METRICS" :key="metricData.metricKey"
        :isDefaultOpen="metricData.isDefaultOpen" :metricKey="metricData.metricKey" :apiKey="metricData.apiKey"
        :icon="metricData.icon" :loading="loading?.[metricData?.apiKey] || false"
        :error="error?.[metricData?.apiKey] || false" :data="data[metricData.apiKey]" :fetchData="fetchData"
        :metricData="metricData" :accIdx="accIdx" :openAccordions="openAccordions"
        :cancelPreviousRequests="cancelPreviousRequests" :triggerFetchApi="triggerFetchApi" :computedDate="computedDate"
        @updateOpenAccordion="$emit('updateOpenAccordion', $event)"
        @updateClosedAccordion="$emit('updateClosedAccordion', $event)" />
</template>

<script setup>
import { ref, computed, reactive, defineExpose } from 'vue'
import axios from "axios"
import MetricCollapse from "./MetricCollapse.vue"
import { FLATTENED_HOURLY_METRICS } from '../../../constants/sonar/home.js'
import { api, apiV2 } from '../../../api/index.js'
import { HOURLY_ORDER_METRIC_LIST_1, HOURLY_ORDER_METRIC_LIST_2 } from '../../../utils/metrics.js'
import { formatDateToString, isDateToday } from '../../../utils/utils.js'


// Props
const props = defineProps({
    cityCode: {
        type: String,
        required: true
    },
    zoneCode: {
        type: String,
        default: null
    },
    storeCode: {
        type: String,
        default: null
    },
    groupId: {
        type: String,
        default: null
    },
    selectedDate: {
        type: Date,
        required: true
    },
    isGlobalUser: {
        type: Boolean,
        default: false
    },
    isCityUser: {
        type: Boolean,
        default: false
    },
    enableGroupView: {
        type: Boolean,
        default: false
    },
    showHeader: {
        type: Boolean,
        default: true
    },
    openAccordions: {
        type: Array,
        default: []
    }
})

// Emits
const emit = defineEmits(['updateOpenAccordion', 'updateClosedAccordion'])

// Reactive data
const cancelTokens = ref({})

// COMPUTED
const computedDate = computed(() => {
    return formatDateToString(props.selectedDate)
})

const computedIsNotToday = computed(() => {
    return !isDateToday(props.selectedDate)
})

const triggerFetchApi = computed(() => {
    return `${props.cityCode}-${props.storeCode}-${props.zoneCode}-${props.selectedDate}-${props.groupId}-${props.enableGroupView}`
})

// Methods
const getMetricApiAndParams = (key, cancelToken) => {
    switch (key) {
        case "order_metrics_1":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchHourlyMetrics, params: [HOURLY_ORDER_METRIC_LIST_1, computedDate.value, props.cityCode, props.zoneCode, props.storeCode, null, cancelToken, props.groupId, props.enableGroupView] }
                : { apiFn: api.fetchHourlyMetrics, params: [HOURLY_ORDER_METRIC_LIST_1, false, props.cityCode, props.zoneCode, props.storeCode, null, cancelToken, props.groupId, props.enableGroupView] };

        case "order_metrics_2":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchHourlyMetrics, params: [HOURLY_ORDER_METRIC_LIST_2, computedDate.value, props.cityCode, props.zoneCode, props.storeCode, null, cancelToken, props.groupId, props.enableGroupView] }
                : { apiFn: api.fetchHourlyMetrics, params: [HOURLY_ORDER_METRIC_LIST_2, false, props.cityCode, props.zoneCode, props.storeCode, null, cancelToken, props.groupId, props.enableGroupView] };

        case "funnel_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchHourlyFunnelMetrics, params: [computedDate.value, props.cityCode, props.zoneCode, props.storeCode, props.groupId, props.enableGroupView, cancelToken] }
                : { apiFn: api.fetchHourlyFunnelMetrics, params: [false, props.cityCode, props.zoneCode, props.storeCode, props.groupId, props.enableGroupView, cancelToken] };

        case "store_hau_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchStoreHAU, params: [props.cityCode, props.zoneCode, props.storeCode, props.groupId, props.enableGroupView, computedDate.value, cancelToken] }
                : { apiFn: api.fetchStoreHAU, params: [props.cityCode, props.zoneCode, props.storeCode, props.groupId, props.enableGroupView, cancelToken] };

        case "pan_india_rider_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchPanIndiaRiderMetrics, params: [computedDate.value, props.cityCode, props.zoneCode, props.storeCode, props.groupId, props.enableGroupView, cancelToken] }
                : { apiFn: api.fetchPanIndiaRiderMetrics, params: [false, props.cityCode, props.zoneCode, props.storeCode, props.groupId, props.enableGroupView, cancelToken] };

        case "surge_seen_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchHourlySurgeMetrics, params: [props.cityCode, props.storeCode, props.zoneCode, props.groupId, props.enableGroupView, computedDate.value, cancelToken] }
                : { apiFn: api.fetchHourlySurgeMetrics, params: [props.cityCode, props.storeCode, props.zoneCode, false, props.groupId, props.enableGroupView, cancelToken] };

        default:
            return { apiFn: "", params: [] };
    }
}

const cancelPreviousRequests = (apiKey) => {
    if (cancelTokens.value?.[apiKey]) {
        cancelTokens.value[apiKey].forEach((source) => {
            source.cancel('Operation canceled due to new request.')
        })
        cancelTokens.value[apiKey] = []
    }
}

function useApiFetcher() {
    const loading = reactive({})
    const error = reactive({})
    const data = reactive({})

    //issue: to handle race condition: fetchData called twice by same apiKey, marks loading true, one api gets cancelled as it is same, so loading gets false. but one call is still in the process
    const activeRequests = reactive({}) // Track number of active requests per apiKey

    async function fetchData(apiKey) {
        if (!apiKey) return

        // Initialize counters if they don't exist
        if (!(apiKey in activeRequests)) {
            activeRequests[apiKey] = 0
        }

        // Increment active request counter
        activeRequests[apiKey]++

        // Set loading to true only if it's the first request
        if (activeRequests[apiKey] === 1) {
            loading[apiKey] = true
            error[apiKey] = false
        }

        try {
            const metricsCancelTokenSource = axios.CancelToken.source()

            // Cancel previous requests for props apiKey
            cancelPreviousRequests(apiKey)

            // Store new cancel token
            cancelTokens.value[apiKey] = [metricsCancelTokenSource]


            if(computedIsNotToday.value && !computedDate.value) {
                error[apiKey] = true
                throw new Error("Invalid date");
            }

            const { apiFn, params } = getMetricApiAndParams(apiKey, metricsCancelTokenSource.token)

            if (!apiFn) return

            const response = await apiFn?.(...params)

            if (response?.data?.metrics) {
                data[apiKey] = response.data.metrics
            }
        } catch (err) {
            if (!axios.isCancel(err)) {
                error[apiKey] = true
                console.error(`Error fetching metrics for ${apiKey}:`, err)
            }
        } finally {
            // Decrement active request counter
            activeRequests[apiKey]--

            // Set loading to false only when all requests are complete
            if (activeRequests[apiKey] <= 0) {
                loading[apiKey] = false
                activeRequests[apiKey] = 0 // Reset to 0 to prevent negative values
            }
        }
    }

    return {
        loading,
        error,
        data,
        fetchData
    }
}


// Use the composable
const { loading, error, data, fetchData } = useApiFetcher()

// Expose everything needed for template access
defineExpose({
    cancelTokens,
    loading,
    error,
    data,
    triggerFetchApi,
    FLATTENED_HOURLY_METRICS,
    computedIsNotToday,
    computedDate,
    fetchData,
    cancelPreviousRequests,
    getMetricApiAndParams,
    MetricCollapse
})
</script>
