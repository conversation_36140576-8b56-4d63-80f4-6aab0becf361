<template>
    <Accordion :activeIndex="activeIndex" @tab-open="handleTabOpen" @tab-close="handleTabClose"
        class="mb-4 bg-transparent">
        <AccordionTab :key="metricKey" class="bg-transparent">
            <template #header>
                <div class="flex justify-between items-center w-full">
                    <div class="flex items-center gap-2 md:gap-3">
                        <i :class="icon" style="color: green;"></i>

                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">
                            {{ metricKey }}
                        </div>
                    </div>

                    <i v-if="loading && activeIndex === 0" class="pi pi-spin pi-spinner mr-2" style="color: blue;"></i>
                </div>
            </template>

            <div v-if="loading" class="flex justify-center items-center p-4">
                loading...
            </div>

            <div v-else-if="error" class="text-center p-4">
                <span class="text-sm md:text-md font-bold text-red-700">
                    Failed to load metrics. Please try again.
                </span>
            </div>

            <div v-else>
                <div v-if="!computedMetrics?.length" class="flex justify-center items-center p-4">
                    Data not present
                </div>
                <div v-else class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4">
                    <MetricBoxV2 v-for="metric in computedMetrics" :key="metric?.[metricData.itemName || 'displayHour']"
                        :name="metric?.[metricData.itemName || 'displayHour']" :curr="metric.curr" :prev="metric.prev"
                        :isReverse="computedIsReverse" :isShortenedNumber="metric?.curr?.type === 'currency'" />
                </div>
            </div>

        </AccordionTab>
    </Accordion>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { hourlyMetrics, metricsForStyleReversal } from '../../../utils/metrics'
import MetricBoxV2 from '../../v2/MetricBoxV2.vue'
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';

// Props
const props = defineProps({
    isDefaultOpen: {
        type: Boolean,
        default: false
    },
    metricKey: {
        type: String,
        required: true
    },
    apiKey: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        required: true
    },
    loading: {
        type: Boolean,
        required: true
    },
    error: {
        type: Boolean,
        required: true
    },
    data: {
        type: Array,
        default: () => []
    },
    fetchData: {
        type: Function,
        required: true
    },
    metricData: {
        type: Object,
        required: true
    },
    accIdx: {
        type: Number,
        required: true
    },
    openAccordions: {
        type: Array,
        default: () => []
    },
    triggerFetchApi: {
        type: String,
        required: true
    },
    cancelPreviousRequests: {
        type: Function,
        required: true
    },
    computedDate: {
        type: String,
        required: true
    },
})

// Reactive data
const activeIndex = ref(null)
const intervalId = ref("")

const emit = defineEmits(['updateOpenAccordion', 'updateClosedAccordion', 'updateSortingOrder'])

// Computed
const computedMetrics = computed(() => {
    return hourlyMetrics(props.data, true, [], props.metricKey, props.computedDate)
})

const computedIsReverse = computed(() => {
    return metricsForStyleReversal.includes(props.metricKey)
})

const fetchApi = computed(() => {
    return props.triggerFetchApi
})

// Methods
const handleTabOpen = (val) => {
    activeIndex.value = 0
    emit('updateOpenAccordion', props.metricKey)
    props.cancelPreviousRequests(props.apiKey)

    props.fetchData(props.apiKey)
    intervalId.value = window.setInterval(() => {
        props.fetchData(props.apiKey)
    }, 120000)
}

const handleTabClose = (val) => {
    activeIndex.value = null
    emit('updateClosedAccordion', props.metricKey)
    clearInterval(intervalId.value)
    props.cancelPreviousRequests(props.apiKey)
}

// Watchers
watch(fetchApi, () => {
    if (activeIndex.value === 0) {
        props.cancelPreviousRequests(props.apiKey)
        props.fetchData(props.apiKey)
    }
})

// Lifecycle hooks
onMounted(() => {
    const isDefaultOpen = props.isDefaultOpen
    const isAlreadyOpen = props.openAccordions?.includes(props.metricKey) //from local storage

    if (isDefaultOpen || isAlreadyOpen) {
        activeIndex.value = 0
        props.fetchData(props.apiKey)

        nextTick(() => {
            intervalId.value = window.setInterval(() => {
                props.fetchData(props.apiKey)
            }, 120000)
        })
    }
})

onBeforeUnmount(() => {
    clearInterval(intervalId.value)
    props.cancelPreviousRequests(props.apiKey)
})

defineExpose({
    activeIndex,
    handleTabOpen,
    handleTabClose,
    computedMetrics,
    intervalId,
    computedIsReverse
})

</script>


<style scoped>
/* Base accordion styles with enhanced glass effect */
:deep(.p-accordion-tab) {
    @apply mb-6 rounded-2xl overflow-hidden;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Header styles with Blinkit theme */
:deep(.p-accordion-header) {
    background: rgba(255, 255, 255, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    border-left: 3px solid #00D290;
    /* Blinkit green */
}

:deep(.p-accordion-header:not(.p-disabled)) {
    &:hover {
        background: rgba(0, 210, 144, 0.05);
        /* Blinkit green with low opacity */
        transform: translateY(-2px);
    }

    &:active {
        transform: translateY(0);
        background: rgba(0, 210, 144, 0.1);
    }
}

/* Header link styles */
:deep(.p-accordion-header-link) {
    @apply p-5 font-medium;
    display: flex;
    align-items: center;
    color: #00D290 !important;
    /* Blinkit green */
    background: linear-gradient(135deg, rgba(0, 210, 144, 0.05), rgba(255, 255, 255, 0.05));
}

/* Content styles */
:deep(.p-accordion-content) {
    @apply p-6;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

/* Active state styles */
:deep(.p-accordion-tab-active) {
    .p-accordion-header {
        background: rgba(0, 210, 144, 0.08);
        border-bottom-color: rgba(0, 210, 144, 0.2);
    }
}

/* Animation effects */
:deep(.p-accordion-tab) {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 0 12px 40px 0 rgba(0, 210, 144, 0.15),
            inset 0 0 0 1px rgba(0, 210, 144, 0.1);
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    :deep(.p-accordion-tab) {
        @apply mb-4;
        backdrop-filter: blur(12px);
    }

    :deep(.p-accordion-header-link) {
        @apply p-4 min-h-[56px];
        -webkit-tap-highlight-color: transparent;
    }

    :deep(.p-accordion-content) {
        @apply p-4;
    }
}

/* Custom scrollbar for content */
:deep(.p-accordion-content)::-webkit-scrollbar {
    width: 6px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-track {
    background: rgba(0, 210, 144, 0.1);
    border-radius: 3px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-thumb {
    background: rgba(0, 210, 144, 0.2);
    border-radius: 3px;
}
</style>