<template>
    <div v-if="isLoading">
        <Loading />
    </div>
    <div v-else-if="isError">
        <span class="text-sm md:text-lg font-bold text-red-700 inline-flex items-center">
            Failed to load. Please try again.
        </span>
    </div>
    <div v-else>
        <v-chart class="h-[50vh]" :option="chartOption" autoresize />
    </div>
</template>

<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    MarkLineComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';
import Loading from '../Loading.vue';
import { apiV2 } from '../../api/index.js';
import axios from 'axios';
import isEmpty from 'lodash.isempty';

use([
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Toolt<PERSON>Component,
    <PERSON><PERSON><PERSON>ponent,
    <PERSON><PERSON><PERSON>omponent,
    MarkLineComponent
]);

export default {
    components: {
        VChart,
        Loading
    },

    props: {
        city: {
            type: String,
            default: ""
        },
        zone: {
            type: String,
            default: ""
        },
        store: {
            type: String,
            default: ""
        },
        groupId: {
            type: String,
            default: ""
        },
        enableGroupView: {
            type: Boolean,
            default: false
        },
        date: {
            type: Date,
            required: true
        }
    },

    data() {
        return {
            isLoading: false,
            isError: false,
            cancelTokens: [],
            chartData: null
        }
    },

    computed: {
        chartOption() {
            if (isEmpty(this.chartData)) return {};

            const sortedMetrics = this.chartData.sort(
                (a, b) => new Date(a.minute_bucket) - new Date(b.minute_bucket)
            );

            return {
                tooltip: {
                    trigger: 'axis'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    name: 'minute',
                    nameLocation: 'middle',
                    nameGap: 30,
                    boundaryGap: false,
                    data: sortedMetrics.map(m => m.minute_bucket?.split(" ")?.[1]),
                },
                yAxis: {
                    type: 'value',
                    name: 'Orders',
                    nameLocation: 'middle',
                    nameGap: 60,
                    nameRotate: 90,
                    smooth: true,
                },
                series: [
                    {
                        name: 'Orders per Minute',
                        type: 'line',
                        smooth: true,
                        data: sortedMetrics.map(m => m.orders_per_minute)
                    }
                ]
            }
        }
    },

    methods: {
        formatNumber(value) {
            if (value === null || value === undefined) return '0';
            if (typeof value !== 'number') return '0';
            if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K';
            }
            return value.toLocaleString();
        },

        formatDateToString(date) {
            if (!date) return '';
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },

        async fetchOrdersPerMinute() {
            try {
                const cancelToken = axios.CancelToken.source();
                this.cancelTokens = [cancelToken];

                const response = await apiV2.fetchOrdersPerMinute({
                    city: this.city,
                    zone: this.zone,
                    store: this.store,
                    group_id: this.groupId,
                    enable_group_view: this.enableGroupView,
                    date_str: this.date,
                    cancelToken: cancelToken.token
                });

                if (response) this.chartData = response.data.metrics;

                this.isError = false;
            } catch (error) {
                console.error("Error fetching orders per minute:", error);
                this.isError = true;
            } finally {
                this.isLoading = false;
            }
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = [];
        }
    },

    mounted() {
        this.isLoading = true;
        this.isError = false;
        this.fetchOrdersPerMinute();
    },

    beforeUnmount() {
        this.cancelPreviousRequests();
    }
}
</script>