<template>
    <div class="md:flex gap-8" v-if="!isError && (isGlobalUser || isCityUser)">
        <div v-for="projectionMetric in computedProjectionMetrics" class="text-left font-bold text-gray-900 flex gap-2">
            <span>{{ projectionMetric.title }} :</span>

            <span v-if="isLoading" class="flex items-center ">
                <i class="pi pi-spin pi-spinner" style="color: blue;"></i>
            </span>

            <span v-else class="flex gap-2">
                <AnimatedNumber :value="projectionMetric.value" :type="projectionMetric.type" />

                <span class="text-xs md:text-sm font-light">
                    (
                    <AnimatedNumber :value="projectionMetric?.change" type="percentage" class="font-normal"
                        :class="projectionMetric.class" />)
                </span>
            </span>
        </div>
    </div>
</template>

<script>
import Loading from "../Loading.vue";
import AnimatedNumber from "../AnimatedNumber.vue";
import { MetricChange } from "../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import { formatDateToString, isDateToday } from "../../utils/utils.js";
import { api, apiV2 } from "../../api/index.js";
import axios from "axios";

export default {
    name: "Projections",

    components: {
        Loading,
        AnimatedNumber,
    },

    props: {
        cityCode: {
            type: String,
            required: true
        },
        zoneCode: {
            type: String,
            default: null
        },
        storeCode: {
            type: String,
            default: null
        },
        groupId: {
            type: String,
            default: null
        },
        selectedDate: {
            type: Date,
            required: true
        },
        isGlobalUser: {
            type: Boolean,
            default: false
        },
        isCityUser: {
            type: Boolean,
            default: false
        },
        enableGroupView: {
            type: Boolean,
            default: false
        },
    },

    data() {
        return {
            isLoading: true,
            isError: false,
            metrics: [],
            cancelTokens: [],
            intervalId: "",
            projectedCartCount: 0,
            projectedGMV: 0
        };
    },

    computed: {
        computedDate() {
            return formatDateToString(this.selectedDate);
        },

        computedIsNotToday() {
            return !isDateToday(this.selectedDate);
        },

        computedProjectionMetrics() {
            return [
                {
                    title: "Projected GMV",
                    value: this.projectedGMV?.curr?.value,
                    change: this.projectedGMV?.diffs?.at(-1)?.change(),
                    class: this.projectedGMV?.diffs?.at(-1).style(),
                    type: 'currency'
                },
                {
                    title: "Projected Carts",
                    value: this.projectedCartCount?.curr?.value,
                    change: this.projectedCartCount?.diffs?.at(-1)?.change(),
                    class: this.projectedCartCount?.diffs?.at(-1).style(),
                    type: 'number'
                }
            ]
        },

        triggerFetchApi() {
            return `${this.cityCode}-${this.zoneCode}-${this.storeCode}-${this.selectedDate}-${this.groupId}-${this.enableGroupView}`
        }
    },

    methods: {
        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = [];
        },

        async fetchMetrics() {
            this.cancelPreviousRequests();

            this.isLoading = true;
            this.isError = false;

            try {
                const projectionMetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [projectionMetricsCancelTokenSource]

                if (this.computedIsNotToday && !this.computedDate) {
                    this.isError = true;
                    throw new Error("Invalid date");
                }

                let projectionMetrics = (this.isGlobalUser || this.isCityUser)
                    ? (this.computedIsNotToday
                        ? apiV2.fetchCurrentRateMetrics(this.cityCode, this.storeCode, this.zoneCode, this.computedDate, projectionMetricsCancelTokenSource.token, this.groupId, this.enableGroupView)
                        : api.fetchTodaysProjectionMetrics(this.cityCode, this.storeCode, this.zoneCode, projectionMetricsCancelTokenSource.token, this.groupId, this.enableGroupView)
                    ) : Promise.resolve(null);

                let [
                    projectionMetricsResponse,
                ] = await Promise.all([
                    projectionMetrics
                ]);

                if (projectionMetricsResponse) {
                    let projectedMetrics = this.filterMetricsWithNoData(projectionMetricsResponse.data.metrics);
                    projectedMetrics = sortedAllMetrics(projectedMetrics, undefined, true, 2, this.computedDate)
                    let metrics = this.calculatedMetrics(projectedMetrics)

                    this.projectedCartCount = metrics?.find((metric) => metric.name === 'Projected Cart Volume')
                    this.projectedGMV = metrics?.find((metric) => metric.name === 'Projected GMV')
                }

                this.isLoading = false
                this.isError = false

            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    console.error("Error fetching metrics:", error);
                    this.isError = true;
                }
                this.isLoading = false;
            }
        },


        filterMetricsWithNoData(metrics) {
            const parsedMetrics = JSON.parse(JSON.stringify(metrics))
            let filteredMetrics = parsedMetrics.filter(metric => metric.data.length > 0);
            return filteredMetrics;
        },

        calculatedMetrics(metrics) {
            return metrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },
    },

    watch: {
        triggerFetchApi() {
            this.isLoading = true
            this.isError = false
            this.fetchMetrics()
        }
    },

    mounted() {
        if (this.isGlobalUser || this.isCityUser) {
            this.fetchMetrics();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => { this.fetchMetrics() }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}
</script>
