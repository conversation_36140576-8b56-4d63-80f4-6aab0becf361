<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="isError" class="text-center p-6">
        <div class="text-sm md:text-md font-semibold text-red-700 mb-3">
            Failed to load metrics. Please try again.
        </div>

        <button @click="fetchMetrics"
            class="px-3 py-1.5 text-sm rounded-md border border-red-300 text-red-700 hover:bg-red-50">
            Retry
        </button>
    </div>

    <div v-else>
        <div v-if="!allMetrics || !allMetrics.length" class="text-center text-slate-500 p-6">
            No search spikes found for {{ computedDate }}.
        </div>

        <div class="mt-1.5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 lg:gap-3">
            <div v-for="metric in calculatedMetrics" :key="metric.name"
                class="bg-gray-100 p-1 md:p-2 border-b md:border-b-0 md:rounded-lg">
                <div class="text-xs md:text-sm text-gray-500">{{ metric.name }}</div>

                <div class="mt-2 flex justify-between items-center">
                    <AnimatedNumber :styles="['text-sm', 'font-semibold', 'md:text-md']" :value="metric?.curr?.value"
                        :type="metric?.curr?.type" />

                    <div class="flex justify-between items-center">
                        <div class="flex justify-between">
                            <div v-for="diff in metric?.diffs" :key="diff.date">
                                <span class="text-xs font-[650] mr-0.25 md:mr-0.5"
                                    v-bind:class="diff.style(metric.reverseStyle)">
                                    <template v-if="diff.change() !== '-'">
                                        {{ diff.change() }}
                                        {{ absoluteMetricDeltaConstant.includes(metric.name) ? '' : '%'
                                        }}
                                    </template>
                                    <template v-else>
                                        -
                                    </template>
                                </span>
                                <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                    class="text-xs font-bold mr-1 text-positive-metric"> »
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Loading from "../Loading.vue";
import AnimatedNumber from "../AnimatedNumber.vue";
import { api } from "../../api/index.js";
import axios from "axios";
import { ABSOLUTE_METRIC_DELTA, metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import { MetricChange } from "../../interfaces/index.js";
import InfoDialog from "../InfoDialog.vue";

export default {
    name: "L1DataPopUp",

    components: {
        Loading,
        AnimatedNumber,
        InfoDialog,
        AnimatedNumber
    },

    props: {
        city: {
            type: String,
            required: true
        },
        isYesterday: {
            type: Boolean,
            default: false
        },
        metricName: {
            type: String,
            required: true
        },
        l0Category: {
            type: String,
            required: true
        },
    },

    data() {
        return {
            isLoading: true,
            isError: false,
            metrics: [],
            cancelTokens: [],
            allMetrics: [],
            absoluteMetricDeltaConstant: ABSOLUTE_METRIC_DELTA,
        };
    },

    computed: {
        triggerFetchApi() {
            return `${this.city}-${this.isYesterday}-${this.metricName}-${this.l0Category}`
        },

        calculatedMetrics() {
            const mutatedMetrics = this.allMetrics?.map((it) => {
                const { l1_category, metrics } = it || {}

                // * assuming metrics will ahve only 0th index
                return metrics.map((m) => ({
                    ...m,
                    name: l1_category
                }))[0]
            })

            const sortedMetrics = sortedAllMetrics(mutatedMetrics, undefined, true, 5);

            return sortedMetrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];

                for (let j = 0; j < data.length - 1; j++) {
                    if (j == 1 || j == 2) continue; //only show wow1, wow4
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            })?.filter(metric => metric.curr && metric.curr.value !== "-")
        },
    },

    methods: {
        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = [];
        },

        async fetchMetrics() {
            this.cancelPreviousRequests();

            this.isLoading = true;
            this.isError = false;

            const metricMap = {
                'AOV Contribution': 'aov_contribution',
                'GMV': 'gmv',
                'Cart Pen': 'unique_carts',
                'Total Items Sold': 'total_items_sold',
            }

            try {
                const MetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [MetricsCancelTokenSource]


                if (this.enableGroupView) {
                    this.isError = true;
                    throw new Error("Not available for group view");
                }

                const baseFunc = this.isYesterday ? api.fetchYesterdayL1CategoryMetrics : api.fetchL1CategoryMetrics

                const metricsResponse = await baseFunc({
                    city: this.city,
                    metric: metricMap[this.metricName],
                    l0_category: this.l0Category,
                    cancelToken: MetricsCancelTokenSource.token
                })

                if (metricsResponse) this.allMetrics = metricsResponse.data.data

                this.isError = false
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    console.error("Error fetching metrics:", error);
                    this.isError = true;
                }
            } finally {
                this.isLoading = false;
            }
        },
    },

    watch: {
        triggerFetchApi() {
            this.isLoading = true
            this.isError = false
            this.fetchMetrics()
        }
    },

    mounted() {
        this.fetchMetrics();
    },

    beforeUnmount() {
        this.cancelPreviousRequests();
    }
}
</script>
