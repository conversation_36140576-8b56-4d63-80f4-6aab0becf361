<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else class="my-4">
        <div v-if="computedStoreCode" v-for="itemData in computedData"
            class="text-xs md:text-base p-3 pb-1 mb-4 rounded-xl"
            :style="itemData?.total > 0 ? 'background:#E5F3F3' : 'background:#FFEDEF'">
            <div v-for="itemDetails in getItemDetails(itemData)" class="flex pb-1">
                <div class="flex-1">{{ itemDetails.label }}</div>
                <div v-if="itemDetails.label === 'Inventory Qty'" class="flex-1 text-right font-semibold"
                    :class="itemData?.total > 0 ? 'text-green-600' : 'text-red-600'">{{ itemDetails.value }}</div>
                <div v-else class="flex-1 text-right font-semibold">{{ itemDetails.value }}</div>
            </div>

            <div v-if="isPresent(itemData.metric)"
                class="mt-2 gap-x-3 gap-y-2 md:gap-3 mb-2 grid grid-cols-2 md:grid-cols-4">
                <div v-for="metric in itemData.metric" class="rounded-xl bg-white card">
                    <div class="py-3 px-3 md:px-4 text-xs md:text-base flex justify-between items-center card_title">
                        <div class="font-semibold flex items-center justify-between w-full">
                            {{ metric?.name }}
                            <Chip v-if="metric?.name === 'Inventory'"
                                class="chip font-light md:font-normal bg-orange-50 text-orange-400"
                                label="During ARS" />
                        </div>
                    </div>

                    <div class="pt-2 pb-3 px-3 md:px-4 text-xs md:text-lg font-light">
                        <div v-for="itemMetricData in metric.data"
                            class="flex items-center mb-1 last-of-type:mb-0 md:mb-0">
                            <div class="flex-1" :style="computedLabelStyle">{{ getLabel(itemMetricData?.name) }}</div>
                            <div class="flex-1 font-normal text-right" :class="getValueClass(itemMetricData)">
                                {{ getValue(itemMetricData) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else v-for="itemData in computedData" class="text-xs md:text-base p-3 pb-1 mb-4 rounded-xl"
            :style="itemData?.unavailableStores === 0 ? 'background:#E5F3F3' : 'background:#FFEDEF'">
            <div v-for="itemDetails in getItemDetails(itemData)" class="flex pb-1">
                <div class="flex-1">{{ itemDetails.label }}</div>
                <div class="flex-1 text-right font-semibold">{{ itemDetails.value }}</div>
            </div>

            <div v-if="isPresent(itemData.metric)"
                class="mt-2 gap-x-3 gap-y-2 md:gap-3 mb-2 grid grid-cols-1 md:grid-cols-2">
                <div v-for="metric in itemData.metric" class="rounded-xl bg-white card">
                    <div class="py-3 px-3 md:px-4 text-xs md:text-base flex justify-between items-center card_title">
                        <div class="font-semibold flex items-center justify-between w-full">
                            <span>{{ metric?.name }}</span>
                            <span :class="metric?.data?.[0]?.metric === 0 ? 'text-green-600' : 'text-red-600'">
                                {{ metric?.data?.[0]?.metric }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions } from "pinia";
import { useUserStore } from "../../stores/users";
import Loading from "../Loading.vue";
import axios from "axios";
import { api } from "../../api";
import { PanIndiaConstants } from "../../constants";
import isEmpty from "lodash.isempty";
import Chip from "primevue/chip";
import startCase from 'lodash.startcase';


export default {
    components: {
        Loading,
        Chip
    },

    props: {
        cityName: {
            type: String,
            default: "",
        },
        storeName: {
            type: String,
            default: "",
        },
        storeCode: {
            type: String,
            default: "",
        },
        ptype: {
            type: String,
            default: "Curd",
        },
        isYesterday: {
            type: Boolean,
            default: false,
        },
        selectedHour: {
            type: Number,
            default: -1,
        },
        showHeader: {
            type: Boolean,
            default: true,
        }
    },

    data() {
        return {
            isLoading: true,
            isError: false,
            intervalId: null,
            cancelToken: null,
            itemMetrics: [],
        };
    },

    computed: {
        computedCityName() {
            return this.cityName;
        },

        computedCityCode() {
            return this.cityName && this.cityName === PanIndiaConstants.PAN_INDIA.name ? "" : this.cityName;
        },

        computedStoreName() {
            return this.storeName;
        },

        computedStoreCode() {
            return this.storeCode;
        },

        computedPtype() {
            return this.ptype;
        },

        computedIsYesterday() {
            return this.isYesterday;
        },

        computedSelectedHour() {
            return this.selectedHour;
        },

        computedData() {
            if (!this.computedStoreCode) {
                return this.itemMetrics?.map((item) => {
                    const lastUpdatedAt = item?.metric?.find((item) => item.name === 'ARS Created')?.data?.[0]?.metric;
                    const updatedMetric = item?.metric?.filter((item) => item.name !== 'ARS Created')
                    return { ...item, metric: updatedMetric, last_updated_at: lastUpdatedAt }
                })
            }

            // else
            return this.itemMetrics?.map((item) => {
                const { key, grain, metric } = item || {};

                const metricMapping = {
                    "Item Sales": ["Sold Qty T0", "Sold Qty T1", "Current Availability"],
                    "ARS Details": ["ARS CPD", "ARS Demand"],
                    Inventory: ["FE Inventory", "BE Inventory"],
                    "STO Raised": ["Indent V1", "STO Raised"],
                    Truncation: ["Truncation Qty", "Truncation Reason"],
                    Transfers: ["Billed Qty", "In Transit Qty"],
                    GRN: ["GRN Qty", "DN", "B2B"],
                    "Open STO": ["STO Qty"]
                }

                const v1_demand_gap = metric.find((item) => item.name === "V1 Demand Gap")?.data?.[0]?.metric >= 90
                const indent_gap = metric.find((item) => item.name === "Indent Gap")?.data?.[0]?.metric >= 90
                const truncation = metric.find((item) => item.name === "Truncation Qty")?.data?.[0]?.metric <= 0
                const billed_fill_rate = metric.find((item) => item.name === "Billed Fill Rate")?.data?.[0]?.metric >= 90
                const dispatch_fill_rate = metric.find((item) => item.name === "Dispatch Fill Rate")?.data?.[0]?.metric >= 90
                const grn_fill_rate = metric.find((item) => item.name === "GRN Fill Rate")?.data?.[0]?.metric >= 90
                const dn_b2b = ((metric.find((item) => item.name === "DN Fill Rate")?.data?.[0]?.metric || 0)
                    + (metric.find((item) => item.name === "B2B Fill Rate")?.data?.[0]?.metric || 0)) <= 0
                const current_availability = (metric.find((item) => item.name === "Current Availability")?.data?.[0]?.metric || 0) * 100 >= 80

                let isValidConditionObj = {
                    "Indent V1": v1_demand_gap,
                    "STO Raised": indent_gap,
                    "Truncation Qty": truncation,
                    "Billed Qty": billed_fill_rate,
                    "In Transit Qty": dispatch_fill_rate,
                    "GRN Qty": grn_fill_rate,
                    "DN/B2B": dn_b2b,
                    "Current Availability": current_availability
                }

                let mutatedMetric = Object.keys(metricMapping).map((metricName) => {
                    return ({
                        name: metricName,
                        data: metric.filter((item) => metricMapping[metricName].includes(item.name)).map((metricItemData) => {
                            let metricItem = {
                                name: metricItemData?.name,
                                data: metricItemData?.data?.[0]?.metric,
                                type: metricItemData?.type,
                            }

                            if (Object(isValidConditionObj).hasOwnProperty(metricItemData?.name)) {
                                metricItem = { ...metricItem, isValid: isValidConditionObj?.[metricItemData?.name] }
                            }

                            return metricItem;
                        })
                    })
                })

                // changes for GRN
                mutatedMetric = mutatedMetric.map((metric) => {
                    if (metric.name === "GRN") {
                        const { data = [] } = metric;

                        // combine DN and B2B
                        let DN_B2B = data.find((itemMetric) => itemMetric?.name === "DN")
                        DN_B2B.name = "DN/B2B"
                        DN_B2B.data += data.find((itemMetric) => itemMetric?.name === "B2B")?.data || 0
                        DN_B2B.isValid = isValidConditionObj['DN/B2B']

                        //remove B2B data
                        let grnData = data.filter((itemMetric) => itemMetric.name !== "B2B")

                        return { ...metric, data: grnData }
                    }
                    else return metric;
                })

                const computedTotal = metric.find((item) => item.name === "Total Inventory")?.data?.[0]?.metric
                const computedLastUpdatedAt = metric.find((item) => item.name === "ARS Created")?.data?.[0]?.metric
                const computedMaxGRNAt = metric.find((item) => item.name === "Max GRN Time")?.data?.[0]?.metric

                return {
                    key, grain, total: computedTotal,
                    last_updated_at: computedLastUpdatedAt, max_grn_time: computedMaxGRNAt,
                    metric: mutatedMetric,
                };
            });
        },

        computedLabelStyle() {
            if (this.showHeader) return { fontSize: "0.8rem" }
            return { fontSize: "0.625rem" }
        }
    },

    methods: {
        getLabel(metricName) {
            const labelMapping = {
                "Sold Qty T0": "Sold Qty (T)",
                "Sold Qty T1": "Sold Qty (T-1)",
                "Current Availability": "Availability",
                "FE Inventory": "FE",
                "BE Inventory": "BE",
                "In Transit Qty": "Dispatched",
                "Truncation Qty": "Qty",
                "Truncation Reason": "Reason",
                "STO Qty": "Qty"
            }

            return labelMapping[metricName] || metricName
        },

        getValue(metricData) {
            const { name = "", data } = metricData || {}
            if (name === "Truncation Reason") return data === "null" ? "" : startCase(data)
            if (name === "Current Availability") return `${data}%`
            return data
        },

        getValueClass(itemMetricData) {
            if (Object(itemMetricData).hasOwnProperty('isValid')) {
                return itemMetricData?.isValid ? "text-green-600" : "text-red-600";
            }
            if (itemMetricData?.name === "Truncation Reason") return "text-xs";
            return "";
        },

        getItemDetails(itemData) {
            const { key, grain, total = 0, last_updated_at = "", max_grn_time = "" } = itemData || {};

            const mapping = [
                { label: "Item Name", value: key },
                { label: "Item Id", value: grain },
                { label: "Inventory Qty", value: total },
                { label: "ARS Runtime", value: last_updated_at === "null" ? "ARS didn't run for last 7 days" : last_updated_at },
                { label: "Last GRN", value: max_grn_time === "null" ? "No GRN in the last 7 days" : max_grn_time },
            ]

            if (this.computedStoreCode) return mapping
            return [mapping[0], mapping[1], mapping[3]]
        },

        async fetchAllMetrics() {
            this.cancelPreviousRequests();
            try {
                const itemMetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelToken = itemMetricsCancelTokenSource;

                let itemMetrics = api.fetchItemInsightsMetrics(
                    [], this.computedIsYesterday, this.computedCityCode, this.computedStoreCode, this.computedSelectedHour, this.computedPtype, itemMetricsCancelTokenSource.token
                );

                let [itemMetricsResponse] = await Promise.all([itemMetrics]);

                if (itemMetricsResponse) {
                    this.itemMetrics = itemMetricsResponse.data;
                }

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                    let status = error.response.status;
                    if (status === 403) {
                        this.logout();
                        console.log("Unauthenticated");
                        this.$router.replace("/login");
                    }
                }
            }
        },

        cancelPreviousRequests() {
            if (this.cancelToken) this.cancelToken.cancel("Operation canceled due to new request.");
            this.cancelToken = null; // Clear the tokens
        },

        isPresent(obj) {
            return !isEmpty(obj);
        },

        ...mapActions(useUserStore, ["logout"]),
    },

    watch: {
        // to auto fetch, when yesterday is toggled
        computedIsYesterday() {
            this.isLoading = true;
            this.isError = false;
            this.fetchAllMetrics();
        },
    },

    mounted() {
        this.isLoading = true;
        this.isError = false;

        this.fetchAllMetrics();
        this.$nextTick(function () {
            this.intervalId = window.setInterval(() => {
                this.fetchAllMetrics();
            }, 120000);
        });
    },
    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    },
};
</script>

<style scoped>
.card {
    border: 1px solid #edf4ff;
}

.card_title {
    border-bottom: 1px solid #edf4ff;
}

.chip {
    font-size: 0.5rem;
    padding: 0.1rem 0.3rem;
}


@media only screen and (min-width: 600px) {
    .chip {
        font-size: 0.75rem;
        padding: 0.3rem;
    }
}
</style>
