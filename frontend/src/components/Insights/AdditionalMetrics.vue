<template>
    <Sidebar v-if="!showHeader" v-model:visible="showbar" @hide="hideSidebar" :showCloseIcon="false" position="bottom"
        :blockScroll="true"
        style="height:fit-content; border-top-left-radius: 20px; border-top-right-radius: 20px; background-color: #FBFBFB;">
        <template #header>
            <div class=" flex justify-between items-center w-full">
                <div class="text-xl font-bold">{{ computedMetricName(additionalMetrics?.overallMetric?.name) }}</div>

                <div class="py-2 px-3 text-lg font-bold flex items-center">
                    <AnimatedNumber :value="additionalMetrics?.overallMetric?.data?.value"
                        :type="additionalMetrics?.overallMetric?.data?.type"
                        :class="additionalMetrics?.overallMetric?.data?.valueClass" />

                    <div v-if="additionalMetrics?.overallMetric?.data?.diff" class="text-xs lg:text-sm font-light pl-1">
                        <AnimatedNumber :value="additionalMetrics?.overallMetric?.data?.diff"
                            :type="additionalMetrics?.overallMetric?.data?.type"
                            :class="[additionalMetrics?.overallMetric?.data?.diffClass, 'md:text-sm']" />
                    </div>

                    <span v-if="additionalMetrics?.overallMetric?.data?.diff"
                        :class="additionalMetrics?.overallMetric?.data?.arrowClass"
                        style="font-weight: 300; font-size: 10px; padding-left: 0.25rem;"></span>
                </div>
            </div>
        </template>

        <div class="mb-20">
            <div class="mb-4 text-left">{{ computedHeader }}</div>

            <div class="flex flex-wrap gap-2">
                <div v-for="metric in additionalMetrics?.remainingMetrics" class="rounded-xl bg-white card">
                    <div class="py-3 px-4 text-xs lg:text-sm font-semibold card-title ">
                        {{ computedMetricName(metric?.name) }}
                    </div>

                    <div class="pt-2 pb-3 px-4 text-base lg:text-lg font-semibold">
                        <AnimatedNumber :value="metric.data?.value" :type="metric.data?.type"
                            :class="getValueClass(metric.data?.value, metric.data?.valueClass)" />
                    </div>
                </div>
            </div>
        </div>
    </Sidebar>

    <Dialog v-else v-model:visible="showbar" @hide="hideSidebar" :dismissableMask="true" :closable="false" modal
        :style="{ width: '25rem' }" :contentStyle="{ background: '#FBFBFB' }">
        <template #header>
            <div class="flex justify-between items-center w-full">
                <div class="text-xl font-bold">{{ computedMetricName(additionalMetrics?.overallMetric?.name) }}</div>

                <div class="py-2 px-3 text-base lg:text-lg font-bold flex items-center">
                    <AnimatedNumber :value="additionalMetrics?.overallMetric?.data?.value"
                        :type="additionalMetrics?.overallMetric?.data?.type"
                        :class="additionalMetrics?.overallMetric?.data?.valueClass" />

                    <div v-if="additionalMetrics?.overallMetric?.data?.diff" class="text-xs lg:text-sm font-light pl-1">
                        <AnimatedNumber :value="additionalMetrics?.overallMetric?.data?.diff"
                            :type="additionalMetrics?.overallMetric?.data?.type"
                            :class="[additionalMetrics?.overallMetric?.data?.diffClass, 'md:text-sm']" />
                    </div>

                    <span v-if="additionalMetrics?.overallMetric?.data?.diff"
                        :class="additionalMetrics?.overallMetric?.data?.arrowClass"
                        style="font-weight: 300; font-size: 10px; padding-left: 0.25rem;"></span>
                </div>
            </div>
        </template>

        <div class="py-2">
            <div class="mb-4 text-left">{{ computedHeader }}</div>
            <div class="flex flex-wrap mt-4 gap-2">
                <div v-for="metric in additionalMetrics?.remainingMetrics" class="rounded-xl bg-white card">
                    <div class="py-2 px-3 text-xs lg:text-sm card-title ">
                        {{ computedMetricName(metric?.name) }}
                    </div>

                    <div class="py-2 px-3 text-base lg:text-lg font-semibold">
                        <AnimatedNumber :value="metric.data?.value" :type="metric.data?.type"
                            :class="getValueClass(metric.data?.value, metric.data?.valueClass)" />
                    </div>
                </div>
            </div>
        </div>
    </Dialog>
</template>

<script>
import Card from 'primevue/card';
import AnimatedNumber from '../AnimatedNumber.vue';
import Sidebar from 'primevue/sidebar';
import Dialog from 'primevue/dialog';
import { MetricNameMapping } from '../../constants';
import isEmpty from 'lodash.isempty';

export default {
    components: {
        Sidebar,
        Dialog,
        Card,
        AnimatedNumber
    },

    props: {
        showSidebar: {
            type: Boolean,
            required: true,
        },
        showHeader: {
            type: Boolean,
            required: true,
        },
        additionalMetrics: {
            type: Object,
            required: true,
        }
    },

    data() {
        return {
            showbar: false
        }
    },

    computed: {
        computedHeader() {
            if (this.additionalMetrics?.overallMetric?.name === 'ATC %') return 'ATC Contributions';
            else return 'C2Co Contributions';
        }
    },

    methods: {
        hideSidebar() {
            this.$emit('update:showSidebar', false);
        },

        computedMetricName(metricName = '') {
            return MetricNameMapping[metricName] || metricName;
        },

        getValueClass(val, valClass) {
            if ((isEmpty(valClass) || isEmpty(valClass[0])) && val !== 0) {
                return val > 0 ? ['text-green-600'] : ['text-red-600'];
            }
            return valClass;
        }
    },

    mounted() {
        this.showbar = this.showSidebar;
    }
}
</script>

<style scoped>
.card {
    margin-bottom: 0.5rem;
    border: 1px solid #EDF4FF;
    flex-basis: 48%;
}

.card-title {
    border-bottom: 1px solid #EDF4FF;
}
</style>
