<template>
    <div v-if="computedShowOverallMetric">
        <Chip v-if="!showHeader" class="chip text-xs lg:text-sm mb-2 rounded-sm font-semibold"
            label="Overall metrics" />

        <div class="flex justify-between items-center">
            <div>
                <div class="text-xl lg:text-2xl font-bold">{{ overallMetricHeaderName }}</div>

                <div class="text-xs lg:text-sm text-zinc-800">Conversion Insights</div>
            </div>

            <div class="overall-conversion flex flex-col items-center"
                :style="overallData?.contributionDrop?.value >= 0 ? 'background-color: #E5F3F3' : 'background-color: #FFEDEF'">
                <div v-if="overallData" class="text-xl lg:text-2xl font-normal">
                    <AnimatedNumber :value="overallData?.contributionDrop?.value"
                        :type="overallData?.contributionDrop?.type"
                        :class="overallData?.contributionDrop?.valueClass" />
                </div>
                <span :style="showHeader ? 'font-size: 12px;' : 'font-size: 9px;'">Conv Delta</span>
            </div>
        </div>

        <div v-if="overallData" class="flex flex-wrap mt-4 gap-2">
            <div v-for="metric in overallData?.metrics?.filter(({ name }) => showMetrics.includes(name))"
                class="rounded-xl bg-white card" :style="computedCardStyles"
                :class="showMoreMetrics(metric?.name) ? 'cursor-pointer' : 'cursor-default'"
                @click="showMoreContribution(metric, overallData)">
                <div class="py-3 px-4 text-xs lg:text-sm flex justify-between items-center card_title">
                    <div class="font-semibold">{{ metric?.name || '' }}</div>
                    <i v-if="showMoreMetrics(metric?.name)" class="pi pi-chevron-right see-more" />
                </div>

                <div class="pt-2 pb-3 px-4 text-base lg:text-lg font-bold flex items-center">
                    <AnimatedNumber :value="metric.data?.value" :type="metric.data?.type"
                        :class="metric.data?.valueClass" />

                    <div v-if="metric.data?.diff" class="text-xs lg:text-sm font-light pl-1">
                        <AnimatedNumber :value="metric.data?.diff" :type="metric.data?.type"
                            :class="[metric.data?.diffClass, 'md:text-sm']" />
                    </div>

                    <span v-if="metric.data?.diff" :class="metric.data?.arrowClass"
                        style=" font-size: 8px; padding-left: 0.25rem;"></span>
                </div>
            </div>
        </div>
    </div>

    <div v-if="!hideAccordion" :class="containerClass">
        <div class="lg:flex lg:justify-between lg:items-center mb-4 lg:mb-4">
            <div class="mb-2 text-xl font-bold lg:text-2xl md:flex items-center w-full" :class="titleClass">
                {{ header.header }}
                <span v-if="header.subHeader" class="hidden md:inline mx-2">-</span>
                <div v-if="header.subHeader" class="font-light">{{ header.subHeader }}</div>
            </div>

            <div v-if="hasSearchFilter && !showHeader" class="mb-5 lg:w-1/3">
                <v-select v-model="filterValue" :options="filterOptions" :placeholder="filterPlaceholder"
                    class="bg-white" />
            </div>
        </div>

        <Accordion :activeIndex="activeIndex" @update:activeIndex="onAccordionChange"
            class="lg:flex lg:flex-wrap md:gap-4 lg:gap-6" multiple>
            <AccordionTab v-for="(cityData, index) in computedDataList" :key="cityData?.[nonMetricColumns?.key]"
                :headerClass="getBackgroundTrendClass(cityData?.contributionDrop?.value)"
                :contentClass="getBackgroundTrendClass(cityData?.contributionDrop?.value)">
                <template #header>
                    <div class="flex justify-between items-center w-full">
                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">{{
                            cityData?.[nonMetricColumns?.key] }}
                        </div>

                        <div v-if="cityData.contributionDrop" class="text-lg md:text-xl font-semibold text-black">
                            <AnimatedNumber :value="cityData?.contributionDrop?.value"
                                :type="cityData?.contributionDrop?.type"
                                :class="cityData?.contributionDrop?.valueClass" />
                        </div>
                    </div>
                </template>

                <!-- Content -->
                <div class="mb-3">
                    <div class="bg-white rounded-xl flex flex-wrap border-collapse">
                        <div v-for="metric in cityData?.metrics?.filter(({ name }) => showMetrics.includes(name))"
                            class="w-1/2 content-card"
                            :class="showMoreMetrics(metric?.name) ? 'cursor-pointer' : 'cursor-default'"
                            @click="showMoreContribution(metric, cityData)">
                            <div class="pt-3 px-2 lg:pt-4 lg:px-3 text-xs md:text-sm flex justify-between items-center">
                                <div>{{ getMetricName(metric?.name) }}</div>
                                <i v-if="showMoreMetrics(metric?.name)" class="pi pi-chevron-right see-more" />
                            </div>
                            <div class="pb-3 pt-1 px-2 lg:px-3 text-base md:text-xl font-bold flex items-center">
                                <AnimatedNumber :value="metric.data?.value" :type="metric.data?.type"
                                    :class="metric.data?.valueClass" />

                                <div v-if="metric.name !== 'Conversion Drop %' && metric.data?.diff"
                                    class="text-xs md:text-sm font-light pl-1">
                                    <AnimatedNumber :value="metric.data?.diff" :type="getDiffMetricType(metric)"
                                        :class="[metric.data?.diffClass, 'md:text-sm']" />
                                </div>

                                <span v-if="metric.name !== 'Conversion Drop %' && metric.data?.diff"
                                    :class="metric.data?.arrowClass"
                                    style="font-weight: 300; font-size: 8px; padding-left: 0.25rem;"></span>
                            </div>
                        </div>
                    </div>

                    <div v-if="!conversionType.includes('p-types')" class="flex justify-between pt-3 btn-container">
                        <Button label="PType Contribution" icon="pi pi-angle-right" iconPos="right"
                            @click="onSelectId(cityData?.[nonMetricColumns?.key], true)"
                            class="p-3 text-black text-left text-xs bg-white rounded-lg" plain text style="flex: 6;" />
                        <div style="flex: 1;" />
                        <Button :label="isCityLevelView ? 'City Details' : 'Store Details'" iconPos="right"
                            icon="pi pi-angle-right" @click="onSelectId(cityData?.[nonMetricColumns?.key])"
                            class="p-3 text-black text-left text-xs bg-white rounded-lg" plain text style="flex: 6;" />
                    </div>
                </div>
            </AccordionTab>
        </Accordion>

        <div v-if="!filterValue" class="flex justify-center items-center">
            <Button :label="showMore ? 'view less' : 'view more'" plain text @click="updateShowMore" iconPos="right"
                class="text-xs md:text-base p-0 mt-2 md:mt-4" :icon="showMore ? 'pi pi-angle-up' : 'pi pi-angle-down'"
                style="color: green;" />
        </div>
    </div>

    <AdditionalMetrics v-if="showSidebar" :showSidebar="showSidebar" @update:showSidebar="handleShowSidebar"
        :showHeader="showHeader" :additionalMetrics="additionalMetrics" />

    <Dialog v-model:visible="showItemInsights" modal :style="{ width: '70rem' }" :dismissableMask="true">
        <template #header>
            <div class="font-bold text-2xl flex items-center">
                {{ computedSelectedPtype }}
                <Chip class="font-extralight ml-2" style="border-radius: 0.5rem;">
                    Availability: <span class="font-normal ml-2">{{ availability }}%</span>
                </Chip>
            </div>
        </template>
        <ItemInsightsDetail :cityName="currentFilters?.cityName" :storeName="currentFilters?.storeName"
            :storeCode="currentFilters?.storeCode" :ptype="computedSelectedPtype"
            :isYesterday="currentFilters?.isYesterday" :selectedHour="computedSelectedHour"
            :availability="availability" />
    </Dialog>
</template>


<script>

import Chip from 'primevue/chip';
import AnimatedNumber from '../AnimatedNumber.vue';
import { insights, PanIndiaConstants, PTypeMetricNameMapping } from '../../constants';
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab'
import Button from 'primevue/button';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import AdditionalMetrics from './AdditionalMetrics.vue';
import Dialog from 'primevue/dialog';
import ItemInsightsDetail from './ItemInsightsDetail.vue';

export default {

    components: {
        Chip,
        AnimatedNumber,
        Accordion,
        AccordionTab,
        Button,
        'v-select': vSelect,
        AdditionalMetrics,
        Dialog,
        ItemInsightsDetail
    },

    props: {
        conversionType: {
            type: String,
            required: true,
        },
        data: {
            type: Array,
            required: true
        },
        nonMetricColumns: {
            type: Object,
            required: false
        },
        includesOverall: {
            type: Boolean,
            required: false,
            default: false
        },
        isCityLevelView: {  // isCityLevelView = where we show all cities...
            type: Boolean,
            required: false,
            default: true
        },
        showHeader: {
            type: Boolean,
            default: true,
            required: true
        },
        defaultRowCount: {  // for app view only when we are showing 10-10 of each
            type: Number,
            default: 10,
            required: false
        },
        reversedMetrics: {
            type: Array,
            required: false,
            default: [],
        },
        hideAccordion: {
            type: Boolean,
            default: false,
            required: false
        },
        onSelectId: {
            type: Function,
            required: false,
            default: () => { }
        },
        hasSearchFilter: {
            type: Boolean,
            required: false,
            default: false
        },
        showOverallMetric: {
            type: Boolean,
            default: true,
        },
        currentFilters: {
            type: Object,
            default: {}
        },
        selectedHour: {
            type: Number,
            default: -1,
        },
        isPanIndia: {
            type: Boolean,
            default: false
        },
        containerClass: {
            type: String,
            default: "mt-8 lg:mt-12"
        },
        titleClass: {
            type: String,
            default: ""
        }
    },

    emits: ["onPtypeSelect"],

    data() {
        return {
            activeIndex: null,
            showMore: false,
            filterValue: null,
            showSidebar: false,
            additionalMetrics: [],
            showItemInsights: false,
            selectedPtype: "",
            availability: null
        }
    },

    watch: {
        computedDataList(newVal, oldVal) {
            if (!this.showHeader && newVal.length === 1) {
                this.activeIndex = [0];
            }

            if (this.showHeader && newVal.length >= 3) {
                this.activeIndex = [0, 1, 2];
            }
        }
    },

    computed: {
        header() {
            const headerDict = {
                cities: ["City Contributions - Conv Drop"],
                stores: ["Store Contributions - Conv Drop"],
                "p-types": ["PType Search Conv Drop", "Contribution Based Sorting"],
                "p-types-spike": [" PType Search Spike", "Absolute spikes on Search DAU"],
                "p-types-conv": ["PType Search Conv Drop", "Absolute Deviation Sorting"]
            }
            return {
                header: headerDict[this.conversionType][0] || "",
                subHeader: headerDict[this.conversionType][1] || ""
            };
        },

        computedShowOverallMetric() {
            return this.includesOverall && this.showOverallMetric
        },

        overallMetricHeaderName() {
            if (this.conversionType === 'cities') {
                return this.overallData.City && this.overallData.City === 'Overall' ? "Pan India" :
                    this.overallData.City
            } else if (this.conversionType === 'stores') {
                return this.overallData.Store
            }
        },

        overallData() {
            if (this.includesOverall) {
                if (this.conversionType === 'cities') {
                    return (this.isPanIndia ? this.cities?.find((item) => item.City === 'Overall') : this.cities?.[0]) || {}
                }
                else if (this.conversionType === 'stores') return (this.cities?.[0] || {});
            }
            return;
        },

        cities() {
            if (!this.data || !Array.isArray(this.data)) {
                return [];
            }
            let cityData = this.data.map(key => {
                let keyObject = {};
                if (this.nonMetricColumns) {
                    for (let prop in this.nonMetricColumns) {
                        keyObject[this.nonMetricColumns?.[prop]] = key?.[prop];
                    }
                }
                Object.entries(key.metrics).forEach(([index, metric]) => {
                    let valueClass = this.getValueClass(metric?.curr?.value, metric?.name);
                    let diffInfo = this.calculateDiff(metric?.diffs, metric?.name);
                    keyObject[metric?.name] = {
                        value: metric?.curr?.value,
                        type: metric?.curr?.type,
                        valueClass: valueClass,
                        diff: diffInfo?.value,
                        diffClass: diffInfo?.class,
                        arrowClass: diffInfo?.arrowClass,
                    };
                });
                return keyObject;
            });

            let mutated = cityData.map((item) => {
                let metricsArr = [...Object.keys(item)?.map((metricKey) => {
                    if (this.isExcludedColumn(metricKey)) return;
                    if (metricKey === this.computedTabMetric) return;
                    return ({
                        name: metricKey,
                        data: item?.[metricKey]
                    })
                })].filter(Boolean);
                return ({
                    [this.nonMetricColumns?.key]: this.conversionType.includes('p-types') ? item?.[this.nonMetricColumns?.grain] : item?.[this.nonMetricColumns?.key],
                    metrics: metricsArr,
                    contributionDrop: item?.[this.computedTabMetric],
                })
            })

            return mutated;
        },

        citiesData() {
            if (this.includesOverall) {
                let cityToExclude = 'Overall';
                return this.cities?.filter((item) => item.City !== cityToExclude);
            } else {
                return this.cities;
            }
        },

        getBackgroundTrendClass() {
            return (value) => {
                return value < 0 ? 'negative-trend' : 'positive-trend';
            }
        },

        computedCardStyles() {
            if (!this.showHeader) return { flexBasis: "48%" };
            return { flex: 1 };
        },

        computedDataList() {
            if (this.filterValue) {
                return [this.citiesData.find((item) => item?.[this.nonMetricColumns?.key] === this.filterValue)]
            }
            if (this.showMore) return this.citiesData;
            else return this.citiesData?.slice(0, this.defaultRowCount);
        },

        filterOptions() {
            return this.citiesData?.map((item) => item?.[this.nonMetricColumns?.key])
        },

        filterPlaceholder() {
            const placeholderDict = {
                cities: "Search City",
                stores: "Search Store",
                "p-types": "Search P-Type",
                "p-types-spike": "Search P-Type",
                "p-types-conv": "Search P-Type"
            }
            return placeholderDict[this.conversionType] || "";
        },

        showMetrics() {
            if (this.conversionType.includes('p-types'))
                return ["Unique Impressions", "ATC %", "Search Conv %", "FE Availability", "Search Impressions", "FE Inventory", "Search Spike %", "Conversion Drop %"];
            else
                return ["Daily Active Users", "Overall Conv %", "ATC %", "C2Co %", "Demand Based Block %", "Unserviceable DAU %"];
        },

        computedCurrentFilters() {
            return this.currentFilters;
        },

        computedSelectedPtype() {
            return this.selectedPtype || "";
        },

        computedSelectedHour() {
            return this.selectedHour;
        },

        computedTabMetric() {
            switch (this.conversionType) {
                case 'p-types':
                    // return 'Search Conv %';
                    return 'Conversion Drop %';
                case 'p-types-spike':
                    return 'Search Spike %';
                case 'p-types-conv':
                    return 'Conversion Drop %';
                default:
                    return 'Conv Δ Contri';
            }
        }
    },


    methods: {
        calculateDiff(diffs, metricName) {
            if (!diffs || diffs.length === 0) {
                return {
                    value: '',
                    class: ['text-gray-500', 'text-xs'],
                    arrowClass: ['text-gray-500', 'pi', 'text-xs']
                };
            }
            let diffValues = diffs.map(diff => diff.change() !== '-' ? parseFloat(diff.change()) : '-');
            let totalDiff = diffValues.reduce((acc, curr) => acc + curr, 0);
            let avgDiff = totalDiff / diffValues.length;
            let arrowClass, valueClass;

            if (this.reversedMetrics.includes(metricName)) {
                arrowClass = avgDiff > 0 ? ['text-red-600', 'pi pi-arrow-up', 'text-xs'] : ['text-green-600', 'pi pi-arrow-down', 'text-xs'];
                valueClass = avgDiff > 0 ? ['text-red-600', 'text-xs'] : ['text-green-600', 'text-xs'];
            } else {
                arrowClass = avgDiff > 0 ? ['text-green-600', 'pi pi-arrow-up', 'text-xs'] : ['text-red-600', 'pi pi-arrow-down', 'text-xs'];
                valueClass = avgDiff > 0 ? ['text-green-600', 'text-xs'] : ['text-red-600', 'text-xs'];
            }

            return {
                value: parseFloat(avgDiff)?.toFixed(1),
                class: valueClass,
                arrowClass: arrowClass,
            };
        },

        getValueClass(value, metricName) {
            if (insights.DELTA_COLUMNS.includes(metricName)) {
                return value > 0 ? ['text-green-600'] : value < 0 ? ['text-red-600'] : [''];
            }
            return [''];
        },

        isExcludedColumn(colKey) {
            return this.nonMetricColumns && Object.values(this.nonMetricColumns).includes(colKey);
        },

        onAccordionChange(indexes) {
            this.activeIndex = indexes;
        },

        updateShowMore() {
            this.showMore = !this.showMore;
        },

        showMoreMetrics(metric) {
            if (this.conversionType.includes('p-types')) {
                const additionalChecks = this.selectedHour === -1;
                if (['FE Availability'].includes(metric) && additionalChecks) return true;
            } else {
                if (['ATC %', 'C2Co %'].includes(metric)) return true;
            }
            return false;
        },

        handleShowSidebar(value) {
            this.showSidebar = value;
        },

        showMoreContribution(metric, data) {
            if (!this.showMoreMetrics(metric?.name)) return;

            if (this.conversionType.includes('p-types')) {
                if (this.showHeader) {
                    this.selectedPtype = data?.[this.nonMetricColumns?.key]
                    this.showItemInsights = true;
                    this.availability = metric?.data?.value;
                }
                else this.$emit('onPtypeSelect', { data: data?.[this.nonMetricColumns?.key], availability: metric?.data?.value })
                return;
            }

            if (metric?.name === 'ATC %') {
                const additionalMetricsNames = ["Blocks Contribution to ATC", "DAU Contribution to ATC", "Category Mix", "Avail & Other Contribution to ATC"]
                this.additionalMetrics = {
                    overallMetric: data?.metrics?.find((metric) => metric.name === "ATC %"),
                    remainingMetrics: data?.metrics?.filter((metric) => additionalMetricsNames.includes(metric.name))
                        ?.sort((a, b) =>
                            additionalMetricsNames.findIndex(x => x === a.name) - additionalMetricsNames.findIndex(x => x === b.name))
                }
                this.showSidebar = !this.showSidebar;
            } else {
                const additionalMetricsNames = ["Blocks Contribution to C2Co", "DAU Contribution to C2Co", "Surge Contribution", "Avail & Other Contribution to C2Co"]
                this.additionalMetrics = {
                    overallMetric: data?.metrics?.find((metric) => metric.name === "C2Co %"),
                    remainingMetrics: data?.metrics?.filter((metric) => additionalMetricsNames.includes(metric.name))
                }
                this.showSidebar = !this.showSidebar;
            }
        },

        getMetricName(metricName = "") {
            if (this.conversionType.includes('p-types') && PTypeMetricNameMapping[metricName]) return PTypeMetricNameMapping[metricName];
            return metricName;
        },

        //* using this because there are new metric which have different curr (num) and diff (%) types
        getDiffMetricType(metric) {
            if (["Unique Impressions", "Search Impressions", "FE Inventory"].includes(metric.name)) return "percentage";
            return metric.data?.type;
        }
    },

    mounted() {
        if (this.cities?.length <= this.defaultRowCount) this.showMore = true;
    }
}
</script>

<style scoped>
.chip {
    color: #256FEF;
    background-color: #EDF4FF;
    padding: 2px 4px;
}

:deep(.p-chip) {
    border-radius: 4px;
    padding: 2px 4px;
}

.overall-conversion {
    padding: 0.375rem 0.5rem;
    border-radius: 0.75rem;
}

@media only screen and (min-width: 600px) {
    .overall-conversion {
        padding: 0.5rem 1rem;
    }
}

.card {
    margin-bottom: 0.5rem;
    border: 1px solid #EDF4FF;
}

.card_title {
    border-bottom: 1px solid #EDF4FF;
}

.see-more {
    font-size: 0.5rem;
}

.content-card {

    &:nth-of-type(1),
    &:nth-of-type(2) {
        border-bottom: 1px solid #F4F6FB;
    }

    &:nth-of-type(3),
    &:nth-of-type(4) {
        border-top: none !important;
    }

    &:nth-of-type(odd) {
        border-right: 1px solid #F4F6FB;
    }

    &:not(:nth-of-type(-n+2)) {
        border-top: 1px solid #F4F6FB;
    }
}

:deep(.p-card-body) {
    padding: 0 !important;
}

:deep(.p-accordion-tab) {
    width: 32%;
    border: 0;
}

@media only screen and (max-width: 600px) {
    :deep(.p-accordion-tab) {
        width: 100%;
        border: 2px solid #fff;
        position: relative;
        margin-top: -0.5rem;
        border-radius: 0.5rem;
        z-index: 1;
    }
}

:deep(.p-accordion-toggle-icon) {
    display: none !important;
}

:deep(.p-accordion-header-link) {
    height: inherit;
    padding: 0.75rem !important;
    height: 58px;
}

:deep(.positive-trend .p-accordion-header-link) {
    background-color: #E5F3F3 !important;
}

:deep(.negative-trend .p-accordion-header-link) {
    background-color: #FFEDEF !important;
}

:deep(.p-accordion-tab-active .positive-trend .p-accordion-header-link) {
    background-color: #D2F0F0 !important;
}

:deep(.p-accordion-tab-active .negative-trend .p-accordion-header-link) {
    background-color: #FFDBE0 !important;
}

/* not a good practice */
:deep(.p-toggleable-content) {
    margin-top: -0.75rem !important;
}

:deep(.p-accordion-content) {
    padding: 0.75rem !important;
}

@media only screen and (min-width: 800px) {
    :deep(.p-accordion-content) {
        border-bottom-left-radius: 8px !important;
        border-bottom-right-radius: 8px !important;
    }
}

:deep(.positive-trend .p-accordion-content) {
    background-color: #D2F0F0 !important;

    .p-button {
        background-color: #12A2AB;
        color: #FFFFFF;
    }
}

:deep(.negative-trend .p-accordion-content) {
    background-color: #FFDBE0 !important;

    .p-button {
        background-color: #EF4F5F;
        color: #FFFFFF;
    }
}

:deep(.btn-container .p-button-label) {
    font-weight: 400;
}

:deep(.btn-container .pi-angle-right) {
    font-size: 0.75rem !important;
}

:deep(.vs__dropdown-toggle) {
    border-radius: 8px;
    font-weight: 200;
    border-color: #e2e8f0;
    padding: 0.125rem 0.5rem;
    color: #e2e8f0;
}
</style>
