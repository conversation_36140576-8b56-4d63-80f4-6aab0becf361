<template>
    <div class="px-1 py-2 container flex justify-between items-center">
        <div class="flex-[4] flex overflow-auto gap-4 pb-4">
            <Button v-if="computedFilters.length > 0" label="Filters" outlined severity="contrast"
                class="bg-white rounded-lg py-2 px-4 btn"
                :class="totalNumberOfAppliedFilters ? 'selected-filter' : null" :badge="totalNumberOfAppliedFilters"
                icon="pi pi-filter" @click="showFilters(computedFilters[0]?.key)" />

            <Button v-if="showYesterdayFilter" label="Yesterday" outlined severity="contrast"
                class="bg-white rounded-lg py-2 px-4 btn"
                :class="isFilterApplied('yesterday') ? 'selected-filter' : null" iconPos="right"
                :icon="isYesterday ? 'pi pi-times' : ''" @click="handleToggle" />

            <Button v-for="filter in computedFilters" :key="filter.key" :label="filter.label" outlined
                severity="contrast" class="bg-white rounded-lg py-2 px-4 btn"
                :class="isFilterApplied(filter.key) ? 'selected-filter' : null" iconPos="right" icon="pi pi-caret-down"
                @click="showFilters(filter.key)" />
        </div>
    </div>

    <Sidebar v-model:visible="visibleBottom" position="bottom" :blockScroll="true" :showCloseIcon="false"
        style="height: 64%; border-top-left-radius: 16px; border-top-right-radius: 16px;">
        <template #header>
            <div class="w-full flex justify-between items-center">
                <div class="font-bold text-lg flex items-center">
                    Filters
                    <Button text class="pi pi-refresh text-black text-xs ml-4 p-0" @click="refreshFilters" />
                </div>

                <div class="flex items-center">
                    <Button label="Apply" plain text class="text-xs px-2 py-1 btn" @click="handleApply" />
                </div>
            </div>
        </template>

        <!-- content -->
        <div>
            <TabView v-model:activeIndex="activeTab">
                <TabPanel v-if="showCityFilter" key="city" header="City">
                    <Listbox id="citySelect" :reduce="computedCityList => computedCityList.code"
                        :model-value="selectedCityObj" :options="computedCityList" filter optionLabel="name"
                        class="w-full" @update:model-value="updateSelectedCity" :clearable="false"
                        placeholder="click to select city" />
                </TabPanel>

                <TabPanel v-if="showStoreFilter" key="store" header="Store">
                    <Listbox id="storeSelect" :model-value="insightsPage" :options="computedStoreList" filter
                        optionLabel="merchant_name_id" class="w-full" @update:model-value="updateInsightsPage"
                        :clearable="false" placeholder="click to select store" />
                </TabPanel>

                <TabPanel v-if="showHourFilter" key="hour" header="Hour">
                    <HourList id="hourSelect" :model-value="selectedHourObj" @update:model-value="updateSelectedHour"
                        :startHour="5" :isYesterday="isYesterday" :styles="['flex-1']" :isList="true" />
                </TabPanel>
            </TabView>
        </div>
    </Sidebar>

</template>

<script>
import Button from 'primevue/button';
import Sidebar from 'primevue/sidebar';
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';
import HourList from '../HourList.vue';
import VueToggle from 'vue-toggle-component';
import { PanIndiaConstants } from '../../constants';
import Listbox from 'primevue/listbox';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import isEmpty from 'lodash.isempty';

export default {
    components: {
        Button,
        Sidebar,
        'v-select': vSelect,
        HourList,
        VueToggle,
        Listbox,
        TabView,
        TabPanel
    },

    props: {
        selectedCity: {
            type: String,
            default: PanIndiaConstants.PAN_INDIA.code
        },
        insightsPage: {
            type: Object,
            default: {}
        },
        selectedHour: {
            type: Number,
            default: -1
        },
        computedCityList: {
            type: Array,
            default: []
        },
        computedStoreList: {
            type: Array,
            default: []
        },
        isYesterday: {
            type: Boolean,
            default: false,
        },
        filters: {
            type: Array,
            required: false,
            default: ['isYesterday', 'city', 'store', 'hour']
        },
        appliedFilters: {
            type: Object,
            required: true
        }
    },

    data() {
        return {
            visibleBottom: false,
            activeTab: 0,
            selectedCityObj: null,
            selectedHourObj: null,
            insightsPageObj: null,
        }
    },

    computed: {
        yesterdayOptions() {
            return [
                { name: 'Today', value: false },
                { name: 'Yesterday', value: true },
            ]
        },

        showYesterdayFilter() {
            return this.filters.includes('isYesterday');
        },

        showCityFilter() {
            return this.filters.includes('city');
        },

        showStoreFilter() {
            return this.filters.includes('store');
        },

        showHourFilter() {
            return this.filters.includes('hour');
        },

        computedSelectedCity() {
            return this.selectedCity;
        },

        computedSelectedHour() {
            return this.selectedHour;
        },

        hourOptions() {
            const hours = this.isToday ? this.currentHour + 1 : 24;
            let options = [];
            for (let i = this.startHour; i < hours; i++) {
                options.push({ name: i.toString() + " : 00", code: i });
            }
            options.unshift({ name: 'Full Day', code: -1 });
            return options;
        },

        computedFilters() {
            const allFilters = [
                { key: "city", label: "City" },
                { key: "store", label: "Store" },
                { key: "hour", label: "Hour" },
            ]
            return allFilters?.filter((filter) => this.filters.includes(filter.key))
        },

        appliedFilterItemMapping() {
            return {
                "city": this.appliedFilters?.city,
                "store": this.appliedFilters?.store,
                "yesterday": this.appliedFilters?.yesterday,
                "hour": this.appliedFilters?.hour,
            }
        },

        totalNumberOfAppliedFilters() {
            const number = Object.keys(this.appliedFilterItemMapping)
                ?.filter((filterItem) => this.isFilterApplied(filterItem))
                ?.filter(Boolean)?.length
            if (number) return `(${number})`
            return null
        },
    },

    emits: ['update:isYesterday', 'toggle', 'update:selectedCity', 'update:insightsPage', 'update:selectedHour', 'loadMetrics', 'refreshList'],

    methods: {
        showFilters(val) {
            const visibleFilters = ["city", "store", "hour"]
            const tabIndexes = this.filters?.filter((filter) => visibleFilters.includes(filter))
                ?.sort((a, b) =>
                    visibleFilters.findIndex((x) => x === a) - visibleFilters.findIndex((x) => x === b))

            const idx = tabIndexes.findIndex((filterItem) => filterItem === val) ?? 0

            this.visibleBottom = !this.visibleBottom;
            this.activeTab = idx;
        },

        handleToggle() {
            this.$emit("update:isYesterday", !this.isYesterday);
            this.$emit("toggle", !this.isYesterday);
        },

        updateSelectedCity(value) {
            this.selectedCityObj = value;
            this.$emit('update:selectedCity', value.code);
        },

        updateInsightsPage(value) {
            this.$emit('update:insightsPage', value);
        },

        updateSelectedHour(value) {
            this.selectedHourObj = value;
            this.$emit('update:selectedHour', value.code);
        },

        handleApply() {
            this.$emit('loadMetrics');
            this.visibleBottom = !this.visibleBottom;
        },

        refreshFilters() {
            this.$emit('refreshList');
            this.visibleBottom = !this.visibleBottom;
        },

        isFilterApplied(filterItem) {
            if (filterItem === "city") {
                return this.appliedFilterItemMapping?.city && this.appliedFilterItemMapping.city !== 'pan-india'
            } else if (filterItem === "store") {
                return !isEmpty(this.appliedFilterItemMapping?.store?.merchant_name_id) && this.appliedFilterItemMapping.store.merchant_name_id !== 'Overall'
            } else if (filterItem === "yesterday") {
                return this.appliedFilterItemMapping?.yesterday
            } else { // hour
                return this.appliedFilterItemMapping?.hour && this.appliedFilterItemMapping?.hour !== -1 || this.appliedFilterItemMapping?.hour === 0
            }
        },
    },

    watch: {
        computedSelectedCity(newVal) {
            this.selectedCityObj = this.computedCityList.find((city) => city.code === newVal);
        },

        computedSelectedHour(newVal, oldVal) {
            this.selectedHourObj = this.hourOptions.find((hour) => hour.code === newVal)
        }
    }
}
</script>

<style scoped>
.btn {
    flex: 1;
    min-width: max-content;
    border: 1px solid #4f4f4f;
}

:deep(.btn .p-button-label) {
    font-weight: 300;
}

:deep(.btn .p-button-icon) {
    font-size: 0.75rem;
}

.selected-filter {
    border: 1px solid #328616;
    background-color: #F6FFF8;
}

:deep(.selected-filter .p-badge) {
    color: #000;
    font-size: 1rem;
    font-weight: 300;
    background-color: #F6FFF8;
    padding: 0;
}

:deep(.p-sidebar) {
    border-top-left-radius: 16px !important;
    border-top-right-radius: 16px !important;
}

:deep(.p-sidebar .p-sidebar-header) {
    border-bottom: 1px solid #efefef;
}

:deep(.p-sidebar-content) {
    overflow: hidden !important;
    padding-bottom: 0;
}

:deep(.p-tabview) {
    display: flex !important;
}


:deep(.p-tabview .p-tabview-nav li .p-tabview-nav-link) {
    border-radius: 0 !important;
    border: none;
    padding: 0.75rem 3rem 0.75rem 0;
    font-size: 0.75rem;
    font-weight: 300;
}

:deep(.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link) {
    border-right: 2px solid #10b981;
    font-weight: 600;
}

:deep(.p-tabview-nav) {
    flex-direction: column;
}

:deep(.p-tabview-ink-bar) {
    display: none;
}

:deep(.p-tabview-panels) {
    width: 100%;
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 0;
    border-left: 1px solid #efefef;
    border-radius: 0;
    height: 38vh;
}

:deep(.p-listbox) {
    border: none;
    box-shadow: none;
}

:deep(.p-listbox-list-wrapper) {
    height: 38vh;
    overflow-y: auto;
    font-size: 0.75rem;
}

:deep(.vs__dropdown-toggle) {
    border-radius: 12px;
    font-size: 0.75rem;
    box-shadow: 0px 2px 4px 0px #1C1C1C0D;
}

:deep(.vs__dropdown-menu) {
    font-size: 0.75rem !important;
}
</style>