<template>
    <MetricCollapse v-for="(metricData, accIdx) in FLATTENED_INSTORE_HOURLY_METRICS" :key="metricData.metricKey"
        :isDefaultOpen="metricData.isDefaultOpen" :metricKey="metricData.metricKey" :apiKey="metricData.apiKey"
        :icon="metricData.icon" :loading="loading?.[metricData?.apiKey] || false"
        :error="error?.[metricData?.apiKey] || false" :data="data[metricData.apiKey]" :fetchData="fetchData"
        :metricData="metricData" :accIdx="accIdx" :openAccordions="openAccordions"
        :cancelPreviousRequests="handleCancelPreviousRequests" :triggerFetchApi="triggerFetchApi" :computedDate="computedDate"
        @updateOpenAccordion="$emit('updateOpenAccordion', $event)"
        @updateClosedAccordion="$emit('updateClosedAccordion', $event)" />
</template>

<script setup>
import { ref, computed, reactive, defineExpose } from 'vue'
import axios from "axios"
import MetricCollapse from "../Home/HourlyMetricsV2/MetricCollapse.vue"
import { FLATTENED_INSTORE_HOURLY_METRICS } from '../../constants/sonar/instore.js'
import { api, apiV2 } from '../../api/index.js'
import { INSTORE_ORDER_METRIC_LIST, INSTORE_ACTIVE_HOURS_METRICS_LIST } from '../../utils/metrics.js'
import { formatDateToString, isDateToday, cancelPreviousRequests } from '../../utils/utils.js'

// Props
const props = defineProps({
    cityCode: {
        type: String,
        required: true
    },
    storeCode: {
        type: String,
        default: null
    },
    storeType: {
        type: String,
        default: null
    },
    selectedDate: {
        type: Date,
        required: true
    },
    showHeader: {
        type: Boolean,
        default: true
    },
    openAccordions: {
        type: Array,
        default: []
    }
})

// Emits
const emit = defineEmits(['updateOpenAccordion', 'updateClosedAccordion'])

// Reactive data
const cancelTokens = ref({})

// COMPUTED
const computedDate = computed(() => {
    return formatDateToString(props.selectedDate)
})

const computedIsNotToday = computed(() => {
    return !isDateToday(props.selectedDate)
})

const triggerFetchApi = computed(() => {
    return `${props.cityCode}-${props.storeCode}-${props.selectedDate}-${props.storeType}`
})

// Methods for instore-specific API calls
const getMetricApiAndParams = (key, cancelToken) => {
    switch (key) {
        case "instore_order_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchHourlyMetrics, params:  [INSTORE_ORDER_METRIC_LIST, computedDate.value, props.cityCode, '', props.storeCode, props.storeType, cancelToken] }
                : { apiFn: api.fetchHourlyMetrics, params:  [INSTORE_ORDER_METRIC_LIST, false, props.cityCode, '', props.storeCode, props.storeType, cancelToken] };

        case "instore_active_time_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchInstoreHourlyMetrics, params: [INSTORE_ACTIVE_HOURS_METRICS_LIST, computedDate.value, props.cityCode, props.storeCode, props.storeType, cancelToken] }
                : { apiFn: api.fetchInstoreHourlyMetrics, params: [INSTORE_ACTIVE_HOURS_METRICS_LIST, false, props.cityCode, props.storeCode, props.storeType, cancelToken] };

        case "instore_complaints_metrics":
            return computedIsNotToday.value
                ? { apiFn: apiV2.fetchComplaintsHourlyMetrics, params: [["total_complaints"], computedDate.value, props.cityCode, props.storeCode, props.storeType, cancelToken] }
                : { apiFn: api.fetchComplaintsHourlyMetrics, params: [["total_complaints"], false, props.cityCode, props.storeCode, props.storeType, cancelToken] };

        default:
            return { apiFn: "", params: [] };
    }
}


const handleCancelPreviousRequests = (apiKey) => {
    cancelPreviousRequests({ cancelTokens: cancelTokens.value, apiKey: apiKey , useApiKey: true})
}

function useApiFetcher() {
    const loading = reactive({})
    const error = reactive({})
    const data = reactive({})
    const activeRequests = reactive({})

    async function fetchData(apiKey) {
        if (!apiKey) return

        if (!(apiKey in activeRequests)) {
            activeRequests[apiKey] = 0
        }

        activeRequests[apiKey]++

        if (activeRequests[apiKey] === 1) {
            loading[apiKey] = true
            error[apiKey] = false
        }

        try {
            const metricsCancelTokenSource = axios.CancelToken.source()

            handleCancelPreviousRequests(apiKey)
            cancelTokens.value[apiKey] = [metricsCancelTokenSource]

            if(computedIsNotToday.value && !computedDate.value) {
                error[apiKey] = true
                throw new Error("Invalid date");
            }

            const { apiFn, params } = getMetricApiAndParams(apiKey, metricsCancelTokenSource.token)

            if (!apiFn) return

            const response = await apiFn?.(...params)

            if (response?.data?.metrics) {
                data[apiKey] = response.data.metrics
            }
        } catch (err) {
            if (!axios.isCancel(err)) {
                error[apiKey] = true
                console.error(`Error fetching metrics for ${apiKey}:`, err)
            }
        } finally {
            activeRequests[apiKey]--

            if (activeRequests[apiKey] <= 0) {
                loading[apiKey] = false
                activeRequests[apiKey] = 0
            }
        }
    }

    return {
        loading,
        error,
        data,
        fetchData
    }
}

// Use the composable
const { loading, error, data, fetchData } = useApiFetcher()

// Expose everything needed for template access
defineExpose({
    cancelTokens,
    loading,
    error,
    data,
    triggerFetchApi,
    FLATTENED_INSTORE_HOURLY_METRICS,
    computedIsNotToday,
    computedDate,
    fetchData,
    cancelPreviousRequests: handleCancelPreviousRequests,
    getMetricApiAndParams,
    MetricCollapse
})
</script>