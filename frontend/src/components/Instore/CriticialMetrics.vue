<template>
    <div>
        <!-- City Critical Metrics (Pan-India) -->
        <div v-if="cityCode === 'pan-india'" class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <span class="text-lg font-bold text-blue-900">TOP IMPACTED CITIES</span>
            <TabView class="mt-2 md:mt-4">
                <TabPanel header="PPI > 20s">
                    <Table 
                        :data="computedCityCriticalPpiMetrics" 
                        :nonMetricColumns="{ key: 'City' }"
                        :reversedMetrics="['Picking Time Per Item']" 
                    />
                </TabPanel>
                <TabPanel header="Fill Rate % < 99.5%">
                    <Table 
                        :data="computedCityCriticalRateMetrics" 
                        :nonMetricColumns="{ key: 'City' }" 
                    />
                </TabPanel>
                <TabPanel header="Direct Handover %">
                    <Table 
                        :data="computedCityCriticalHandoverMetrics" 
                        :nonMetricColumns="{ key: 'City' }" 
                    />
                </TabPanel>
            </TabView>
        </div>

        <!-- Store Critical Metrics (Overall) -->
        <div v-if="storeCode === 'Overall'" class="max-w-7xl mx-auto px-2 py-1 lg:px-8 lg:py-4">
            <span class="text-lg font-bold text-blue-900">TOP IMPACTED STORES</span>
            <TabView class="mt-2 md:mt-4">
                <TabPanel header="PPI > 20s">
                    <Table 
                        :data="computedStoreCriticalPpiMetrics" 
                        :nonMetricColumns="{ key: 'Store' }"
                        :reversedMetrics="['Picking Time Per Item']" 
                    />
                </TabPanel>
                <TabPanel header="Fill Rate % < 99.5%">
                    <Table 
                        :data="computedStoreCriticalRateMetrics" 
                        :nonMetricColumns="{ key: 'Store' }" 
                    />
                </TabPanel>
                <TabPanel header="Direct Handover %">
                    <Table 
                        :data="computedStoreCriticalHandoverMetrics" 
                        :nonMetricColumns="{ key: 'Store' }" 
                    />
                </TabPanel>
            </TabView>
        </div>
    </div>
</template>

<script>
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import Table from '../Table.vue';
import { api, apiV2 } from "../../api";
import axios from "axios";
import { sortedAllMetrics } from "../../utils/metrics";
import { formatDateToString, formatStoreName, cancelPreviousRequests } from "../../utils/utils";
import { MetricChange } from '../../interfaces';
import { metricsForStyleReversal } from "../../utils/metrics";

export default {
    name: 'CriticalMetrics',
    components: {
        TabView,
        TabPanel,
        Table
    },
    props: {
        cityCode: {
            type: String,
            required: true
        },
        storeCode: {
            type: String,
            required: false,
            default: null
        },
        cityName: {
            type: String,
            required: false,
            default: ''
        },
        storeType: {
            type: String,
            required: false,
            default: null
        },
        selectedDate: {
            type: String,
            required: true
        },
        isNotToday: {
            type: Boolean,
            required: true
        },
    },
    data() {
        return {
            cancelTokens: [],
            storeCriticalPpiMetrics: [],
            storeCriticalRateMetrics: [],
            storeCriticalHandoverMetrics: [],
            cityCriticalPpiMetrics: [],
            cityCriticalRateMetrics: [],
            cityCriticalHandoverMetrics: []
        }
    },
    computed: {
        triggerFetchApi() {
            return `${this.cityCode}-${this.storeCode}-${this.cityName}-${this.storeType}-${this.selectedDate}`
        },
        computedStoreCriticalPpiMetrics() {
        return this.processAndSortMetrics(this.storeCriticalPpiMetrics);
        },
        computedStoreCriticalRateMetrics() {
            return this.processAndSortMetrics(this.storeCriticalRateMetrics, true);
        },
        computedStoreCriticalHandoverMetrics() {
            return this.processAndSortMetrics(this.storeCriticalHandoverMetrics, true);
        },
        computedCityCriticalPpiMetrics() {
            return this.processAndSortMetrics(this.cityCriticalPpiMetrics);
        },
        computedCityCriticalRateMetrics() {
            return this.processAndSortMetrics(this.cityCriticalRateMetrics, true);
        },
        computedCityCriticalHandoverMetrics() {
            return this.processAndSortMetrics(this.cityCriticalHandoverMetrics, true);
        },
        computedDate() {
            return formatDateToString(this.selectedDate);
        }
    },
    methods: {
        filterMetricsWithNoData(metrics) {
            return metrics.filter(metric => metric.data.length > 0);
        },
        processCriticalMetricsResponse(response) {
            if (!response) return [];
            return response.data.map(item => {
                item.metric = this.filterMetricsWithNoData(item.metric);
                item.metric = sortedAllMetrics(item.metric, [], true, 2, this.computedDate);
                item.metrics = item.metric;
                return item;
            });
        },

            
        calculatedMetrics(insightsMetrics) {
            if (!insightsMetrics || !Array.isArray(insightsMetrics)) {
                return [];
            }
            let res = insightsMetrics.map(cityMetric => {
                let filteredMetrics = cityMetric.metric.map(metric => {
                    let { data, name } = metric;
                    let curr = data[data.length - 1];
                    let diffs = data.slice(0, -1).map((prev) => new MetricChange(curr, prev));
                    return {
                        name,
                        reverseStyle: metricsForStyleReversal.includes(name),
                        curr,
                        diffs,
                    };
                }).filter(metric => metric.curr && metric.curr.value !== '-');
                let { metric, ...metricObject } = cityMetric;
                // to remove prefix from store names
                metricObject.key = formatStoreName(metricObject.key)
                return {
                    ...metricObject,
                    metrics: filteredMetrics
                };
            }).filter(cityMetric => cityMetric.metrics.length > 0);
            return res;
        },

        processAndSortMetrics(metrics, ascending = false) {
            if (!metrics || !Array.isArray(metrics)) {
                return [];
            }
            return this.calculatedMetrics(metrics).sort((a, b) => {
                const aValue = a.metrics[0].curr.value;
                const bValue = b.metrics[0].curr.value;
                return ascending ? (aValue - bValue) : (bValue - aValue);
            });
        },

        async fetchCriticalMetrics() {
            cancelPreviousRequests({ cancelTokens: this.cancelTokens });

            try {
                const StoreCriticalPpiMetricsCancelTokenSource = axios.CancelToken.source();
                const StoreCriticalFillRateMetricsCancelTokenSource = axios.CancelToken.source();
                const StoreCriticalHandoverMetricsCancelTokenSource = axios.CancelToken.source();
                const CityCriticalPpiMetricsCancelTokenSource = axios.CancelToken.source();
                const CityCriticalFillRateMetricsCancelTokenSource = axios.CancelToken.source();
                const CityCriticalHandoverMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [
                    StoreCriticalPpiMetricsCancelTokenSource, 
                    StoreCriticalFillRateMetricsCancelTokenSource, 
                    StoreCriticalHandoverMetricsCancelTokenSource, 
                    CityCriticalPpiMetricsCancelTokenSource, 
                    CityCriticalFillRateMetricsCancelTokenSource, 
                    CityCriticalHandoverMetricsCancelTokenSource
                ];

                // Store critical metrics (only when store is 'Overall')
                let StoreCriticalPpiMetrics = (this.storeCode === 'Overall') ?
                    (this.isNotToday
                        ? apiV2.fetchCityWiseMetrics(["ppi_in_seconds"], this.selectedDate, false, true, true, this.cityName, this.storeType, StoreCriticalPpiMetricsCancelTokenSource.token)
                        : api.fetchCityWiseMetrics(["ppi_in_seconds"], false, false, true, true, this.cityName, this.storeType, StoreCriticalPpiMetricsCancelTokenSource.token))
                    : Promise.resolve(null);

                let StoreCriticalFillRateMetrics = (this.storeCode === 'Overall') ?
                    (this.isNotToday
                        ? apiV2.fetchCityWiseMetrics(["sm_fill_rate"], this.selectedDate, false, true, true, this.cityName, this.storeType, StoreCriticalFillRateMetricsCancelTokenSource.token)
                        : api.fetchCityWiseMetrics(["sm_fill_rate"], false, false, true, true, this.cityName, this.storeType, StoreCriticalFillRateMetricsCancelTokenSource.token))
                    : Promise.resolve(null);

                let StoreCriticalHandoverMetrics = (this.storeCode === 'Overall') ?
                    (this.isNotToday
                        ? apiV2.fetchCityWiseMetrics(["billed_to_assigned"], this.selectedDate, false, true, true, this.cityName, this.storeType, StoreCriticalHandoverMetricsCancelTokenSource.token)
                        : api.fetchCityWiseMetrics(["billed_to_assigned"], false, false, true, true, this.cityName, this.storeType, StoreCriticalHandoverMetricsCancelTokenSource.token))
                    : Promise.resolve(null);

                // City critical metrics (only when city is 'pan-india')
                let CityCriticalPpiMetrics = (this.cityCode === 'pan-india') ?
                    (this.isNotToday
                        ? apiV2.fetchCityWiseMetrics(["ppi_in_seconds"], this.selectedDate, true, false, true, '', this.storeType, CityCriticalPpiMetricsCancelTokenSource.token)
                        : api.fetchCityWiseMetrics(["ppi_in_seconds"], false, true, false, true, '', this.storeType, CityCriticalPpiMetricsCancelTokenSource.token))
                    : Promise.resolve(null);

                let CityCriticalFillRateMetrics = (this.cityCode === 'pan-india') ?
                    (this.isNotToday
                        ? apiV2.fetchCityWiseMetrics(["sm_fill_rate"], this.selectedDate, true, false, true, '', this.storeType, CityCriticalFillRateMetricsCancelTokenSource.token)
                        : api.fetchCityWiseMetrics(["sm_fill_rate"], false, true, false, true, '', this.storeType, CityCriticalFillRateMetricsCancelTokenSource.token))
                    : Promise.resolve(null);

                let CityCriticalHandoverMetrics = (this.cityCode === 'pan-india') ?
                    (this.isNotToday
                        ? apiV2.fetchCityWiseMetrics(["billed_to_assigned"], this.selectedDate, true, false, true, '', this.storeType, CityCriticalHandoverMetricsCancelTokenSource.token)
                        : api.fetchCityWiseMetrics(["billed_to_assigned"], false, true, false, true, '', this.storeType, CityCriticalHandoverMetricsCancelTokenSource.token))
                    : Promise.resolve(null);

                let [
                    StoreCriticalPpiMetricsResponse,
                    StoreCriticalFillRateMetricsResponse,
                    StoreCriticalHandoverMetricsResponse,
                    CityCriticalPpiMetricsResponse,
                    CityCriticalFillRateMetricsResponse,
                    CityCriticalHandoverMetricsResponse
                ] = await Promise.all([
                    StoreCriticalPpiMetrics,
                    StoreCriticalFillRateMetrics,
                    StoreCriticalHandoverMetrics,
                    CityCriticalPpiMetrics,
                    CityCriticalFillRateMetrics,
                    CityCriticalHandoverMetrics
                ]);
                this.storeCriticalPpiMetrics = this.processCriticalMetricsResponse(StoreCriticalPpiMetricsResponse);
                this.storeCriticalRateMetrics = this.processCriticalMetricsResponse(StoreCriticalFillRateMetricsResponse);
                this.storeCriticalHandoverMetrics = this.processCriticalMetricsResponse(StoreCriticalHandoverMetricsResponse);
                this.cityCriticalPpiMetrics = this.processCriticalMetricsResponse(CityCriticalPpiMetricsResponse);
                this.cityCriticalRateMetrics = this.processCriticalMetricsResponse(CityCriticalFillRateMetricsResponse);
                this.cityCriticalHandoverMetrics = this.processCriticalMetricsResponse(CityCriticalHandoverMetricsResponse);

            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    console.error("Error fetching critical metrics:", error);
                }
            }
        },

    },
    watch: {
        triggerFetchApi() {
            cancelPreviousRequests({ cancelTokens: this.cancelTokens });
            this.fetchCriticalMetrics();
        }
    },
    mounted() {
        this.fetchCriticalMetrics();
    },

    beforeUnmount() {
        cancelPreviousRequests({ cancelTokens: this.cancelTokens });
    }
}
</script>