<template>
  <div :class="class">
    <div :class="[titleClass, 'flex items-center justify-between w-full']">
      <span>{{ title }}</span>
      <span class="text-xs font-extralight">{{ computedSubTitle }}</span>
    </div>

    <v-select @search:focus="handleFocus" :model-value="modelValue" :options="computedOptions"
      @search="debouncedFetchOptions" :filterable="true" :loading="isLoading" :class="selectClass" :disabled="disabled"
      clearable :multiple="multiple" :close-on-select="!multiple" @update:model-value="updateModelValue($event)" />
  </div>
</template>

<script>
import vSelect from "vue-select";
import debounce from "lodash.debounce";
import { api, bistroApi, apiV2 } from "../api/index.js";
import isEmpty from 'lodash.isempty';
import axios from 'axios';

export default {
  components: {
    vSelect,
  },

  props: {
    title: {
      type: String,
      default: ""
    },
    modelValue: {
      type: String,
      required: true,
    },
    apiFunc: {
      type: String,
      required: true,
      default: "fetchProductFilters",
    },
    apiType: {
      type: String,
      default: "api"
    },
    searchKey: {
      type: String,
      required: true,
    },
    responseKey: {
      type: String,
      default: "filtered_values"
    },
    valueKey: {
      type: String,
      default: ""
    },
    class: {
      type: String,
      default: "flex flex-col items-start max-w-md"
    },
    selectClass: {
      type: String,
      default: "bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-2 text-center"
    },
    titleClass: {
      type: String,
      default: "",
    },
    debounceTimer: {
      type: Number,
      default: 400,
    },
    disabled: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: {}
    },
    minSearchLength: {
      type: Number,
      default: 2
    },
    onlyInitialCall: { // only fetches the list on first focus and doesn't fetch/hit api again on searching a value (searches from already provided list)
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    showHeader: {
      type: Boolean,
      default: false
    },
    isSorted: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      selected: null,
      options: [],
      isLoading: false,
      prevParams: {},
      timer: 0,
      cancelTokens: []
    };
  },

  created() {
    this.debouncedFetchOptions = debounce(this.fetchOptions, this.computedTimer);
  },

  methods: {
    cancelPreviousRequests() {
      this.cancelTokens.forEach((source) => {
        source.cancel('Operation canceled due to new request.');
      });
      this.cancelTokens = []; // Clear the tokens
    },

    async fetchOptions(search) {
      if (this.onlyInitialCall) {
        if (!isEmpty(this.prevParams) && JSON.stringify(this.prevParams) === JSON.stringify({ ...this.params })) return;
      } else {
        if (!isEmpty(this.prevParams) && JSON.stringify(this.prevParams) === JSON.stringify({ ...this.params, [this.searchKey]: search, search_filter: this.searchKey })) return;
      }

      if (search?.length && this.minSearchLength && search?.length < this.minSearchLength) {
        /* 
        call again if values are cleared for the select
        retain the older options and not clear them 
        this.options = [];
        */
        return;
      }

      try {
        this.isLoading = true;

        // Cancel any previous requests before making a new one
        this.cancelPreviousRequests();

        // Create a new cancel token source
        const cancelTokenSource = axios.CancelToken.source();
        this.cancelTokens.push(cancelTokenSource);

        await Promise.all([
          this.computedApiType?.[this.apiFunc]({ ...this.params, [this.searchKey]: search, search_filter: this.searchKey, cancelToken: cancelTokenSource.token })
            .then((response) => {
              let options = [];
              if (this.valueKey) {
                options = response.data[this.responseKey]?.map(item => item?.[this.valueKey])
              } else {
                options = response.data[this.responseKey]
              }
              this.options = options;

              if (this.onlyInitialCall) {
                this.prevParams = { ...this.params };
              } else {
                this.prevParams = { ...this.params, [this.searchKey]: search, search_filter: this.searchKey };
              }
            })
        ]);
      } catch (e) {
        console.error("Error fetching options:", e);
      }
      finally {
        this.isLoading = false;
      }
    },

    updateModelValue(value) {
      this.$emit('update:modelValue', value);
    },

    handleFocus() {
      this.fetchOptions()
    }
  },

  computed: {
    computedTimer() {
      return this.onlyInitialCall ? 0 : this.debounceTimer;
    },

    computedSubTitle() {
      if (this.searchKey === "pid" && this.disabled && this.showHeader) {
        return " (Select P-type or L0)"
      }
      return "";
    },

    computedApiType() {
      const apiHeaders = {
        'bistroApi': bistroApi,
        'api': api,
        'apiV2': apiV2
      }

      return apiHeaders[this.apiType];
    },

    computedOptions() {
      if (this.isSorted) {
        return this.options?.sort() || [];
      }
      return this.options;
    }
  }
};
</script>

<style>
/* You can add custom styles here if needed */
</style>
