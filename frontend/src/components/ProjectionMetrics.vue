<template>
  <div class="border px-2 py-1 border-gray-200" :class="{
    'bg-yellow-100': isAnEvent,
    'bg-gray-300': isWeekend && !isAnEvent,
    'bg-gray-100': !isWeekend && !isAnEvent
  }">
    <div class="flex justify-between">
      <span class="font-normal text-gray-500 text-xs md:text-sm">{{ formattedDate }}</span>

      <span v-if="isAnEvent">
        <i v-if="computedShowHeader" v-tooltip.top="{ value: eventData?.event_name }" class="pi pi-info-circle p-1"
          style="font-size: 0.7rem !important"></i>
        <div v-else v-tooltip.focus.top="eventData?.event_name" tabindex="0" role="button">
          <i class="pi pi-info-circle p-1" style="font-size: 0.7rem !important"></i>
        </div>
      </span>
    </div>
    <div class="flex justify-between mt-1">
      <span class="font-medium text-sm md:text-base">{{ formattedValue }}</span>
    </div>
  </div>
</template>

<script>
import Tooltip from "primevue/tooltip";

export default {
  props: {
    date: String,
    value: [Number, String],
    eventData: Object,
    showHeader: Boolean
  },

  directives: {
    tooltip: Tooltip
  },

  computed: {
    formattedDate() {
      return new Date(this.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    },

    formattedValue() {
      return new Intl.NumberFormat('en-IN').format(Number(this.value));
    },

    isAnEvent() {
      return this.eventData?.is_event || false
    },

    isWeekend() {
      const dayOfTheWeek = new Date(this.date).toLocaleDateString('en-US', { weekday: 'long' })
      return ['Saturday', 'Sunday'].includes(dayOfTheWeek)
    },

    computedShowHeader() {
      return this.showHeader
    }
  }
};
</script>

<style scoped>
/* Add any scoped styles here if needed */
</style>