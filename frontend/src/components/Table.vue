<template>
    <div class="card">
        <div class="p-4 sm:p-2 md:p-1 lg:text-base sm:text-xs md:text-base">
            <DataTable paginator :rows="defaultRowCount" :rowsPerPageOptions="[5, 10, 20, 50]" tableStyle="width: 100%"
                currentPageReportTemplate="{first} to {last} of {totalRecords}" resizableColumns columnResizeMode="expand"
                scrollable selectionMode="single" showGridlines v-if="cities && cities.length" :value="cities">
                <template v-for="(value, colKey) in cities[0]" :key="colKey">
                    <Column v-if="!columnsToHide || (columnsToHide && !columnsToHide.includes(colKey.toString()))" :field="colKey" :header="colKey" :frozen="isExcludedColumn(colKey)" :class="{
                        'text-wrap whitespace-normal': true,
                        'lg:min-w-[300px] md:min-w-[200px] md:min-w-[100px]': colKey === 'Store'
                    }">
                        <template v-if="!isExcludedColumn(colKey)" #body="slotProps">
                            <div class="flex align-items-center gap-2 lg:text-base sm:text-xs md:text-base">
                                <AnimatedNumber :value="slotProps.data?.[colKey]?.value" :type="slotProps.data?.[colKey]?.type"
                                    :class="slotProps.data?.[colKey]?.valueClass" />
                                <div v-if="slotProps.data?.[colKey]?.diff">
                                    <AnimatedNumber :value="slotProps.data?.[colKey]?.diff" :type="'percentage'"
                                        :class="slotProps.data?.[colKey]?.diffClass" />
                                </div>
                                <span v-if="slotProps.data?.[colKey]?.diff" :class="slotProps.data?.[colKey]?.arrowClass"></span>
                            </div>
                        </template>
                    </Column>
                </template>
            </DataTable>
        </div>
    </div>
</template>

<script>
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import AnimatedNumber from './AnimatedNumber.vue';
import { insights } from "../constants";
export default {
    components: {
        DataTable,
        Column,
        AnimatedNumber
    },
    props: {
        data: {
            type: Array,
            required: true,
        },
        nonMetricColumns: {
            type: Object,
            required: false,
        },
        reversedMetrics: {
            type: Array,
            required: false,
            default: [],
        },
        defaultRowCount: {
            type: Number,
            default: 5,
            required: false,
        },
        columnsToHide:{
            type:Array,
            default:[],
            required:false
        }
    },
    computed: {
        cities() {
            if (!this.data || !Array.isArray(this.data)) {
                return [];
            }
            return this.data.map(key => {
                let keyObject = {};
                if (this.nonMetricColumns) {
                    for (let prop in this.nonMetricColumns) {
                        keyObject[this.nonMetricColumns?.[prop]] = key?.[prop];
                    }
                }
                Object.entries(key.metrics).forEach(([index, metric]) => {
                    let valueClass = this.getValueClass(metric?.curr?.value, metric?.name);
                    let diffInfo = this.calculateDiff(metric?.diffs, metric?.name);
                    keyObject[metric?.name] = {
                        value: metric?.curr?.value,
                        type: metric?.curr?.type,
                        valueClass: valueClass,
                        diff: diffInfo?.value,
                        diffClass: diffInfo?.class,
                        arrowClass: diffInfo?.arrowClass,
                    };
                });
                return keyObject;
            });
        },
        numColumns() {
            return Object.keys(this.cities?.[0] || {}).length;
        },
        columnStyle() {
            return `width: calc(100% / ${this.numColumns})`;
        },
    },
    methods: {
        calculateDiff(diffs, metricName) {
            if (!diffs || diffs.length === 0) {
                return {
                    value: '',
                    class: ['text-gray-500', 'text-xs'],
                    arrowClass: ['text-gray-500', 'pi', 'text-xs']
                };
            }
            let diffValues = diffs.map(diff => diff.change() !== '-' ? parseFloat(diff.change()) : '-');
            let totalDiff = diffValues.reduce((acc, curr) => acc + curr, 0);
            let avgDiff = totalDiff / diffValues.length;
            let arrowClass, valueClass;

            if (this.reversedMetrics.includes(metricName)) {
                arrowClass = avgDiff > 0 ? ['text-red-600','pi pi-arrow-up', 'text-xs'] : ['text-green-600', 'pi pi-arrow-down', 'text-xs'];
                valueClass = avgDiff > 0 ? ['text-red-600', 'text-xs'] : ['text-green-600', 'text-xs'];
            } else {
                arrowClass = avgDiff > 0 ? ['text-green-600',   'pi pi-arrow-up', 'text-xs'] : ['text-red-600', 'pi pi-arrow-down', 'text-xs'];
                valueClass = avgDiff > 0 ? ['text-green-600', 'text-xs'] : ['text-red-600', 'text-xs'];
            }

            return {
                value: parseFloat(avgDiff)?.toFixed(1),
                class: valueClass,
                arrowClass: arrowClass,
            };
        },

        getValueClass(value, metricName) {
            if (insights.DELTA_COLUMNS.includes(metricName)) {
                return value > 0 ? ['text-green-600'] : value < 0 ? ['text-red-600'] : [''];
            }
            return [''];
        },

        isExcludedColumn(colKey) {
            return this.nonMetricColumns && Object.values(this.nonMetricColumns).includes(colKey);
        }
    }
};
</script>
