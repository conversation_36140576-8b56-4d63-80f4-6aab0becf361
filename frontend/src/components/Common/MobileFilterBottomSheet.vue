<template>
  <!-- Mobile Filter Summary Tab -->
  <div class="md:hidden bg-white rounded-xl shadow-md border border-gray-200 p-4" @click="openBottomSheet">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <i class="pi pi-filter text-yellow-600"></i>
        <span class="text-sm font-semibold text-gray-700">Filters</span>
        <span v-if="activeFiltersCount > 0"
          class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-1 rounded-full">
          {{ activeFiltersCount }}
        </span>
      </div>
      <i class="pi pi-chevron-up text-gray-400"></i>
    </div>

    <!-- Filter Summary -->
    <div v-if="filterSummary.length > 0" class="mt-3 flex flex-wrap gap-2">
      <div v-for="filter in filterSummary" :key="filter.key"
        class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-md flex items-center space-x-1">
        <span class="font-medium">{{ filter.label }}:</span>
        <span>{{ filter.value }}</span>
      </div>
    </div>
  </div>

  <!-- Bottom Sheet Modal -->
  <Sidebar v-model:visible="isBottomSheetOpen" position="bottom" :blockScroll="true" :showCloseIcon="false"
    class="mobile-filter-bottom-sheet" :style="{
      height: 'auto',
      maxHeight: '80vh',
      borderTopLeftRadius: '20px',
      borderTopRightRadius: '20px',
      background: '#FFFFFF',
      zIndex: 9999,
      padding: '0 0 4.75rem 0'
    }" :appendTo="'body'" :modal="true">

    <!-- Bottom Sheet Header -->
    <template #header>
      <div class="flex justify-between items-center w-full pb-4">
        <div class="text-xl font-bold text-gray-900">Filters</div>
        <div class="flex items-center space-x-2">
          <button v-if="hasActiveFilters" @click="clearAllFilters"
            class="px-3 py-1.5 text-sm text-red-600 font-medium bg-red-50 hover:bg-red-100 rounded-lg border border-red-200 transition-colors">
            <i class="pi pi-trash mr-1 text-xs"></i>
            Clear All
          </button>
          <button @click="closeBottomSheet" class="p-2 rounded-full hover:bg-gray-100 transition-colors">
            <i class="pi pi-times text-gray-500"></i>
          </button>
        </div>
      </div>
    </template>

    <!-- Filter Content -->
    <div class="space-y-6 pb-6">
      <slot name="filters"></slot>
    </div>

    <!-- Apply Button -->
    <div class="sticky bottom-0 bg-white border-t border-gray-200 pt-4 mt-6">
      <button @click="applyFilters"
        :class="shouldEnableApplyButton ? 'bg-yellow-500 hover:bg-yellow-600' : 'bg-gray-400 cursor-not-allowed'"
        class="w-full text-white font-semibold py-3 px-4 rounded-xl transition-colors"
        :disabled="!shouldEnableApplyButton">
        {{ applyButtonText }}
      </button>
    </div>
  </Sidebar>
</template>

<script>
import Sidebar from 'primevue/sidebar';

export default {
  name: 'MobileFilterBottomSheet',

  components: {
    Sidebar
  },

  props: {
    filterSummary: {
      type: Array,
      default: () => []
    },
    activeFiltersCount: {
      type: Number,
      default: 0
    },
    hasActiveFilters: {
      type: Boolean,
      default: false
    },
    deferredMode: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      isBottomSheetOpen: false
    };
  },

  computed: {
    shouldEnableApplyButton() {
      // Always enable the Apply button
      // deferredMode prop indicates if there are pending changes (Home page) or always true (Cities/Stores page)
      return true;
    },

    applyButtonText() {
      return 'Apply Filters';
    }
  },

  emits:['open', 'close', 'apply', 'clear-all'],

  methods: {
    openBottomSheet() {
      this.isBottomSheetOpen = true;
      this.$emit('open');
    },

    closeBottomSheet() {
      this.isBottomSheetOpen = false;
      this.$emit('close');
    },

    applyFilters() {
      this.$emit('apply');
      this.closeBottomSheet();
    },

    clearAllFilters() {
      this.$emit('clear-all');
    }
  }
};
</script>

<style scoped>
/* Custom styles for mobile filter bottom sheet */
:deep(.p-sidebar-content) {
  padding: 1.5rem;
  overflow-y: auto;
  position: relative;
  z-index: 10000;
}

:deep(.p-sidebar-header) {
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: none;
  position: relative;
  z-index: 10001;
}

/* Smooth slide-up animation */
:deep(.p-sidebar-bottom) {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
}

/* Mobile tap highlight removal */
.mobile-filter-bottom-sheet {
  -webkit-tap-highlight-color: transparent;
  z-index: 9999 !important;
}

/* Ensure dropdowns appear above bottom sheet */
:deep(.mobile-filter-bottom-sheet .vue-select .vs__dropdown-menu) {
  z-index: 10002 !important;
  position: fixed !important;
}

/* Custom scrollbar for filter content */
:deep(.p-sidebar-content)::-webkit-scrollbar {
  width: 6px;
}

:deep(.p-sidebar-content)::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

:deep(.p-sidebar-content)::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

/* Fix for dropdown positioning in mobile bottom sheet */
:deep(.vue-select) {
  position: relative;
}

:deep(.vue-select .vs__dropdown-menu) {
  z-index: 10002;
  max-height: 200px;
  overflow-y: auto;
}
</style>
