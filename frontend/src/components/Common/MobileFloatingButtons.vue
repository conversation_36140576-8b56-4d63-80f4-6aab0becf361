<template>
  <!-- Mobile Floating Action Buttons -->
  <div v-if="isMobile" class="fixed bottom-6 right-4 z-50 flex gap-3 mb-28">
    <!-- Go to Top Button -->
    <Transition enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 scale-75 translate-y-4" enter-to-class="opacity-100 scale-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in" leave-from-class="opacity-100 scale-100 translate-y-0"
      leave-to-class="opacity-0 scale-75 translate-y-4">
      <button v-if="showGoToTop" @click="scrollToTop"
        class="w-8 h-8 bg-gray-700 hover:bg-gray-800 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group">
        <i class="pi pi-angle-up text-sm group-hover:scale-110 transition-transform duration-200"></i>
      </button>
    </Transition>

    <!-- Open Filters Button -->
    <button @click="$emit('openFilters')"
      class="w-8 h-8 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group"
      :class="{ 'animate-pulse': hasActiveFilters }">
      <i class="pi pi-filter text-sm group-hover:scale-110 transition-transform duration-200"></i>

      <!-- Active filters indicator -->
      <div v-if="hasActiveFilters"
        class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
        {{ activeFiltersCount }}
      </div>
    </button>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'

// Props
const props = defineProps({
  isMobile: {
    type: Boolean,
    required: true
  },
  hasActiveFilters: {
    type: Boolean,
    default: false
  },
  activeFiltersCount: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['openFilters'])

// Reactive data
const showGoToTop = ref(false)

// Methods
const handleScroll = () => {
  // Show "Go to Top" button when user scrolls down more than 300px
  showGoToTop.value = window.scrollY > 300
}

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// Lifecycle
onMounted(() => {
  window.addEventListener('scroll', handleScroll)
})

onBeforeUnmount(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* Additional hover effects */
button:hover {
  transform: translateY(-2px);
}

button:active {
  transform: translateY(0);
}

/* Pulse animation for active filters */
@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
