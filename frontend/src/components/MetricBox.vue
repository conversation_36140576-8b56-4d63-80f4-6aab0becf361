<template>
  <div class="border px-2 py-1 bg-gray-50 md:bg-gray-100 border-gray-200" :class="hasHover ? 'hover:shadow-md' : ''" @click="handleClick">
      <div class="flex">
          <span class="font-normal text-gray-500 text-xs md:text-sm">{{name}}</span>
      </div>
      <div class="flex justify-between mt-1">
          <AnimatedNumber :styles="['font-medium','text-sm','md:text-base']" :value="curr.value" :type="curr.type" :isShortenedNumber="isShortenedNumber"/>
          <AnimatedNumber :styles="style" :value="diff" type="percentage"/>
      </div>
  </div>
</template>


<script>

import AnimatedNumber from "./AnimatedNumber.vue";
import { Metric, MetricChange} from "../interfaces/";

export default {

  components: {
    AnimatedNumber,
  },

  computed: {
    diff() {
      let val = new MetricChange(this.curr, this.prev).change();
      if(val === "-")return val;
      return parseFloat(new MetricChange(this.curr, this.prev).change());
    },

    style() {
      var metricChange = new MetricChange(this.curr, this.prev);
      var diff_style = metricChange.style(this.isReverse);

      return [
        'font-semibold',
        'text-sm',
        'mt-auto',
        diff_style
      ];
    }
  },

  emits: ['onClick'],

  methods: {
    handleClick() {
      this.$emit('onClick', { val: this.name })
    }
  },

  props: {
    name: String,
    curr: Metric,
    prev: Metric,
    styles: Array,
    isShortenedNumber: {
      type: Boolean,
      default: false,
    },
    isReverse: {
      type: Boolean,
      default: false,
    },
    hasHover: {
      type: Boolean,
      default: false,
    },
  },
}

</script>