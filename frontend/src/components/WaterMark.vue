<template>
  <div>
    <div :class="computedClass" style="z-index: -999;">
      <div class="m-8 text-[#d7dde6] opacity-50 h2 text-bold relative inline-block rotate-45 -z-[1]"
        v-for="(cnt, index) in textCount" :key="index">
        {{ computedText }}
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>

import { useUserStore } from '../stores/users';
import { mapState } from 'pinia';

export default {

  components: {},

  computed: {
    textCount() {
      return Array(300).fill(0);
    },

    computedText() {
      if (this.text.length > 0) {
        return text
      } else {
        if (this.user.id) {
          return parseInt(this.user.id) + 4834;
        }
      }

      return "";
    },

    computedClass() {
      if (this.isGreyBackground) {
        return "fixed top-0 w-full h-screen overflow-hidden flex flex-wrap justify-center items-center main";
      } else {
        return "fixed top-0 w-full h-screen overflow-hidden flex flex-wrap justify-center items-center";
      }
    },

    ...mapState(useUserStore, ['user']),
  },

  props: {
    text: {
      type: String,
      default: ""
    },
    isGreyBackground: {
      type: Boolean,
      default: false
    }
  },
};
</script>

<style scoped>
.main {
  background: #FBFBFB;
}
</style>
