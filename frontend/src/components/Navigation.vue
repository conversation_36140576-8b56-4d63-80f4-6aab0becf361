<template>
  <Disclosure as="nav" class="relative md:block md:min-w-20 z-[100]" :class="computedBackground" v-slot="{ open }">
    <div class="hidden mx-auto md:fixed md:top-0 md:flex md:flex-col justify-between h-screen py-4 px-2 z-20"
      @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
      :class="[computedBackground, { 'w-64': !hideNavBar, 'w-20': hideNavBar }]" style="transition: width 0.2s linear;">
      <div class="relative">
        <div class="flex-shrink-0 md:p-4 flex items-center" @click="toggleOverlay">
          <img class="h-8 w-8 rounded-md" :src="computedLogo" alt="Workflow" />

          <span v-if="showText" class="font-bold text-white text-2xl ml-4">{{ computedHeader }}</span>

          <OverlayPanel ref="overlayPanel" @mouseenter="stopMouseEnter">
            <div class="flex flex-col">
              <Button
                :icon="computedActiveTenant === computedTenantMapping.blinkit ? 'pi pi-check' : 'pi pi-check text-transparent'"
                iconPos="right" @click="updateTemplateView(computedTenantMapping.blinkit)" text
                :label="computedHeaders[computedTenantMapping.blinkit]" class="text-black"
                :class="computedActiveTenant === computedTenantMapping.blinkit ? 'bg-slate-200' : ''" />

              <Button
                :icon="computedActiveTenant === computedTenantMapping.bistro ? 'pi pi-check' : 'pi pi-check text-transparent'"
                iconPos="right" @click="updateTemplateView(computedTenantMapping.bistro)" text
                :label="computedHeaders[computedTenantMapping.bistro]" class="text-black"
                :class="computedActiveTenant === computedTenantMapping.bistro ? 'bg-slate-200' : ''" />
            </div>
          </OverlayPanel>
        </div>

        <div class="hidden md:inline-block my-4 mt-10 mx-2 navbar">
          <router-link v-for="item in filteredNavigation" :key="item.name" :to="item.href"
            :class="[item.current ? 'text-gray-800 bg-white font-bold' : 'text-gray-300 hover:bg-gray-700 hover:text-white', 'px-3 py-2 my-1 rounded-md text-sm font-medium', 'no-underline', 'w-full', 'whitespace-pre', 'flex items-center']"
            :aria-current="item.current ? 'page' : undefined">
            <component :is="item.Icon" class="h-6 w-6" />
            <div v-if="showText" class="ml-4">{{ item.name }}</div>
          </router-link>
        </div>
      </div>

      <div class="hidden md:block sticky bottom-0">
        <div class="flex items-center">
          <!-- Profile dropdown -->
          <Menu as="div" class="relative">
            <div class="flex justify-between p-2">
              <MenuButton
                class="border-none max-w-xs rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                :class="computedAvtarBackground">
                <img class="h-8 w-8 rounded-full" :src="user.imageURL" alt="" />
              </MenuButton>

              <div v-if="showText" class="text-white text-xs mx-2">
                <div>{{ user.name }}</div>
                <span class="text-gray-400">{{ user.email }}</span>
              </div>
            </div>

            <transition enter-active-class="transition ease-out duration-100"
              enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95">
              <MenuItems
                class="origin-top-right absolute right-0 -top-16 left-4 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                <MenuItem v-for="item in computedUserNavigation" :key="item.name" v-slot="{ active }">
                <router-link :to="item.href"
                  :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700', 'no-underline']">
                  {{ item.name }}</router-link>
                </MenuItem>
              </MenuItems>
            </transition>
          </Menu>
        </div>
      </div>
    </div>

    <div class="flex md:hidden justify-between p-4">
      <div v-if="isMobile" class="flex-shrink-0 md:p-4 flex items-center">
        <span class="font-semibold text-white text-lg ml-3">{{ computedCurrentNav?.name }}</span>
      </div>

      <div v-else class="flex-shrink-0 md:p-4 flex items-center" @click="toggleOverlay">
        <img class="h-8 w-8 rounded-md" :src="computedLogo" alt="Workflow" />
        <span class="font-semibold text-white text-lg ml-3">{{ computedHeader }}</span>
      </div>

      <!-- Mobile menu button -->
      <Bars3Icon v-if="!openedMenu"
        class="block h-6 w-6 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
        aria-hidden="true" @click="enterMenu" />

      <XMarkIcon v-else
        class="block h-6 w-6 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
        aria-hidden="true" @click="exitMenu" />
    </div>
  </Disclosure>
</template>

<script>

import { Disclosure, DisclosureButton, DisclosurePanel, Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'
import { mapState, mapActions } from 'pinia'
import { useUserStore } from '../stores/users'
import { useCityStore } from '../stores/cities'
import { getUserAccessMapping, isGlobalUser } from '../utils/utils.js'
import blinkitLogo from '/blinkit.svg';
import bistroLogo from '/bistro.svg';
import OverlayPanel from 'primevue/overlaypanel';
import Button from 'primevue/button';
import { ref, getCurrentInstance } from "vue";
import { TenantMapping } from '../constants/index.js';
import { useScreenSize } from '../composables/useScreenSize.js'
import { BISTRO_NAVIGATION, SONAR_NAVIGATION } from '../constants/pages.js'
import isEmpty from 'lodash.isempty'

export default {
  components: {
    Disclosure,
    DisclosureButton,
    DisclosurePanel,
    Menu,
    MenuButton,
    MenuItem,
    MenuItems,
    Bars3Icon,
    XMarkIcon,
    OverlayPanel,
    Button,
  },

  setup(props, context) {
    const instance = getCurrentInstance();
    const overlayPanel = ref(null);

    const { isMobile } = useScreenSize();

    const toggleOverlay = (event) => {
      if (instance.proxy.getAllowedTenants?.length <= 1 || isMobile.value) return
      overlayPanel.value.toggle(event);
    };

    const handleMouseEnter = () => {
      instance.proxy.hideNavBar = false;
      context.emit("showNav");
      setTimeout(() => {
        instance.proxy.showText = true
      }, 150)
    };

    const handleMouseLeave = (event) => {
      // Access the actual DOM container of the OverlayPanel
      const overlayContent = overlayPanel.value.container;

      // Check if the mouse is entering the OverlayPanel's content
      if (overlayContent && overlayContent.contains(event.relatedTarget)) return

      instance.proxy.hideNavBar = true;
      context.emit("hideNav");
      instance.proxy.showText = false
      overlayPanel.value.hide()
    };

    const stopMouseEnter = (event) => {
      event.stopPropagation();
    };

    return {
      isMobile,
      overlayPanel,
      toggleOverlay,
      handleMouseEnter,
      handleMouseLeave,
      stopMouseEnter,
    };
  },

  beforeMount() {
    // Make an API request after login and set the user type based on the response
    if (this.user.accessToken) {
      this.getUserType();
    }

    this.checkRoute();
  },

  methods: {
    checkRoute() {
      this.computedNavigation.forEach(item => {
        item.current = item.href.toLowerCase() == this.$route.path.toLowerCase();
      });
    },

    getUserType() {
      let userAccessMapping = getUserAccessMapping();
      this.isGlobalUser = true ? isGlobalUser(userAccessMapping) : false;
      this.cityListLength = Object.keys(this.getCityMapping).length;
      this.updateNavigationAndRedirection();
    },

    updateNavigationAndRedirection() {
      let allowedRoutes = this.getAllowedNavs;
      this.computedNavigation.forEach(item => {
        if (!allowedRoutes.includes(item.href)) {
          item.hidden = true;
        }
      });
    },

    updateTemplateView(val) {
      if (val === this.computedActiveTenant) return;
      this.updateActiveTenant(val);

      if (val === this.computedTenantMapping.blinkit) this.$router.replace("/");
      else if (val === this.computedTenantMapping.bistro) this.$router.replace("/bistro");

      this.toggleOverlay()
    },

    enterMenu() {
      this.openedMenu = true;
      if (this.computedActiveTenant === TenantMapping.blinkit) {
        this.$router.push('/navigation')
      } else {
        this.$router.push('/bistro/navigation')
      }
    },

    exitMenu() {
      this.openedMenu = false;
      this.$router.back();
    },

    ...mapActions(useUserStore, ['updateActiveTenant']),
  },

  watch: {
    $route() {
      if (!this.$route.path?.toLowerCase()?.includes('navigation')) this.openedMenu = false;
      this.checkRoute();
    },

    showNavBarText(val) {
      if (val && this.hideNavBar) {
        this.showText = false;
      }
    }
  },

  computed: {
    ...mapState(useUserStore, ['user', 'isLoggedIn', 'getAllowedNavs', 'getActiveTenant', 'getAllowedTenants']),
    ...mapState(useCityStore, ['getCityMapping']),

    computedNavigation() {
      return this.computedActiveTenant === TenantMapping.blinkit ? this.sonarNavigation : this.bistroNavigation
    },

    filteredNavigation() {
      return this.computedNavigation.filter(item => !item.hidden);
    },

    showNavBarText() {
      return this.showText
    },

    computedActiveTenant() {
      return this.getActiveTenant;
    },

    computedTenantMapping() {
      return TenantMapping;
    },

    computedBackground() {
      return this.computedActiveTenant === TenantMapping.blinkit ? 'bg-gray-950' : 'bg-green-950';
    },

    computedAvtarBackground() {
      return this.computedActiveTenant === TenantMapping.blinkit ? 'bg-gray-800' : 'bg-green-950';
    },

    computedLogo() {
      return this.computedActiveTenant === TenantMapping.blinkit ? blinkitLogo : bistroLogo;
    },

    computedUserNavigation() {
      return [
        { name: 'Sign out', href: this.computedActiveTenant === TenantMapping.blinkit ? '/logout' : '/bistro/logout' },
      ]
    },

    computedHeaders() {
      return {
        [TenantMapping.blinkit]: 'Sonar',
        [TenantMapping.bistro]: 'Lidar'
      }
    },

    computedHeader() {
      return this.computedActiveTenant === TenantMapping.blinkit
        ? this.computedHeaders[TenantMapping.blinkit]
        : this.computedHeaders[TenantMapping.bistro];
    },

    computedCurrentNav() {
      if (this.$route.path?.toLowerCase()?.includes('navigation')) return { name: 'Menu' };
      return this.filteredNavigation?.find(item => item.current);
    }
  },

  data() {
    return {
      sonarNavigation: SONAR_NAVIGATION,
      bistroNavigation: BISTRO_NAVIGATION,
      cityListLength: -1,
      isGlobalUser: false,
      hideNavBar: true,
      showText: false,
      openedMenu: false,
    }
  },
};
</script>

<style scoped>
.navbar {
  height: 75vh;
  overflow-y: auto;
}
</style>