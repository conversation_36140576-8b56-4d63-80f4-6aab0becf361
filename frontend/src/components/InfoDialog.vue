<template>
    <Button :class="[this.class, 'inline-flex items-center justify-center']" rounded @mouseover="startHoverDelay"
        @mouseleave="clearHoverDelay" @click="ontoggleDialogVisibility">
        <i class="pi pi-info-circle" style="font-size: 0.7rem !important; background-color: #000;"></i>
    </Button>

    <Dialog v-model:visible="visible" modal
        :header="showMetricValue ? `${this.metrickey} (${computedMetricData.value})` : this.metrickey"
        :style="{ width: '45rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :dismissableMask="true">
        <div v-if="hasDetails" class="m-0 text-sm lg:text-md">
            <div v-if="hasExplanation" class="mb-3">
                <strong class="mr-3">Definition:</strong>
                <div class="text-black font-light whitespace-pre-line">{{ computedMetricData.explanation }}</div>
            </div>

            <div v-if="hasTrendMetric" class="mb-3">
                <strong class="mr-3">Metric Ordering:
                    <span v-if="computedMetricData.hasAbsoluteTrendValues" class="font-light ml-1">
                        (absolute values)
                    </span>
                </strong>
                <div v-if="hasWeeklyTrend" class="flex">
                    <div v-for="(item, index) in computedWOW" :key="item + index">
                        <span class="text-sm mr-0.5" :style="{ color: [1, 3].includes(index) ? 'red' : 'green' }">
                            {{ item }}
                        </span>
                        <span v-if="index < computedWOW.length - 1"
                            class="text-sm font-bold mr-1 text-positive-metric px-2"> »
                        </span>
                    </div>
                </div>
                <div v-else class="flex">
                    <span class="text-black font-light text-sm mr-5">T-0 metric</span>
                    <span class="font-bold" style="color: green;">T-7 Δ</span>
                </div>
            </div>

            <div v-if="showTechnicalDetails">
                <div v-if="hasSource" class="mb-3">
                    <strong class="mr-3">Source:</strong>
                    <div class="text-black font-light whitespace-pre-line">{{ computedMetricData.source }}</div>
                </div>
                <div v-if="hasField" class="mb-3">
                    <strong class="mr-3">Field:</strong>
                    <div class="text-black font-light whitespace-pre-line">{{ computedMetricData.field }}</div>
                </div>
                <div v-if="hasCalculation" class="mb-3">
                    <strong class="mr-3">Calculation:</strong>
                    <div class="text-black font-light whitespace-pre-line">{{ computedMetricData.calculation }}</div>
                </div>
            </div>

            <div class="flex justify-end">
                <Button v-if="hasTechnicalDetails"
                    :label="showTechnicalDetails ? '- Hide Technical Details' : '+ Show Technical Details'" plain text
                    @click="handleTechnicalDetails" class="text-xs lg:text-sm" />
            </div>
        </div>
        <div v-else>
            Metric data not available.
        </div>
    </Dialog>
</template>


<script>
import Dialog from 'primevue/dialog';

import Button from 'primevue/button';
import { mapState } from 'pinia';
import { useUserStore } from '../stores/users.js';
import { TenantMapping } from '../constants/index.js';
import { BistroAbbreviationConstants } from '../constants/abbreviationConstants/bistroAbbreviationConstants.js';
import { BlinkitAbbreviationConstants } from '../constants/abbreviationConstants/blinkitAbbreviationConstants.js';


export default {
    components: {
        Button,
        Dialog,
    },

    props: {
        metrickey: {
            type: String,
            required: true,
        },
        showHeader: {
            type: Boolean,
            default: false,
            required: true,
        },
        class: {
            type: String,
            default: "p-0 w-3 h-3 ml-2 border border-black",
            required: false,
        },
        sourcePage: {
            type: String,
            required: false
        },
        customWOW: {
            type: Array,
            default: ['Wo4W', 'Wo3W', 'Wo2W', 'Wo1W'],
            required: false
        }

    },

    data() {
        return {
            visible: false,
            hoverTimeout: null,
            showTechnicalDetails: false
        }
    },

    computed: {
        computedAbbreviationConstants() {
            if (this.getActiveTenant === TenantMapping.bistro) return BistroAbbreviationConstants;
            return BlinkitAbbreviationConstants;
        },
        computedMetricData() {
            return this.computedAbbreviationConstants[this.metrickey];
        },
        showMetricValue() {
            return this.computedMetricData?.value && this.computedMetricData?.value !== this.metrickey
        },
        hasExplanation() {
            return (this.computedMetricData?.explanation || "").length > 0;
        },
        hasWeeklyTrend() {
            return this.computedMetricData?.showWeekTrend && !["WTD_MTD"].includes(this.sourcePage);
        },
        hasSource() {
            return (this.computedMetricData?.source || "").length > 0;
        },
        hasField() {
            return (this.computedMetricData?.field || "").length > 0;
        },
        hasCalculation() {
            return (this.computedMetricData?.calculation || "").length > 0
        },
        hasTechnicalDetails() {
            return this.hasSource || this.hasField || this.hasCalculation;
        },
        hasDetails() {
            return this.hasExplanation || this.hasTrend || this.hasCalculation;
        },
        hasTrendMetric() {
            return !this.computedMetricData?.hasNoTrendMetric;
        },
        computedWOW() {
            return this.customWOW;
        },

        ...mapState(useUserStore, ['getActiveTenant'])
    },


    methods: {
        ontoggleDialogVisibility(event) {
            event.stopPropagation();
            this.visible = true;
        },

        startHoverDelay(event) {
            if (!this.showHeader) return;
            clearTimeout(this.hoverTimeout);
            this.hoverTimeout = setTimeout(() => {
                this.ontoggleDialogVisibility(event);
            }, 500);
        },

        clearHoverDelay() {
            if (!this.showHeader) return;
            clearTimeout(this.hoverTimeout);
        },

        handleTechnicalDetails() {
            this.showTechnicalDetails = !this.showTechnicalDetails
        }
    },



    mounted() {
        this.visible = false;
    }
}
</script>
