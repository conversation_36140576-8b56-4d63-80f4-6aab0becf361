<template>
  <span :class="styles">{{ this.computedNumber }}</span>
</template>

<script>
import { UNAVAILABLE_METRIC_VALUE } from "../utils/metrics.js";

export default {
  props: {
    type: String,
    styles: Array,
    value: [Number, String],
    isShortenedNumber: {
      type: <PERSON>olean,
      default: false,
    }
  },

  methods: {
    localeString(number) {
      return number.toLocaleString('hi')
    },

    formatCurrency(number) {
      if (number >= 100000 && number < 10000000) {
        return (number / 100000.0).toFixed(2) + "L";
      } else if (number >= 10000000) {
        return (number / 10000000.0).toFixed(2) + "Cr";
      } else {
        return number.toLocaleString('hi');
      }
    },

    formatTime(number) {
      if (number < 60) {
        return number + " s";
      } else if (number < 3600) {
        return (number / 60).toFixed(2) + " m";
      } else {
        return (number / 3600).toFixed(2) + " h";
      }
    },

    shortenedLocaleString(number, type) {
      if (type == 'currency') {
        return this.formatCurrency(number);
      } else if (type == 'time') {
        return this.formatTime(number);
      } else {
        return number.toLocaleString('hi');
      }
    },

    animateValueChange() {
      if (!this.$refs.numberEl) return;

      this.$refs.numberEl.classList.add('number-updated');

      setTimeout(() => {
        if (this.$refs.numberEl) {
          this.$refs.numberEl.classList.remove('number-updated');
        }
      }, 1000);
    }
  },

  computed: {
    computedNumber() {
      let number = this.isShortenedNumber ? this.shortenedLocaleString(this.value, this.type) : this.value;
      if (this.type == 'percentage' && !([UNAVAILABLE_METRIC_VALUE, '-'].includes(this.value))) {
        return this.localeString(number) + '%';
      } else if (this.type == 'currency' && !([UNAVAILABLE_METRIC_VALUE, '-'].includes(this.value))) {
        return '₹' + this.localeString(number);
      } else if (this.type == 'time' && !([UNAVAILABLE_METRIC_VALUE, '-'].includes(this.value))) {
        return this.formatTime(number);
      } else if (this.type == 'number' && !([UNAVAILABLE_METRIC_VALUE, '-'].includes(this.value))) {
        return this.localeString(number);
      } else {
        return number;
      }
    },
  },

  watch: {
    value() {
      this.$nextTick(() => {
        this.animateValueChange();
      });
    }
  }
}
</script>

<style scoped>
.animated-number {
  display: inline-block;
  transition: all 0.3s ease;
}

.number-updated {
  color: #1e40af;
  transform: scale(1.1);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.linear-wipe {
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(to right, #64748b 30%, #1e40af 70%);
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: colorMove 3s infinite linear;
}

@keyframes colorMove {
  0% {
    background-position: -100% 0;
  }

  100% {
    background-position: 100% 0;
  }
}
</style>