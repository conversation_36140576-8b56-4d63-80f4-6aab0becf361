<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else>
        <v-chart class="h-[50vh]" autoresize :option="chartOption" @ready="initializeChart" ref="chart" />
    </div>
</template>

<script>
import { ref, onMounted } from "vue";
import { use } from "echarts/core";
import { BarChart } from "echarts/charts";
import { DatasetComponent, GraphicComponent, GridComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import VChart from "vue-echarts";
import { api } from '../api/';
import Loading from "./Loading.vue";

use([BarChart, DatasetComponent, GraphicComponent, GridComponent, CanvasRenderer]);

export default {
    components: {
        VChart,
        Loading
    },
    setup() {
        const isLoading = ref(null);
        const chartOption = ref(null);
        const chartRef = ref(null);
        const updateFrequency = 500;
        const dimension = 1;
        const countryColors = {
            "Slice Cake": "#F5A3C7",
            "Chocolate": "#7B3F00",
            "Soda Water": "#B4E1FA",
            "Tonic Water": "#C7F5D3",
            "Plum Cake": "#8E4585",
            "Ale": "#D7A86E",
            "Gift Wrapping Paper": "#E0D4FC",
            "Brownie": "#6F4E37",
            "Christmas Ornaments": "#D22730",
            "Santa Cap": "#FF0000",
            "Gift Set": "#FFD700",
            "Christmas Tree": "#228B22",
            "Essence": "#FFD9E8",
            "Layered Cake": "#FCD5CE",
            "Festive Cake": "#FFE4B5",
            "Baking Soda": "#E9F7EF",
            "Soft Toy": "#FFC3A0",
            "Chocolate Gift Pack": "#3D1E00",
            "Baking Powder": "#D9E6F2",
            "Yeast": "#F5E3A1",
            "Gift Pack": "#FFD700",
            "Beer": "#F5C842",
            "Decorative LED Lights": "#FFE400",
            "Decorative Star": "#F0E68C",
            "Cocoa Powder": "#4B2C20",
            "Christmas Garland": "#556B2F",
            "Filled Cake": "#F5C8A1",
            "Choco Chips": "#5B3A29",
            "Card Game": "#FA8072",
            "Christmas Stocking": "#FF6347",
            "Firewood": "#A0522D",
            "Powder": "#FFFFFF",
            "Decorative Lights": "#F0E68C",
            "Calendar": "#D8BFD8",
            "Cake": "#F5A3C7",
            "Cake Mix": "#FAD9A0",
            "Head Band": "#FFC0CB",
            "Wreath": "#228B22",
            "Board Game": "#FFA500",
            "Gift Bag": "#E6E6FA",
            "Banner": "#FFB6C1",
            "Fake Beard": "#FFFFFF",
            "Photobooth Props": "#FF69B4",
            "Party Goggles": "#FFD700",
            "Bracelet": "#FF8C00",
            "Foil Balloons": "#FFC1C1",
            "Biscuits Gift Pack": "#D2B48C",
            "Cake Mould": "#A9A9A9",
            "Hangings": "#FFDAB9",
            "Christmas Toy": "#ADD8E6",
            "Cupcake": "#FFB7C5",
            "Pound Cake": "#FFE5B4",
            "Cigarette": "#D2B48C",
            "Potato Chips": "#FFD700",
            "Cola Soft Drink": "#6A0DAD",
            "Crisps": "#FFA07A",
            "Popcorn": "#FDE910",
            "Cocktail Mix": "#FF4500",
            "Coal": "#2F4F4F",
            "Hookah Coal": "#696969",
            "Magic Coal": "#000000",
            "Ice Cubes": "#D6F1FF",
            "Barbeque Griller": "#D2691E",
            "Disposable Glass": "#C0C0C0",
            "Disposable Plates": "#F5F5F5",
            "Disposable Spoon": "#DCDCDC",
            "Disposable Fork": "#D3D3D3",
            "Nachos": "#F4A460",
            "Hangover Solution": "#C6FFDD",
            "Condom": "#FF1493",
            "Energy Drink": "#FFD700"
        }
        const fetchData = async () => {
            const [dataRes] = await Promise.all([
                api.fetchMagic()
            ]);
            isLoading.value = false
            return dataRes.data
        };

        const initializeChart = async () => {
            const data = await fetchData();
            const years = [...new Set(data.map((item) => item[2]))];
            let startIndex = 10;
            let startYear = years[startIndex];

            chartOption.value = {
                grid: {
                    top: 10,
                    bottom: 30,
                    left: 150,
                    right: 80,
                },
                xAxis: {
                    max: "dataMax",
                    axisLabel: {
                        formatter: (n) => Math.round(n).toString(),
                    },
                },
                dataset: {
                    source: data.filter((d) => d[2] === startYear),
                },
                yAxis: {
                    type: "category",
                    inverse: true,
                    max: 15,
                    axisLabel: {
                        show: true,
                        fontSize: 14,
                        rich: {
                            flag: {
                                fontSize: 25,
                                padding: 5,
                            },
                        },
                    },
                    animationDuration: 300,
                    animationDurationUpdate: 300,
                },
                series: [
                    {
                        realtimeSort: true,
                        seriesLayoutBy: "column",
                        type: "bar",
                        itemStyle: {
                            color: (param) =>
                                countryColors[param.value[0]] || "#5470c6",
                        },
                        encode: {
                            x: dimension,
                            y: 0,
                        },
                        label: {
                            show: true,
                            precision: 1,
                            position: "right",
                            valueAnimation: true,
                            fontFamily: "monospace",
                        },
                    },
                ],
                animationDuration: 0,
                animationDurationUpdate: updateFrequency,
                animationEasing: "linear",
                animationEasingUpdate: "linear",
                graphic: {
                    elements: [
                        {
                            type: "text",
                            right: 160,
                            bottom: 60,
                            style: {
                                text: startYear,
                                font: "bolder 80px monospace",
                                fill: "rgba(100, 100, 100, 0.25)",
                            },
                            z: 100,
                        },
                    ],
                },
            };

            updateYears(years, startIndex, data);
        };

        const updateYears = (years, startIndex, data) => {
            for (let i = startIndex; i < years.length - 1; ++i) {
                setTimeout(() => {
                    updateYear(years[i + 1], data);
                }, (i - startIndex) * updateFrequency);
            }
        };

        const updateYear = (year, data) => {
            const chart = chartRef.value?.echarts;
            const source = data.filter((d) => d[2] === year);
            chartOption.value.series[0].data = source;
            chartOption.value.graphic.elements[0].style.text = year;
            chart.setOption(chartOption.value);
        };

        onMounted(() => {
            isLoading.value = true
            initializeChart();
        });

        return {
            chartOption,
            chartRef,
            isLoading
        };
    },
};
</script>
