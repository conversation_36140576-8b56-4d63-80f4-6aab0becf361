<template>
  <Dialog v-model:visible="openDialog" modal :header="dialogTitle" :style="{ width: isMobile ? '95vw' : '600px' }"
    :breakpoints="{ '1199px': '75vw', '575px': '95vw' }" :dismissableMask="true" @hide="$emit('close')">

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center p-8">
      <i class="pi pi-spin pi-spinner text-2xl text-blue-500"></i>
      <span class="ml-3 text-gray-600">Loading drill-down data...</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center p-8">
      <i class="pi pi-exclamation-triangle text-3xl text-red-500 mb-4"></i>
      <div class="text-red-600 font-semibold mb-2">Failed to load drill-down data</div>
      <div class="text-gray-600 text-sm mb-4">{{ error }}</div>
      <Button label="Retry" @click="fetchDrillDownData" class="p-button-sm" />
    </div>

    <!-- Main Content -->
    <div v-else-if="drillDownData" class="space-y-6">

      <!-- Summary Cards -->
      <div class="grid grid-cols-3 gap-4">
        <div v-for="categoryItem in [
          {
            category: 'red',
            count: drillDownData.summary.red_count,
            title: 'Declining',
            detail: `${isReverse ? '> +5%' : '< -5%'}`,
            class: 'bg-red-50 border border-red-200 hover:bg-red-100'
          },
          {
            category: 'yellow',
            count: drillDownData.summary.yellow_count,
            title: 'Stable',
            detail: `${isReverse ? '+5% to -5%' : '-5% to +5%'}`,
            class: 'bg-orange-50 border border-orange-200 hover:bg-orange-100'
          },
          {
            category: 'green',
            count: drillDownData.summary.green_count,
            title: 'Improving',
            detail: `${isReverse ? '< -5%' : '> +5%'}`,
            class: 'bg-green-50 border border-green-200 hover:bg-green-100'
          }
        ]" @click="updateCategory(categoryItem.category)"
          class="rounded-lg p-4 text-center cursor-pointer transition-colors" :class="categoryItem.class"
          :style="selectedCategory?.includes(categoryItem.category) ? { 'border': `2px solid ${categoryItem.category === 'yellow' ? 'black' : categoryItem.category}` } : {}">
          <div :class="`text-sm md:text-2xl font-bold text-${categoryItem.category}-600`">{{ categoryItem.count }}</div>
          <div :class="`text-xs md:text-sm text-${categoryItem.category}-700`">{{ categoryItem.title }}</div>
          <div :class="`text-xs text-${categoryItem.category}-600 mt-1`">{{ categoryItem.detail }}</div>
        </div>
      </div>

      <!-- Comparison Info -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="text-sm text-gray-600">
          Comparison:
          <strong>{{ drillDownData.current_date }}</strong> vs <strong>{{ drillDownData.comparison_date }}</strong>
          <div>(Week-over-Week)</div>
        </div>
      </div>

      <!-- Metrics for Cities -->
      <div v-if="shouldShowDailyMetrics" class="space-y-2">
        <!-- Day Level Metrics -->
        <div v-if="computedDailyMetrics" class="bg-gray-50 rounded-lg p-4">
          <div class="text-sm text-gray-600">
            <div class="font-semibold text-gray-800">Day Level</div>
            <div v-if="computedDailyMetrics.projectedValue" class="flex justify-between items-center mt-1">
              <span class="text-sm font-medium text-gray-700">Projected (P) Value:</span>
              <span class="text-sm font-semibold text-blue-600">{{ formatValue(computedDailyMetrics?.projectedValue) }}</span>
            </div>
            <div v-if="shouldShowDailyMetrics && computedDailyMetrics" class="flex justify-between items-center mt-1">
              <span class="text-sm font-medium text-gray-700">Current Rate Metrics (A):</span>
              <span class="text-sm font-semibold text-blue-600">{{ formatValue(computedDailyMetrics?.currentValue) }}</span>
            </div>
            <!-- P vs A Delta for Day Level -->
            <div v-if="computedDailyMetrics.currentValue" class="flex justify-between items-center mt-1">
              <span class="text-sm font-medium text-gray-700">P vs A (Day Level):</span>
              <div class="text-right">
                <div class="text-xs font-semibold" :class="computedDayLevelDelta.class">
                  {{ computedDayLevelDelta.delta }}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Hour Level Metrics (only for Cart Volume on current date) -->
        <div v-if="shouldShowHourlyMetrics" class="bg-gray-50 rounded-lg p-4">
          <div class="text-sm text-gray-600">
            <div class="font-semibold text-gray-800">Hour Level</div>
            
            <!-- Current Value (till hour X) -->
            <div class="flex justify-between items-center mt-1">
              <span class="text-sm font-medium text-gray-700">{{ labels.currentValueLabel }}</span>
              <span class="text-sm font-semibold text-blue-600">{{ formatValue(computedHourlyMetrics?.currentValue) }}</span>
            </div>
            
            <!-- P vs A (till hour X-1) -->
            <div v-if="computedHourlyMetrics" class="flex justify-between items-center mt-1">
              <span class="text-sm font-medium text-gray-700">{{ labels.projectedVsActualLabel }}:</span>
              <div class="text-right">
                <div v-if="computedHourlyMetrics?.isFirstHour" class="text-xs text-gray-500">
                  Not available for first hour
                </div>
                <div v-else class="text-xs font-semibold" :class="computedHourLevelDelta.class">
                  {{ computedHourLevelDelta.delta }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Entity List -->
      <div v-if="filteredEntities.length > 0" class="space-y-4">
        <div class="flex justify-between items-center">
          <h3 class="text-sm md:text-lg font-semibold text-gray-800">
            {{ getCategoryTitle() }}
            ({{ filteredEntities.length }})
          </h3>

          <!-- Sort Options -->
          <div class="flex items-center space-x-2">
            <label class="text-xs md:text-sm text-gray-600">Sort by:</label>
            <select v-model="sortBy" class="text-xs md:text-sm border border-gray-300 rounded px-2 py-1">
              <option value="wow_high_to_low">High to Low (WoW)</option>
              <option value="wow_low_to_high">Low to High (WoW)</option>
              <option value="name">Name</option>
            </select>
          </div>
        </div>

        <!-- Entity Cards -->
        <div class="max-h-96 overflow-y-auto space-y-2">
          <div v-for="entity in sortedEntities" :key="entity.name"
            class="flex items-center justify-between p-3 bg-white border rounded-lg hover:shadow-sm transition-shadow">

            <!-- Entity Info -->
            <div class="flex-1">
              <div class="text-sm md:text-base font-medium text-gray-800">{{ entity.name }}</div>
              <div class="text-xs md:text-sm text-gray-600">
                Current: <strong>{{ formatValue(entity.current_value) }}</strong>
                <span class="px-3">|</span>
                T-7: <strong>{{ formatValue(entity.previous_value) }}</strong>
              </div>
            </div>

            <!-- Performance Indicator -->
            <div class="flex items-center space-x-3">
              <div class="text-right">
                <div :class="getPercentageClass(entity.percentage_change)" class="font-semibold">
                  {{ entity.percentage_change > 0 ? '+' : '' }}{{ entity.percentage_change }}%
                </div>
                <div v-if="entity.impact_magnitude" class="text-xs text-gray-500">
                  Diff: <span :class="[getPercentageClass(entity.percentage_change), 'font-semibold']">{{
                    formatValue(entity.impact_magnitude) }}</span>
                </div>
              </div>

              <!-- Color Indicator -->
              <div :class="getCategoryIndicatorClass(entity.category)" class="w-3 h-3 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Data State -->
      <div v-else class="text-center p-8 text-gray-500">
        <i class="pi pi-info-circle text-2xl mb-2"></i>
        <div>No entities found for the selected criteria</div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center p-8 text-gray-500">
      <i class="pi pi-chart-line text-3xl mb-4"></i>
      <div>No drill-down data available</div>
    </div>
  </Dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'
import { api, apiV2 } from '../../api/index.js'
import { useScreenSize } from '../../composables/useScreenSize.js'
import axios from 'axios'
import { metricsForStyleReversal } from '../../utils/metrics.js'
import isEmpty from 'lodash.isempty'
import { isDateToday, formatDateToString } from '../../utils/utils.js'


// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  metricName: {
    type: String,
    required: true
  },
  entityName: {
    type: String,
    default: null // Name of the clicked entity to drill down into
  },
  currentSelection: {
    type: Object,
    default: () => ({})
  },
  selectedDate: {
    type: String,
    required: true
  },
  computedGroupsAndCitiesList: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close'])

// Composables
const { isMobile } = useScreenSize()

// Reactive data
const openDialog = ref(props.visible || false)
const loading = ref(false)
const error = ref(null)
const drillDownData = ref(null)
const selectedCategory = ref(['red'])
const sortBy = ref('wow_high_to_low')
const dailyMetrics = ref(null)
const hourlyMetrics = ref(null)

// Computed

const isReverse = computed(() => {
  return metricsForStyleReversal.includes(props.metricName)
})

const dialogTitle = computed(() => {
  return `${props.metricName} - ${props.entityName}`
})

// Check if metric is city only and not store
const isCityMetric = computed(() => {
  if (!["Cart Volume", "GMV"].includes(props.metricName)) return false; 

  return !!props.computedGroupsAndCitiesList?.find(item => 
    (item.type === 'city' && item.name === props.entityName) ||
    (item.type === 'group' && item.mappings?.some(mapping => mapping.city === props.entityName))
  );
})

const filteredEntities = computed(() => {
  if (!drillDownData.value?.entities) return []

  if (!isEmpty(selectedCategory.value)) {
    return drillDownData.value?.entities?.filter(entity => selectedCategory.value?.includes(entity.category))
  }

  return drillDownData.value?.entities
})

const sortedEntities = computed(() => {
  const entities = [...filteredEntities.value]

  switch (sortBy.value) {
    case 'wow_high_to_low':
      return metricsForStyleReversal.includes(props.metricName)
        ? entities.sort((a, b) => a?.percentage_change - b?.percentage_change)
        : entities.sort((a, b) => b?.percentage_change - a?.percentage_change)
    case 'wow_low_to_high':
      return metricsForStyleReversal.includes(props.metricName)
        ? entities.sort((a, b) => b?.percentage_change - a?.percentage_change)
        : entities.sort((a, b) => a?.percentage_change - b?.percentage_change)
    case 'name':
      return entities.sort((a, b) => a?.name?.localeCompare(b?.name))
    default:
      return entities
  }
})


const getHourlyDataForMetric = (metricName) => {
  if (!hourlyMetrics.value) return { actualData: null, projectedData: null }
  
  const actualHourlyData = hourlyMetrics.value.find(item => item.type === `${metricName} Actual Today`)
  const projectedHourlyData = hourlyMetrics.value.find(item => item.type === `${metricName} Projected Today`)
  
  
  const actualData = actualHourlyData?.metrics[0].data
    .filter(item => item.data?.[0]?.name === metricName)
    .sort((a, b) => a.hour - b.hour)
  
  const projectedData = projectedHourlyData?.metrics[0].data
    .filter(item => item.data?.[0]?.name === metricName)
    .sort((a, b) => a.hour - b.hour)
  
  return { actualData, projectedData }
}

const calculateFullDayMetrics = (actualData, projectedData, currentMetricName) => {
  const latestHour = Math.max(...actualData.map(item => item.hour))
  const hoursToSum = actualData.filter(item => item.hour <= latestHour);
  const totalActualValue = hoursToSum.reduce((sum, item) => sum + (item.data[0]?.metric || 0), 0)
  
  // For P vs A: use data till (latest - 1) hour
  const latestMinusOne = Math.max(0, latestHour - 1)
  const hoursToSumMinusOne = actualData.filter(item => item.hour <= latestMinusOne);
  const projectedHoursToSumMinusOne = projectedData.filter(item => item.hour <= latestMinusOne);
  
  const totalActualValueMinusOne = hoursToSumMinusOne.reduce((sum, item) => sum + (item.data[0]?.metric || 0), 0)
  const totalProjectedValueMinusOne = projectedHoursToSumMinusOne.reduce((sum, item) => sum + (item.data[0]?.metric || 0), 0)
  
  return {
    metricName: currentMetricName,
    currentValue: totalActualValue, // Sum of all hours till latest
    hourMinusOneProjectedValue: totalProjectedValueMinusOne,  // Sum of projected till latest-1 for P vs A
    hourMinusOneActualValue: totalActualValueMinusOne, // Sum of actual till latest-1 for P vs A
    currentHour: latestHour,
    hourMinusOne: latestMinusOne,
    isFullDay: true
  }
}

const calculateSpecificHourMetrics = (actualData, projectedData, selectedHour, currentMetricName) => {
  const hourToUse = selectedHour !== -1 ? selectedHour : actualData[actualData.length - 1].hour
  const currentHourActual = actualData.find(item => item.hour === hourToUse)
  if (!currentHourActual) return null
  
  const currentHourProjected = projectedData.find(item => item.hour === currentHourActual.hour)
  const hourMinusOneActual = hourToUse > 0 ? actualData?.find(item => item.hour === (hourToUse - 1)) : null
  const hourMinusOneProjected = hourMinusOneActual ? projectedData?.find(item => item.hour === hourMinusOneActual.hour) : null
  
  const isFirstHour = hourToUse === 0
  
  if (isFirstHour) {
    return {
      metricName: currentMetricName,
      currentValue: currentHourActual.data[0].metric, // Zeroth hour actual value
      hourMinusOneProjectedValue: null,
      hourMinusOneActualValue: null,
      currentHour: currentHourActual.hour,
      hourMinusOne: null,
      isFullDay: false,
      isFirstHour: true
    }
  }

  if (!currentHourProjected || !hourMinusOneProjected || !hourMinusOneActual) return null
  
  return {
    metricName: currentMetricName,
    currentValue: currentHourActual.data[0].metric, // Current hour actual value
    hourMinusOneProjectedValue: hourMinusOneProjected.data[0].metric, // Hour-1 projected value for P vs A
    hourMinusOneActualValue: hourMinusOneActual.data[0].metric, // Hour-1 actual value for P vs A calculation
    currentHour: currentHourActual.hour,
    currentHour: currentHourActual.hour,
    hourMinusOne: hourMinusOneActual.hour,
    isFullDay: false
  }
}

// For delta of hourly and daily metrics
const getProjectedValueClass = (percentage) => {
  return percentage > 0 ? 'text-positive-metric' : 'text-negative-metric'
}


const computedHourlyMetrics = computed(() => {

  // Handle hourly metrics for current date and Cart Volume
    const { actualData, projectedData } = getHourlyDataForMetric(props.metricName) // array of actual and projected data
    
    if (!actualData || !projectedData || actualData.length === 0 || projectedData.length === 0) {
      return null
    }
    
    const isFullDay = props.currentSelection?.hour === -1
    
    if (isFullDay) {
      return calculateFullDayMetrics(actualData, projectedData, props.metricName)
    } else {
      return calculateSpecificHourMetrics(actualData, projectedData, props.currentSelection?.hour, props.metricName)
    }

  
  return null
})

const computedDailyMetrics = computed(() => {
  // Handle daily metrics for GMV and Cart Volume
  if (!["GMV", "Cart Volume"].includes(props.metricName)) {
    return null
  }

  if (!dailyMetrics.value?.metrics) return null

  const currentMetric = dailyMetrics.value.metrics.find(metric => metric.name === props.metricName)
  const projectedMetric = dailyMetrics.value.metrics.find(metric => metric.name === `Projected ${props.metricName}`)
  
  if (isEmpty(currentMetric) || isEmpty(projectedMetric)) {
    return null
  }
  
  const currentData = currentMetric.data.find(item => {
    return formatDateToString(item.date) === formatDateToString(props.selectedDate)
  })

  const projectedData = projectedMetric.data.find(item => {
    return formatDateToString(item.date) === formatDateToString(props.selectedDate)
  })
  
  if (isEmpty(currentData) || isEmpty(projectedData)) return null
  
  return {
    currentValue: currentData.metric, // Daily current rate value
    projectedValue: projectedData.metric, // Daily projected value for P vs A 
  }
})

const computedDayLevelDelta = computed(() => {
  const { projectedValue, currentValue } = computedDailyMetrics.value
  if (!projectedValue || !currentValue) {
    return {
      class: getProjectedValueClass(0),
      delta: '0%'
    }
  }
  
  const percentage = getProjectedVsActualPercentage(projectedValue, currentValue)
  
  return {
    class: getProjectedValueClass(percentage),
    delta: `${percentage > 0 ? '+' : ''}${percentage}%`
  }
})

const computedHourLevelDelta = computed(() => {
  const { hourMinusOneProjectedValue, hourMinusOneActualValue } = computedHourlyMetrics.value || {}
  
  if (!hourMinusOneProjectedValue || !hourMinusOneActualValue) {
    return {
      class: getProjectedValueClass(0),
      delta: '0%'
    }
  }
  
  const percentage = getProjectedVsActualPercentage(hourMinusOneProjectedValue, hourMinusOneActualValue)
  
  return {
    class: getProjectedValueClass(percentage),
    delta: `${percentage > 0 ? '+' : ''}${percentage}%`
  }
})

const shouldShowDailyMetrics = computed(() => {
  return isCityMetric.value && computedDailyMetrics.value !== null
})

const shouldShowHourlyMetrics = computed(() => {
  return isCityMetric.value && computedHourlyMetrics.value !== null && isDateToday(props.selectedDate) && props.metricName === 'Cart Volume'
})

const labels = computed(() => {
  const { isFullDay, currentHour, hourMinusOne } = computedHourlyMetrics.value || {}
  
  return {
    currentValueLabel: isFullDay ? `Current Value (A) (till hour ${currentHour}):` : `Current Value (A) (Hour ${currentHour}):`,
    projectedVsActualLabel: isFullDay ? `P vs A (till hour ${hourMinusOne})` : `P vs A (Hour ${hourMinusOne})`
  }
})




const getGroupParam = () => {
  // pan india won't ever be clicked..
  const clickedGroup = props.computedGroupsAndCitiesList?.find(group => group.name === props.entityName)
  return clickedGroup?.type === 'city' ? { city: clickedGroup.name } : { group_id: clickedGroup.id }
}


const getMetricPromise = () => {
  //todo: cancel token not used to cancel
  const metricsCancelTokenSource = axios.CancelToken.source()

  let params =
    props.currentSelection?.enable_group_view ? {
      group_id: props.currentSelection?.group_id,
      date: props.selectedDate,
      hour: props.currentSelection?.hour,
      enable_group_view: props.currentSelection?.enable_group_view,
      enable_zone_view: false,
      cancelToken: metricsCancelTokenSource.token,
    } :
      {
        date: props.selectedDate,
        hour: props.currentSelection?.hour,
        enable_zone_view: false,
        cancelToken: metricsCancelTokenSource.token,
      }

  if (props.currentSelection?.enable_group_view) {
    const groupParam = getGroupParam()
    params = { ...params, ...groupParam }

  } else {
    if (props.currentSelection?.city) {
      params['city'] = props.currentSelection?.city
      params['zone'] = props.entityName
    } else {
      params['city'] = props.entityName
    }
  }

  if (["Cart Volume", "GMV", "AOV", "Surge Checkouts %", "Rain Order %"].includes(props.metricName)) {
    return { api: api.fetchStoreMetrics, params }
  } else if (["DAU Conversion Percentage", "Daily Active Users", "Demand Based Block %", "Unserviceable DAU %", "Express OOH Based Block %", "Express Manual Based Block %"].includes(props.metricName)) {
    return { api: api.fetchStoreDauMetrics, params }
  } else if (["Rider Login hrs"].includes(props.metricName)) {
    return { api: api.fetchStoresRiderMetrics, params }
  } else if (["New Riders"].includes(props.metricName)) {
    return { api: api.fetchStoresNewRiderMetrics, params }
  }

  return {}
}


const convertToDrillDownResponse = (storeDateMetrics) => {
  if (!storeDateMetrics || !Array.isArray(storeDateMetrics.metrics)) {
    console.warn("Invalid or missing storeDateMetrics");
    return null;
  }

  const { metricName, selectedDate } = props;


  const currentData = storeDateMetrics?.metrics?.find(m => {
    return formatDateToString(m.date) === formatDateToString(selectedDate);
  });

  const previousData = storeDateMetrics?.metrics?.find(m => {
    return formatDateToString(m.date) !== formatDateToString(selectedDate);
  });

  if (!currentData || !previousData) {
    return {
      metric_name: metricName,
      current_date: formatDateToString(selectedDate),
      comparison_date: previousData ? formatDateToString(previousData.date) : null,
      summary: {
        red_count: 0,
        yellow_count: 0,
        green_count: 0
      },
      entities: []
    };
  }

  const entityMap = {};

  function getMetricValue(storeMetric) {
    const metricObj = Array.isArray(storeMetric.data)
      ? storeMetric.data.find(m => m.name === metricName)
      : null;

    if (!metricObj) return null;

    const value = parseFloat(metricObj.metric);
    return isNaN(value) ? null : value;
  }

  for (const storeMetric of previousData.data || []) {
    const merchantId = storeMetric.frontend_merchant_id;
    if (!merchantId) continue;

    entityMap[merchantId] = {
      name: storeMetric.frontend_merchant_name || "",
      previous_value: getMetricValue(storeMetric),
    };
  }

  const entities = [];
  let red_count = 0, yellow_count = 0, green_count = 0;

  for (const storeMetric of currentData.data || []) {
    const merchantId = storeMetric.frontend_merchant_id;
    const merchantName = storeMetric.frontend_merchant_name || "";
    const type = storeMetric?.data?.find(m => m.name === metricName)?.type;

    if (!merchantId) continue;

    const current_value = getMetricValue(storeMetric);
    const previous_value = entityMap[merchantId]?.previous_value;

    if (current_value == null || previous_value == null) continue;

    let percentage_change = 0;

    if (type === 'percentage') {
      // Show absolute difference for percentage type
      percentage_change = Number((current_value - previous_value).toFixed(2));
    } else {
      // Show percentage diff as usual
      if (previous_value !== 0) {
        percentage_change = ((current_value - previous_value) / previous_value) * 100;
      } else {
        percentage_change = current_value == 0 ? 0 : 100;
      }
      percentage_change = Number(percentage_change.toFixed(2));
    }

    let category = "yellow";
    if (percentage_change < -5) category = isReverse.value ? "green" : "red";
    else if (percentage_change > 5) category = isReverse.value ? "red" : "green";

    if (category === "red") red_count++;
    else if (category === "yellow") yellow_count++;
    else green_count++;

    let impact_magnitude = null;
    const merticType = storeMetric?.data?.find(m => m.name === metricName)?.type;

    if (merticType !== 'percentage') {
      impact_magnitude = current_value - previous_value;
    }


    entities.push({
      name: merchantName,
      current_value,
      previous_value,
      percentage_change,
      category,
      impact_magnitude
    });
  }

  return {
    metric_name: metricName,
    current_date: formatDateToString(selectedDate),
    comparison_date: formatDateToString(previousData.date),
    summary: {
      red_count,
      yellow_count,
      green_count
    },
    entities
  };
};

// Methods
const fetchDrillDownData = async () => {
  if (!props.visible || !props.metricName) return

  loading.value = true
  error.value = null

  try {
    const { api, params } = getMetricPromise()
    const metricsCancelTokenSource = axios.CancelToken.source()
    const drillDownMetrics = api({ ...params })
    const projectionMetrics = isCityMetric.value ? apiV2.fetchCurrentRateAllMetrics(props.entityName, metricsCancelTokenSource.token) : Promise.resolve()
    const [drillDownMetricsResponse, projectionMetricsResponse] = await Promise.all([
      drillDownMetrics,
      projectionMetrics
    ])

    if (drillDownMetricsResponse?.data) {
      const mutatedResponse = convertToDrillDownResponse(drillDownMetricsResponse?.data)
      drillDownData.value = mutatedResponse
    }
    
    if (projectionMetricsResponse?.data) {
      if (projectionMetricsResponse.data.daily_metrics) {
        dailyMetrics.value = projectionMetricsResponse.data.daily_metrics
      }
      
      if (isDateToday(props.selectedDate) && projectionMetricsResponse.data.hourly_metrics) {
        hourlyMetrics.value = projectionMetricsResponse.data.hourly_metrics
      }
    }

  } catch (err) {
    console.error('Error fetching drill-down data:', err)
    error.value = err.response?.data?.detail || 'Failed to load drill-down data'
  } finally {
    loading.value = false
  }
}

const formatValue = (value) => {
  if (typeof value === 'string') return value
  if (typeof value === 'number') {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M'
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K'
    }
    return value.toFixed(0)
  }
  return value
}

const getProjectedVsActualPercentage = (projectedValue, actualValue) => {
  if (!projectedValue || !actualValue || projectedValue === 0) return 0
  const percentage = ((actualValue - projectedValue) / projectedValue) * 100
  return parseFloat(percentage.toFixed(2))
}

const getPercentageClass = (percentage) => {
  if (isReverse.value) {
    if (percentage < -5) return 'text-green-600'
    if (percentage > 5) return 'text-red-600'
    return 'text-yellow-600'
  }

  if (percentage < -5) return 'text-red-600'
  if (percentage > 5) return 'text-green-600'
  return 'text-yellow-600'
}

const getCategoryIndicatorClass = (category) => {
  switch (category) {
    case 'red': return 'bg-red-500'
    case 'yellow': return 'bg-yellow-500'
    case 'green': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}

const getCategoryTitle = () => {
  const categoryLen = selectedCategory.value?.length

  switch (categoryLen) {
    case 1, 2: return 'Selected Entities'
    default: return 'All Entities'
  }
}

const updateCategory = (category) => {
  if (selectedCategory.value?.includes(category)) {
    selectedCategory.value = selectedCategory.value.filter(c => c !== category)
  } else {
    selectedCategory.value.push(category)
  }
}

// Watchers
watch(() => [props.visible, props.metricName, props.selectedDate, props.currentSelection?.hour],
  ([newVisible, newMetricName, newSelectedDate, newHour], [oldVisible]) => {
    if (newVisible) {
      openDialog.value = true

      // Reset category selection when popup opens or data changes
      if (!oldVisible) {
        selectedCategory.value = ['red']
      }
      fetchDrillDownData()
    } else {
      openDialog.value = false
      drillDownData.value = null
      error.value = null
      dailyMetrics.value = null
      hourlyMetrics.value = null
    }
  }
)
</script>

<style scoped>
/* Custom scrollbar for entity list */
.max-h-96::-webkit-scrollbar {
  width: 6px;
}

.max-h-96::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-96::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
