<template>
    <Accordion :activeIndex="activeIndex" @tab-open="handleTabOpen" @tab-close="handleTabClose"
        class="mt-4 bg-transparent">
        <AccordionTab :key="metricKey" class="bg-transparent">
            <template #header>
                <div class="flex justify-between items-center w-full">
                    <div class="flex items-center gap-2 md:gap-3">
                        <i :class="metricData.icon" style="color: green;"></i>

                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">
                            {{ metricKey }}
                        </div>
                    </div>

                    <i v-if="loading && activeIndex === 0" class="pi pi-spin pi-spinner mr-2" style="color: blue;"></i>
                </div>
            </template>

            <div v-if="loading" class="flex justify-center items-center p-4">
                loading...
            </div>

            <div v-else-if="error" class="text-center p-4">
                <span class="text-sm md:text-md font-bold text-red-700">
                    Failed to load metrics. Please try again.
                </span>
            </div>

            <div v-else>
                <div v-if="!computedMetrics?.length" class="flex justify-center items-center p-4">
                    Data not present
                </div>
                <div v-else class="relative">
                    <!-- Scrollable container for city/store metrics -->
                    <div ref="scrollContainer" @scroll.passive="checkScrollEnd"
                        class="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
                        <div class="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 lg:gap-4 pr-2 pb-4">
                            <MetricBoxV2 v-for="metric in computedMetrics" :key="metric.key"
                                :name="metric?.[computedMetricItemKey]" :curr="metric.curr" :prev="metric.prev"
                                :isReverse="computedIsReverse" :isShortenedNumber="metric?.curr?.type === 'currency'"
                                :isDrillDownEnabled="isDrillDownSupported" @onDrillDown="handleDrillDown" />
                        </div>
                    </div>

                    <!-- Scroll indicator -->
                    <span v-if="showScrollIndicator"
                        class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-white to-transparent pointer-events-none flex items-end justify-center pb-1 backdrop-blur-sm">
                        <span class="text-xs text-gray-500 bg-white px-2 rounded flex items-center text-center">
                            <i class="pi pi-angle-down"></i>
                            <span>Scroll for more</span>
                        </span>
                    </span>
                </div>
            </div>
        </AccordionTab>
    </Accordion>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { cityMetrics, storeMetrics, metricsForStyleReversal } from '../../../utils/metrics'
import MetricBoxV2 from '../../v2/MetricBoxV2.vue'
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab';

// Props
const props = defineProps({
    isDefaultOpen: {
        type: Boolean,
        default: false
    },
    metricKey: {
        type: String,
        required: true
    },
    apiKey: {
        type: String,
        required: true
    },
    icon: {
        type: String,
        required: true
    },
    loading: {
        type: Boolean,
        required: true
    },
    error: {
        type: Boolean,
        required: true
    },
    data: {
        type: Array,
        default: () => []
    },
    fetchData: {
        type: Function,
        required: true
    },
    metricData: {
        type: Object,
        required: true
    },
    accIdx: {
        type: Number,
        required: true
    },
    openAccordions: {
        type: Array,
        default: () => []
    },
    triggerFetchApi: {
        type: String,
        required: true
    },
    cancelPreviousRequests: {
        type: Function,
        required: true
    },
    computedDate: {
        type: String,
        required: true
    },
    computedIsPanIndiaState: {
        type: Boolean,
        required: true
    },
    sortingOrder: {
        type: Array,
        default: () => []
    },
    zoneCode: {
        type: String,
        default: null
    },
    groupView: {
        type: Boolean,
        default: false
    }
})

// Reactive data
const activeIndex = ref(null)
const intervalId = ref("")
const scrollContainer = ref(null)
const showScrollIndicator = ref(false)

const emit = defineEmits(['updateOpenAccordion', 'updateClosedAccordion', 'updateSortingOrder', 'drillDown'])

// Computed
const computedMetricItemKey = computed(() => {
    return props.computedIsPanIndiaState ? 'city' : 'frontend_merchant_name'
})

const isDrillDownSupported = computed(() => {
    if (props.computedIsPanIndiaState || (!props.groupView && !props.zoneCode)) return true
    return false
})

const computedMetrics = computed(() => {
    const metricsFunc = (metrics, metricName) => {
        return props.computedIsPanIndiaState ? cityMetrics(metrics, metricName) : storeMetrics(metrics, metricName);
    }

    if (props.metricKey === "Cart Volume") {
        const cartMetrics = metricsFunc(props.data, "Cart Volume");

        // update sorting order
        const sortingOrder = cartMetrics?.map(metric => metric?.[computedMetricItemKey.value]);
        emit('updateSortingOrder', sortingOrder)
        return cartMetrics
    } else {
        const calculateMetricData = metricsFunc(props.data, props.metricKey);

        const sortedCalculateMetricData = calculateMetricData.sort((a, b) => {
            const indexA = props.sortingOrder?.indexOf(a?.[computedMetricItemKey.value]);
            const indexB = props.sortingOrder?.indexOf(b?.[computedMetricItemKey.value]);
            // Items not in sortingOrder go to the end
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;
            return indexA - indexB;
        });

        return sortedCalculateMetricData
    }
})

const computedIsReverse = computed(() => {
    return metricsForStyleReversal.includes(props.metricKey)
})

const fetchApi = computed(() => props.triggerFetchApi)

// Methods
const handleTabOpen = (val) => {
    activeIndex.value = 0
    emit('updateOpenAccordion', props.metricKey)
    props.cancelPreviousRequests(props.apiKey)

    props.fetchData(props.apiKey)
    intervalId.value = window.setInterval(() => {
        props.fetchData(props.apiKey)
    }, 120000)

    nextTick(() => checkScrollEnd())
}

const handleTabClose = (val) => {
    activeIndex.value = null
    emit('updateClosedAccordion', props.metricKey)
    clearInterval(intervalId.value)
    props.cancelPreviousRequests(props.apiKey)
}

const checkScrollEnd = () => {
    if (!scrollContainer.value) return
    const el = scrollContainer.value
    const scrollable = el.scrollHeight > el.clientHeight
    const atBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - 1
    showScrollIndicator.value = scrollable && !atBottom
}

const handleDrillDown = (drillDownData) => {
    emit('drillDown', {
        ...drillDownData,
        metricName: props.metricKey,        // Override with correct metric name
        entityName: drillDownData.metricName // Store the entity name separately
    })
}

// Watchers
watch(fetchApi, () => {
    if (activeIndex.value === 0) {
        props.cancelPreviousRequests(props.apiKey)
        props.fetchData(props.apiKey)

        nextTick(() => checkScrollEnd())
    }
})

watch(computedMetrics, async () => {
    await nextTick()
    checkScrollEnd()
})

onMounted(() => {
    const isDefaultOpen = props.isDefaultOpen
    const isAlreadyOpen = props.openAccordions?.includes(props.metricKey)

    if (isDefaultOpen || isAlreadyOpen) {
        activeIndex.value = 0
        props.fetchData(props.apiKey)
        nextTick(() => {
            intervalId.value = window.setInterval(() => {
                props.fetchData(props.apiKey)
            }, 120000)
            checkScrollEnd()
        })
    }

    window.addEventListener('resize', checkScrollEnd)
})

onBeforeUnmount(() => {
    clearInterval(intervalId.value)
    props.cancelPreviousRequests(props.apiKey)
    window.removeEventListener('resize', checkScrollEnd)
})

defineExpose({
    activeIndex,
    handleTabOpen,
    handleTabClose,
    computedMetrics,
    intervalId,
    computedMetricItemKey,
    computedIsReverse
})

</script>


<style scoped>
/* Base accordion styles with enhanced glass effect */
:deep(.p-accordion-tab) {
    @apply mb-6 rounded-2xl overflow-hidden;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Header styles with Blinkit theme */
:deep(.p-accordion-header) {
    background: rgba(255, 255, 255, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    border-left: 3px solid #00D290;
    /* Blinkit green */
}

:deep(.p-accordion-header:not(.p-disabled)) {
    &:hover {
        background: rgba(0, 210, 144, 0.05);
        /* Blinkit green with low opacity */
        transform: translateY(-2px);
    }

    &:active {
        transform: translateY(0);
        background: rgba(0, 210, 144, 0.1);
    }
}

/* Header link styles */
:deep(.p-accordion-header-link) {
    @apply p-5 font-medium;
    display: flex;
    align-items: center;
    color: #00D290 !important;
    /* Blinkit green */
    background: linear-gradient(135deg, rgba(0, 210, 144, 0.05), rgba(255, 255, 255, 0.05));
}

/* Content styles */
:deep(.p-accordion-content) {
    @apply p-6;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

/* Active state styles */
:deep(.p-accordion-tab-active) {
    .p-accordion-header {
        background: rgba(0, 210, 144, 0.08);
        border-bottom-color: rgba(0, 210, 144, 0.2);
    }
}

/* Animation effects */
:deep(.p-accordion-tab) {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 0 12px 40px 0 rgba(0, 210, 144, 0.15),
            inset 0 0 0 1px rgba(0, 210, 144, 0.1);
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    :deep(.p-accordion-tab) {
        @apply mb-4;
        backdrop-filter: blur(12px);
    }

    :deep(.p-accordion-header-link) {
        @apply p-4 min-h-[56px];
        -webkit-tap-highlight-color: transparent;
    }

    :deep(.p-accordion-content) {
        @apply p-4;
    }
}

/* Custom scrollbar for content */
:deep(.p-accordion-content)::-webkit-scrollbar {
    width: 6px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-track {
    background: rgba(0, 210, 144, 0.1);
    border-radius: 3px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-thumb {
    background: rgba(0, 210, 144, 0.2);
    border-radius: 3px;
}

/* Custom scrollbar for metric sections */
.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.scrollbar-thumb-gray-300:hover::-webkit-scrollbar-thumb {
    background: #9ca3af;
}
</style>