<template>
    <MetricCollapse v-for="(metricData, accIdx) in COLLAPSIBLE_METRIC_LIST" :key="metricData.metricKey"
        :isDefaultOpen="metricData.isDefaultOpen" :metricKey="metricData.metricKey" :apiKey="metricData.apiKey"
        :icon="metricData.icon" :loading="loading?.[metricData?.apiKey] || false" :error="error?.[metricData?.apiKey] || false"
        :data="data[metricData.apiKey]" :fetchData="fetchData" :metricData="metricData" :accIdx="accIdx"
        :openAccordions="openAccordions" :cancelPreviousRequests="cancelPreviousRequests"
        :triggerFetchApi="triggerFetchApi" :computedDate="computedDate"
        :computedIsPanIndiaState="computedIsPanIndiaState" :sortingOrder="sortingOrder" :zoneCode="zoneCode"
        :groupView="groupView" @updateSortingOrder="updateSortingOrder"
        @updateOpenAccordion="$emit('updateOpenAccordion', $event)"
        @updateClosedAccordion="$emit('updateClosedAccordion', $event)" @drillDown="$emit('drillDown', $event)" />
</template>

<script setup>
import { ref, computed, reactive, defineExpose } from 'vue'
import axios from "axios"
import MetricCollapse from "./MetricCollapse.vue"
import { COLLAPSIBLE_METRIC_LIST } from '../../../constants/sonar/citiesStores.js'
import { api } from '../../../api/index.js'
import { formatDateToString, isDateToday } from '../../../utils/utils.js'

// Props
const props = defineProps({
    cityCode: {
        type: String,
        required: true
    },
    zoneCode: {
        type: String,
        default: null
    },
    groupId: {
        type: String,
        default: null
    },
    groupView: {
        type: Boolean,
        default: false
    },
    selectedDate: {
        type: Date,
        required: true
    },
    selectedHour: {
        type: Number,
        default: -1
    },
    showHeader: {
        type: Boolean,
        default: true
    },
    fetchCityMetrics: {
        type: Boolean,
        default: false
    },
    isPanIndia: {
        type: Boolean,
        default: false
    },
    computedGroup: {
        type: Object,
        default: null
    },
    sortingOrder: {
        type: Array,
        default: []
    },
    openAccordions: {
        type: Array,
        default: []
    }
})

// Emits
const emit = defineEmits(['updateOpenAccordion', 'updateClosedAccordion', 'drillDown'])

// Reactive data
const sortingOrder = ref([])
const cancelTokens = ref({})

// COMPUTED
const computedIsPanIndiaState = computed(() => {
    return props.groupView ? props.computedGroup?.isPanIndia : props.isPanIndia
})

const computedDate = computed(() => {
    return formatDateToString(props.selectedDate)
})

const computedIsNotToday = computed(() => {
    return !isDateToday(props.selectedDate)
})

// Manual trigger for API calls - not reactive to props
const triggerFetchApi = ref(`${Date.now()}`)

// Method to manually trigger API refresh
const refreshMetrics = () => {
    triggerFetchApi.value = `${props.cityCode}-${props.selectedHour}-${props.zoneCode}-${props.selectedDate}-${props.groupId}-${props.groupView}-${Date.now()}`
}

// Methods
const updateSortingOrder = (order) => {
    sortingOrder.value = order
}

const getMetricApiAndParams = (key, cancelToken) => {
    const params = {
        city: {
            city: props.cityCode,  // ✅ Added missing city parameter
            date: computedDate.value,  // Use formatted date string instead of Date object
            hour: props.selectedHour,
            enable_group_view: props.groupView
        },
        store: {
            city: props.cityCode,
            zone: props.zoneCode,
            date: computedDate.value,  // Use formatted date string instead of Date object
            hour: props.selectedHour,
            group_id: props.groupId,
            enable_group_view: props.groupView
        }
    };

    switch (key) {
        case "order_metrics":
            return props.fetchCityMetrics
                ? { apiFn: api.fetchCityMetrics, params: { ...params.city, cancelToken } }
                : { apiFn: api.fetchStoreMetrics, params: { ...params.store, cancelToken } };

        case "dau_metrics":
            return props.fetchCityMetrics
                ? { apiFn: api.fetchCityDauMetrics, params: { ...params.city, cancelToken } }
                : { apiFn: api.fetchStoreDauMetrics, params: { ...params.store, cancelToken } };

        case "rider_metrics":
            return props.fetchCityMetrics
                ? { apiFn: api.fetchCitiesRiderMetrics, params: { ...params.city, cancelToken } }
                : { apiFn: api.fetchStoresRiderMetrics, params: { ...params.store, cancelToken } };

        case "new_rider_metrics":
            return props.fetchCityMetrics
                ? { apiFn: api.fetchCitiesNewRiderMetrics, params: { ...params.city, cancelToken } }
                : { apiFn: api.fetchStoresNewRiderMetrics, params: { ...params.store, cancelToken } };

        default:
            return null;
    }
}

const cancelPreviousRequests = (apiKey) => {
    if (cancelTokens.value?.[apiKey]) {
        cancelTokens.value[apiKey].forEach((source) => {
            source.cancel('Operation canceled due to new request.')
        })
        cancelTokens.value[apiKey] = []
    }
}

function useApiFetcher() {
    const loading = reactive({})
    const error = reactive({})
    const data = reactive({})

    //issue: to handle race condition: fetchData called twice by same apiKey, marks loading true, one api gets cancelled as it is same, so loading gets false. but one call is still in the process
    const activeRequests = reactive({}) // Track number of active requests per apiKey

    async function fetchData(apiKey) {
        if (!apiKey) return

        // Initialize counters if they don't exist
        if (!(apiKey in activeRequests)) {
            activeRequests[apiKey] = 0
        }

        // Increment active request counter
        activeRequests[apiKey]++

        // Set loading to true only if it's the first request
        if (activeRequests[apiKey] === 1) {
            loading[apiKey] = true
            error[apiKey] = false
        }

        try {
            const metricsCancelTokenSource = axios.CancelToken.source()

            // Cancel previous requests for this apiKey
            cancelPreviousRequests(apiKey)

            if (!computedDate.value) {
                error[apiKey] = true
                throw new Error("Invalid date");
            }

            // Store new cancel token
            cancelTokens.value[apiKey] = [metricsCancelTokenSource]

            const { params, apiFn } = getMetricApiAndParams(apiKey, metricsCancelTokenSource.token)

            const response = await apiFn?.({ ...params })

            if (response?.data?.metrics) {
                data[apiKey] = response.data.metrics
            }
        } catch (err) {
            if (!axios.isCancel(err)) {
                error[apiKey] = true
                console.error(`Error fetching metrics for ${apiKey}:`, err)
            }
        } finally {
            // Decrement active request counter
            activeRequests[apiKey]--

            // Set loading to false only when all requests are complete
            if (activeRequests[apiKey] <= 0) {
                loading[apiKey] = false
                activeRequests[apiKey] = 0 // Reset to 0 to prevent negative values
            }
        }
    }

    return {
        loading,
        error,
        data,
        fetchData
    }
}


// Use the composable
const { loading, error, data, fetchData } = useApiFetcher()

// Expose everything needed for template access
defineExpose({
    // Props are automatically available in template
    sortingOrder,
    cancelTokens,
    loading,
    error,
    data,
    triggerFetchApi,
    COLLAPSIBLE_METRIC_LIST,
    computedIsNotToday,
    computedDate,
    computedIsPanIndiaState,
    fetchData,
    cancelPreviousRequests,
    getMetricApiAndParams,
    MetricCollapse,
    refreshMetrics
})
</script>
