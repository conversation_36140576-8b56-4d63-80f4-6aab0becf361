<template>
    <div class="mt-4">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            <div
                class="flex justify-between items-center p-5 bg-gradient-to-r from-yellow-50 to-white border-b border-gray-100">
                <div class="flex items-center gap-2 md:gap-3">
                    <i :class="computedIcon" style="color: black;"></i>
                    <div class="text-md md:text-lg font-bold lg:font-normal text-black">{{ computedTitle }}</div>
                </div>
                <i v-if="isLoading" class="pi pi-spin pi-spinner mr-2" style="color: blue;"></i>
            </div>

            <div v-if="isLoading" class="flex justify-center items-center p-8">
                <div class="text-gray-600">Loading metrics...</div>
            </div>

            <div v-else-if="isError" class="text-center p-8">
                <span class="text-sm md:text-md font-bold text-red-700">
                    Failed to load PAAS metrics. Please try again.
                </span>
            </div>

            <div v-else class="p-6">
                <div v-if="computedPaasMetrics.length === 0" class="flex justify-center items-center p-8">
                    <div class="text-gray-600">No PAAS data available</div>
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div v-for="metric in computedPaasMetrics" :key="metric.name"
                        class="bg-white/80 backdrop-blur-sm px-4 py-3 md:py-5 rounded-xl shadow-lg border border-gray-100 cursor-pointer hover:shadow-xl transition-shadow"
                        @click="handleMetricClick(metric.name)">
                        <div class="flex justify-between px-1 md:px-2">
                            <span
                                class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                                {{ metric.name }}
                                <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />

                                <i v-if="metric.name !== 'All Complaints'"
                                    class="pi pi-chart-line ml-2 text-sm text-gray-500 hover:text-blue-600"
                                    title="Click to view hourly chart"></i>
                            </span>
                            <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                                :type="metric.curr.type" />
                        </div>
                        <div class="flex justify-between items-center px-2">
                            <span class="text-xs font-light gray-900"></span>
                            <div class="flex justify-between">
                                <div v-for="diff in metric.diffs" :key="diff.date">
                                    <span class="text-xs font-[650] mr-0.5"
                                        v-bind:class="diff.style(metric.reverseStyle)">
                                        <template v-if="diff.change() !== '-'">
                                            {{ diff.change() }}
                                            {{ ABSOLUTE_METRIC_DELTA.includes(metric.name) ? '' : '%' }}
                                        </template>
                                        <template v-else>-</template>
                                    </span>
                                    <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                        class="text-xs font-bold mr-1 text-positive-metric"> »
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart Modal -->
    <Dialog v-model:visible="showChartModal" modal :header="`${selectedMetricForChart} - Hourly Trends`"
        :style="{ width: '75rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }" :dismissableMask="true">
        <div class="w-80 md:w-full">
            <PaasHourlyChart :metricName="selectedMetricForChart" :cityCode="cityCode" :storeId="storeId"
                :printType="printTypeCode" :date="date" />
        </div>
    </Dialog>

    <!-- Complaints Dialog -->
    <Dialog v-model:visible="showComplaints" modal header="All Complaints" :style="{ width: '75rem', margin: '1rem' }"
        :dismissableMask="true">
        <div class="w-80 md:w-full">
            <DataTable :value="computedComplaintsTableData" tableClass="text-xs md:text-md" showGridlines scrollable
                paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                <Column field="complaint_type" header="Complaint Type" bodyClass="p-1 md:px-4 md:py-3"
                    headerClass="p-1 md:px-4 md:py-3">
                </Column>
                <Column v-for="(date, index) in computedComplaintsTableColumns" :key="index" :field="date"
                    :header="date" sortable>
                </Column>
            </DataTable>
        </div>
    </Dialog>
</template>

<script>
import axios from 'axios';
import InfoDialog from '../InfoDialog.vue';
import AnimatedNumber from '../AnimatedNumber.vue';
import PaasHourlyChart from './PaasHourlyChart.vue';
import Dialog from 'primevue/dialog';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { MetricChange } from '../../interfaces/index.js';
import { ABSOLUTE_METRIC_DELTA, metricsForStyleReversal, sortedAllMetrics } from '../../utils/metrics.js';
import { PanIndiaConstants } from '../../constants/index.js';
import startCase from 'lodash.startcase';
import isEmpty from 'lodash.isempty';
import { isDateToday } from '../../utils/utils.js';
import { formatDateToString } from '../../utils/utils.js';

export default {
    name: 'PaasMetricGroup',

    components: {
        InfoDialog,
        AnimatedNumber,
        PaasHourlyChart,
        Dialog,
        DataTable,
        Column
    },

    props: {
        metricGroup: {
            type: Object,
            required: true
        },
        title: {
            type: String,
            required: false
        },
        icon: {
            type: String,
            required: false
        },
        cityCode: {
            type: String,
            required: true
        },
        storeObject: {
            type: Object,
            default: null
        },
        printType: {
            type: String,
            default: null
        },
        date: {
            type: String,
            required: true
        },
        showHeader: {
            type: Boolean,
            default: true
        }
    },

    data() {
        return {
            isLoading: true,
            isError: false,
            paasMetrics: [],
            allComplaints: [],
            showComplaints: false,
            showChartModal: false,
            selectedMetricForChart: '',
            cancelTokens: [],
            intervalId: null,
            ABSOLUTE_METRIC_DELTA: ABSOLUTE_METRIC_DELTA
        }
    },

    computed: {
        computedTitle() {
            return this.metricGroup?.title || 'Business';
        },

        computedIcon() {
            return this.metricGroup?.icon || 'pi pi-print';
        },

        computedIsNotToday() {
            return !isDateToday(this.date);
        },

        computedDate() {
            return formatDateToString(this.date);
        },

        computedPaasMetrics() {
            if (isEmpty(this.paasMetrics)) return []

            let allMetrics = [...this.paasMetrics];

            if (!isEmpty(this.allComplaints)) {
                const complaintsMetric = this.getComputedComplaints();
                if (complaintsMetric) {
                    allMetrics.push(complaintsMetric);
                }
            }
            return this.calculatedMetrics(allMetrics);
        },

        computedAllComplaints() {
            const complaintsData = this.allComplaints?.reduce((obj, item) => {
                const complaintType = item?.category_type || 'Uncategorized';
                obj[complaintType] = isEmpty(obj?.[complaintType]) ? [item] : [...obj[complaintType], item];
                return obj;
            }, {});

            return Object.keys(complaintsData).map((type) => ({ complaint_type: type, data: complaintsData[type] }));
        },

        computedComplaintsTableData() {
            return this.computedAllComplaints?.map(category => {
                const row = { complaint_type: startCase(category?.complaint_type || 'Uncategorized') };
                category.data?.forEach(item => {
                    row[item.date] = item.count;
                });
                return row;
            });
        },

        computedComplaintsTableColumns() {
            const dateColumns = [...new Set(this.computedAllComplaints?.flatMap(cat => cat.data?.map(d => d.date)))];
            return dateColumns?.sort((a, b) => {
                let dateA = new Date(a);
                let dateB = new Date(b);
                return dateB - dateA;
            });
        },

        printTypeCode() {
            return this.printType && this.printType !== 'all' ? this.printType : null;
        },

        storeId() {
            return this.storeObject?.frontend_merchant_id || "";
        },

        triggerFetchApi() {
            return `${this.cityCode}-${this.storeId}-${this.printType}-${this.date}`
        },
    },

    watch: {
        triggerFetchApi() {
            this.fetchPaasMetrics();
        },
    },

    methods: {
        handleMetricClick(metricName) {
            if (metricName === 'All Complaints') {
                this.showComplaints = true;
            } else {
                this.selectedMetricForChart = metricName;
                this.showChartModal = true;
            }
        },

        async fetchPaasMetrics() {
            this.isLoading = true;
            this.isError = false;
            this.cancelPreviousRequests();


            let commonParams = {
                city: this.cityCode,
                store: this.storeId,
                print_type: this.printTypeCode,
                date_str: this.date,
            };

            const metricsPromises = this.metricGroup?.metrics.map((metric) => {
                const { metricList, fetchApi } = metric;

                if (metricList) {
                    let metricsToFetch = [];
                    for (let idx = 0; idx < metricList.length; idx += 3) {
                        metricsToFetch.push(metricList.slice(idx, idx + 3));
                    }

                    return metricsToFetch.map((metrics) => {
                        const cancelToken = axios.CancelToken.source();
                        this.cancelTokens.push(cancelToken);

                        let params = {
                            ...commonParams,
                            metrics,
                            cancelToken: cancelToken.token
                        };

                        return fetchApi(params);
                    });
                } else {
                    const cancelToken = axios.CancelToken.source();
                    this.cancelTokens.push(cancelToken);

                    let params = {
                        ...commonParams,
                        cancelToken: cancelToken.token
                    };

                    return fetchApi(params);
                }
            })?.filter(Boolean);

            const results = await Promise.allSettled(metricsPromises?.flat());

            let allMetrics = [];
            let anySuccess = false;
            let anyFailure = false;

            results?.forEach((result) => {
                if (result.status === "fulfilled") {
                    const response = result.value;
                    if (response?.config?.url?.includes("complaints")) {
                        this.allComplaints = (response.data.complaints || []).map(complaint => ({
                            ...complaint,
                            complaint_type: complaint.category_type || complaint.complaint_type || 'Uncategorized'
                        }));
                    } else {
                        if (response?.data?.metrics) {
                            anySuccess = true;
                            const metrics = this.filterMetricsWithNoData(response.data.metrics);
                            allMetrics.push(...metrics);
                        }
                    }
                } else {
                    anyFailure = true;
                    const error = result.reason;
                    if (axios.isCancel(error)) {
                        console.log("Request canceled:", error.message);
                    } else {
                        console.error("Error fetching metrics batch:", error);
                    }
                }
            });

            this.paasMetrics = allMetrics;
            this.isError = !anySuccess; // Only true if ALL requests failed
            this.isLoading = false;

            this.$emit('metricsUpdated', allMetrics);
        },

        getComputedComplaints() {
            if (!this.allComplaints || this.allComplaints.length === 0) return null;

            // Aggregate all complaints by date
            const complaintsAggregatedOnDate = this.allComplaints.reduce((acc, item) => {
                if (item.date) {
                    if (acc.hasOwnProperty(item.date)) {
                        acc[item.date] += item.count ?? 0;
                    } else {
                        acc[item.date] = item.count ?? 0;
                    }
                }
                return acc;
            }, {});

            const cartVolumeDateDict = this.paasMetrics
                ?.find((m) => m.name === 'Cart Volume')
                ?.data
                ?.reduce((acc, it) => {
                    acc[it.date] = it.metric
                    return acc
                }, {})

            return {
                name: "All Complaints",
                type: "percentage",
                data: Object.entries(complaintsAggregatedOnDate).map(([key, value]) => {
                    const mutatedValue = ((value / cartVolumeDateDict?.[key]) * 100) || 0
                    return ({
                        date: key,
                        metric: mutatedValue?.toFixed(2)
                    })
                })
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach(token => {
                if (token && token.cancel) {
                    token.cancel('Request cancelled due to new request');
                }
            });
            this.cancelTokens = [];
        },

        filterMetricsWithNoData(metrics) {
            return metrics?.filter(metric => {
                // For PAAS metrics, keep all metrics even if they have zero values
                // Zero cancellations and zero complaints are meaningful data
                return metric.data && metric.data.some(dataPoint =>
                    dataPoint.metric !== null && dataPoint.metric !== "-"
                );
            }) || [];
        },

        calculatedMetrics(metrics) {
            const paasPriorityOrder = [
                "Cart Volume",
                "Order Cancellation",
                "All Complaints"
            ];
            let sortedMetrics = sortedAllMetrics(metrics, paasPriorityOrder, true, 5, this.computedDate);
            return sortedMetrics?.map(metric => {
                let data = metric.data;
                if (!data || data.length === 0) return null;

                let curr = data[data.length - 1];
                if (!curr) return null;

                // Transform curr to match expected structure for AnimatedNumber
                let transformedCurr = {
                    value: curr.value,
                    type: curr.type,
                    date: curr.meta.date
                };

                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    if (!prev) continue;

                    // Transform prev to match expected structure
                    let transformedPrev = {
                        value: prev.value,
                        type: prev.type,
                        date: prev.meta.date
                    };
                    diffs.push(new MetricChange(transformedCurr, transformedPrev, metric.name));
                }
                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: transformedCurr,
                    diffs: diffs,
                }
            }).filter(metric => metric && metric.curr && metric.curr.value !== "-");
        },

        setupAutoRefresh() {
            // Set up auto-refresh similar to Home MetricGroup
            this.intervalId = window.setInterval(() => {
                this.fetchPaasMetrics();
            }, 120000); // Refresh every 2 minutes
        }
    },

    mounted() {
        this.fetchPaasMetrics();
        this.setupAutoRefresh();
    },

    beforeUnmount() {
        // Clear interval when component is unmounted
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }

        // Cancel any pending requests
        this.cancelPreviousRequests();
    }
}
</script>

<style scoped>
/* You can add component-specific styles here if needed */
.text-positive-metric {
    color: #10B981;
    /* Emerald 600 */
}
</style>
