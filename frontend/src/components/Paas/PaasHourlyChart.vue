<template>
    <div class="max-w-7xl mx-auto pb-2 lg:pb-10 flex justify-center">
        <div class="text-gray-900 flex items-center">
            <div class="mr-2" :style="{ fontWeight: isToggleView ? '200' : '600' }">Hourly Trend</div>
            <VueToggle name="VueToggle" title="" :toggled="computedToggleState" @toggle="toggleView"
                activeColor="#F0F0F0" />
            <div :style="{ fontWeight: isToggleView ? '600' : '200' }">Daily Trend</div>
        </div>
    </div>

    <div v-if="isLoading">
        <Loading />
    </div>
    <div v-else-if="isError">
        <span class="text-sm md:text-lg font-bold text-red-700 inline-flex items-center">
            Failed to load. Please try again.
        </span>
    </div>
    <div v-else class="mt-4 bg-white border border-gray-200 rounded-lg p-2">
        <div v-if="computedToggleState && !computedIsNotToday"
            class="flex items-center w-full justify-end my-2 text-sm">
            <Checkbox v-model="forCurrentHour" :binary="true" @update:modelValue="fetchForCurrentHour" />
            <label class="ml-2"> Till current hour </label>
        </div>

        <v-chart class="h-[50vh]" :option="computedToggleState ? computedWeekOptions : computedHourlyOptions"
            autoresize />
    </div>
</template>

<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
    TooltipComponent,
    LegendComponent,
    GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import Loading from '../Loading.vue';
import { api } from '../../api/index.js';
import axios from 'axios';
import isEmpty from 'lodash.isempty';
import Checkbox from 'primevue/checkbox'
import { isDateToday } from "../../utils/utils.js";
import VueToggle from 'vue-toggle-component';

use([
    CanvasRenderer,
    LineChart,
    TooltipComponent,
    LegendComponent,
    GridComponent
]);

export default {
    components: {
        VChart,
        Loading,
        Checkbox,
        VueToggle
    },

    props: {
        metricName: {
            type: String,
            required: true
        },
        cityCode: {
            type: String,
            default: ""
        },
        storeId: {
            type: String,
            default: ""
        },
        printType: {
            type: String,
            default: ""
        },
        date: {
            type: String,
            default: ""
        }
    },

    data() {
        return {
            isLoading: false,
            isError: false,
            cancelTokens: [],
            metrics: [],
            isToggleView: false,
            forCurrentHour: true,
        }
    },

    computed: {
        computedIsNotToday() {
            return !isDateToday(this.date);
        },

        computedHourlyOptions() {
            let metricNames = [];
            let ySeries = [];

            this.metrics?.map(metric => {
                const { date = "", data: hoursData = [] } = metric || {}

                let maxHour = 24;

                if (new Date(date).getDate() === new Date(Date.now()).getDate()) {
                    maxHour = new Date(Date.now()).getHours() + 1;
                } else {
                    maxHour = 24;
                }

                let allHourData = [...Array(maxHour).keys()].map((hour) => {
                    let hourData = hoursData.find((item) => item?.hour === hour)?.data;
                    if (isEmpty(hourData)) {
                        hourData = [{ metric: 0 }]
                    }
                    return ({ hour, data: hourData })
                })

                let metricData = allHourData?.sort((a, b) => {
                    return a.hour - b.hour;
                });

                let yDataPoints = [];
                metricData?.map(point => {
                    yDataPoints.push(point?.data?.[0]?.metric);
                });

                metricNames.push(date);

                ySeries.push({
                    name: date,
                    type: 'line',
                    smooth: true,
                    data: yDataPoints
                });
            });

            let xAxisPoints = [...Array(24).keys()].map(hour => hour);

            metricNames = this.sortDate(metricNames);

            return {
                tooltip: {
                    trigger: 'axis',
                    order: 'valueDesc'
                },
                legend: {
                    data: metricNames
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxisPoints
                },
                yAxis: {
                    type: 'value'
                },
                series: ySeries
            }
        },

        computedWeekOptions() {
            let metricNames = [];
            let ySeries = [];
            let xAxisPoints = [];

            this.metrics?.map(metric => {
                const { name = "", data = [] } = metric || {};

                const metricData = this.sortDate(data, false);

                let yDataPoints = [];

                metricData?.map(point => {
                    yDataPoints.push(point?.metric);
                    xAxisPoints.push(point?.date);
                });

                ySeries.push({
                    name: name,
                    type: 'line',
                    smooth: true,
                    data: yDataPoints
                });

                metricNames.push(name);
            });

            return {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: metricNames
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxisPoints
                },
                yAxis: {
                    type: 'value'
                },
                series: ySeries
            }
        },

        computedToggleState() {
            return this.isToggleView;
        },

        computedForCurrentHour() {
            return !this.computedIsNotToday && this.forCurrentHour
        }
    },

    watch: {
        metricName: {
            handler() {
                this.fetchAllMetrics();
            },
            immediate: true
        },
    },

    methods: {
        async fetchAllMetrics() {
            if (!this.metricName) return;

            this.isLoading = true;
            this.isError = false;

            try {
                const cancelToken = axios.CancelToken.source();
                this.cancelTokens.push(cancelToken);

                const metricKeyMap = {
                    'Cart Volume': 'order_count',
                    'Order Cancellation': 'cancellation_percentage',
                };

                const metricKey = metricKeyMap[this.metricName];
                if (!metricKey) {
                    console.error('Unknown metric name:', this.metricName);
                    this.isError = true;
                    return;
                }

                const params = {
                    metrics: [metricKey],
                    city: this.cityCode,
                    store: this.storeId,
                    print_type: this.printType && this.printType !== 'all' ? this.printType : null,
                    date_str: this.date,
                    cancelToken: cancelToken.token
                }

                const response = this.isToggleView
                    ? await api.fetchPaasMetrics({
                        ...params, is_daywise: true,
                        current_hour: this.computedForCurrentHour
                    })
                    : await api.fetchPaasHourlyMetrics({ ...params })

                if (response) {
                    this.metrics = response.data.metrics || [];
                } else {
                    this.metrics = [];
                }
            } catch (error) {
                if (!axios.isCancel(error)) {
                    console.error('Error fetching PAAS hourly data:', error);
                    this.isError = true;
                }
            } finally {
                this.isLoading = false;
            }
        },

        sortDate(metricDates = [], isDateArray = true) {
            //* isDateArray --> metricDates is an array of dates only i.e. ['2024-08-10', '2024-08-09', '2024-08-11'];
            if (isDateArray) {
                return metricDates?.sort((a, b) => {
                    var dateA = new Date(a);
                    dateA = dateA.getTime();

                    var dateB = new Date(b);
                    dateB = dateB.getTime();

                    return dateA - dateB;
                })
            }
            //* else it is an array of objects having dates --> metricDates is an array of dates only i.e. [{date:'2024-08-10'}, {date:'2024-08-09'}, {date:'2024-08-11'}];
            return metricDates?.sort((a, b) => {
                var dateA = new Date(a?.date);
                dateA = dateA.getTime();

                var dateB = new Date(b?.date);
                dateB = dateB.getTime();

                return dateA - dateB;
            })
        },

        toggleView(isToggled) {
            this.isToggleView = isToggled;
            this.isLoading = true;
            this.isError = false;
            this.fetchAllMetrics();
        },

        fetchForCurrentHour() {
            this.isLoading = true;
            this.isError = false;
            this.fetchAllMetrics();
        }
    },

    beforeUnmount() {
        // Cancel any pending requests
        this.cancelTokens.forEach(source => {
            if (source && source.cancel) {
                source.cancel('Component unmounted');
            }
        });
    }
}
</script>
