<template>
    <div class="card">
        <h3 v-if="header" class="text-lg font-bold mb-2">{{ header }}</h3>
        <div class="p-4 sm:p-2 md:p-1 lg:text-base sm:text-xs md:text-base">
            <DataTable paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 30]" tableStyle="width: 100%"
                currentPageReportTemplate="{first} to {last} of {totalRecords}" resizableColumns
                columnResizeMode="expand" scrollable selectionMode="single" showGridlines
                v-if="tableData && tableData.length" :value="tableData" :sortMode="'multiple'" removableSort>

                <!-- Column for City/Store -->
                <Column :field="labelField" :header="labelHeader" frozen sortable class="text-wrap whitespace-normal">
                </Column>

                <!-- Column for Value -->
                <Column field="valueNumeric" header="Value" sortable>
                    <template #body="slotProps">
                        <div class="flex align-items-center gap-2 lg:text-base sm:text-xs md:text-base">
                            <AnimatedNumber :value="slotProps.data.value" :type="slotProps.data.type" />
                        </div>
                    </template>
                </Column>

                <!-- Column for Percentage Change -->
                <Column field="diffNumeric" header="Change %" sortable>
                    <template #body="slotProps">
                        <div class="flex align-items-center gap-2 lg:text-base sm:text-xs md:text-base">
                            <div v-if="slotProps.data.diff !== undefined && slotProps.data.diff !== null">
                                <AnimatedNumber :value="slotProps.data.diff" type="percentage"
                                    :class="slotProps.data.diffClass" />
                                <span :class="slotProps.data.arrowClass"></span>
                            </div>
                            <div v-else>-</div>
                        </div>
                    </template>
                </Column>
            </DataTable>
            <div v-if="!tableData || tableData.length === 0" class="text-center p-4">
                <span class="text-sm font-medium text-gray-500">No data available</span>
            </div>
        </div>
    </div>
</template>

<script>
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import AnimatedNumber from '../AnimatedNumber.vue';
import { MetricChange } from "../../interfaces";

export default {
    name: 'MetricTable',

    components: {
        DataTable,
        Column,
        AnimatedNumber
    },
    props: {
        metrics: {
            type: Array,
            required: true
        },
        isPanIndiaView: {
            type: Boolean,
            required: true
        },
        header: {
            type: String,
            default: ""
        },
        isReverse: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        labelField() {
            return this.isPanIndiaView ? 'city' : 'frontend_merchant_id';
        },
        labelHeader() {
            return this.isPanIndiaView ? 'City' : 'Store';
        },
        tableData() {
            if (!this.metrics || !Array.isArray(this.metrics)) {
                return [];
            }

            return this.metrics.map(item => {
                const valueDiff = this.calculateDiff(item);

                return {
                    [this.labelField]: this.isPanIndiaView ? item.city : (item.frontend_merchant_name || item.frontend_merchant_id),
                    value: item.curr?.value,
                    valueNumeric: parseFloat(item.curr?.value) || 0, // Add numeric version for sorting
                    type: item.curr?.type,
                    diff: valueDiff.value,
                    diffNumeric: parseFloat(valueDiff.value) || 0, // Add numeric version for sorting
                    diffClass: valueDiff.class,
                    arrowClass: valueDiff.arrowClass
                };
            });
        }
    },
    methods: {
        diff(item) {
            let val = new MetricChange(item.curr, item.prev).change();
            if (val === "-") return val;
            return parseFloat(new MetricChange(item.curr, item.prev).change());
        },

        style(item) {
            var metricChange = new MetricChange(item.curr, item.prev);
            var diff_style = metricChange.style(this.isReverse);

            return [
                'font-semibold',
                'text-sm',
                'mt-auto',
                diff_style
            ];
        },

        arrowClass(diffPercentage) {
            if (this.isReverse) {
                return diffPercentage > 0
                    ? ['text-red-600', 'pi pi-arrow-up', 'text-xs']
                    : ['text-green-600', 'pi pi-arrow-down', 'text-xs']
            }

            return diffPercentage > 0
                ? ['text-green-600', 'pi pi-arrow-up', 'text-xs']
                : ['text-red-600', 'pi pi-arrow-down', 'text-xs']
        },

        calculateDiff(item) {
            const diff = this.diff(item)
            return {
                value: parseFloat(diff).toFixed(1),
                class: this.style(item),
                arrowClass: this.arrowClass(diff)
            };
        }
    }
};
</script>