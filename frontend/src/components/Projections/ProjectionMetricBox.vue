<template>
  <div class="border px-2 py-1 border-gray-200 bg-gray-50 md:bg-gray-100">
    <div class="flex justify-between items-center">
      <span class="font-normal text-gray-500 text-xs md:text-sm">{{ name }}</span>

      <span v-if="computedHasCurrentHourProperty">
        <i v-if="name < computedCurrentHour" class="pi pi-check" style="color: green;"></i>
        <i v-else-if="name == computedCurrentHour" class="pi pi-spin pi-spinner" style="color: blue;"></i>
      </span>
    </div>

    <div class="flex justify-between mt-1">
      <div class="flex items-center">
        <span class="text-xs mr-2 text-gray-600">{{ metricLabel }}</span>
        <AnimatedNumber :styles="['font-medium', 'text-sm', 'md:text-base']" :value="curr.value" :type="curr.type"
          :isShortenedNumber="isShortenedNumber" />
      </div>

      <div class="flex items-center">
        <span class="text-xs mr-2 text-gray-600">{{ diffLabel }}</span>
        <AnimatedNumber :styles="[style, 'text-right']" :value="diff" type="percentage" />
      </div>
    </div>
  </div>
</template>


<script>

import AnimatedNumber from "../AnimatedNumber.vue";
import { Metric, MetricChange } from "../../interfaces/";

export default {

  components: {
    AnimatedNumber,
  },

  computed: {
    diff() {
      let val = new MetricChange(this.curr, this.prev).change();
      if (val === "-") return val;
      return parseFloat(new MetricChange(this.curr, this.prev).change());
    },

    style() {
      var metricChange = new MetricChange(this.curr, this.prev);
      var diff_style = metricChange.style(this.isReverse);

      return [
        'font-semibold',
        'text-sm',
        diff_style
      ];
    },

    computedCurrentHour() {
      return this.currentHour
    },

    computedHasCurrentHourProperty() {
      return this.hasCurrentHourProperty
    }
  },

  props: {
    name: String,
    curr: Metric,
    prev: Metric,
    styles: Array,
    isShortenedNumber: {
      type: Boolean,
      default: false,
    },
    isReverse: {
      type: Boolean,
      default: false,
    },
    metricLabel: {
      type: String,
      default: ""
    },
    diffLabel: {
      type: String,
      default: ""
    },
    currentHour: {
      type: Number,
      default: 0
    },
    hasCurrentHourProperty: {
      type: Boolean,
      default: false
    }
  },
}

</script>
