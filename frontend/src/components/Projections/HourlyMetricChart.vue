<template>
    <div v-if="isLoading">
        <Loading />
    </div>
    <div v-else-if="isError">
        <span class="text-sm md:text-lg font-bold text-red-700 inline-flex items-center">
            Failed to load. Please try again.
        </span>
    </div>
    <div v-else>
        <v-chart class="h-[50vh]" :option="chartOption" autoresize />
    </div>
</template>

<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
    TooltipComponent,
    LegendComponent,
    GridComponent,
    MarkLineComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import Loading from '../Loading.vue';
import { apiV2 } from '../../api/index.js';
import axios from 'axios';

use([
    Can<PERSON><PERSON>enderer,
    LineChart,
    TooltipComponent,
    LegendComponent,
    GridComponent,
    MarkLineComponent
]);

export default {
    components: {
        VChart,
        Loading
    },

    props: {
        city: {
            type: String,
            default: ""
        }
    },

    data() {
        return {
            isLoading: false,
            isError: false,
            cancelTokens: [],
            hourlyMetrics: []
        }
    },

    computed: {
        chartOption() {
            if (!this.hourlyMetrics || !Array.isArray(this.hourlyMetrics)) return {};

            // Extract data from the three metric types
            const actualToday = this.hourlyMetrics.find(item => item.type === 'Cart Volume Actual Today');
            const projectedToday = this.hourlyMetrics.find(item => item.type === 'Cart Volume Projected Today');
            const wowData = this.hourlyMetrics.find(item => item.type === 'Cart Volume WoW');

            if (!actualToday || !projectedToday || !wowData) return {};

            // Create hour labels (0-23)
            const hours = Array.from({ length: 24 }, (_, i) => i);

            // Helper function to extract metrics by hour
            const extractHourlyData = (metricsData) => {
                const hourMap = {};
                if (metricsData.metrics?.[0]?.data) {
                    metricsData.metrics[0].data.forEach(hourData => {
                        const hour = hourData.hour;
                        const metric = hourData.data.find(d => d.name === 'Cart Volume')?.metric || 0;
                        hourMap[hour] = metric;
                    });
                }
                return hours.map(hour => hourMap[hour] || null);
            };

            // Helper function to calculate WoW percentage change
            const calculateWowPercentage = (current, previous) => {
                if (previous === null || previous === undefined || previous === 0) return 0;
                if (current === null || current === undefined) return 0;
                return ((current - previous) / previous * 100);
            };

            const actualData = extractHourlyData(actualToday);
            const projectedData = extractHourlyData(projectedToday);
            const wowBaseData = extractHourlyData(wowData);

            // Calculate WoW percentage for actual vs wow data
            // const wowPercentages = hours.map(hour => {
            //     const actual = actualData[hour];
            //     const wow = wowBaseData[hour];
            //     if (actual === null || actual === undefined || wow === null || wow === undefined) return null;
            //     return calculateWowPercentage(actual, wow);
            // });

            // Get current hour
            const currentHour = new Date().getHours();

            return {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                    formatter: (params) => {
                        let tooltip = `<strong>Hour: ${params[0].axisValue}</strong><br/>`;
                        params.forEach(param => {
                            if (param.value !== null && param.value !== undefined) {
                                // const value = param.seriesName === 'WoW Change'
                                //     ? (typeof param.value === 'number' ? `${param.value.toFixed(1)}%` : '0%')
                                //     : this.formatNumber(param.value);
                                const value = this.formatNumber(param.value);
                                tooltip += `${param.marker} ${param.seriesName}: ${value}<br/>`;
                            }
                        });
                        return tooltip;
                    }
                },
                legend: {
                    data: ['Actual Today', 'Projected Today', 'WoW'],
                    top: 10,
                    textStyle: {
                        fontSize: 12
                    },
                    selected: {
                        'Actual Today': true,
                        'Projected Today': true,
                        'WoW': true
                    }
                },
                grid: {
                    left: '3%',
                    right: '5%',
                    bottom: '8%',
                    top: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: hours,
                    name: 'Hour',
                    nameLocation: 'middle',
                    nameGap: 30,
                    axisLine: {
                        lineStyle: {
                            color: '#666'
                        }
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: 'Cart Volume',
                        nameLocation: 'middle',
                        nameGap: 60,
                        nameRotate: 90,
                        axisLine: {
                            lineStyle: {
                                color: '#666'
                            }
                        },
                        smooth: true,
                        axisLabel: {
                            formatter: (value) => this.formatNumber(value)
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: '#f0f0f0'
                            }
                        }
                    },
                    {
                        type: 'value',
                        name: 'WoW',
                        nameLocation: 'middle',
                        nameGap: 60,
                        nameRotate: -90,
                        position: 'right',
                        axisLine: {
                            lineStyle: {
                                color: '#666'
                            }
                        },
                        smooth: true,
                        axisLabel: {
                            // formatter: '{value}%'
                            formatter: (value) => this.formatNumber(value)
                        },
                        splitLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        name: 'Actual Today',
                        type: 'line',
                        smooth: true,
                        data: actualData,
                        lineStyle: {
                            color: '#10B981',
                            width: 3,
                            type: 'line',
                            smooth: true,
                        },
                        itemStyle: {
                            color: '#10B981',
                        },
                        z: 10,
                        symbolSize: 6,
                        yAxisIndex: 0,
                        connectNulls: false,
                        markLine: {
                            silent: true,
                            lineStyle: {
                                color: '#3B82F6',
                                opacity: 0.3,
                            },
                            data: [
                                {
                                    xAxis: currentHour,
                                    label: {
                                        formatter: 'Current Hour',
                                        position: 'insideEndTop'
                                    }
                                }
                            ]
                        }
                    },
                    {
                        name: 'Projected Today',
                        type: 'line',
                        smooth: true,
                        data: projectedData,
                        lineStyle: {
                            color: '#3B82F6',
                        },
                        itemStyle: {
                            color: '#3B82F6',
                        },
                        symbolSize: 6,
                        yAxisIndex: 0,
                        connectNulls: false,
                        emphasis: {
                            focus: 'series'
                        }
                    },
                    {
                        name: 'WoW',
                        type: 'line',
                        smooth: true,
                        // data: wowPercentages,
                        data: wowBaseData,
                        lineStyle: {
                            color: '#F59E0B',
                        },
                        itemStyle: {
                            color: '#F59E0B',
                        },
                        symbol: 'diamond',
                        symbolSize: 6,
                        // yAxisIndex: 1,
                        yAxisIndex: 0,
                        connectNulls: false,
                        emphasis: {
                            focus: 'series'
                        }
                    }
                ],
            };
        }
    },

    methods: {
        formatNumber(value) {
            if (value === null || value === undefined) return '0';
            if (typeof value !== 'number') return '0';
            if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K';
            }
            return value.toLocaleString();
        },

        calculateCurrentHourPosition(currentHour) {
            // Approximate position calculation for current hour marker
            const chartWidth = 600; // Approximate chart width
            const leftPadding = 50;
            const rightPadding = 50;
            const usableWidth = chartWidth - leftPadding - rightPadding;
            const hourWidth = usableWidth / 24;
            return leftPadding + (currentHour * hourWidth);
        },

        async fetchCartVolumeHourlyTrend() {
            try {
                const currentRateMetricsCancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [currentRateMetricsCancelTokenSource]

                let fetchCurrentRateMetrics = apiV2.fetchCurrentRateAllMetrics(this.city, currentRateMetricsCancelTokenSource.token)

                const [fetchCurrentRateMetricsResponse] = await Promise.all([
                    fetchCurrentRateMetrics
                ]);

                if (fetchCurrentRateMetricsResponse) {
                    const { hourly_metrics } = fetchCurrentRateMetricsResponse?.data || {}
                    this.hourlyMetrics = hourly_metrics
                }
            } catch (error) {
                this.isError = true;
            } finally {
                this.isLoading = false;
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },
    },

    mounted() {
        this.hourlyMetrics = []
        this.isLoading = true;
        this.isError = false;
        this.fetchCartVolumeHourlyTrend()
    }
}
</script>