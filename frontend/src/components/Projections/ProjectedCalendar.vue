<template>
    <div class="p-4 max-w-7xl mx-auto">
        <h2 class="text-2xl font-bold mb-4">Projected Metrics</h2>

        <div class="overflow-x-auto w-full md:overflow-hidden md:p-1">

            <!-- Weekday header (starting from Monday) -->
            <div class="w-[840px] md:w-full grid grid-cols-7 gap-2 text-center text-sm font-medium mb-2">
                <div v-for="(day, i) in weekDays" :key="i">{{ day }}</div>
            </div>

            <!-- Calendar grid -->
            <div class="w-[840px] md:w-full grid grid-cols-7 gap-2">
                <!-- Blank cells before first date -->
                <div v-for="n in leadingEmptyDays" :key="'empty-' + n"
                    class="bg-gray-100 border border-gray-300 rounded-lg p-2 h-full text-xs text-gray-400 flex items-start justify-start w-[120px] md:w-full">
                    <span>-</span>
                </div>

                <!-- Actual calendar cells -->
                <div v-for="(dateObj, index) in calendarData" :key="index"
                    class="rounded-lg p-2 border transition-all duration-200 h-full shadow-md w-[120px] md:w-full"
                    :class="[
                        isWeekend(dateObj.date) ? 'bg-blue-50' : 'bg-white',
                        dateObj.event?.is_event ? 'border-orange-400 !bg-orange-50' : 'border-gray-200',
                        isToday(dateObj.date) ? 'ring-2 ring-indigo-500' : ''
                    ]">
                    <div class="text-xs font-bold mb-1">{{ formatDate(dateObj.date) }}</div>
                    <div class="text-[0.65rem] text-gray-600">GMV:
                        <AnimatedNumber :value="dateObj?.gmv" type="currency"
                            :styles="['text-xs md:text-md font-semibold']" isShortenedNumber />
                    </div>
                    <div class="text-[0.65rem] text-gray-600">Carts:
                        <AnimatedNumber :value="dateObj?.cartVolume" :styles="['text-xs md:text-md font-semibold']" />
                    </div>
                    <div v-if="dateObj.event?.is_event"
                        class="mt-1 text-[0.65rem] px-1 py-0.5 bg-orange-100 text-orange-800 rounded">
                        {{ dateObj.event.event_name }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import AnimatedNumber from '../AnimatedNumber.vue'

export default {
    name: 'ProjectedCalendar',

    props: {
        metricData: {
            type: Array,
            required: true
        },

        eventData: {
            type: Array,
            required: true
        }
    },

    components: {
        AnimatedNumber
    },

    data() {
        return {
            weekDays: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            calendarData: [],
            leadingEmptyDays: 0
        }
    },

    methods: {
        prepareCalendarData() {
            const gmvData = this.metricData.find(m => m.name === 'Projected GMV')?.data || []
            const cartData = this.metricData.find(m => m.name === 'Projected Cart Volume')?.data || []

            const combined = gmvData.map(gmvEntry => {
                const cartEntry = cartData.find(c => c.date === gmvEntry.date)
                const eventEntry = this.eventData.find(e => e.date === gmvEntry.date)
                return {
                    date: gmvEntry.date,
                    gmv: gmvEntry.metric,
                    cartVolume: cartEntry?.metric || 0,
                    event: eventEntry || { is_event: false }
                }
            })

            const sorted = combined.sort((a, b) => new Date(a.date) - new Date(b.date))
            this.calendarData = sorted

            // Determine padding: how many days from Monday to first date
            const firstDate = new Date(sorted[0].date)
            let dayOfWeek = firstDate.getDay() // 0 = Sun, 1 = Mon, ..., 6 = Sat

            // Adjust for Monday start: shift Sunday (0) to 6, rest -1
            dayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1

            this.leadingEmptyDays = dayOfWeek
        },

        formatDate(dateStr) {
            const date = new Date(dateStr)
            const day = date.getDate()
            const month = date.toLocaleString('default', { month: 'short' })
            return `${day < 10 ? '0' + day : day} ${month}`
        },

        formatNumber(num) {
            return num?.toLocaleString('en-IN') || '0'
        },

        isWeekend(dateStr) {
            const day = new Date(dateStr).getDay()
            return day === 0 || day === 6
        },

        isToday(dateStr) {
            const today = new Date()
            const date = new Date(dateStr)
            return (
                date.getDate() === today.getDate() &&
                date.getMonth() === today.getMonth() &&
                date.getFullYear() === today.getFullYear()
            )
        }
    },

    mounted() {
        this.prepareCalendarData()
    }
}
</script>

<style scoped>
@media (max-width: 640px) {
    .grid-cols-7 {
        grid-template-columns: repeat(7, minmax(0, 1fr));
        font-size: 0.65rem;
    }
}
</style>