<template>
    <v-select v-if="!isList" :reduce="hourOptions => hourOptions.code" v-model="selectedHour" :options="hourOptions"
        :clearable="false" label="name" placeholder="click to select hour" outlined :class="styles" />

    <Listbox v-else id="hourSelect" v-model="selectedHour" :options="hourOptions" filter optionLabel="name" 
    :clearable="false" :class="styles" placeholder="click to select hour" />
</template>

<script>
import vSelect from 'vue-select'
import 'vue-select/dist/vue-select.css';
import Listbox from 'primevue/listbox';

export default {
    components: {
        'v-select': vSelect,
        Listbox
    },
    props: {
        styles: Array,
        isYesterday: {
            type: Boolean,
            required: true
        },
        startHour: {
            type: Number,
            required: false,
            default: 0
        },
        isList: {
            type: Boolean,
            required: false,
            default: false
        },
        insightsHour: {
            type: Number,
            required: false,
        }
    },
    computed: {
        isToday() {
            return !this.isYesterday;
        },

        currentHour() {
            return new Date().getHours();
        },

        hourOptions() {
            const hours = this.isToday ? this.currentHour + 1 : 24;
            let options = [];
            for (let i = this.startHour; i < hours; i++) {
                options.push({ name: i.toString() + " : 00", code: i });
            }
            options.unshift({ name: 'Full Day', code: -1 });
            return options;
        },

        computedInsightsHour() {
            return this.insightsHour;
        }
    },
    data() {
        return {
            selectedHour: -1
        };
    },
    watch: {
        computedInsightsHour(newVal) {
            this.selectedHour = this.hourOptions.find((hour) => hour.code === newVal);
        }
    }
};
</script>