<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError">error: outlet id not found</div>

    <div v-else class="my-10">
        <div class="text-lg font-bold text-black px-1 md:px-0 mb-2">
            Manpower Metrics - <span class="text-blue-800"> {{ computedOutletName }}</span>
        </div>

        <div class="mt-2 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 lg:gap-3">
            <div v-for="metric in calculatedFcData" :key="metric.name" class="md:bg-white px-1 py-1.5 rounded-lg">
                <div class="flex justify-between px-1 md:px-2">
                    <span
                        class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">{{
                            metric.name }}
                        <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                    </span>
                    <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                        :type="metric.curr.type" />
                </div>
                <div class="flex justify-between items-center px-2">
                    <span class="text-xs font-light gray-900"></span>
                    <div class="flex justify-between">
                        <div v-for="diff in metric.diffs" :key="diff.date">
                            <span class="text-xs font-[650] mr-0.5" v-bind:class="diff.style(metric.reverseStyle)">
                                <template v-if="diff.change() !== '-'">
                                    {{ diff.change() }}
                                    {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                </template>
                                <template v-else>
                                    -
                                </template>
                            </span>
                            <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                class="text-xs font-bold mr-1 text-positive-metric"> »
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mb-8" />

        <TimeSlots :outlet_id="computedOutletId" :outlet_name="computedOutletName" />
    </div>
</template>

<script>
import TimeSlots from './TimeSlots.vue';
import Loading from '../Loading.vue';
import axios from 'axios';
import { Metric, MetricChange } from '../../interfaces';
import { ABSOLUTE_METRIC_DELTA, formatDate, metricsForStyleReversal } from '../../utils/metrics';
import AnimatedNumber from '../AnimatedNumber.vue';
import InfoDialog from '../InfoDialog.vue';
import { api } from '../../api';

export default {

    components: {
        Loading,
        TimeSlots,
        AnimatedNumber,
        InfoDialog
    },

    props: {
        outlet_id: {
            type: Number,
            required: true
        },
    },

    data() {
        return {
            isLoading: false,
            isError: false,
            cancelTokens: [],
            fcMetrics: [],
        }
    },

    computed: {
        computedOutletId() {
            return this.outlet_id
        },

        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        computedFcData() {
            const { data: metricsData = [] } = this.fcMetrics?.[0] || {}

            const metricData = metricsData.map(metric => {
                let data = metric.data
                data.sort((a, b) => {
                    let dateA = new Date(a.date);
                    let dateB = new Date(b.date);
                    return dateA - dateB;
                });
                return {
                    name: metric.name,
                    data: data.map(x => new Metric(x.metric, metric.type, { date: x.date })),
                    ...(new Date(metric.etl_snapshot_ts_ist).toString() !== 'Invalid Date' ? { updated_at: formatDate(metric.etl_snapshot_ts_ist) } : {}),
                }
            })

            return { ...this.fcMetrics?.[0], data: metricData }
        },

        computedOutletName() {
            return this.computedFcData.outlet_name
        },

        calculatedFcData() {
            return this.computedFcData?.data?.map(metric => {
                let data = metric.data;

                let curr = data[data.length - 1];

                let diffs = [];
                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                    type: metric.type
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },
    },

    watch: {
        computedOutletId() {
            this.isLoading = true
            this.isError = false
            this.resetAllMetrics()
            this.fetchManPowerMetrics()
        }
    },

    methods: {
        async fetchManPowerMetrics() {
            this.cancelPreviousRequests();

            try {
                const manPowerMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [manPowerMetricsCancelTokenSource]

                let manPowerMetrics = api.fetchWarehouseManPowerMetrics({ outlet_id: this.computedOutletId, cancelToken: manPowerMetricsCancelTokenSource.token })

                let [manPowerMetricsResponse] = await Promise.all([manPowerMetrics])

                if (manPowerMetricsResponse) this.fcMetrics = manPowerMetricsResponse.data.metrics

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.fcMetrics = []
        },
    },

    mounted() {
        this.isLoading = true
        this.isError = false
        this.resetAllMetrics()
        this.fetchManPowerMetrics()
    },

    beforeUnMount() {
        this.cancelPreviousRequests();
    }
}
</script>