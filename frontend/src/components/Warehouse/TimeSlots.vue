<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError">error: outlet id not found</div>

    <div v-else class="my-2">
        <div class="text-lg font-bold text-black px-1 md:px-0 mb-2">
            Slot Metrics - <span class="text-blue-800"> {{ computedOutletName }}</span>
        </div>

        <Galleria v-if="computedSlots.length" v-model:activeIndex="activeIndex" :value="computedSlots"
            :responsiveOptions="responsiveOptions" :numVisible="computedSlots.length > 10 ? 10 : computedSlots.length"
            containerClass="w-full" thumbnailsPosition="top">
            <template #item="slotProps" class=" bg-transparent">
                <div class="w-full my-6 px-2 md:px-0 bg-transparent">
                    <div class="text-2xl font-semibold text-black mb-2 text-center">
                        SLOT - <span class="text-blue-800"> {{ slotProps.item.timeSlot }}</span>
                    </div>
                    <SlotOverview :outlet_id="computedOutletId" :slot="slotProps.item.slot"
                        :slotData="slotProps.item.data" />
                </div>
            </template>

            <template #thumbnail="slotProps">
                <div :class="slotProps.item.at_risk ? 'p-2 rounded-md bg-red-300' : 'bg-white p-2 rounded-md'">
                    {{ slotProps.item.timeSlot }}
                </div>
            </template>
        </Galleria>
    </div>
</template>

<script>
import axios from 'axios';
import SlotOverview from './SlotOverview.vue';
import { api } from '../../api';
import Galleria from 'primevue/galleria'
import Loading from "../Loading.vue";
import isEmpty from 'lodash.isempty';

export default {
    components: {
        Loading,
        SlotOverview,
        Galleria
    },

    props: {
        outlet_id: {
            type: Number,
            required: true
        },
        outlet_name: {
            type: String,
            required: false
        },
    },

    data() {
        return {
            isLoading: false,
            isError: false,
            cancelTokens: [],
            slotWiseMetrics: [],
            activeIndex: 0
        }
    },

    computed: {
        computedOutletId() {
            return this.outlet_id
        },

        computedOutletName() {
            return this.outlet_name
        },

        computedSlots() {
            const slots = Object.keys(this.slotWiseMetrics).map((slot, idx) => {
                const slotData = this.slotWiseMetrics[slot]
                if (slotData?.upcoming_slot) this.activeIndex = idx

                const timeSlot = this.convertEpochToTime(slot)

                return {
                    timeSlot,
                    slot,
                    at_risk: slotData?.at_risk,
                    upcoming_slot: slotData?.upcoming_slot,
                    data: slotData?.data,
                }
            })
            return slots;
        },

        responsiveOptions() {
            return [
                { breakpoint: '1300px', numVisible: 10 },
                { breakpoint: '1000px', numVisible: 5 },
                { breakpoint: '575px', numVisible: 2 }
            ]
        }
    },

    methods: {
        async fetchSlotWiseMetrics() {
            this.cancelPreviousRequests();

            try {
                const slotWiseMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [slotWiseMetricsCancelTokenSource]

                let slotWiseMetrics = api.fetchWarehouseSlotWiseMetrics({ outlet_id: this.computedOutletId, cancelToken: slotWiseMetricsCancelTokenSource.token })

                let [slotWiseMetricsResponse] = await Promise.all([slotWiseMetrics])

                if (slotWiseMetricsResponse) this.slotWiseMetrics = slotWiseMetricsResponse.data

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.slotWiseMetrics = []
        },

        convertEpochToTime(epochSlot) { // epochSlot is in seconds
            const date = new Date(epochSlot * 1000 + 5.5 * 60 * 60 * 1000) // +5:30 for IST
            const timeSlot = date.toISOString().split('T')[1].split('.')[0]

            const [hour, minutes, seconds] = timeSlot.split(':').map(Number); // Split and convert to numbers

            // Convert to 12-hour format
            const period = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12; // Convert 0 to 12 for midnight
            const time12 = `${hour12}:${minutes.toString().padStart(2, '0')} ${period}`;
            return time12
        }
    },

    mounted() {
        this.isLoading = true
        this.isError = false
        this.resetAllMetrics()
        this.fetchSlotWiseMetrics()
    },

    beforeUnMount() {
        this.cancelPreviousRequests();
    }

}
</script>


<style scoped>
:deep(.p-galleria-thumbnail-container) {
    background-color: #3f3f3f;
    border-radius: 1rem;
}

:deep(.p-galleria-thumbnail-items-container) {
    margin: auto;
}
</style>