<template>
    <div v-if="isLoading">
        <Loading />
    </div>

    <div v-else-if="!isLoading && isError">error: outlet id not found</div>

    <div v-else>
        <div class="text-lg font-semibold text-blue-800 px-1 md:px-0 mb-2">AVAILABILITY</div>
        <div
            class="mt-2 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 lg:gap-3 rounded-lg md:rounded-none bordered-box  mb-3 md:m-0">
            <div v-for="metric in computedAvailabilityMetrics" :key="metric?.name"
                class="md:bg-white px-1 py-1.5 rounded-lg">
                <div class="flex justify-between px-1 md:px-2">
                    <span
                        class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                        {{ metric?.name }}
                    </span>
                    <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr.value"
                        :type="metric?.curr.type" />
                </div>
            </div>
        </div>

        <div class="mt-4 text-lg font-semibold text-blue-800 px-1 md:px-0 mb-2">TRUNCATION</div>
        <div
            class="mt-2 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 lg:gap-3 rounded-lg md:rounded-none bordered-box  mb-3 md:m-0">
            <div v-for="metric in computedTruncationData" :key="metric?.name"
                class="md:bg-white px-1 py-1.5 rounded-lg">
                <div class="flex justify-between px-1 md:px-2">
                    <span
                        class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                        {{ metric?.name }}
                    </span>
                    <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr.value"
                        :type="metric?.curr.type" />
                </div>
            </div>
        </div>

        <div class="mt-4 text-lg font-semibold text-blue-800 px-1 md:px-0 mb-2">OUTBOUND METRICS</div>
        <div class="mt-2 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 lg:gap-3">
            <div v-for="metrics in computedOutBoundData" class="rounded-lg md:bg-white bordered-box  mb-3 md:m-0">
                <div v-for="metric in metrics" :key="metric?.name" class="px-1 py-1.5 ">
                    <div class="flex justify-between px-1 md:px-2">
                        <span
                            class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                            {{ metric?.name }}
                        </span>
                        <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr.value"
                            :type="metric?.curr.type" />
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4 text-lg font-semibold text-blue-800 px-1 md:px-0 mb-2">INVENTORY SANITY</div>
        <div
            class="mt-2 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 lg:gap-3 rounded-lg md:rounded-none bordered-box  mb-3 md:m-0">
            <div v-for="metric in computedInvSanityData" :key="metric?.name" class="md:bg-white px-1 py-1.5 rounded-lg">
                <div class="flex justify-between px-1 md:px-2">
                    <span
                        class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                        {{ metric?.name }}
                    </span>
                    <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric?.curr.value"
                        :type="metric?.curr.type" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import Loading from "../Loading.vue";
import { api } from "../../api";
import { formatDate, metricsForStyleReversal } from "../../utils/metrics";
import AnimatedNumber from "../AnimatedNumber.vue";
import { Metric } from "../../interfaces";
import isEmpty from "lodash.isempty";

export default {
    components: {
        Loading,
        AnimatedNumber
    },

    props: {
        outlet_id: {
            type: Number,
            required: true
        },
        slot: {

            type: Number,
            required: true
        },
        slotData: {
            type: Object,
            required: true
        }
    },

    data() {
        return {
            isLoading: false,
            isError: false,
            cancelTokens: [],
            slotMetrics: []
        }
    },

    computed: {
        computedSlot() {
            return this.slot
        },

        computedOutletId() {
            return this.outlet_id
        },

        computedData() {
            if (isEmpty(this.slotMetrics)) return this.slotData;
            return [...this.slotMetrics, ...this.slotData]
        },

        computedSlotData() {
            const metricData = this.computedData.map(metric => {
                let data = metric.data
                return {
                    name: metric?.name,
                    data: data.map(x => new Metric(x.metric, metric.type, { date: x.date })),
                    ...(new Date(metric.etl_snapshot_ts_ist).toString() !== 'Invalid Date' ? { updated_at: formatDate(metric.etl_snapshot_ts_ist) } : {}),
                }
            })

            return metricData
        },

        calculatedSlotData() {
            return this.computedSlotData?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];

                return {
                    name: metric?.name,
                    reverseStyle: metricsForStyleReversal.includes(metric?.name),
                    curr: curr,
                    type: metric.type
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },

        computedAvailabilityMetrics() {
            const truncationMetrics = ['FE %', 'BE %', 'FE (Post STO) %']
            return this.calculatedSlotData.filter((metric) => truncationMetrics.includes(metric?.name))
        },

        computedTruncationData() {
            const truncationMetrics = ['Line Items', 'Created Qty', 'Cancelled Qty']
            return this.calculatedSlotData.filter((metric) => truncationMetrics.includes(metric?.name))
        },

        computedOutBoundData() {
            const outBoundMetrics = [
                ['Picking OTIF', 'Picking Fill Rate', 'Picking Pendency'],
                ['Packaging OTIF', 'Packaging Fill Rate', 'Packaging Pendency'],
                ['Sort OTIF', 'Sort Fill Rate', 'Sort Pendency'],
                ['Dispatch OTIF', 'Dispatch Fill Rate', 'Dispatch Pendency'],
            ]

            return outBoundMetrics?.map((lineItems) => (
                lineItems?.map((metricName) => this.calculatedSlotData.find((metric) => metric?.name === metricName))
            ))
        },

        computedInvSanityData() {
            const invSanityMetrics = ['IRT %', 'Raise Audit %']
            return this.calculatedSlotData.filter((metric) => invSanityMetrics.includes(metric?.name))
        },

    },

    watch: {
        computedSlot() {
            this.isLoading = true
            this.isError = false
            this.resetAllMetrics()
            this.fetchSlotMetrics()
        }
    },

    methods: {
        async fetchSlotMetrics() {
            this.cancelPreviousRequests();

            try {
                const slotMetricsCancelTokenSource = axios.CancelToken.source();

                this.cancelTokens = [slotMetricsCancelTokenSource]

                let slotMetrics = api.fetchWarehouseSlotMetrics({ outlet_id: this.computedOutletId, slot: this.computedSlot, cancelToken: slotMetricsCancelTokenSource.token })

                let [slotMetricsResponse] = await Promise.all([slotMetrics])

                if (slotMetricsResponse) this.slotMetrics = slotMetricsResponse.data // return {} if not present

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        resetAllMetrics() {
            this.slotMetrics = []
        },
    },

    mounted() {
        this.isLoading = true
        this.isError = false
        this.resetAllMetrics()
        this.fetchSlotMetrics()
    },

    beforeUnMount() {
        this.cancelPreviousRequests();
    }
}
</script>

<style scoped>
@media only screen and (max-width: 600px) {
    .bordered-box {
        border: 1px solid #EFEFEF;
    }
}
</style>