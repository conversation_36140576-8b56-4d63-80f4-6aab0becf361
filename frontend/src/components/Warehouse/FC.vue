<template>
    <div class="text-lg font-bold">FC OVERVIEW</div>
    <div class="my-4">
        <Accordion :activeIndex="activeIndex" @update:activeIndex="onAccordionChange"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-4">
            <AccordionTab v-for="(data, index) in computedData" :key="data?.outlet_name"
                :headerClass="getBackgroundTrendClass(data?.highlightMetric?.value)"
                :contentClass="getBackgroundTrendClass(data?.highlightMetric?.value)">
                <template #header>
                    <div class="flex justify-between items-center w-full">
                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">{{
                            data?.outlet_name }}
                        </div>

                        <div v-if="data?.highlightMetric?.value"
                            class="text-lg md:text-xl font-semibold flex items-center text-black">
                            <AnimatedNumber :value="data?.highlightMetric?.value" :type="data?.highlightMetric?.type"
                                :class="data?.highlightMetric?.value < 90 ? 'text-red-500' : 'text-green-500'" />
                        </div>
                    </div>
                </template>

                <!-- Content -->
                <div class="mb-1">
                    <Chip v-if="data.shift"
                        class="bg-yellow-50 mb-2 flex justify-between text-xs italic font-semibold p-1 rounded">
                        Shift
                        <span>{{ getShift(data.shift).date }}</span>
                        <span>{{ getShift(data.shift).time }}</span>
                    </Chip>
                    <div class="bg-white rounded-xl grid grid-cols-2">
                        <div v-for="metric in data?.data" class="content-card p-2 py-4">
                            <div class="flex justify-between">
                                <div class="text-sm">{{ metric?.name }}</div>
                                <AnimatedNumber :styles="['text-base', 'font-bold', 'text-right']"
                                    :value="metric.curr.value" :type="metric.curr.type" />
                            </div>
                            <div class="flex justify-end" v-if="metric.diffs?.length > 0">
                                <div v-for="diff in metric.diffs" :key="diff.date">
                                    <span class="text-xs font-[650] mr-0.5"
                                        v-bind:class="diff.style(metric.reverseStyle)">
                                        <template v-if="diff.change() !== '-'">
                                            {{ diff.change() }}
                                            {{ computedAbsoluteMetricDeltaConstant.includes(metric.name) ? '' : '%'
                                            }}
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </span>
                                    <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                        class="text-xs font-bold mr-1 text-positive-metric">﹥
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AccordionTab>
        </Accordion>

        <div v-if="this.data.length > 9" class="flex justify-center items-center">
            <Button :label="showMore ? 'view less' : 'view more'" plain text @click="updateShowMore" iconPos="right"
                class="text-xs md:text-base p-0 mt-2 md:mt-4" :icon="showMore ? 'pi pi-angle-up' : 'pi pi-angle-down'"
                style="color: green;" />
        </div>
    </div>

    <div v-if="computedSelectedOutletId">
        <ManpowerMetrics :outlet_id="computedSelectedOutletId" />
    </div>
</template>

<script>
import Accordion from 'primevue/accordion';
import AccordionTab from 'primevue/accordiontab'
import AnimatedNumber from '../AnimatedNumber.vue';
import { ABSOLUTE_METRIC_DELTA } from '../../utils/metrics';
import ManpowerMetrics from './ManpowerMetrics.vue';
import Button from 'primevue/button';

export default {
    components: {
        AnimatedNumber,
        Accordion,
        AccordionTab,
        ManpowerMetrics,
        Button
    },

    props: {
        data: {
            type: Array,
            required: true
        },
    },

    data() {
        return {
            activeIndex: 0,
            showMore: false
        }
    },

    computed: {
        computedData() {
            return this.showMore && this.data.length > 9 ? this.data : this.data.slice(0, 9)
        },

        computedAbsoluteMetricDeltaConstant() {
            return ABSOLUTE_METRIC_DELTA;
        },

        getBackgroundTrendClass() {
            return (value) => {
                return value < 90 ? 'negative-trend' : 'positive-trend';
            }
        },

        computedSelectedOutletId() {
            return this.computedData?.[this.activeIndex]?.outlet_id
        },
    },

    methods: {
        onAccordionChange(newVal) {
            this.activeIndex = newVal
        },


        updateShowMore() {
            this.showMore = !this.showMore;
        },

        getShift(shift) {
            return {
                date: shift?.split(" ")?.[0],
                time: shift?.split(" ")?.[1]
            }
        },
    },
}
</script>


<style scoped>
.content-card {

    &:nth-of-type(1),
    &:nth-of-type(2) {
        border-bottom: 1px solid #efefef;
    }

    &:nth-of-type(3),
    &:nth-of-type(4) {
        border-top: none !important;
    }

    &:nth-of-type(odd) {
        border-right: 1px solid #efefef;
    }

    &:not(:nth-of-type(-n+2)) {
        border-top: 1px solid #efefef;
    }
}

:deep(.p-accordion-tab) {
    border: 0;
}

@media only screen and (max-width: 600px) {
    :deep(.p-accordion-tab) {
        border: 2px solid #fff;
        position: relative;
        margin-top: -0.5rem;
        border-radius: 0.5rem;
        z-index: 1;
    }
}

:deep(.p-accordion-toggle-icon) {
    display: none !important;
}

:deep(.p-accordion-header-link) {
    height: inherit;
    padding: 0.75rem !important;
    height: 58px;
}

:deep(.positive-trend .p-accordion-header-link) {
    background-color: #E5F3F3 !important;
}

:deep(.negative-trend .p-accordion-header-link) {
    background-color: #FFEDEF !important;
}

:deep(.p-accordion-tab-active .positive-trend .p-accordion-header-link) {
    background-color: #D2F0F0 !important;
}

:deep(.p-accordion-tab-active .negative-trend .p-accordion-header-link) {
    background-color: #FFDBE0 !important;
}

:deep(.p-toggleable-content) {
    margin-top: -0.75rem !important;
}

:deep(.p-accordion-content) {
    padding: 0.75rem !important;
}

@media only screen and (min-width: 800px) {
    :deep(.p-accordion-content) {
        border-bottom-left-radius: 8px !important;
        border-bottom-right-radius: 8px !important;
    }
}

:deep(.positive-trend .p-accordion-content) {
    background-color: #D2F0F0 !important;

    .p-button {
        background-color: #12A2AB;
        color: #FFFFFF;
    }
}

:deep(.negative-trend .p-accordion-content) {
    background-color: #FFDBE0 !important;

    .p-button {
        background-color: #EF4F5F;
        color: #FFFFFF;
    }
}
</style>
