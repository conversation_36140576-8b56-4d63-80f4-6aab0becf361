<template>
  <div>
    <Toast position="top-center">
      <template #icon />
    </Toast>

    <div class="max-w-6xl mx-auto px-2 py-1 lg:max-w-7xl">
      <div class="flex px-8 py-4 items-center lg:items-end justify-between">
        <div class="flex justify-between">
          <div class="flex items-end flex-wrap gap-8 gap-y-4">
            <div class="flex flex-col items-start max-w-md">
              City:
              <v-select :model-value="selectedCity" :reduce="(computedCityList) => computedCityList.code"
                :options="computedCityList" label="name" @update:model-value="updateSelectedCity" :clearable="false"
                placeholder="click to select city"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center" />
            </div>

            <div class="flex flex-col items-start max-w-md">
              Store:
              <v-select :model-value="selectedStore" :options="computedStoreList" label="merchant_name_id" :disabled="this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code
                " @update:model-value="updateSelectedStore" :clearable="false" placeholder="click to select store"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center" />
            </div>

            <div class="flex flex-col items-start max-w-md">
              P-Type:
              <v-select v-model="selectedPType" :options="computedPTypeList" :clearable="true"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center"
                :multiple="true" :close-on-select="false" />
            </div>

            <div class="flex flex-col items-start max-w-md">
              L0-Category:
              <v-select v-model="selectedL0" :options="computedL0CategoryList" :clearable="true"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center" />
            </div>

            <div class="flex flex-col items-start max-w-md">
              Date Range:
              <Calendar v-model="dateRange" showIcon iconDisplay="input" selectionMode="range" :maxDate="maxDate"
                showButtonBar :numberOfMonths="2"
                inputClass="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center"
                panelClass="py-1" :inputStyle="{ height: '36px', marginBottom: '4px' }" />
            </div>

            <div class="flex flex-col items-start max-w-md">
              <AsyncSelect v-model="selectedItemId" title="Item Id:"
                selectClass="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center"
                titleClass="mb-0" apiFunc="fetchBadStockFilters" searchKey="item_id" :params="computedParams" />
            </div>

            <div class="flex flex-col items-start max-w-md">
              Er Id:
              <input 
                v-model="selectedErId" 
                type="text" 
                placeholder="Enter Er ID"
                class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-80 lg:py-1 text-center border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                style="height: 36px; margin-bottom: 4px;"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <Button v-tooltip.top="{ value: 'Additional Filters', position: 'top' }" icon="pi pi-sliders-h"
              iconPos="right" aria-label="Filter" class="filter-btn text-black" text @click="toggleOverlay" />

            <OverlayPanel ref="overlayPanel">
              <div class="p-2">
                <div class="text-lg font-semibold">Additional Filters:</div>
                <span v-if="computedDisabledAsync" class="text-xs font-extralight">
                  Please select P-Type or L0-Category first.
                </span>

                <div class="additional-filters">
                  <AsyncSelect v-model="selectedBrand" title="Brand" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchBadStockFilters" searchKey="brand" :params="computedParams" />

                  <AsyncSelect v-model="selectedL1" title="L1-Category" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchBadStockFilters" searchKey="l1_category" :params="computedParams" />

                  <AsyncSelect v-model="selectedL2" title="L2-Category" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchBadStockFilters" searchKey="l2_category" :params="computedParams" />

                  <AsyncSelect v-model="selectedItemName" title="Item Name" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchBadStockFilters" searchKey="item_name" :params="computedParams" />
                </div>
              </div>
            </OverlayPanel>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center flex-wrap gap-16">
        <Button label="Apply" severity="contrast" @click="handleApply" class="px-10 py-3" />
      </div>
    </div>
  </div>
</template>

<script>
import Button from "primevue/button";
import Sidebar from "primevue/sidebar";
import vSelect from "vue-select";
import Listbox from "primevue/listbox";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import { PanIndiaConstants } from "../../constants";
import AsyncSelect from "../AsyncSelect.vue";
import isEmpty from "lodash.isempty"
import Tooltip from 'primevue/tooltip';
import OverlayPanel from 'primevue/overlaypanel';
import { mapState } from 'pinia';
import { useCityStore } from "../../stores/cities";
import Calendar from 'primevue/calendar';
import FloatLabel from 'primevue/floatlabel';
import Toast from 'primevue/toast';

export default {
  components: {
    Button,
    Sidebar,
    "v-select": vSelect,
    Listbox,
    TabView,
    TabPanel,
    AsyncSelect,
    OverlayPanel,
    Calendar,
    FloatLabel,
    Toast
  },

  directives: {
    tooltip: Tooltip
  },

  props: {
    selectedCity: {
      type: String,
      default: PanIndiaConstants.PAN_INDIA.code,
    },
    selectedStore: {
      type: Object,
      default: {},
    },
    computedCityList: {
      type: Array,
      default: [],
    },
    computedStoreList: {
      type: Array,
      default: [],
    },
    finalValue: {
      type: Object,
      default: {},
    }
  },

  data() {
    return {
      visibleBottom: false,
      activeTab: 0,
      isOverlayVisible: false,
      selectedItemName: null,
      selectedPType: null,
      selectedBrand: null,
      selectedL0: null,
      selectedL1: null,
      selectedL2: null,
      dateRange: null,
      maxDate: null,
      selectedItemId: null,
      selectedErId: null
    };
  },

  computed: {
    computedPanIndiaConstants() {
      return PanIndiaConstants;
    },

    processedCityList() {
      return this.computedCityList.map(city => ({
        name: city.name,
        code: city.code,
      }));
    },

    computedDisabledAsync() {
      if (isEmpty(this.selectedL0) && isEmpty(this.selectedPType)) return true;
      return false;
    },

    computedParams() {
      let params = {};
      if (this.selectedL0) params['l0_category'] = this.selectedL0;
      if (this.selectedL1) params['l1_category'] = this.selectedL1;
      if (this.selectedL2) params['l2_category'] = this.selectedL2;
      if (this.selectedPType) params['ptype'] = this.selectedPType;
      if (this.selectedItemName) params['item_name'] = this.selectedItemName;
      if (this.selectedBrand) params['brand'] = this.selectedBrand;
      if (this.selectedItemId) params['item_id'] = this.selectedItemId;
      if (this.selectedErId) params['er_id'] = this.selectedErId;

      return params;
    },

    computedPTypeList() {
      return this.getBadStockPTypeList
    },

    computedL0CategoryList() {
      return this.getBadStockL0CategoryList;
    },

    computedSelectedCity() {
      return this.selectedCity;
    },

    computedIndependantFilterState() {
      return JSON.stringify({ productType: this.selectedPType, productL0: this.selectedL0 })
    },

    computedDateRange() {
      return this.dateRange;
    },

    ...mapState(useCityStore, ['getBadStockPTypeList', 'getBadStockL0CategoryList', 'getBadStocksPage']),
  },

  emits: [
    "update:selectedCity",
    "update:selectedStore",
    "finalClick"
  ],

  methods: {
    showFilters() {
      this.visibleBottom = !this.visibleBottom;
    },

    updateSelectedCity(value) {
      this.$emit("update:selectedCity", value);
    },

    updateSelectedStore(value) {
      this.$emit("update:selectedStore", value);
    },

    handleApply() {
      const hasErId = this.selectedErId && this.selectedErId.toString().trim() !== '';

      if (hasErId) {
        // If er_id is provided, date range is compulsory
        if (!this.dateRange?.[0] || !this.dateRange?.[1]) {
          this.$toast.add({
            severity: 'error',
            summary: "Required!",
            detail: "Date range is required when Er ID is provided",
            life: 3000
          });
          return;
        }
      } else {
        // Only validate if er_id is NOT provided
        if ((!this.dateRange?.[0] || !this.dateRange?.[1]) || (isEmpty(this.selectedL0) && isEmpty(this.selectedPType))) {
          let messageConfig = {
            date: 'Select both start date and end date in the date range',
            product: 'Select either Ptype or L0 to filter.',
            both: 'Select either Ptype or L0 and the Date Range',
          }

          let messageKey = "";
          if (!isEmpty(this.selectedL0) || !isEmpty(this.selectedPType)) messageKey = 'date';
          else if (this.dateRange?.[0] || this.dateRange?.[1]) messageKey = 'product';
          else messageKey = 'both';

          this.$toast.add({
            severity: 'error',
            summary: "Required!",
            detail: messageConfig[messageKey],
            life: 3000
          });
          return;
        }
      }

      // Validate dates if they are selected (for both er_id and non-er_id cases)
      if (this.dateRange?.[0] && this.dateRange?.[1]) {
        if (new Date(this.dateRange[1]) > new Date()) {
          this.$toast.add({
            severity: 'error',
            summary: "Error!",
            detail: "Max date can be of today itself!",
            life: 3000
          });
          return;
        }

        if (Math.abs(new Date(this.dateRange[1]) - new Date(this.dateRange[0])) > 30 * 24 * 60 * 60 * 1000) {
          this.$toast.add({
            severity: 'error',
            summary: "Error!",
            detail: "Maximum difference in dates can only be of 30 days.",
            life: 3000
          });
          return;
        }
      }

      const values = {
        productL0: this.selectedL0,
        productL1: this.selectedL1,
        productL2: this.selectedL2,
        productBrand: this.selectedBrand,
        productType: this.selectedPType,
        itemName: this.selectedItemName,
        startDate: this.formatDate(this.dateRange[0]),
        endDate: this.formatDate(this.dateRange[1]),
        itemId: this.selectedItemId,
        erId: this.selectedErId
      }

      this.$emit("finalClick", values);
      this.visibleBottom = false;
    },

    toggleOverlay(event) {
      if (this.$refs.overlayPanel) {
        this.$refs.overlayPanel.show(event); // or any method available on OverlayPanel
      }
    },

    makeDependantFiltersEmpty() {
      this.selectedItemName = null
      this.selectedBrand = null
      this.selectedL1 = null
      this.selectedL2 = null
      this.selectedItemId = null
    },

    formatDate(timestamp) {
      if (!timestamp) return null;

      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    },
  },

  mounted() {
    if (this.getBadStocksPage) {
      this.selectedL0 = this.getBadStocksPage.filterValues?.l0_category;
      this.selectedPType = this.getBadStocksPage.filterValues?.ptype;
      this.dateRange = (this.getBadStocksPage.filterValues?.start_date && this.getBadStocksPage.filterValues?.end_date) ?
        [new Date(this.getBadStocksPage.filterValues.start_date), new Date(this.getBadStocksPage.filterValues.end_date)] : null;
      this.selectedErId = this.getBadStocksPage.filterValues?.er_id;
    }

    this.maxDate = new Date();
  },

  watch: {
    computedDisabledAsync(newVal) {
      if (newVal) this.makeDependantFiltersEmpty();
    },

    computedIndependantFilterState(newVal, oldVal) {
      if (newVal !== oldVal) this.makeDependantFiltersEmpty();
    },

    computedDateRange(val) {
      const currentDate = new Date();

      if (val?.[0]) {
        const date = new Date(val[0]);
        date.setDate(date.getDate() + 30);
        this.maxDate = new Date(Math.min(currentDate.getTime(), date.getTime()));
      } else {
        this.maxDate = currentDate;
      }
    }
  }
};
</script>


<style scoped>
.additional-filters {
  >div {
    margin-top: 2rem;
  }
}

:deep(.filter-btn .p-button-icon) {
  font-size: 2rem;
}
</style>
