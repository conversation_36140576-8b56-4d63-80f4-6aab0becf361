<template>
  <div v-if="showHeader">
    <div class="max-w-6xl mx-auto px-2 py-1 lg:max-w-7xl">
      <div class="flex px-8 py-4 items-center lg:items-end justify-between">
        <div class="flex items-end flex-wrap gap-8">
          <div class="flex flex-col items-start max-w-md">
            City:
            <v-select :model-value="selectedCity" :reduce="(computedCityList) => computedCityList.code"
              :options="computedCityList" label="name" @update:model-value="updateSelectedCity" :clearable="false"
              placeholder="click to select city"
              class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-64 lg:py-1 text-center" />
          </div>

          <div class="flex flex-col items-start max-w-md">
            Store:
            <v-select :model-value="selectedStore" :options="computedStoreList" label="merchant_name_id" :disabled="this.selectedCity === computedPanIndiaConstants.PAN_INDIA.code
              " @update:model-value="updateSelectedStore" :clearable="false" placeholder="click to select store"
              class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-64 lg:py-1 text-center" />
          </div>

          <div class="flex flex-col items-start max-w-md">
            P-Type:
            <v-select v-model="selectedPType" :options="computedPTypeList" :clearable="true"
              class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-64 lg:py-1 text-center"
              :multiple="true" :close-on-select="false" />
          </div>

          <div class="flex flex-col items-start max-w-md">
            L0-Category:
            <v-select v-model="selectedL0" :options="computedL0CategoryList" :clearable="true" :multiple="true"
              class="bg-gray-50 text-gray-900 text-sm rounded-lg focus:border-blue-500 block max-w-md mt-1 mx-auto w-64 lg:py-1 text-center"
              :close-on-select="false" />
          </div>

          <div class="flex items-center justify-between">
            <Button v-tooltip.top="{ value: 'Additional Filters', position: 'top' }" icon="pi pi-sliders-h"
              iconPos="right" aria-label="Filter" class="filter-btn text-black" text @click="toggleOverlay" />

            <OverlayPanel ref="overlayPanel">
              <div class="p-2">
                <div class="text-lg font-semibold">Additional Filters:</div>
                <span v-if="computedDisabledAsync" class="text-xs font-extralight">
                  Please select P-Type or L0-Category first.
                </span>

                <div class="additional-filters">
                  <AsyncSelect v-model="selectedPId" title="P-Id:" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchProductFilters" searchKey="pid" :params="computedParams"
                    :multiple="true" />

                  <AsyncSelect v-model="selectedBrand" title="Brand" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchProductFilters" searchKey="brand" :params="computedParams" :multiple="true" />

                  <AsyncSelect v-model="selectedL1" title="L1-Category" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchProductFilters" searchKey="l1_category" :params="computedParams"
                    :multiple="true" />

                  <AsyncSelect v-model="selectedL2" title="L2-Category" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchProductFilters" searchKey="l2_category" :params="computedParams" :multiple="true"  />

                  <AsyncSelect v-model="selectedPName" title="P-Name" :disabled="computedDisabledAsync"
                    selectClass="bg-gray-50 text-gray-900 text-sm focus:border-blue-500 block max-w-md mx-auto w-80 text-center"
                    titleClass="mb-2" apiFunc="fetchProductFilters" searchKey="pname" :params="computedParams" :multiple="true"  />
                </div>
              </div>
            </OverlayPanel>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center flex-wrap gap-16">
        <div class="text-left text-gray-900 flex items-center">
          <VueToggle class="max-w-md m-auto" title="" name="VueToggle" :toggled="isYesterday" @toggle="handleToggle" />
          <p class="ml-2">Yesterday</p>
        </div>

        <Button label="Apply" severity="contrast" @click="handleApply" class="px-10 py-3" />

        <Button text @click="refreshFilters" class="pi pi-refresh text-black"
          v-tooltip.top="{ value: 'Refresh Filters List' }" />
      </div>
    </div>
  </div>

  <div v-else class="m-2">
    <div class="px-1 py-2 container flex justify-between items-center">
      <div class="flex-[4] flex overflow-auto gap-4 pb-4">
        <Button label="Filters" outlined severity="contrast" class="bg-white rounded-lg py-2 px-4 btn"
          :class="totalNumberOfAppliedFilters ? 'selected-filter' : null" :badge="totalNumberOfAppliedFilters"
          icon="pi pi-filter" @click="showFilters('city')" />

        <Button label="Yesterday" outlined severity="contrast" class="bg-white rounded-lg py-2 px-4 btn"
          :class="isFilterApplied('yesterday') ? 'selected-filter' : null" iconPos="right"
          :icon="isYesterday ? 'pi pi-times' : ''" @click="handleToggle" />

        <Button v-for="filter in computedFilters" :key="filter.key" :label="filter.label" outlined severity="contrast"
          class="bg-white rounded-lg py-2 px-4 btn" :class="isFilterApplied(filter.key) ? 'selected-filter' : null"
          iconPos="right" icon="pi pi-caret-down" :badge="filter.hasBadge ? numberOfFilters(filter.key) : null"
          @click="showFilters(filter.key)" />
      </div>
    </div>


    <Sidebar v-model:visible="visibleBottom" header="Filters" position="bottom" :blockScroll="true"
      :showCloseIcon="false" style="
        height: 64%;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
      ">
      <template #header>
        <div class="w-full flex justify-between items-center">
          <div class="font-bold text-lg flex items-center">
            Filters
            <Button text class="pi pi-refresh text-black text-xs ml-4 p-0" @click="refreshFilters" />
          </div>

          <div class="flex items-center">
            <Button label="Clear" class="text-xs px-2 py-1 mr-2" @click="handleClearFilters" severity="secondary" />
            <Button label="Apply" plain text class="text-xs px-2 py-1 btn" @click="handleApply" />
          </div>
        </div>
      </template>

      <div class="flex flex-col justify-between h-full">
        <TabView v-model:activeIndex="activeTab">
          <TabPanel key="city" header="City">
            <Listbox id="citySelect" :model-value="selectedCityObj" :options="computedCityList" filter
              optionLabel="name" class="w-full" @update:model-value="updateSelectedCityCode" :clearable="false"
              placeholder="click to select city" />
          </TabPanel>

          <TabPanel key="store" header="Store">
            <Listbox id="storeSelect" :model-value="selectedStore" :options="computedStoreList" filter
              optionLabel="merchant_name_id" class="w-full" @update:model-value="updateSelectedStore" :clearable="false"
              placeholder="click to select store" :disabled="selectedCity == '#Pan-India'" />
          </TabPanel>

          <TabPanel key="p-type" header="P-Type">
            <AsyncSelect v-model="selectedPType" selectClass="w-11/12 m-auto pt-2" apiFunc="fetchProductFilters"
              searchKey="ptype" :onlyInitialCall="false" :multiple="true" :minSearchLength="0" />
          </TabPanel>

          <TabPanel key="l0-category" header="L0-Category">
            <Listbox id="storeSelect" v-model="selectedL0" :options="computedL0CategoryList" filter class="w-full"
              :clearable="true" multiple placeholder="click to select L0-Category" />
          </TabPanel>

          <TabPanel key="pid" header="P-Id">
            <AsyncSelect v-model="selectedPId" selectClass="w-11/12 m-auto pt-2" apiFunc="fetchProductFilters"
              searchKey="pid" :params="computedParams" :disabled="computedDisabledAsync" :multiple="true" />
            <div v-if="computedDisabledAsync" class="text-xs mt-2 p-3 font-extralight">
              Please select P-Type or L0-Category first.
            </div>
          </TabPanel>

          <TabPanel key="brand" header="Brand">
            <AsyncSelect v-model="selectedBrand" selectClass="w-11/12 m-auto pt-2" apiFunc="fetchProductFilters"
              searchKey="brand" :params="computedParams" :disabled="computedDisabledAsync" :showHeader="showHeader" />
            <div v-if="computedDisabledAsync" class="text-xs mt-2 p-3 font-extralight">
              Please select P-Type or L0-Category first.
            </div>
          </TabPanel>

          <TabPanel key="l1-category" header="L1-Category">
            <AsyncSelect v-model="selectedL1" selectClass="w-11/12 m-auto pt-2" apiFunc="fetchProductFilters"
              searchKey="l1_category" :params="computedParams" :disabled="computedDisabledAsync" multiple />
            <div v-if="computedDisabledAsync" class="text-xs mt-2 p-3 font-extralight">
              Please select P-Type or L0-Category first.
            </div>
          </TabPanel>

          <TabPanel key="l2-category" header="L2-Category">
            <AsyncSelect v-model="selectedL2" selectClass="w-11/12 m-auto pt-2" apiFunc="fetchProductFilters"
              searchKey="l2_category" :params="computedParams" :disabled="computedDisabledAsync" />
            <div v-if="computedDisabledAsync" class="text-xs mt-2 p-3 font-extralight">
              Please select P-Type or L0-Category first.
            </div>
          </TabPanel>

          <TabPanel key="p-name" header="P-Name">
            <AsyncSelect v-model="selectedPName" selectClass="w-11/12 m-auto pt-2" apiFunc="fetchProductFilters"
              searchKey="pname" :params="computedParams" :disabled="computedDisabledAsync" />
            <div v-if="computedDisabledAsync" class="text-xs mt-2 p-3 font-extralight">
              Please select P-Type or L0-Category first.
            </div>
          </TabPanel>
        </TabView>
      </div>
    </Sidebar>
  </div>
</template>

<script>
import Button from "primevue/button";
import Sidebar from "primevue/sidebar";
import vSelect from "vue-select";
import VueToggle from "vue-toggle-component";
import Listbox from "primevue/listbox";
import TabView from "primevue/tabview";
import TabPanel from "primevue/tabpanel";
import { PanIndiaConstants } from "../../constants";
import AsyncSelect from "../AsyncSelect.vue";
import isEmpty from "lodash.isempty"
import Tooltip from 'primevue/tooltip';
import OverlayPanel from 'primevue/overlaypanel';
import { mapState, mapActions } from 'pinia';
import { useCityStore } from "../../stores/cities";

export default {
  components: {
    Button,
    Sidebar,
    "v-select": vSelect,
    VueToggle,
    Listbox,
    TabView,
    TabPanel,
    AsyncSelect,
    OverlayPanel
  },

  directives: {
    tooltip: Tooltip
  },

  props: {
    showHeader: {
      type: Boolean,
      required: true,
      default: true,
    },
    selectedCity: {
      type: String,
      default: PanIndiaConstants.PAN_INDIA.code,
    },
    selectedStore: {
      type: Object,
      default: {},
    },
    computedCityList: {
      type: Array,
      default: [],
    },
    computedStoreList: {
      type: Array,
      default: [],
    },
    isYesterday: {
      type: Boolean,
      default: false,
    },
    finalValue: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      visibleBottom: false,
      activeTab: 0,
      isOverlayVisible: false,
      selectedCityObj: null,
      selectedPName: null,
      selectedPType: null,
      selectedBrand: null,
      selectedL0: null,
      selectedL1: null,
      selectedL2: null,
      selectedPId: null
    };
  },

  computed: {
    computedPanIndiaConstants() {
      return PanIndiaConstants;
    },

    processedCityList() {
      return this.computedCityList.map(city => ({
        name: city.name,
        code: city.code,
      }));
    },

    yesterdayOptions() {
      return [
        { name: "Today", value: false },
        { name: "Yesterday", value: true },
      ];
    },

    computedDisabledAsync() {
      if (isEmpty(this.selectedL0) && isEmpty(this.selectedPType)) return true;
      return false;
    },

    computedParams() {
      let params = {};
      if (this.selectedL0) params['l0_category'] = this.selectedL0;
      if (this.selectedL1) params['l1_category'] = this.selectedL1;
      if (this.selectedL2) params['l2_category'] = this.selectedL2;
      if (this.selectedPType) params['ptype'] = this.selectedPType;
      if (this.selectedPName) params['pname'] = this.selectedPName;
      if (this.selectedBrand) params['brand'] = this.selectedBrand;
      if (this.selectedPId) params['pid'] = this.selectedPId;

      return params;
    },

    computedPTypeList() {
      return this.getPTypeList.sort((a, b) => a.localeCompare(b));
    },

    computedL0CategoryList() {
      return this.getL0CategoryList.sort((a, b) => a.localeCompare(b));
    },

    computedSelectedCity() {
      return this.selectedCity;
    },

    computedIndependantFilterState() {
      return JSON.stringify({ productType: this.selectedPType, productL0: this.selectedL0 })
    },

    computedFilters() {
      return [
        { key: "city", label: "City", hasBadge: false },
        { key: "store", label: "Store", hasBadge: false },
        { key: "p-type", label: "P-Type", hasBadge: true },
        { key: "l0-category", label: "L0-Category", hasBadge: true },
        { key: "pid", label: "P-Id", hasBadge: true },
        { key: "brand", label: "Brand", hasBadge: false },
        { key: "l1-category", label: "L1-Category", hasBadge: true },
        { key: "l2-category", label: "L2-Category", hasBadge: true },
        { key: "p-name", label: "P-Name", hasBadge: false }
      ]
    },

    appliedFilterItemMapping() {
      return {
        "city": this.finalValue?.city,
        "store": this.finalValue?.store,
        "yesterday": this.isYesterday,
        "p-id": this.finalValue?.pid,
        "l0-category": this.finalValue?.l0_category,
        "l1-category": this.finalValue?.l1_category,
        "l2-category": this.finalValue?.l2_category,
        "brand": this.finalValue?.brand,
        "p-type": this.finalValue?.ptype,
        "p-name": this.finalValue?.pname,
      }
    },


    totalNumberOfAppliedFilters() {
      const number = Object.keys(this.appliedFilterItemMapping).filter((filterItem) => this.isFilterApplied(filterItem))?.filter(Boolean)?.length
      if (number) return `(${number})`
      return null
    },

    ...mapState(useCityStore, ['getPTypeList', 'getL0CategoryList', 'getFestivesPage']),
  },

  emits: [
    "update:isYesterday",
    "toggle",
    "update:selectedCity",
    "update:selectedStore",
    "update:selectedHour",
    "finalClick"
  ],

  methods: {
    showFilters(val) {
      const tabIndexes = ["city", "store", "p-type", "l0-category", "pid", "brand", "l1-category", "l2-category", "p-name"]
      const idx = tabIndexes.findIndex((filterItem) => filterItem === val) ?? 0

      this.visibleBottom = !this.visibleBottom;
      this.activeTab = idx;
    },

    handleToggle() {
      this.$emit("update:isYesterday", !this.isYesterday);
      this.$emit("toggle", !this.isYesterday);
    },

    updateSelectedCity(value) {
      this.$emit("update:selectedCity", value);
    },

    updateSelectedCityCode(value) {
      this.selectedCityObj = value;
      this.$emit("update:selectedCity", value.code);
    },

    updateSelectedStore(value) {
      this.$emit("update:selectedStore", value);
    },

    handleApply() {
      const values = {
        productL0: this.selectedL0,
        productL1: this.selectedL1,
        productL2: this.selectedL2,
        productBrand: this.selectedBrand,
        productType: this.selectedPType,
        productName: this.selectedPName,
        productId: this.selectedPId
      }

      this.$emit("finalClick", values);
      this.visibleBottom = false;
    },

    toggleOverlay(event) {
      if (this.$refs.overlayPanel) {
        this.$refs.overlayPanel.show(event); // or any method available on OverlayPanel
      }
    },

    refreshFilters() {
      this.$emit("refreshFilters");
      this.visibleBottom = false;
    },

    makeDependantFiltersEmpty() {
      this.selectedPName = null
      this.selectedBrand = null
      this.selectedL1 = null
      this.selectedL2 = null
      this.selectedPId = null
    },

    handleClearFilters() {
      this.selectedL0 = null;
      this.selectedL1 = null;
      this.selectedL2 = null;
      this.selectedBrand = null;
      this.selectedPType = null;
      this.selectedPName = null;
    },

    isFilterApplied(filterItem) {
      if (filterItem === "city") {
        return this.appliedFilterItemMapping['city']
      } else if (filterItem === "store") {
        return this.appliedFilterItemMapping['store']
      } else if (filterItem === "yesterday") {
        return this.isYesterday
      } else {
        return !isEmpty(this.appliedFilterItemMapping[filterItem])
      }
    },

    // for multi-selects, gives the number of selected filters
    numberOfFilters(filterItem) {
      const number = this.appliedFilterItemMapping[filterItem]?.length
      if (number) return `(${number})`
      return null
    },

    ...mapActions(useCityStore, ["updateFestivesPages"]),
  },

  mounted() {
    if (this.getFestivesPage) {
      // Todo: add pre-filled l0 category...
      // if (isEmpty(this.getFestivesPage.filterValues)) {
      //   let l0Category = [this.getL0CategoryList[0]]
      //   this.selectedL0 = l0Category
      //   // this.updateFestivesPages({
      //   //   ...(this.getFestivesPage || {}),
      //   //   // filterValues: { l0_category: l0Category }
      //   // })
      // } else {

      let l0Category = this.getFestivesPage.filterValues?.l0_category
      this.selectedL0 = l0Category;
      this.selectedPType = this.getFestivesPage.filterValues?.ptype;
      // }
    }
  },

  watch: {
    computedSelectedCity(newVal) {
      this.selectedCityObj = this.computedCityList.find((city) => city.code === newVal);
    },

    computedDisabledAsync(newVal) {
      if (newVal) this.makeDependantFiltersEmpty();
    },

    computedIndependantFilterState(newVal, oldVal) {
      if (newVal !== oldVal) this.makeDependantFiltersEmpty();
    }
  }
};
</script>


<style scoped>
.btn {
  flex: 1;
  min-width: max-content;
  border: 1px solid #4f4f4f;
}

:deep(.btn .p-button-label) {
  font-weight: 300;
}

:deep(.btn .p-button-icon) {
  font-size: 0.75rem;
}

.selected-filter {
  border: 1px solid #328616;
  background-color: #F6FFF8;
}

:deep(.selected-filter .p-badge) {
  color: #000;
  font-size: 1rem;
  font-weight: 300;
  background-color: #F6FFF8;
  padding: 0;
}

.additional-filters {
  >div {
    margin-top: 2rem;
  }
}

:deep(.filter-btn .p-button-icon) {
  font-size: 2rem;
}

:deep(.p-sidebar) {
  border-top-left-radius: 16px !important;
  border-top-right-radius: 16px !important;
}

:deep(.p-sidebar .p-sidebar-header) {
  border-bottom: 1px solid #efefef;
}

:deep(.p-tabview) {
  display: flex !important;
}

:deep(.p-tabview .p-tabview-nav li .p-tabview-nav-link) {
  border-radius: 0 !important;
  border: none;
  padding: 0.75rem 3rem 0.75rem 0;
  font-size: 0.75rem;
  font-weight: 300;
}

:deep(.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link) {
  border-right: 2px solid #10b981;
  font-weight: 600;
}

:deep(.p-tabview-nav) {
  flex-direction: column;
}

:deep(.p-tabview-ink-bar) {
  display: none;
}

:deep(.p-tabview-nav-container) {
  height: 44vh;
  overflow-y: auto;
}

:deep(.p-tabview-panels) {
  width: 100%;
  padding-top: 0;
  padding-right: 0;
  padding-bottom: 0;
  border-left: 1px solid #efefef;
  border-radius: 0;
  height: 44vh;
}

:deep(.p-listbox) {
  border: none;
  box-shadow: none;
}

:deep(.p-listbox-list-wrapper) {
  height: 44vh;
  overflow-y: auto;
  font-size: 0.75rem;
}

:deep(.vs__dropdown-menu) {
  font-size: 0.75rem !important;
}
</style>
