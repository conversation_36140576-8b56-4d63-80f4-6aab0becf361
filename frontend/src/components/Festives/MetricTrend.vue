<template>
    <div class="max-w-7xl mx-auto pb-2 lg:pb-10 flex justify-center">
        <div class="text-gray-900 flex items-center">
            <div class="mr-2" :style="{ fontWeight: isToggleView ? '200' : '600' }">Hourly Trend</div>
            <VueToggle name="VueToggle" title="" :toggled="computedToggleState" @toggle="toggleView"
                activeColor="#F0F0F0" />
            <div :style="{ fontWeight: isToggleView ? '600' : '200' }">Daily Trend</div>
        </div>
    </div>

    <div v-if="isLoading">
        <div class="flex items-center justify-center h-80">
            <div style="border-top-color:transparent"
                class="w-16 h-16 border-4 border-blue-400 border-solid rounded-full animate-spin">
            </div>
        </div>
    </div>

    <div v-else-if="!isLoading && isError"
        class="flex h-80 items-center justify-center text-red-600 text-xl font-semibold">
        Data Not Available
    </div>

    <div v-else>
        <div v-if="computedToggleState && !isYesterday" class="flex items-center w-full justify-end my-2 text-sm">
            <Checkbox v-model="forCurrentHour" :binary="true" @update:modelValue="fetchForCurrentHour" />
            <label class="ml-2"> Till current hour </label>
        </div>

        <v-chart class="h-[50vh]" :option="computedToggleState ? computedWeekOptions : computedHourlyOptions"
            autoresize />
    </div>
</template>

<script>

import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { LineChart } from 'echarts/charts';
import {
    TooltipComponent,
    LegendComponent,
    GridComponent
} from 'echarts/components';
import VChart from 'vue-echarts';
import { api } from '../../api';
import { productMetricNameMapping } from '../../utils/metrics';
import Loading from '../Loading.vue';
import VueToggle from 'vue-toggle-component';
import Checkbox from 'primevue/checkbox'
import axios from 'axios';
import isEmpty from 'lodash.isempty';

use([
    CanvasRenderer,
    LineChart,
    TooltipComponent,
    LegendComponent,
    GridComponent
]);

export default {

    components: {
        VChart,
        Loading,
        VueToggle,
        Checkbox
    },

    props: {
        metricName: {
            type: String,
            required: true
        },
        filters: {
            type: Object,
            required: true
        },
        isYesterday: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            isToggleView: false,
            metrics: [],
            isLoading: false,
            isError: false,
            forCurrentHour: true,
            cancelTokens: []
        };
    },

    computed: {
        computedHourlyOptions() {
            let metricNames = [];
            let ySeries = [];

            this.metrics?.map(metric => {
                const { date = "", data: hoursData = [] } = metric || {}

                let maxHour = 24;

                if (new Date(date).getDate() === new Date(Date.now()).getDate()) {
                    maxHour = new Date(Date.now()).getHours() + 1;
                } else {
                    maxHour = 24;
                }

                let allHourData = [...Array(maxHour).keys()].map((hour) => {
                    let hourData = hoursData.find((item) => item?.hour === hour)?.data;
                    if (isEmpty(hourData)) {
                        hourData = [{ metric: 0 }]
                    }
                    return ({ hour, data: hourData })
                })

                let metricData = allHourData?.sort((a, b) => {
                    return a.hour - b.hour;
                });

                let yDataPoints = [];
                metricData?.map(point => {
                    yDataPoints.push(point?.data?.[0]?.metric);
                });

                metricNames.push(date);

                ySeries.push({
                    name: date,
                    type: 'line',
                    smooth: true,
                    data: yDataPoints
                });
            });

            let xAxisPoints = [...Array(24).keys()].map(hour => hour);

            metricNames = this.sortDate(metricNames);

            return {
                tooltip: {
                    trigger: 'axis',
                    order: 'valueDesc'
                },
                legend: {
                    data: metricNames
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxisPoints
                },
                yAxis: {
                    type: 'value'
                },
                series: ySeries
            }
        },

        computedWeekOptions() {
            let metricNames = [];
            let ySeries = [];
            let xAxisPoints = [];

            this.metrics?.map(metric => {
                const { name = "", data = [] } = metric || {};

                const metricData = this.sortDate(data, false);

                let yDataPoints = [];

                metricData?.map(point => {
                    yDataPoints.push(point?.metric);
                    xAxisPoints.push(point?.date);
                });

                ySeries.push({
                    name: name,
                    type: 'line',
                    smooth: true,
                    data: yDataPoints
                });

                metricNames.push(name);
            });

            return {
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: metricNames
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: xAxisPoints
                },
                yAxis: {
                    type: 'value'
                },
                series: ySeries
            }
        },

        computedToggleState() {
            return this.isToggleView;
        },

        computedForCurrentHour() {
            return !this.isYesterday && this.forCurrentHour
        }
    },

    methods: {
        async fetchAllMetrics() {
            this.cancelPreviousRequests();

            try {
                const cancelTokenSource = axios.CancelToken.source();
                this.cancelTokens = [cancelTokenSource];

                let metrics = this.isToggleView ? api.fetchProductMetrics({
                    metrics: [productMetricNameMapping[this.metricName]], ...this.filters,
                    yesterday_metric: this.isYesterday, is_daywise: true, current_hour: this.computedForCurrentHour,
                    cancelToken: cancelTokenSource.token
                }).then(response => {
                    this.metrics = response.data.metrics;
                }) : api.fetchHourlyProductMetrics({
                    metrics: [productMetricNameMapping[this.metricName]], ...this.filters,
                    yesterday_metric: this.isYesterday, cancelToken: cancelTokenSource.token
                }).then(response => {
                    this.metrics = response.data.metrics;
                });

                await Promise.all([
                    metrics
                ]);

                this.isLoading = false;
                this.isError = false;
            } catch (error) {
                if (axios.isCancel(error)) {
                    console.log("Request canceled", error.message);
                } else {
                    this.isLoading = false;
                    this.isError = true;
                }
            };
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = []; // Clear the tokens
        },

        toggleView(isToggled) {
            this.isToggleView = isToggled;
            this.isLoading = true;
            this.isError = false;
            this.fetchAllMetrics();
        },

        sortDate(metricDates = [], isDateArray = true) {
            //* isDateArray --> metricDates is an array of dates only i.e. ['2024-08-10', '2024-08-09', '2024-08-11'];
            if (isDateArray) {
                return metricDates?.sort((a, b) => {
                    var dateA = new Date(a);
                    dateA = dateA.getTime();

                    var dateB = new Date(b);
                    dateB = dateB.getTime();

                    return dateA - dateB;
                })
            }
            //* else it is an array of objects having dates --> metricDates is an array of dates only i.e. [{date:'2024-08-10'}, {date:'2024-08-09'}, {date:'2024-08-11'}];
            return metricDates?.sort((a, b) => {
                var dateA = new Date(a?.date);
                dateA = dateA.getTime();

                var dateB = new Date(b?.date);
                dateB = dateB.getTime();

                return dateA - dateB;
            })
        },

        fetchForCurrentHour() {
            this.isLoading = true;
            this.isError = false;
            this.fetchAllMetrics();
        }
    },

    mounted() {
        this.isLoading = true;
        this.isError = false;
        this.fetchAllMetrics();
    }
}
</script>

<styles scoped>
</styles>