<template>
    <Accordion :activeIndex="activeAccordions" @tab-open="handleTabOpen" @tab-close="handleTabClose" class="mt-4">
        <AccordionTab>
            <template #header>
                <div class="flex justify-between items-center w-full">
                    <div class="flex items-center gap-2 md:gap-3">
                        <i :class="icon" style="color: black;"></i>
                        <div class="text-md md:text-lg font-bold lg:font-normal text-black">{{ title }}</div>
                    </div>

                    <i v-if="isLoading" class="pi pi-spin pi-spinner mr-2" style="color: blue;"></i>
                </div>
            </template>

            <div v-if="isLoading" class="flex justify-center items-center p-4">
                loading...
            </div>

            <div v-else-if="isError" class="text-center p-4">
                <span class="text-sm md:text-md font-bold text-red-700">
                    Failed to load metrics. Please try again.
                </span>
            </div>

            <div v-else>
                <div v-if="calculatedMetrics.length === 0" class="flex justify-center items-center p-4">
                    Data not present
                </div>

                <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-3 lg:gap-3">
                    <div v-for="metric in calculatedMetrics" :key="metric.name" @click="showDetails(metric.name)"
                        class="bg-white/80 backdrop-blur-sm px-4 py-3 md:py-5 rounded-xl border border-gray-100"
                        :class="metric?.name === 'Freebie %' ? '' : 'shadow-md hover:shadow-lg'">
                        <div class="flex justify-between px-1 md:px-2">
                            <span
                                class="text-md md:text-lg font-[600] md:font-[450] inline-flex items-center whitespace-nowrap">
                                {{ metric.name }}
                                <InfoDialog :metrickey="metric.name" :showHeader="showHeader" />
                            </span>
                            <AnimatedNumber :styles="['text-lg', 'font-semibold']" :value="metric.curr.value"
                                :type="metric.curr.type" />
                        </div>
                        <div class="flex justify-between items-center px-2">
                            <span class="text-xs font-light gray-900"></span>
                            <div class="flex justify-between">
                                <div v-for="diff in metric.diffs" :key="diff.date">
                                    <span class="text-xs font-[650] mr-0.5"
                                        v-bind:class="diff.style(metric.reverseStyle)">
                                        <template v-if="diff.change() !== '-'">
                                            {{ diff.change() }}
                                            {{ absoluteMetricDeltaConstant.includes(metric.name) ? '' : '%' }}
                                        </template>
                                        <template v-else>
                                            -
                                        </template>
                                    </span>
                                    <span v-if="diff !== metric.diffs[metric.diffs.length - 1]"
                                        class="text-xs font-bold mr-1 text-positive-metric">
                                        »</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AccordionTab>
    </Accordion>

    <Dialog v-model:visible="showComplaints" modal header="All Complaints" :style="{ width: '75rem', margin: '1rem' }"
        :dismissableMask="true">
        <div class="w-80 md:w-full">
            <DataTable :value="computedTableData" tableClass="text-xs md:text-md" showGridlines removableSort scrollable
                paginator :rows="10" :rowsPerPageOptions="[5, 10, 20, 50]">
                <Column field="category_type" header="Category Type" bodyClass="p-1 md:px-4 md:py-3"
                    headerClass="p-1 md:px-4 md:py-3">
                </Column>
                <Column v-for="(date, index) in computedTableColumns" :key="index" :field="date" :header="date"
                    sortable>
                </Column>
            </DataTable>
        </div>
    </Dialog>

    <Dialog v-model:visible="visible" modal :header="`${trendMetric} Trend`" :style="{ width: '75rem', margin: '1rem' }"
        :dismissableMask="true">
        <MetricTrend :metricName="trendMetric" :filters="finalValue" :isYesterday="isYesterday" />
    </Dialog>
</template>

<script>
import Loading from "../Loading.vue";
import AnimatedNumber from "../AnimatedNumber.vue";
import InfoDialog from "../InfoDialog.vue";
import { MetricChange } from "../../interfaces/index.js";
import { metricsForStyleReversal, sortedAllMetrics } from "../../utils/metrics.js";
import { ABSOLUTE_METRIC_DELTA } from "../../utils/metrics.js";
import axios from "axios";
import Accordion from "primevue/accordion";
import AccordionTab from "primevue/accordiontab";
import isEmpty from "lodash.isempty";
import Dialog from "primevue/dialog";
import DataTable from "primevue/datatable";
import Column from "primevue/column";
import startCase from 'lodash.startcase';
import { defineAsyncComponent } from "vue";

export default {
    name: "MetricGroup",

    components: {
        Loading,
        AnimatedNumber,
        InfoDialog,
        Accordion,
        AccordionTab,
        Dialog,
        DataTable,
        Column,
        MetricTrend: defineAsyncComponent(() => import("./MetricTrend.vue")),
    },

    props: {
        isDefaultOpen: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            required: true
        },
        icon: {
            type: String,
            default: "pi pi-chart-line"
        },
        metricGroup: {
            type: Object,
            required: true
        },
        finalValue: {
            type: Object,
            default: {}
        },
        isYesterday: {
            type: Boolean,
            default: false
        },
        showHeader: {
            type: Boolean,
            default: true
        },
        idx: {
            type: Number,
        },
        openAccordions: {
            type: Array,
            default: []
        }
    },

    emits: ['updateOpenAccordion', 'updateClosedAccordion'],

    data() {
        return {
            activeAccordions: null,
            isLoading: false,
            isError: false,
            metrics: [],
            cancelTokens: [],
            absoluteMetricDeltaConstant: ABSOLUTE_METRIC_DELTA,
            intervalId: "",
            allComplaints: [],
            trendMetric: "",
            showComplaints: false,
            visible: false,
        };
    },

    computed: {
        calculatedMetrics() {
            const sortedMetrics = sortedAllMetrics(this.metrics, undefined, undefined, 5);

            return sortedMetrics?.map(metric => {
                let data = metric.data;
                let curr = data[data.length - 1];
                let diffs = [];

                for (let j = 0; j < data.length - 1; j++) {
                    let prev = data[j];
                    diffs.push(new MetricChange(curr, prev, metric.name));
                }

                return {
                    name: metric.name,
                    reverseStyle: metricsForStyleReversal.includes(metric.name),
                    curr: curr,
                    diffs: diffs,
                }
            }).filter(metric => metric.curr && metric.curr.value !== "-");
        },

        triggerFetchApi() {
            return `${Object.values(this.finalValue)?.join('-')}-${this.isYesterday}`
        },

        hasPtypeFilters() {
            return !isEmpty(this.finalValue?.ptype) || !isEmpty(this.finalValue?.l0_category)
        },

        // complaints:
        computedAllComplaints() {
            const red = this.allComplaints.reduce((obj, item) => {
                obj[item?.category_type] = isEmpty(obj?.[item?.category_type]) ? [item] : [...obj[item?.category_type], item];
                return obj;
            }, {});

            return Object.keys(red).map((type) => ({ category_type: type, data: red[type] }));
        },

        computedTableData() {
            return this.computedAllComplaints?.map(category => {
                const row = { category_type: startCase(category?.category_type) };
                category.data?.forEach(item => {
                    row[item.date] = item.count;
                });
                return row;
            });
        },

        computedTableColumns() {
            const dateColumns = [...new Set(this.computedAllComplaints?.flatMap(cat => cat.data?.map(d => d.date)))];

            // sorting dates in descending order:
            return dateColumns?.sort((a, b) => {
                var dateA = new Date(a);
                dateA = dateA.getTime();

                var dateB = new Date(b);
                dateB = dateB.getTime();

                return dateB - dateA;
            });
        },

        computedTotalComplaintsMetrics() {
            const dates = this.computedTableColumns?.reverse();
            const computedTableData = this.computedTableData || [];

            const data = dates?.map((date) => {
                const value = computedTableData?.reduce((acc, item) => acc + (item?.[date] || 0), 0);
                return ({
                    date,
                    metric: value
                })
            })

            return [{ data, name: "All Complaints", type: 'number' }]
        },
    },

    methods: {
        handleTabOpen(val) {
            this.activeAccordions = 0
            this.$emit('updateOpenAccordion', this.title)
            this.fetchMetrics()
            this.intervalId = window.setInterval(() => {
                this.fetchMetrics();
            }, 120000);
        },

        handleTabClose(val) {
            this.activeAccordions = null
            this.$emit('updateClosedAccordion', this.title)
            this.cancelPreviousRequests()
            clearInterval(this.intervalId);
        },

        filterMetricsWithNoData(metrics) {
            const parsedMetrics = JSON.parse(JSON.stringify(metrics));
            let filteredMetrics = parsedMetrics.filter(metric => metric.data.length > 0);
            return filteredMetrics;
        },

        cancelPreviousRequests() {
            this.cancelTokens.forEach((source) => {
                source.cancel('Operation canceled due to new request.');
            });
            this.cancelTokens = [];
        },

        async fetchMetrics() {
            this.isLoading = true;
            this.isError = false;
            this.metrics = [];
            this.cancelPreviousRequests();

            try {

                // Create metrics batches (process 3 at a time to avoid too many parallel requests)
                let commonParams = { ...this.finalValue, yesterday_metric: this.isYesterday };

                // Create promises for all metric batches
                const metricsPromises = this.metricGroup?.metrics.map((metric, index) => {
                    const { metricList, currDayFetchApi, previousDayFetchApi, metricKeyForParams } = metric;

                    let baseFunc = this.isYesterday ? previousDayFetchApi : currDayFetchApi

                    if (metricKeyForParams === "base-metric" && metricList) { //will always be true ek saath
                        let metricsToFetch = [];
                        for (let idx = 0; idx < metricList.length; idx += 2) {
                            metricsToFetch.push(metricList.slice(idx, idx + 2));
                        }

                        return metricsToFetch.map((metrics, index) => {
                            const cancelToken = axios.CancelToken.source()

                            this.cancelTokens.push(cancelToken);

                            let params = {
                                ...commonParams,
                                metrics: metrics,
                                cancelToken: cancelToken.token
                            };
                            return baseFunc(params);
                        });
                    } else {
                        const cancelToken = axios.CancelToken.source()

                        const getParams = () => {
                            switch (metricKeyForParams) {
                                case "freebie":
                                    return {
                                        city_code: commonParams?.city,
                                        store_code: commonParams?.store,
                                        cancelToken: cancelToken.token
                                    };
                                case "complaints":
                                    return {
                                        ...commonParams,
                                        cancelToken: cancelToken.token
                                    };
                                default: return {}
                            }
                        }

                        // fetch only when hasPtypeFilters
                        if (metricKeyForParams === "freebie" && this.hasPtypeFilters) return Promise.resolve(null);

                        this.cancelTokens.push(cancelToken);
                        let params = getParams()
                        return baseFunc(params);
                    }
                });

                // Execute all requests in parallel and flatten nested arrays
                const responses = await Promise.all(metricsPromises?.flat());

                // Process responses
                let allMetrics = [];
                responses.forEach((response) => {
                    if (response?.data?.metrics) {
                        const metrics = this.filterMetricsWithNoData(response.data.metrics);
                        allMetrics.push(...metrics);
                    } else if (response?.data?.complaints) {
                        this.allComplaints = response.data.complaints;
                        allMetrics.push(...this.computedTotalComplaintsMetrics || []);

                    }
                });

                this.metrics = [...allMetrics];
                this.isLoading = false;

                // Emit the metrics to the parent component
                this.$emit('metricsUpdated', allMetrics);
            } catch (error) {
                if (!axios.isCancel(error)) {
                    console.error("Error fetching metrics:", error);
                    this.isError = true;
                }
                this.isLoading = false;
            }
        },

        showDetails(metricName) {
            if (metricName === "Freebie %") return;

            if (metricName === "All Complaints") {
                this.showComplaints = !this.showComplaints;
                return;
            } else {
                this.visible = !this.visible;
                this.trendMetric = metricName;
            }
        },
    },

    watch: {
        triggerFetchApi() {
            this.cancelPreviousRequests()
            if (this.activeAccordions == 0) {
                this.isLoading = true
                this.isError = false
                this.fetchMetrics()
            }
        }
    },

    mounted() {
        if (this.openAccordions.includes(this.title)) {
            this.activeAccordions = 0
            this.fetchMetrics();

            this.$nextTick(function () {
                this.intervalId = window.setInterval(() => {
                    this.fetchMetrics();
                }, 120000);
            })
        }
    },

    beforeUnmount() {
        clearInterval(this.intervalId);
        this.cancelPreviousRequests();
    }
}
</script>


<style scoped>
/* Base accordion styles with enhanced glass effect */
:deep(.p-accordion-tab) {
    @apply mb-6 rounded-2xl overflow-hidden;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.2),
        inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Header styles with Yellow theme */
:deep(.p-accordion-header) {
    background: rgba(255, 255, 255, 0.08);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    border-left: 3px solid #FFD700;
    /* Yellow */
}

:deep(.p-accordion-header:not(.p-disabled)) {
    &:hover {
        background: rgba(255, 215, 0, 0.05);
        /* Yellow with low opacity */
        transform: translateY(-2px);
    }

    &:active {
        transform: translateY(0);
        background: rgba(255, 215, 0, 0.1);
    }
}

/* Header link styles */
:deep(.p-accordion-header-link) {
    @apply p-5 font-medium;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 255, 255, 0.05));
}

/* Content styles */
:deep(.p-accordion-content) {
    @apply p-6;
    background: rgba(255, 255, 255, 0.05);
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

/* Active state styles */
:deep(.p-accordion-tab-active) {
    .p-accordion-header {
        background: rgba(255, 215, 0, 0.08);
        border-bottom-color: rgba(255, 215, 0, 0.2);
    }
}

/* Animation effects */
:deep(.p-accordion-tab) {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
        box-shadow: 0 12px 40px 0 rgba(255, 215, 0, 0.15),
            inset 0 0 0 1px rgba(255, 215, 0, 0.1);
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    :deep(.p-accordion-tab) {
        @apply mb-4;
        backdrop-filter: blur(12px);
    }

    :deep(.p-accordion-header-link) {
        @apply p-4 min-h-[56px];
        -webkit-tap-highlight-color: transparent;
    }

    :deep(.p-accordion-content) {
        @apply p-4;
    }
}

/* Custom scrollbar for content */
:deep(.p-accordion-content)::-webkit-scrollbar {
    width: 6px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-track {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 3px;
}

:deep(.p-accordion-content)::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.2);
    border-radius: 3px;
}
</style>