import { defineStore } from 'pinia'

export const useCityStore = defineStore('cityStore', {
    state: () => ({
        cityMapping: {},
        zoneMapping: [],
        storePageCityMapping: {},
        WTDMTDPage: {},
        storesCurrentViewCity: null,
        warehouseMapping: {},
        warehouse: null,
        yesterdayCity: null,
        storesDayViewCity: null,
        storesDayViewDate: null,
        storeToCityMapping: [],
        insightsPage: null,
        inStore: null,
        storesTrendPage: null,
        confettiShown: {},
        pTypes: [],
        l0Categories: [],
        festivesPage: {},
        complaintsPage: {},
        bistroProductPage: {},
        bistroStoreToCityMapping: [],
        bistroCityMapping: {},
        bistroStorePageCityMapping: {},
        bistroHomePage: {},
        categoryPage: {},
        badStockPage: {},
        badStockPTypes: [],
        BadStockL0Categories: [],
        projectionPage: {},
        bistroCitiesStoresTrendPage: {},
        bistroWtdMtdPage: {},
        emergencyServicePage: {},
        paasPage: {},
        bistroInstorePage: {},
        sonarHomePage: {}
    }),

    getters: {
        getStoreToCityMapping() {
            return this.storeToCityMapping;
        },

        getCityMapping() {
            return this.cityMapping;
        },

        getZoneMapping() {
            return this.zoneMapping;
        },

        getStorePageCityMapping(){
            return this.storePageCityMapping;
        },

        getWarehouse(){
            return this.warehouse;
        },

        getWarehouseMapping() {
            return this.warehouseMapping;
        },

        getWarehouseList() {
            let warehouses = [];
            for (let key in this.warehouseMapping) {
                let warehouseInfo = this.warehouseMapping[key];
                warehouseInfo["code"] = key;
                warehouses.push(warehouseInfo);
            }
            return warehouses;
        },

        getCityList() {
            let cities = [];
            for (let key in this.cityMapping) {
                let cityInfo = this.cityMapping[key];
                cityInfo["code"] = key;
                cities.push(cityInfo);
            }
            return cities;
        },

        getZoneList() {
            return this.zoneMapping;
        },

        getStoreCityList() {
            let cities = [];
            for (let key in this.storePageCityMapping) {
                let cityInfo = this.storePageCityMapping[key];
                cityInfo["code"] = key;
                cities.push(cityInfo);
            }
            return cities;
        },

        getStoresCurrentViewCity(){
            return this.storesCurrentViewCity;
        },

        getYesterdayCity(){
            return this.yesterdayCity;
        },

        getStoresDayViewCity(){
            return this.storesDayViewCity;
        },

        getStoresDayViewDate(){
            return this.storesDayViewDate;
        },

        getStoresTrendPage(){
            return this.storesTrendPage;
        },

        getWTDMTDPage(){
            return this.WTDMTDPage;
        },

        getInsightsPage() {
            return this.insightsPage;
        },

        getInstore(){
            return this.inStore;
        },

        getConfettiShown(){
            return this.confettiShown
        },

        getPTypeList(){
            return this.pTypes;
        },

        getL0CategoryList(){
            return this.l0Categories;
        },

        getFestivesPage(){
            return this.festivesPage;
        },

        getComplaintsPage(){
            return this.complaintsPage;
        },

        getBistroProductPage(){
            return this.bistroProductPage;
        },

        getBistroCityMapping() {
            return this.bistroCityMapping;
        },

        getBistroStoreToCityMapping() {
            return this.bistroStoreToCityMapping;
        },

        getBistroStorePageCityMapping() {
            return this.bistroStorePageCityMapping
        },

        getBistroStoreCityList() {
            let cities = [];
            for (let key in this.bistroStorePageCityMapping) {
                let cityInfo = this.bistroStorePageCityMapping[key];
                cityInfo["code"] = key;
                cities.push(cityInfo);
            }
            return cities;
        },

        getBistroHomePage() {
            return this.bistroHomePage
        },

        getCategoryPage() {
            return this.categoryPage
        },

        getBadStocksPage() {
            return this.badStockPage
        },

        getBadStockPTypeList(){
            return this.badStockPTypes;
        },

        getBadStockL0CategoryList(){
            return this.BadStockL0Categories;
        },

        getProjectionPage(){
            return this.projectionPage
        },

        getBistroCitiesStoresTrendPage(){
            return this.bistroCitiesStoresTrendPage;
        },

        getBistroWtdMtdPage(){
            return this.bistroWtdMtdPage
        },

        getEmergencyServicePage(){
            return this.emergencyServicePage
        },

        getPaasPage(){
            return this.paasPage
        },

        getSonarHomePage(){
            return this.sonarHomePage
        },

        getBistroInkitchenPage() {
            return this.bistroInstorePage
        },
    },

    actions: {
        updateStoreToCityMapping(mapping) {
            this.storeToCityMapping = mapping;
        },

        updateWarehouse(warehouse) {
            this.warehouse = warehouse;
        },

        updateWarehouseMapping(mapping) {
            this.warehouseMapping = mapping;
        },

        updateCityMapping(cityList) {
            this.cityMapping = cityList;
            return this.cityMapping;
        },

        updateZoneMapping(zoneMapping) {
            this.zoneMapping = zoneMapping;
        },

        updateStorePageCityMapping(cityList) {
            this.storePageCityMapping = cityList;
            return this.storePageCityMapping;
        },

        updateStoresCurrentViewCity(city){
            this.storesCurrentViewCity = city;
        },

        updateYesterdayCity(city){
            this.yesterdayCity = city;
        },

        updateStoresDayViewCity(city){
            this.storesDayViewCity = city;
        },

        updateStoresDayViewDate(date){
            this.storesDayViewDate = date;
        },

        updateStoresTrendPage(value){
            this.storesTrendPage = value;
        },

        updateWTDMTDPage(value){
            this.WTDMTDPage = value;
        },

        updateInsightsPage(value){
            this.insightsPage = value;
        },

        updateInstore(store){
            this.inStore = store;
        },

        updateConfettiShown(confetti){
            this.confettiShown = confetti;
        },

        updatePTypeList(pTypes){
            return this.pTypes = pTypes;
        },

        updateL0CategoryList(l0Categories){
            return this.l0Categories = l0Categories;
        },

        updateFestivesPages(festivesPage){
            this.festivesPage = festivesPage;
        },

        updateComplaintsPage(complaintsPage){
            this.complaintsPage = complaintsPage;
        },

        updateBistroProductPage(val){
            this.bistroProductPage = val;
        },

        updateBistroStoreToCityMapping(mapping) {
            this.bistroStoreToCityMapping = mapping;
        },

        updateBistroCityMapping(cityList) {
            this.bistroCityMapping = cityList;
            return this.bistroCityMapping;
        },

        updateBistroStorePageCityMapping(cityList) {
            this.bistroStorePageCityMapping = cityList;
            return this.bistroStorePageCityMapping;
        },

        updateBistroHomePage(val) {
            this.bistroHomePage = val
        },

        updateCategoryPage(val) {
            this.categoryPage = val
        },

        updateBadStocksPage(badStockPage) {
            this.badStockPage = badStockPage;
        },

        updateBadStockPTypeList(val) {
            this.badStockPTypes = val
        },

        updateBadStockL0CategoryList(val) {
            this.BadStockL0Categories = val
        },

        updateProjectionPage(val) {
            this.projectionPage = val
        },

        updateBistroCitiesStoresTrendPage(val) {
            this.bistroCitiesStoresTrendPage = val
        },

        updateBisroWtdMtdPage(val) {
            this.bistroWtdMtdPage = val
        },

        updateEmergencyServicePage(val) {
            this.emergencyServicePage = val
        },

        updatePaasPage(val) {
            this.paasPage = val
        },

        updateSonarHomePage(val) {
            this.sonarHomePage = val
        },

        updateBistroInkitchenPage(val) {
            this.bistroInstorePage = val
        },
    },

    persist: true
})
