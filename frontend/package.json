{"name": "sonar-ui", "private": true, "version": "0.1.0", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/vue": "^1.4.3", "@heroicons/vue": "^2.0.0", "@vuepic/vue-datepicker": "^v8.0.0", "axios": "^0.25.0", "core-js": "^3.21.0", "echarts": "^5.5.0", "lodash.debounce": "^4.0.8", "lodash.isempty": "^4.4.0", "lodash.startcase": "^4.4.0", "pinia": "^2.0.11", "pinia-plugin-persistedstate": "^1.2.3", "primeicons": "^v3.0.0", "primevue": "^3.53.0", "vee-validate": "^4.5.8", "vue": "^3.2.25", "vue-confetti": "^2.3.0", "vue-echarts": "^6.7.2", "vue-number-animation": "^1.1.1", "vue-router": "^4.0.12", "vue-select": "^4.0.0-beta.1", "vue-toggle-component": "^1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^2.2.0", "autoprefixer": "^10.4.2", "postcss": "^8.4.6", "tailwindcss": "^3.0.23", "vite": "^2.8.0"}}