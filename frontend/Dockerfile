FROM public.ecr.aws/zomato/node:14 as build-stage

WORKDIR /app

COPY package*.json /app/

RUN npm install

COPY ./ /app/

# # Comment out the next line to disable tests
# # RUN npm run test:unit

RUN npm run build

# # Stage 1, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM public.ecr.aws/zomato/nginx:1.21.6

COPY --from=build-stage /app/dist/ /usr/share/nginx/html

COPY ./nginx.conf /etc/nginx/conf.d/default.conf
