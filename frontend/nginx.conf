server {
  listen 80;

  if ($http_x_forwarded_proto = 'http') {
    return 301 https://$host$request_uri;
  }

  location /assets/ {
        alias /usr/share/nginx/html/assets/;
    }

  location / {
    root /usr/share/nginx/html;
    index index.html index.htm;
    try_files $uri $uri/ /index.html =404;

    # Add cache control headers for HTML files
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
  }

  location /api {
    proxy_pass http://0.0.0.0:8080;
    proxy_redirect     off;
    proxy_set_header   Host $host;
  }

  include /etc/nginx/extra-conf.d/*.conf;
}
