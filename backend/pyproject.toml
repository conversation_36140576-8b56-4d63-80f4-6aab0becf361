[tool.poetry]
name = "app"
version = "0.1.0"
description = "A tool to help you deep dive into data depths"
authors = ["Tech DE <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.9"
uvicorn = "^0.17.4"
fastapi = "^0.115.0"
requests = "^2.27.1"
gunicorn = "^20.1.0"
SQLAlchemy = "^1.4.31"
pydantic = "^2.11.0"
tenacity = "^8.0.1"
raven = "^6.10.0"
Jinja2 = "^3.0.3"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
email-validator = "^2.0.0"
pydantic-settings = "^2.0.0"
python-multipart = "^0.0.5"
pinotdb=">=0.3.2 <0.3.9"
requests-oauthlib = "^1.3.1"
psycopg2-binary = "^2.9.3"
httpx="^0.28.0"
aiohttp = "^3.8.5"
# fastapi-jwt-auth = {extras = ["asymmetric"], version = "^0.5.0"}  # Removed due to Pydantic v2 incompatibility
redis = "4.1.0"
arrow = "1.2.2"
ddtrace = "2.10.0"
boto3 = "^1.35.0"
langchain = "^0.3.26"
langgraph = "^0.5.1"
langsmith = "^0.4.4"
langchain-aws = "^0.2.27"
langchain-community = "^0.3.27"
langchain-core = "^0.3.68"
langchain-tavily = "^0.1.0"
pyowm = "^3.3.0"

[tool.poetry.dev-dependencies]
pytest = "^5.2"
alembic= "^1.4.2"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
