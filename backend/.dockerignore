# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.cache
.mypy_cache
.dmypy.json
dmypy.json

# Virtual environments
venv/
venv_new/
.venv/
ENV/
env/
.env

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.log
.pytest_cache/
test_pydantic_v2.py

# Git
.git
.gitignore
