FROM public.ecr.aws/zomato/python:3.9-slim

# Set environment variables
ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    POETRY_VERSION=1.6.1 \
    POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_CREATE=false \
    POETRY_CACHE_DIR='/var/cache/pypoetry' \
    PATH="$PATH:/root/.local/bin" \
    TESTING=True

# Install necessary system packages
RUN apt-get update && \
    apt-get install --no-install-recommends -y curl && \
    curl -sSL 'https://install.python-poetry.org' | python - && \
    poetry --version && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy configuration files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --no-root

# Copy the application code
COPY . .

# Build and install the package
RUN poetry build && pip install dist/*.whl

# Install testing dependencies
RUN pip install pytest pytest-mock load_dotenv

WORKDIR /app/src

CMD ["pytest", "-s", "tests/test_api_status.py", "-v"]
