import os
import pytest
from dotenv import load_dotenv
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch
from app.core.config import Settings
from redis import Redis
from functools import wraps

load_dotenv('.env')


def mock_cache_metrics(*args, **kwargs):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper
    return decorator

@pytest.fixture(autouse=True)
def mock_cache():
    with patch('app.cache.decorator.cache_metrics', mock_cache_metrics):
        yield


@pytest.fixture(autouse=True)
def mock_settings():
    """Override settings for testing using .env.test"""
    test_settings = Settings(
        # API Settings
        API_V1_STR="/api/v1",
        API_BISTRO_V1_STR="/api/bistro/v1",
        API_V2_STR="/api/v2",
        
        # Project Settings
        PROJECT_NAME="sonar_test",
        BACKEND_CORS_ORIGINS=[],
        
        # Auth Settings
        authjwt_secret_key=os.getenv("AUTHJWT_SECRET_KEY", "test-secret-key"),
        ACCESS_TOKEN_EXPIRE_MINUTES=15,
        REFRESH_TOKEN_EXPIRE_DAYS=5,
        
        # Google Auth
        GOOGLE_CLIENT_ID=os.getenv("GOOGLE_CLIENT_ID", "test-client-id"),
        GOOGLE_CLIENT_SECRET=os.getenv("GOOGLE_CLIENT_SECRET", "test-client-secret"),
        GOOGLE_REDIRECT_URI=os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:8000/auth/callback"),
        USER_REGISTRAION_ALLOWED=True,
        
        # Database Settings
        POSTGRES_HOST=os.getenv("POSTGRES_HOST", "localhost"),
        POSTGRES_USER=os.getenv("POSTGRES_USER", "postgres"),
        POSTGRES_PASSWORD=os.getenv("POSTGRES_PASSWORD", "password"),
        POSTGRES_DB=os.getenv("POSTGRES_DB", "sonar_test"),
        SQLALCHEMY_DATABASE_URI=os.getenv(
            "SQLALCHEMY_DATABASE_URI", 
            "postgresql://postgres:password@localhost:5432/sonar_test"
        ),
        
        # Redis Settings
        FORCE_REDIS_CACHE=False,
        REDIS_HOST_URL=os.getenv("REDIS_HOST_URL", "redis://localhost:6379"),
        
        # Pinot and Complaints
        PINOT_DB_URL=os.getenv("PINOT_DB_URL", "http://localhost:8000"),
        COMPLAINT_IMAGES_URL=os.getenv("COMPLAINT_IMAGES_URL", "http://localhost:8000/images"),
        COMPLAINT_IMAGES_TOKEN=os.getenv("COMPLAINT_IMAGES_TOKEN", "test-token"),
        
        # Datadog Settings
        DD_AGENT_HOST=os.getenv("DD_AGENT_HOST", "localhost"),
        DD_SERVICE=os.getenv("DD_SERVICE", "sonar-test"),
        DD_ENV=os.getenv("DD_ENV", "test"),
        DD_TRACE_ENABLED=os.getenv("DD_TRACE_ENABLED", "false"),
        DD_PROFILING_ENABLED=os.getenv("DD_PROFILING_ENABLED", "false"),
        DD_SERVICE_MAPPING=os.getenv("DD_SERVICE_MAPPING", ""),
        
        # AWS Settings
        TEST_S3_BUCKET=os.getenv("TEST_S3_BUCKET", "test-bucket"),
        BAD_STOCK_S3_BUCKET=os.getenv("BAD_STOCK_S3_BUCKET", "bad-stock-bucket"),
        AWS_S3_REGION=os.getenv("AWS_S3_REGION", "us-east-1"),
    )
    
    with patch('app.core.config.settings', test_settings):
        yield


@pytest.fixture
def client():
    """Create a test client for the FastAPI app"""
    from app.main import app
    return TestClient(app)


@pytest.fixture
def auth_headers():
    """Generate authentication headers for API requests"""
    test_token = os.environ.get("TEST_BEARER_TOKEN", "test_token")
    return {"Authorization": f"Bearer {test_token}"}
