import pytest
from unittest.mock import patch
from datetime import datetime, timedelta, timezone

IST = timezone(timedelta(hours=5, minutes=30))

#calculating today's date as a string in IST format(yyyy-mm-dd)
today_date = datetime.now(IST).strftime('%Y-%m-%d')

class TestAPIStatus:
    """Test that all API endpoints return 200 status codes"""

    @pytest.mark.parametrize("endpoint,params", [
        ("/api/v1/stores/storecitymapping", {}),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["gmv", "order_count", "aov"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["delivery_cost", "surge_shown_carts_percentage", "surge_paid_carts_percentage"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["rain_surge_carts_percentage", "cancellation_percentage", "percentage_orders_delivered_in_10mins"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["percentage_orders_delivered_in_15mins", "percentage_orders_delivered_in_30mins", "checkout_to_picker_assigned"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["billed_to_assigned", "rain_order_percentage", "platform_cost"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["batched_orders", "free_delivery_percentage", "slot_charge"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["handling_charge", "convenience_charge", "night_charge"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["small_cart_charge"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["new_transacting_users_count"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/base-metrics", {
            "metric": ["promo_percentage", "promo_orders", "promo_charge"],
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/rider-metrics", {
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/dau-metrics", {
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/funnel-metrics", {
            "city": ["UP-NCR"]
        }),
        ("/api/v2/order-metrics/all/surge-seen-metrics", {
            "city": ["UP-NCR"]
        }),
        ("/api/v1/order-metrics/hourly", {
            "yesterday_metric": "false",
            "metric": ["order_count", "checkout_to_picker_assigned", "rain_order_percentage", "billed_to_assigned", "surge_shown_carts_percentage", "aov"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/order-metrics/hourly/funnel", {
            "yesterday_metric": "false",
            "city": ["UP-NCR"]
        }),
        ("/api/v1/hau/pan_india", {
            "past_days_diff": "7",
            "city": ["UP-NCR"]
        }),
        ("/api/v1/rider_metrics/hourly", {
            "yesterday_metric": "false",
            "city": ["UP-NCR"]
        }),
        ("/api/v1/current_rate_metrics/all/projection_metrics", {
            "city": ["UP-NCR"]
        }),
        ("/api/v1/order-metrics/hourly/surge", {
            "city": ["UP-NCR"]
        }),
        # cities endpoints
        ("/api/v1/order-metrics/cities", {
            "date": today_date
        }),
        ("/api/v1/rider_metrics/cities", {
            "date": today_date
        }),
        ("/api/v1/rider_metrics/new/cities", {
            "date": today_date
        }),
        # stores endpoints
        ("/api/v1/stores/store_metrics", {
            "city": "Delhi"
        }),
        ("/api/v1/stores/active_stores", {
            "city": "Delhi"
        }),
        # instore endpoints
        ("/api/v1/instore-metrics/all/order", {
            "yesterday_metric": "false",
            "metric": [
                "order_count",
                "avg_item_count",
                "percent_instore_sla_less_than_150_sec",
                "checkout_to_picker_assigned",
                "ppi_in_seconds",
                "picker_surge_orders",
                "rider_plus_picker_surge_orders",
                "total_procured_items_quantity",
                "picker_assigned_to_picking_start",
                "total_orders",
                "sm_fill_rate",
                "billed_to_assigned"
            ]
        }),
        ("/api/v1/instore-metrics/all/order", {
            "yesterday_metric": "false",
            "metric": ["true_fill_rate"]
        }),
        ("/api/v1/instore-metrics/all/dau", {
            "yesterday_metric": "false"
        }),
        ("/api/v1/instore-metrics/all/active-time", {
            "yesterday_metric": "false",
            "metric": [
                "total_active_time",
                "picker_active_time_percentage",
                "putter_active_time_percentage",
                "auditor_active_time_percentage",
                "fnv_active_time_percentage",
                "items_putaway_per_hour",
                "orders_picked_per_hour",
                "new_manpower",
                "total_od_active_time"
            ]
        }),
        ("/api/v1/instore-metrics/all/complaints", {
            "yesterday_metric": "false",
            "metric": ["total_complaints"]
        }),
        ("/api/v1/order-metrics/hourly", {
            "yesterday_metric": "false",
            "metric": [
                "order_count",
                "avg_item_count",
                "percent_instore_sla_less_than_150_sec",
                "checkout_to_picker_assigned",
                "ppi_in_seconds",
                "picker_surge_orders",
                "rider_plus_picker_surge_orders",
                "total_procured_items_quantity",
                "picker_assigned_to_picking_start",
                "total_orders",
                "sm_fill_rate",
                "billed_to_assigned"
            ]
        }),
        ("/api/v1/instore-metrics/hourly", {
            "yesterday_metric": "false",
            "metric": [
                "total_active_time",
                "picker_active_time_percentage",
                "putter_active_time_percentage",
                "auditor_active_time_percentage",
                "fnv_active_time_percentage",
                "items_putaway_per_hour",
                "orders_picked_per_hour"
            ]
        }),
        ("/api/v1/instore-metrics/hourly/complaints", {
            "yesterday_metric": "false",
            "metric": ["total_complaints"]
        }),
        ("/api/v1/order-metrics/cities/base/comparision", {
            "yesterday_metric": "false",
            "city_wise": "false",
            "store_wise": "true",
            "is_critical_metrics": "true",
            "metric": "ppi_in_seconds"
        }),
        ("/api/v1/order-metrics/cities/base/comparision", {
            "yesterday_metric": "false",
            "city_wise": "false",
            "store_wise": "true",
            "is_critical_metrics": "true",
            "metric": "sm_fill_rate"
        }),
        ("/api/v1/order-metrics/cities/base/comparision", {
            "yesterday_metric": "false",
            "city_wise": "false",
            "store_wise": "true",
            "is_critical_metrics": "true",
            "metric": "billed_to_assigned"
        }),
        ("/api/v1/order-metrics/cities/base/comparision", {
            "yesterday_metric": "false",
            "city_wise": "true",
            "store_wise": "false",
            "is_critical_metrics": "true",
            "metric": "ppi_in_seconds"
        }),
        ("/api/v1/order-metrics/cities/base/comparision", {
            "yesterday_metric": "false",
            "city_wise": "true",
            "store_wise": "false",
            "is_critical_metrics": "true",
            "metric": "sm_fill_rate"
        }),
        ("/api/v1/order-metrics/cities/base/comparision", {
            "yesterday_metric": "false",
            "city_wise": "true",
            "store_wise": "false",
            "is_critical_metrics": "true",
            "metric": "billed_to_assigned"
        }),
        # Warehouse endpoints
        ("/api/v1/warehouses/availability", {}),
        ("/api/v1/warehouses/manpower_metrics", {
            "outlet": "1725"
        }),
        ("/api/v1/warehouses/availability", {
            "outlet": "1725",
            "slot": "1748436060",
            "is_slot_wise": "true"
        }),
        #availability endpoints
        ("/api/v1/availability_metrics/cities", {}),
        # WTD endpoints
        ("/api/v1/wtd_metrics/all", {
            "metric": [
                "gmv",
                "order_count",
                "aov",
                "transacting_users_count",
                "delivered_order_count",
                "new_transacting_users_count",
                "rm",
                "rm_percentage",
                "wau",
                "order_conversion",
                "total_items_sold",
                "total_items_per_order",
                "avg_item_count",
                "order_per_store"
            ],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/wtd_metrics/trends", {
            "metric": [
                "gmv",
                "order_count",
                "aov",
                "transacting_users_count",
                "delivered_order_count",
                "new_transacting_users_count",
                "rm",
                "rm_percentage",
                "wau",
                "order_conversion",
                "total_items_sold",
                "total_items_per_order",
                "avg_item_count",
                "order_per_store"
            ],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/current_rate_metrics/bucket/projection_metrics", {
            "weekly_projection": "true",
            "city": ["UP-NCR"]
        }),

        # MTD endpoints
        ("/api/v1/mtd_metrics/all", {
            "metric": [
                "gmv",
                "order_count",
                "aov",
                "transacting_users_count",
                "delivered_order_count",
                "new_transacting_users_count",
                "rm",
                "rm_percentage",
                "mau",
                "order_conversion",
                "total_items_sold",
                "total_items_per_order",
                "avg_item_count",
                "order_per_store"
            ],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/mtd_metrics/trends", {
            "metric": [
                "gmv",
                "order_count",
                "aov",
                "transacting_users_count",
                "delivered_order_count",
                "new_transacting_users_count",
                "rm",
                "rm_percentage",
                "mau",
                "order_conversion",
                "total_items_sold",
                "total_items_per_order",
                "avg_item_count",
                "order_per_store"
            ],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/current_rate_metrics/bucket/projection_metrics", {
            "monthly_projection": "true",
            "city": ["UP-NCR"]
        }),
        #projection metrics
        ("/api/v2/current_rate_metrics/all", {
            "city": ["UP-NCR"]
        }),
        ("/api/v1/current_rate_metrics/daily/projection_metrics", {
            "projection_days": "15",
            "city": ["UP-NCR"]
        }),
        ("/api/v1/current_rate_metrics/daily/projection_metrics/events", {
            "projection_days": "15",
            "city": ["UP-NCR"]
        }),
        #insights endpoint
        ("/api/v1/insights/conversion_funnel", {
            "yesterday_metric": "false",
            "filter_hour": "-1",
            "day_diff": "7"
        }),
        ("/api/v1/insights/conversion_funnel", {
            "yesterday_metric": "false",
            "city": "UP-NCR",
            "filter_hour": "-1",
            "day_diff": "7"
        }),
        ("/api/v1/insights/conversion_funnel", {
            "yesterday_metric": "false",
            "city": "UP-NCR",
            "filter_hour": "-1",
            "day_diff": "7",
            "store_response": "true"
        }),
        ("/api/v1/insights/ptype_metrics", {
            "yesterday_metric": "false",
            "city": "UP-NCR",
            "filter_hour": "-1",
            "day_diff": "7",
            "metrics_type": "contributed_conv_drop"
        }),
        ("/api/v1/insights/ptype_metrics", {
            "yesterday_metric": "false",
            "city": "UP-NCR",
            "filter_hour": "-1",
            "day_diff": "7",
            "metrics_type": "absolute_conv_drop"
        }),
        #product endpoints
        ("/api/v1/product-metrics/all", {
            "metric": ["gmv", "order_count"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/all", {
            "metric": ["ipc", "aov"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/all", {
            "metric": ["new_transacting_users_count"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/all", {
            "metric": ["total_items_sold", "canceled_quantity"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/all", {
            "metric": ["transacting_users_count", "asp"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/all", {
            "metric": ["unique_carts"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/cities", {
            "metric": ["gmv", "total_items_sold", "order_count"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/product-metrics/complaints", {
            "city": ["UP-NCR"]
        }),
        ("/api/v1/order-metrics/freebie", {
            "city": ["UP-NCR"]
        }),
        ("/api/v1/insights/ptype_metrics", {
            "city": ["UP-NCR"],
            "metrics_type": "contributed_conv_drop"
        }),
        ("/api/v1/insights/ptype_metrics", {
            "city": ["UP-NCR"],
            "metrics_type": "search_spike"
        }),
        #category endpoints
        ("/api/v1/category-metrics/all", {
            "metric": ["aov_contribution"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/category-metrics/all", {
            "metric": ["gmv"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/category-metrics/all", {
            "metric": ["total_items_sold"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/category-metrics/all", {
            "metric": ["unique_carts"],
            "city": ["UP-NCR"]
        }),
        ("/api/v1/category-metrics/insight", {
            "city": ["UP-NCR"]
        }),
        #emergency endpoint
        ("/api/v1/emergency-service-metrics/all/base-metrics", {
            "metric": [
                "total_cases",
                "approved_cases",
                "cancelled_cases",
                "completed_cases",
                "active_cases",
                "avg_eta_show"
            ],
            "date_str": today_date
        }),
        ## Bisto
        # Home Page APIs
        ("/api/bistro/v1/stores/storecitymapping", {}),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["gmv", "order_count", "aov"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["avg_checkout_to_enroute", "percentage_orders_delivered_in_15mins", "cancellation_percentage"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["surge_shown_carts_percentage", "billed_to_assigned", "ipc"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["batched_orders", "enroute_to_billed", "percentage_orders_arrived_doorstep_in_15mins"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["enroute_to_delivery", "enroute_to_doorstep", "percentage_cod_orders"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["direct_handover_wait_time", "indirect_handover_wait_time"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["promo_percentage", "promo_orders", "promo_charge"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["avg_kpt", "avg_wait_time", "avg_prep_time"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["avg_assembly_time", "percent_order_breaching_wait_prep_time", "percent_order_breaching_assembly_time"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/dau-metrics", {
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/hourly", {
            "metric": ["order_count", "surge_shown_carts_percentage", "billed_to_assigned", "avg_checkout_to_enroute", "enroute_to_billed"],
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/hau/pan_india", {
            "past_days_diff": "7",
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/ntu-metrics", {
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/rider-metrics/all", {
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/rider-metrics/hourly", {
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/complaints", {
            "city": ["Bengaluru"],
            "date_str": today_date
        }),
        ("/api/bistro/v1/rating-metrics/order", {
            "city": ["Bengaluru"],
            "date_str": today_date
        }),

        # Product page APIs with yesterday_metric and city/store
        ("/api/bistro/v1/product-metrics/all", {
            "metric": ["gmv", "order_count", "total_items_sold", "aov", "canceled_quantity", "unique_carts"],
            "yesterday_metric": "true",
            "city": "Bengaluru",
            "store": "39930"
        }),
        ("/api/bistro/v1/product-metrics/complaints", {
            "yesterday_metric": "true",
            "city": "Bengaluru",
            "store": "39930"
        }),
        ("/api/bistro/v1/rating-metrics/product", {
            "yesterday_metric": "true",
            "city": "Bengaluru",
            "store": "39930"
        }),

        # City/Stores page APIs 
        ("/api/bistro/v1/order-metrics/cities/stores", {
            "city": "Delhi",
            "date": today_date
        }),
        ("/api/bistro/v1/rider-metrics/cities/stores", {
            "city": "Delhi",
            "date": today_date
        }),
        ("/api/bistro/v1/rating-metrics/cities/stores", {
            "city": "Delhi",
            "date": today_date
        }),

        # WTD/MTD APIs with metrics and city
        ("/api/bistro/v1/wtd_metrics/all", {
            "metric": [
                "gmv", "order_count", "aov", "transacting_users_count", "delivered_order_count",
                "new_transacting_users_count", "rm", "rm_percentage", "wau", "order_conversion",
                "total_items_sold", "total_items_per_order", "avg_item_count", "order_per_store"
            ],
            "city": "HR-NCR"
        }),
        ("/api/bistro/v1/mtd_metrics/all", {
            "metric": [
                "gmv", "order_count", "aov", "transacting_users_count", "delivered_order_count",
                "new_transacting_users_count", "rm", "rm_percentage", "mau", "order_conversion",
                "total_items_sold", "total_items_per_order", "avg_item_count", "order_per_store"
            ],
            "city": "HR-NCR"
        }),


        # Kitchen page APIs with metrics, city, and date_str
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["order_volume", "billed_to_assigned", "direct_handover_wait_time"],
            "city": "Delhi",
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["indirect_handover_wait_time", "ipc", "avg_kpt"],
            "city": "Delhi",
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["avg_wait_time", "avg_prep_time", "avg_assembly_time"],
            "city": "Delhi",
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/all/base-metrics", {
            "metric": ["percent_order_breaching_wait_prep_time", "percent_order_breaching_assembly_time"],
            "city": "Delhi",
            "date_str": today_date
        }),
        ("/api/bistro/v1/order-metrics/hourly", {
            "metric": [
                "avg_kpt", "avg_wait_time", "avg_prep_time", "avg_assembly_time",
                "percent_order_breaching_wait_prep_time", "percent_order_breaching_assembly_time"
            ],
            "city": "Delhi",
            "date_str": today_date
        }),   
    ])



    def test_endpoint_returns_200(self, client, auth_headers, endpoint, params):
        """Test that each endpoint returns a 200 status code when properly authenticated"""
        with patch("app.api.deps.get_pinot_db"), \
             patch("app.api.deps.get_access_mapping", return_value={"city": {1: True}}):
            
            response = client.get(endpoint, headers=auth_headers, params=params)
            print(f"\nEndpoint: {endpoint}")
            print(f"Parameters: {params}")
            print(f"Status Code: {response.status_code}")
            # print(f"Response Headers: {response.headers}")
            # print(f"Response Body: {response.text}")
            print(f"------------------------------------------------------------------------")
            
            assert response.status_code == 200, f"Endpoint {endpoint} returned {response.status_code}"
