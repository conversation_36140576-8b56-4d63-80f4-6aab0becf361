from typing import Literal, List, Optional
from pydantic import BaseModel
from .order_metric import DatewiseMetric, HourlyDateMetricsWithType
from app.schemas.metric import Metric

class CurrentRateDatewiseMetric(BaseModel):
    name: Literal["GMV", "AOV", "Cart Volume", "Daily Active Users", "Projected Cart Volume", "Projected GMV"]
    data: List[DatewiseMetric]
    type: Literal["currency", "percentage", "number"]

class CurrentRateMetric(BaseModel):
    metrics: List[CurrentRateDatewiseMetric]

class ProjectionMetrics(BaseModel):
    metrics: List[Metric]
    yesterday_projection: Optional[bool]
    weekly_projection: Optional[bool]
    monthly_projection: Optional[bool]

class CurrentRateDailyHourly(BaseModel):
    daily_metrics: CurrentRateMetric
    hourly_metrics: List[HourlyDateMetricsWithType]