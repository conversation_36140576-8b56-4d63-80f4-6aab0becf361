import datetime
from typing import List, Literal, Optional
from pydantic import BaseModel

class S3Link(BaseModel):
    media_url: Optional[str]
    media_type: Optional[str]
    user_comment: Optional[str]
    description: Optional[str]
    
class Complaint(BaseModel):
    complaint_images: List[S3Link]
    order_id: str
    merchant_id: Optional[str]
    merchant_name: Optional[str]

class ComplaintOrdersImages(BaseModel):
    complaints_data: Optional[List[Complaint]]
