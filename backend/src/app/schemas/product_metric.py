import datetime
from typing import List, Literal, Optional, Annotated
from decimal import Decimal
from pydantic import BaseModel, Field, field_serializer
from app.schemas.order_metric import DatewiseMetric

# class DatewiseMetric(BaseModel):
#     date: datetime.date
#     metric: Annotated[Decimal, Field(decimal_places=2)]

#     @field_serializer('metric')
#     def serialize_metric(self, value):
#         """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
#         if isinstance(value, Decimal):
#             return float(value)
#         return value

class ProductDatewiseMetric(BaseModel):
    name: Literal[
        "GMV", "Cart Volume", "Total Items Sold", "Canceled Quantity", "New Transacting Customers", 
        "Transacting Customers", "Average Selling Price", "IPC", "AOV", "Unique Carts", "Cart Pen", "AOV Contribution",
        "Total Count", "Average Rating", "Rating 1 Count", "Rating 2 Count", "Rating 3 Count", "Rating 4 Count", "Rating 5 Count", "Freebie %", 
        "Order Count", "Canceled Quantity", "Order Cancellation"
    ]
    data: List[DatewiseMetric]
    type: Literal["currency", "percentage", "number", "time"]

class CategoryProductDatewiseMetric(BaseModel):
    item_name: str
    data: List[ProductDatewiseMetric]

class CategoryMetrics(BaseModel):
    metrics: List[CategoryProductDatewiseMetric]


class ComplaintsDateMetric(BaseModel):
    category_type: str
    date: datetime.date
    count: Annotated[Decimal, Field(decimal_places=2)]
    @field_serializer('count')
    def serialize_count(self, value):
        """Convert Decimal to float for JSON serialization"""
        if isinstance(value, Decimal):
            return float(value)
        return value


class ComplaintsDateMetricItemWise(BaseModel):
    item_name: Optional[str] = None
    complaint_type: Optional[str] = None
    date: datetime.date
    count: Annotated[Decimal, Field(decimal_places=2)]
    @field_serializer('count')
    def serialize_count(self, value):
        """Convert Decimal to float for JSON serialization"""
        if isinstance(value, Decimal):
            return float(value)
        return value


class ComplaintsDateMetricTypeWise(BaseModel):
    complaint_type: Optional[str] = None
    date: datetime.date
    count: Annotated[Decimal, Field(decimal_places=2)]
    @field_serializer('count')
    def serialize_count(self, value):
        """Convert Decimal to float for JSON serialization"""
        if isinstance(value, Decimal):
            return float(value)
        return value

class ItemComplaints(BaseModel):
    complaints: List[ComplaintsDateMetricItemWise]

class ComplaintsTypeWise(BaseModel):
    complaints: List[ComplaintsDateMetricTypeWise]


class RatingsDateMetricItemWise(BaseModel):
    item_name: str
    date: datetime.date
    rating: Annotated[Decimal, Field(decimal_places=2)]
    rating_count: int
    @field_serializer('rating')
    def serialize_rating(self, value):
        """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
        if isinstance(value, Decimal):
            return float(value)
        return value

class ItemRatings(BaseModel):
    ratings: List[RatingsDateMetricItemWise]


class ProductMetrics(BaseModel):
    metrics: List[ProductDatewiseMetric]


class L0CategoryProductMetrics(BaseModel):
    metrics: List[ProductDatewiseMetric]
    l0_category: str

class L0CategoryMetrics(BaseModel):
    data: List[L0CategoryProductMetrics]

class L1CategoryProductMetrics(BaseModel):
    metrics: List[ProductDatewiseMetric]
    l1_category: str
    
class L1CategoryMetrics(BaseModel):
    data: List[L1CategoryProductMetrics]
    l0_category: str



class ProductComplaints(BaseModel):
    complaints: List[ComplaintsDateMetric]


class ProductPrimaryDetails(BaseModel):
    l0_category: Optional[List]
    product_type: Optional[List]


class FilteredProductFieldwise(BaseModel):
    field: str
    filtered_values: List

class FieldwiseList(BaseModel):
    data: List[FilteredProductFieldwise]

class ProductMetric(BaseModel):
    metric: Annotated[Decimal, Field(decimal_places=2)]
    name: str
    type: str

    @field_serializer('metric')
    def serialize_metric(self, value):
        """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
        if isinstance(value, Decimal):
            return float(value)
        return value

class CityAllMetrics(BaseModel):
    city: str
    frontend_merchant_name: Optional[str]
    frontend_merchant_id: Optional[str]
    data: List[ProductMetric]

class DateAllCityMetrics(BaseModel):
    date: datetime.date
    data: List[CityAllMetrics]
    date_diff: int

class ProductDateWiseAllCityMetrics(BaseModel):
    metrics: List[DateAllCityMetrics]


class HourMetrics(BaseModel):
    hour: int
    data: List[ProductMetric]

class DatewiseHourMetrics(BaseModel):
    date: datetime.date
    data: List[HourMetrics]

class ProductHourWiseMetrics(BaseModel):
    metrics: List[DatewiseHourMetrics]


class ProductMetricTypes:
    product = 'product'
    search_spike = 'search_spike'
    absolute_conv_drop = 'absolute_conv_drop'
    contributed_conv_drop = 'contributed_conv_drop'
