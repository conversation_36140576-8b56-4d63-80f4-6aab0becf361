import datetime
from typing import List, Literal, Optional, Union
from decimal import Decimal
from pydantic import BaseModel, condecimal, field_serializer

from app.schemas.metric import Metric


class DatewiseMetric(BaseModel):
    date: datetime.date
    metric: Union[condecimal(decimal_places=2), str]

    @field_serializer('metric')
    def serialize_metric(self, value):
        """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
        if isinstance(value, Decimal):
            return float(value)
        return value

class OrderDatewiseMetric(BaseModel):
    name: Literal[
        "GMV", "ATH GMV", "AOV", "Cart Volume", "ATH Cart Volume", "Order Cancellation", "Transacting Customers", "Unique SKU/Order",
        "Daily Active Users", "%Delivery < 30mins", "%Delivery < 15mins",
        "%Delivery < 10mins", "% orders arrived in 10mins", "% orders arrived in 15mins",
        "Total Items Sold", "Surge Checkouts %", "Surge Paid Checkouts %", "Rain Surge Orders %", "Surge Shown %", "New Transacting Customers",
        "Unserviceable DAU %", "Demand Based Block %", "Express Demand Based Block %" , "Express OOH Based Block %", "Express Manual Based Block %", "DAU Conversion Percentage", "% ETA below 10mins",
        "Longtail Demand Based Block %", "Longtail OOH Based Block %", "Longtail Manual Based Block %",
        "<15 mins ETA%", "% ETA below 30mins", "Delivery Charge", "Delivery Cost Revenue", "Rider Login hrs",
        "New Riders", "Direct Handover %", "Rain Order %", "%Picker assigned 10 secs",
        "Projected Cart Volume", "Batched Order %", "Customer Paid Charges", "ATC %", "C2Co %",
        "Instore SLA % < 2.5 mins", "Picking Time Per Item", "Picker Surge Orders %", "Both Surge Orders %",
        "Fill Rate %", "IPO", "Total Active Hrs", "Picker Active Time %", "Putter Active Time %",
        "Auditor Active Time %", "FNV Active Time %", "OPH", "IPH", "New Manpower", "Total Complaints",
        "Picking Start % (5 secs)", "Total Orders", "Complaints %", "OTIF %", "Dispatch Fill Rate %", "OTD %",
        "Active Stores Count", "OPD Per Store",  "Overall Conv %", "Search Conv %", "Search Impressions", 
        "Unique Impressions", "Conv Δ Contri", "ATC Δ Serv Contri", "ATC Δ Demand Contri", "ATC Δ Misc Contri", 
        "C2Co Δ Serv Contri", "C2Co Δ Demand Contri", "C2Co Δ Misc Contri", "FE Availability", "Blocks Contribution to ATC",
        "DAU Contribution to ATC", "Avail & Other Contribution to ATC", "Blocks Contribution to C2Co", "DAU Contribution to C2Co",
        "Avail & Other Contribution to C2Co", "Surge Contribution", "FE Inventory", "Surge Seen %", "Rain Surge Seen %", "Picker Surge Seen %", "Rider Surge Seen %",
        "Search Spike %", "Conversion Drop %", "Item Rank", "ARS Created", "Sold Qty T0", "Sold Qty T1", "ARS CPD", "ARS Demand", "BE Inventory", "Indent V1",
        "STO Raised", "Truncation Qty", "Billed Qty","In Transit Qty", "GRN Qty", "DN", "B2B", "Current Availability", "Total Inventory", "Truncation Reason",
        "Max GRN Time", "STO Qty", "V1 Demand Gap", "Indent Gap", "Billed Fill Rate", "Dispatch Fill Rate", "GRN Fill Rate",
        "DN Fill Rate", "B2B Fill Rate", "Stores with Unavailability", "Store with Sale Spike", "Store - STO Fill Rate < 90 %",
        "Store with Storage Truncation", "Store with Fleet Truncation", "Store with Manpower Truncation", "Store with Inward Truncation", "Store - GRN Fill Rate < 90 %",
        "Line Items", "Created Qty", "Cancelled Qty", "IRT %", "Raise Audit %", "Picked Qty", "Merchandising Page DAUs", "Merchandising Page ATC%", "Cat Grid PLP DAUs", "Cat Grid PLP ATC%",
        "IPC", "Checkout to Enroute Time", "Free DC %", "Freebie %", "BE %", "FE %", "FE (Post STO) %", "Picking Fill Rate","Packaging Fill Rate",
        "Sort Fill Rate","Dispatch Fill Rate","Picking OTIF","Packaging OTIF","Sort OTIF","Dispatch OTIF","Picking Pendency",
        "Packaging Pendency","Sort Pendency","Dispatch Pendency", "Putters", "Pickers", "Packers", "Sorters", "Auditors", "On-Demand Active Hours", "Indirect Rider Handshake time",
        "Express Outlet Count", "Longtail Outlet Count", "True Fill Rate %", "% orders at doorstep in 15m", "Enroute to Delivery time", "Enroute to Doorstep time",
        "Total Count", "Average Rating", "Rating 1 Count", "Rating 2 Count", "Rating 3 Count", "Rating 4 Count", "Rating 5 Count", "Surge Charge", "Handling Charge", "Convenience Charge",
        "Night Charge", "Small Cart Charge", "Category Mix", "COD orders %", "Total Cases", "Approved Cases", "Cancelled Cases", "Completed Cases", "Active Cases", "Average ETA Shown",
        "Total Orders", "Cart Volume", "Cancellations", "Complaints",
        "Rider Handshake time", "Direct Rider Handshake time", "Promo %", "Promo Orders", "Promo Charge", "Average KPT", "Average Wait Time", "Average Preparation Time", "Average Assembly Time", "% Orders Breaching Wait+Prep Time", "% Orders Breaching Assembly Time", "Order Volume", 
        "Priority Customers Demand Block %", "Priority Customers OOH Block %", "Priority Customers Manual Block %", "Priority Customers %", "Station Wait Time", "Station Preparation Time",
        "Station Cart Volume", "Station Average KPT",

        # Assortment-type specific metrics
        "Express Cart Volume", "Longtail Cart Volume", "Super Longtail Cart Volume", "Unicorn Cart Volume",
        "Express Free DC %", "Longtail Free DC %", "Super Longtail Free DC %", "Unicorn Free DC %",
        "Express %Delivery < 10mins", "Longtail %Delivery < 10mins",
        "Super Longtail %Delivery < 10mins", "Unicorn %Delivery < 10mins",
        "Express Direct Handover %", "Longtail Direct Handover %",
        "Super Longtail Direct Handover %", "Unicorn Direct Handover %",
        "Express Surge Checkouts %", "Longtail Surge Checkouts %",
        "Super Longtail Surge Checkouts %", "Unicorn Surge Checkouts %",
        "Express Surge Paid Checkouts %", "Longtail Surge Paid Checkouts %",
        "Super Longtail Surge Paid Checkouts %", "Unicorn Surge Paid Checkouts %",
        "Express Batched Order %", "Longtail Batched Order %",
        "Super Longtail Batched Order %", "Unicorn Batched Order %",
        "Express Rain Order %", "Longtail Rain Order %",
        "Super Longtail Rain Order %", "Unicorn Rain Order %",
        "Express %Delivery < 15mins", "Longtail %Delivery < 15mins",
        "Super Longtail %Delivery < 15mins", "Unicorn %Delivery < 15mins",
        "Express %Delivery < 30mins", "Longtail %Delivery < 30mins",
        "Super Longtail %Delivery < 30mins", "Unicorn %Delivery < 30mins",
        "Express Order Cancellation", "Longtail Order Cancellation",
        "Super Longtail Order Cancellation", "Unicorn Order Cancellation",
        "Express %Picker assigned 10 secs", "Longtail %Picker assigned 10 secs",
        "Super Longtail %Picker assigned 10 secs", "Unicorn %Picker assigned 10 secs",


        # Union order metrics
        "Union Cart Volume", "Union Free DC %", "Union %Delivery < 10mins",
        "Union Direct Handover %", "Union Surge Checkouts %", "Union Surge Paid Checkouts %",
        "Union Batched Order %", "Union Rain Order %", "Union %Delivery < 15mins",
        "Union %Delivery < 30mins", "Union Order Cancellation",
        "Union %Picker assigned 10 secs",

        "Order Count",

        "Search Impressions",
        "Search Conversion %",
        "Spike % WoW1", 
    ]
    data: List[DatewiseMetric]
    type: Literal["currency", "percentage", "number", "time", "string"]


class AthDatewiseMetric(BaseModel):
    ath_data : List[OrderDatewiseMetric]


class OrderMetricsFilter(BaseModel):
    name: Literal["City", "Order Status"]
    data: List[str]


class OrderMetrics(BaseModel):
    metrics: List[OrderDatewiseMetric]


class LocationMetric(BaseModel):
    type: Literal["city", "region", "zone", "group", "station"]
    name: str
    data: List[Metric]


class StoreMetric(BaseModel):
    frontend_merchant_name: str
    frontend_merchant_id: str
    data: List[Metric]


class LocationDateMetric(BaseModel):
    date: datetime.date
    etl_snapshot_ts_ist: Optional[datetime.datetime]
    date_diff: int
    data: List[LocationMetric]


class StoreDateMetric(BaseModel):
    date: datetime.date
    etl_snapshot_ts_ist: Optional[datetime.datetime]
    date_diff: int
    data: List[StoreMetric]


class WarehouseStoreMetric(BaseModel):
    outlet_name: str
    outlet_id: Optional[str]
    frontend_merchant_name: Optional[str]
    shift: Optional[str]
    data: List[OrderDatewiseMetric]

class WarehouseSlotMetrics(BaseModel):
    at_risk: bool
    upcoming_slot: bool
    data: List[OrderDatewiseMetric]

class LocationDateMetrics(BaseModel):
    metrics: List[LocationDateMetric]


class StoreDateMetrics(BaseModel):
    metrics: List[StoreDateMetric]


class WarehouseStoreDateMetrics(BaseModel):
    metrics: List[WarehouseStoreMetric]


class HourMetric(BaseModel):
    hour: int
    data: List[Metric]


class HourlyDateMetric(BaseModel):
    date: datetime.date
    date_diff: int
    data: List[HourMetric]


class HourlyDateMetrics(BaseModel):
    metrics: List[HourlyDateMetric]


class HourlyDateMetricsWithType(BaseModel):
    metrics: List[HourlyDateMetric]
    type: str


class OutletNameMapping(BaseModel):
    outlet_id: str
    outlet_name: str


class OutletsList(BaseModel):
    filters: List[OutletNameMapping]


class StoresNameMapping(BaseModel):
    frontend_merchant_id: str
    frontend_merchant_name: str
    backend_merchant_id: str
    merchant_type: Optional[str]
    is_longtail_outlet: Optional[bool]


class StoresCityMapping(BaseModel):
    city: str
    # region: Optional[str]
    zone: Optional[str]
    data: List[StoresNameMapping]


class StoresCityList(BaseModel):
    filters: List[StoresCityMapping]


class StoreMetrics(BaseModel):
    frontend_merchant_id: str
    backend_merchant_id: str
    frontend_merchant_name: str
    surge_reason: List[str]
    metrics: List[OrderDatewiseMetric]


class StoreMetricsResponse(BaseModel):
    metrics: List[StoreMetrics]


class ConversionGroupMetric(BaseModel):
    key: str
    grain: Optional[str]
    metric: List[OrderDatewiseMetric]

class StationComplaintMetric(BaseModel):
    station_id: str
    date: datetime.date
    complaint_count: int

class StationComplaints(BaseModel):
    metrics: List[StationComplaintMetric]

class StationRatingMetric(BaseModel):
    station_id: str
    date: datetime.date
    avg_rating: Union[float, int]

class StationRatingsResponse(BaseModel):
    metrics: List[StationRatingMetric]

class StationMappingList(BaseModel):
    station_id: str
    station_name: str

class StationMapping(BaseModel):
    metrics: List[StationMappingList]
    

class OrderPerMinuteData(BaseModel):
    minute_bucket: str
    orders_per_minute: int

class OrdersPerMinute(BaseModel):
    metrics: List[OrderPerMinuteData]
    
