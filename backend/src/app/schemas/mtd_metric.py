import datetime
from typing import Literal, List, Optional, Annotated
from decimal import Decimal

from pydantic import BaseModel, Field, field_serializer

class MonthwiseMetric(BaseModel):
    date: datetime.date
    metric: Annotated[Decimal, Field(decimal_places=2)]
    
    @field_serializer('metric')
    def serialize_metric(self, value):
        """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
        if isinstance(value, Decimal):
            return float(value)
        return value

class MTDMetric(BaseModel):
    name: Literal["GMV", "AOV", "Cart Volume", "Delivered Cart Volume", "Order Cancellation", "Transacting Customers", "New Transacting Customers", "Monthly Active Users", "Retained Margin", "Absolute Retained Margin", "Order Conversion", "Total Items Sold", "Unique SKU/Order", "Total Items/Order", "Delivery Charge", "Delivery Cost Revenue", "Rider Login hrs", "Order Per Store/Day"]
    data: List[MonthwiseMetric]
    etl_snapshot_ts_ist: Optional[datetime.datetime]
    type: Literal["currency", "percentage", "number", "time"]


