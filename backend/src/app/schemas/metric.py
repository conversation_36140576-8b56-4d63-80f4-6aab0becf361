from typing import Literal, Union, Annotated
from decimal import Decimal
from pydantic import BaseModel, Field, field_serializer


class Metric(BaseModel):
    name: Literal[
        "GMV", "AOV", "Cart Volume", "Order Cancellation", "Transacting Customers", "Unique SKU/Order",
        "Daily Active Users", "Hourly Active Users", "Total Items Sold", "Stressed Stores", "Surge Checkouts %", "Surge Paid Checkouts %"
        "Rain Surge Orders %", "Surge Shown %", "New Transacting Customers", "Unserviceable DAU %", "Demand Based Block %", "Express Demand Based Block %",
        "Express OOH Based Block %", "Express Manual Based Block %","DAU Conversion Percentage", "HAU Conversion Percentage", "Delivery Charge", "Delivery Cost Revenue",
        "Rider Login hrs", "New Riders", "%Delivery < 15mins", "% orders arrived in 10mins",
        "% orders arrived in 15mins", "Direct Handover %", "Rain Order %", "%Picker assigned 10 secs",
        "Projected Cart Volume", "Projected GMV", "Batched Order %", "Customer Paid Charges", "ATC %", "C2Co %",
        "Instore SLA % < 2.5 mins", "Picking Time Per Item", "Picker Surge Orders %", "Both Surge Orders %",
        "Fill Rate %", "IPO", "Total Active Hrs", "Picker Active Time %", "Putter Active Time %",
        "Auditor Active Time %", "FNV Active Time %", "OPH", "IPH", "New Manpower", "Total Complaints",
        "Picking Start % (5 secs)", "Total Orders", "Complaints %", "OTIF %", "Dispatch Fill Rate %", "OTD %",
        "Active Stores Count", "OPD Per Store", "Delivered Cart Volume", "Total Items/Order", "Order Conversion", "Weekly Active Users",
        "Retained Margin", "Absolute Retained Margin", "Monthly Active Users", "Order Per Store/Day", "Surge Seen %", "Rain Surge Seen %",
        "Picker Surge Seen %", "Rider Surge Seen %", "Upcoming Dispatch", "Picking Pendency", "Picking ETC", "Checkout to Enroute Time", "On-Demand Active Hours",
        "Indirect Rider Handshake time", "Total Count", "Average Rating", "Rating 1 Count", "Rating 2 Count" , "Rating 3 Count" , "Rating 4 Count" , "Rating 5 Count",
        "Rider Handshake time", "Direct Rider Handshake time", "Average KPT", "Average Wait Time", "Average Preparation Time", "Average Assembly Time",
        "% Orders Breaching Wait+Prep Time", "% Orders Breaching Assembly Time", "Promo %", "Promo Orders", "Promo Charge",
        "Station Average KPT", "Station Cart Volume", "Station Wait Time", "Station Prep Time",
        
        # Assortment-type specific metrics
        "Express Cart Volume", "Longtail Cart Volume", "Super Longtail Cart Volume", "Unicorn Cart Volume",
        "Express Free DC %", "Longtail Free DC %", "Super Longtail Free DC %", "Unicorn Free DC %",
        "Express %Delivery < 10mins", "Longtail %Delivery < 10mins",
        "Super Longtail %Delivery < 10mins", "Unicorn %Delivery < 10mins",
        "Express Direct Handover %", "Longtail Direct Handover %",
        "Super Longtail Direct Handover %", "Unicorn Direct Handover %",
        "Express Surge Checkouts %", "Longtail Surge Checkouts %",
        "Super Longtail Surge Checkouts %", "Unicorn Surge Checkouts %",
        "Express Surge Paid Checkouts %", "Longtail Surge Paid Checkouts %",
        "Super Longtail Surge Paid Checkouts %", "Unicorn Surge Paid Checkouts %",
        "Express Batched Order %", "Longtail Batched Order %",
        "Super Longtail Batched Order %", "Unicorn Batched Order %",
        "Express Rain Order %", "Longtail Rain Order %",
        "Super Longtail Rain Order %", "Unicorn Rain Order %",
        "Express %Delivery < 15mins", "Longtail %Delivery < 15mins",
        "Super Longtail %Delivery < 15mins", "Unicorn %Delivery < 15mins",
        "Express %Delivery < 30mins", "Longtail %Delivery < 30mins",
        "Super Longtail %Delivery < 30mins", "Unicorn %Delivery < 30mins",
        "Express Order Cancellation", "Longtail Order Cancellation",
        "Super Longtail Order Cancellation", "Unicorn Order Cancellation",
        "Express %Picker assigned 10 secs", "Longtail %Picker assigned 10 secs",
        "Super Longtail %Picker assigned 10 secs", "Unicorn %Picker assigned 10 secs",
        
        # Union order metrics
        "Union Cart Volume", "Union Free DC %", "Union %Delivery < 10mins",
        "Union Direct Handover %", "Union Surge Checkouts %", "Union Surge Paid Checkouts %",
        "Union Batched Order %", "Union Rain Order %", "Union %Delivery < 15mins",
        "Union %Delivery < 30mins", "Union Order Cancellation",
        "Union %Picker assigned 10 secs",
    ]
    metric: Union[Annotated[Decimal, Field(decimal_places=2)], str]
    type: Literal["currency", "percentage", "number", "time", "string"]

    @field_serializer('metric')
    def serialize_metric(self, value):
        """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
        if isinstance(value, Decimal):
            return float(value)
        return value
