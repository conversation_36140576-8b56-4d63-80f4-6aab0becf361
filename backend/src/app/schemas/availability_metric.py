import datetime
from typing import Literal, List, Annotated
from decimal import Decimal

from pydantic import BaseModel, Field, field_serializer


class AvailabilityMetric(BaseModel):
    name: Literal["Weighted Availability", "Cumulative Weighted Availability"]
    metric: Annotated[Decimal, Field(decimal_places=2)]
    type: Literal["percentage"]
    
    @field_serializer('metric')
    def serialize_metric(self, value):
        """Convert Decimal to float for JSON serialization to maintain number format in frontend"""
        if isinstance(value, Decimal):
            return float(value)
        return value

class HourMetric(BaseModel):
    hour: int
    city: str
    type: str
    data: List[AvailabilityMetric]

class HourlyDateMetric(BaseModel):
    date: datetime.date
    date_diff: int
    data: List[HourMetric]

class HourlyDateMetrics(BaseModel):
    metrics: List[HourlyDateMetric]