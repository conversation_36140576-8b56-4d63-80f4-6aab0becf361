from typing import List, Optional, Annotated
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime

class GroupCityMapping(BaseModel):
    city: str
    stores: List[str] = []

class GroupBase(BaseModel):
    name: Annotated[str, Field(min_length=1)]
    type: str  # CITY or STORE
    is_global: bool = False

class GroupCreate(GroupBase):
    mappings: List[GroupCityMapping] = []

class GroupUpdate(BaseModel):
    name: Optional[Annotated[str, Field(min_length=1)]] = None
    type: Optional[str] = None  # CITY or STORE
    is_global: Optional[bool] = None
    mappings: Optional[List[GroupCityMapping]] = None

class GroupWithMappings(GroupBase):
    id: int
    created_by: Optional[int]
    mappings: List[GroupCityMapping]

    model_config = ConfigDict(from_attributes=True)

class GroupResponse(BaseModel):
    id: int
    name: str
    type: str
    is_global: bool

    model_config = ConfigDict(from_attributes=True)

class GroupList(BaseModel):
    groups: List[GroupResponse]
