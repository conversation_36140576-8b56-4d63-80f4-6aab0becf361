from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import QueuePool

from app.core.config import settings

# Explicitly creating engine with QueuePool
engine = create_engine(
    settings.PINOT_DB_URL, 
    poolclass=QueuePool,
    pool_size=50,
    max_overflow=100,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
    # Use LIFO strategy for better connection reuse
    pool_use_lifo=True
)
postgres_engine = create_engine(settings.SQLALCHEMY_DATABASE_URI, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=postgres_engine)


