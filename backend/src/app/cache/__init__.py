from app.cache.cache import RedisCache
from app.core.config import settings

THIRTY_SECONDS = 30
TEN_SECONDS = 10
TEN_MINUTES = 600
ELEVEN_MINUTES = 11 * 60
ONE_HOUR_IN_SECONDS = 3600
THIRTY_MINUTES = TEN_MINUTES * 3
SIX_HOURS_IN_SECONDS = ONE_HOUR_IN_SECONDS * 6
ONE_DAY_IN_SECONDS = ONE_HOUR_IN_SECONDS * 24
THREE_DAY_IN_SECONDS = ONE_HOUR_IN_SECONDS * 24 * 3
ONE_WEEK_IN_SECONDS = ONE_DAY_IN_SECONDS * 7
ONE_MONTH_IN_SECONDS = ONE_DAY_IN_SECONDS * 30
ONE_YEAR_IN_SECONDS = ONE_DAY_IN_SECONDS * 365
THREE_MINUTES = 3 * 60
FIVE_MINUTES = 5 * 60
NINE_MINUTES = 9 * 60
FIFTEEN_MINUTES = THREE_MINUTES * 5
TWENTY_NINE_MINUTES = 29 * 60

cache = RedisCache(redis_host=settings.REDIS_HOST_URL)
