import json
import logging
from typing import Dict, Optional, <PERSON><PERSON>

import redis
from redis import client

from app.cache.enums import RedisEvent, RedisStatus
from app.cache.coder import JsonCoder

logger = logging.getLogger(__name__)

class RedisCache():
    redis_host: str
    redis: client.Redis = None
    status: RedisStatus = RedisStatus.NONE

    @property
    def connected(self):
        return self.status == RedisStatus.CONNECTED

    def __init__(self, redis_host: str):
        self.redis_host = redis_host
        self.coder = JsonCoder()

    def connect(self):
        if self.redis:
            return self.redis

        self.log(RedisEvent.CONNECT_BEGIN, msg="Attempting to connect to Redis server...")
        try:
            self.redis = redis.from_url(self.redis_host)
            if self.redis.ping():
                self.status = RedisStatus.CONNECTED
                self.log(RedisEvent.CONNECT_SUCCESS, msg="Redis client is connected to server")
            else:
                self.status = RedisStatus.CONN_ERROR
                self.log(RedisEvent.CONNECT_FAIL, msg="Redis server did not respond to PING message.")

        except redis.AuthenticationError:
            self.status = RedisStatus.AUTH_ERROR
            self.log(RedisEvent.CONNECT_FAIL, msg="Unable to connect to redis server due to authentication error.")
        except redis.ConnectionError:
            self.status = RedisStatus.CONN_ERROR
            self.log(RedisEvent.CONNECT_FAIL, msg="Redis server did not respond to PING message.")

    def close(self):
        if self.redis:
            self.redis.close()

    def flushdb(self):
        self.redis.flushdb()

    def check_cache(self, key: str) -> Tuple[int, Dict]:
        pipe = self.redis.pipeline()
        ttl, in_cache = pipe.ttl(key).get(key).execute()
        if in_cache:
            self.log(RedisEvent.KEY_FOUND_IN_CACHE, key=key)
            return (ttl, self.coder.decode(in_cache))
        return (ttl, None)

    def add_to_cache(self, key: str, value: Dict, expire: int = None) -> bool:
        response_data = None
        try:
            response_data = self.coder.encode(value)
        except TypeError:
            message = f"Object of type {type(value)} is not JSON-serializable"
            self.log(RedisEvent.FAILED_TO_CACHE_KEY, msg=message, key=key)
            return False
        cached = self.redis.set(name=key, value=response_data, ex=expire)
        if cached:
            self.log(RedisEvent.KEY_ADDED_TO_CACHE, key=key)
        else:
            self.log(RedisEvent.FAILED_TO_CACHE_KEY, key=key, value=value)
        return cached

    def log(self, event: RedisEvent, msg: Optional[str] = None, key: Optional[str] = None, value: Optional[str] = None):
        message = f" {event.name}"
        if msg:
            message += f": {msg}"
        if key:
            message += f": key={key}"
        if value:
            message += f", value={value}"
        logger.info(message)


    def get_multiple_keys(self, keys: list) -> Dict[str, Optional[Dict]]:
        """
        Retrieve multiple keys from the cache.
        
        Args:
            keys (list): List of keys to retrieve.
            
        Returns:
            Dict[str, Optional[Dict]]: Dictionary of key-value pairs, where value is None if the key doesn't exist.
        """
        if not self.connected:
            self.log(RedisEvent.FAILED_TO_GET_KEYS, msg="Redis is not connected.")
            return {}

        try:
            values = self.redis.mget(keys)
            decoded_values = {
                key: self.coder.decode(value) if value else None
                for key, value in zip(keys, values)
            }
            self.log(RedisEvent.KEYS_FOUND_IN_CACHE, msg=f"Fetched multiple keys: {keys}")
            return decoded_values
        except Exception as e:
            self.log(RedisEvent.FAILED_TO_GET_KEYS, msg=str(e))
            return {}
