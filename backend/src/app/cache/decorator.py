import logging
from functools import wraps
from typing import List
from datetime import datetime, timezone, timedelta

import arrow

from app.cache import cache, THIRTY_SECONDS, THREE_DAY_IN_SECONDS, TEN_MINUTES, ONE_DAY_IN_SECONDS, SIX_HOURS_IN_SECONDS
from app.cache.key_builder import default_key_builder
from app.core.config import settings
from app.api import deps

logger = logging.getLogger(__name__)


def dynamic_cache_metrics(expire=THIRTY_SECONDS, tenant='Blinkit', **decorator_kwargs):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            yesterday_metric = kwargs.get('yesterday_metric', False)
            date_str = kwargs.get('date_str')
            date_param = kwargs.get('date')

            today_date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
            
            # Handle both date_str and date parameters (both are strings)
            target_date_str = date_str or date_param
            
            # Determine cache expiry based on whether it's historical data
            if (target_date_str and target_date_str != today_date_str) or (yesterday_metric):
                actual_expire = SIX_HOURS_IN_SECONDS
            else:
                actual_expire = expire

            # Only pass the decorator_kwargs to cache_metrics, not the function kwargs
            cached_func = cache_metrics(expire=actual_expire, tenant=tenant, **decorator_kwargs)(func)
            return cached_func(*args, **kwargs)
        return wrapper
    return decorator

def cache_metrics(
        expire: int = THIRTY_SECONDS, 
        force_cache_burst_at_eod: bool = False, 
        force_cache_burst_seconds_after_midnight_list: List[int] = None,
        tenant: str = 'Blinkit'
    ):

    def wrapper(func):

        @wraps(func)
        def inner(*args, **kwargs):
            copy_kwargs = kwargs.copy()

            # Pop the objects that get changed with every request
            # Applies to injected deps objects as well
            # TODO: Need to figure out a better way to solve this
            
            # Get the access_mapping to check for sensitive data access
            access_mapping = kwargs.get("access_mapping")
            city = copy_kwargs.get("city")
            store = copy_kwargs.get("store")
            
            # Add is_sensitive flag to cache key params if we have the necessary info
            is_sensitive = None
            if access_mapping:
                is_sensitive = deps.check_access(city, store, access_mapping, tenant=tenant)
                copy_kwargs["_is_sensitive"] = is_sensitive
                
            for key in ["request", "response", "db_conn", "http_client", "db_postgres", "access_mapping"]:
                copy_kwargs.pop(key, None)

            cache_key = default_key_builder(func, "metrics", args=args, kwargs=copy_kwargs)
            _, data = cache.check_cache(cache_key)

            # Since we don't set expiry, we need to check the ttl from the response
            # In case of force redis cache, we don't check for ttl and return whatever is saved
            if data and (settings.FORCE_REDIS_CACHE or is_ttl_epoch_valid(data.get("ttl_epoch", -1))):
                return data["response"]

            response =  func(*args, **kwargs)
            if response is None:
                return None

            # We are not setting expiry on the key, instead passing ttl_epoch
            # This ensure that if we want to have to bypass pinot (in case of downtime)
            # we can get the data from redis
            cache.add_to_cache(
                key=cache_key,
                value={
                    "response": response,
                    "ttl_epoch": fetch_ttl_epoch(expire, 
                                                 force_cache_burst_at_eod, 
                                                 force_cache_burst_seconds_after_midnight_list)
                },
                expire=ONE_DAY_IN_SECONDS
            )
            return response

        return inner

    return wrapper


def cache_sync_function(expire: int = THIRTY_SECONDS, force_cache_burst_at_eod: bool = False, cache_custom_key=None, tenant: str = 'Blinkit'):
    def wrapper(func):
        @wraps(func)
        def inner(*args, **kwargs):
            copy_kwargs = kwargs.copy()

            # Pop the objects that get changed with every request
            # Applies to injected deps objects as well
            # TODO: Need to figure out a better way to solve this
            
            # Get the access_mapping to check for sensitive data access
            access_mapping = kwargs.get("access_mapping")
            city = copy_kwargs.get("city")
            store = copy_kwargs.get("store")
            
            # Add is_sensitive flag to cache key params if we have the necessary info
            is_sensitive = None
            if access_mapping:
                is_sensitive = deps.check_access(city, store, access_mapping, tenant=tenant)
                copy_kwargs["_is_sensitive"] = is_sensitive
                
            for key in ["request", "response", "db_conn", "http_client", "db_postgres", "access_mapping"]:
                copy_kwargs.pop(key, None)
                
            if cache_custom_key:
                cache_key = default_key_builder(func, cache_custom_key)
            else:
                cache_key = default_key_builder(func, "metrics", args=args, kwargs=copy_kwargs)
            _, data = cache.check_cache(cache_key)

            # Since we don't set expiry, we need to check the ttl from the response
            # In case of force redis cache, we don't check for ttl and return whatever is saved
            if data and (settings.FORCE_REDIS_CACHE or is_ttl_epoch_valid(data.get("ttl_epoch", -1))):
                return data["response"]

            response =  func(*args, **kwargs)
            if response is None:
                return None

            # We are not setting expiry on the key, instead passing ttl_epoch
            # This ensure that if we want to have to bypass pinot (in case of downtime)
            # we can get the data from redis
            cache.add_to_cache(
                key=cache_key,
                value={
                    "response": response,
                    "ttl_epoch": fetch_ttl_epoch(expire, force_cache_burst_at_eod)
                },
                expire=ONE_DAY_IN_SECONDS
            )
            return response
        return inner
    return wrapper


# NOTE : pass variables as kwargs instead of args
def cache_class_metrics(
    expire: int = 30, 
    namespace: str = "metrics", 
    force_cache_burst_at_eod: bool = False, 
    force_cache_burst_seconds_after_midnight_list: List[int] = None,
    tenant: str = 'Blinkit'
):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Extract 'self' and class name for namespacing
            instance = args[0]
            class_name = instance.__class__.__name__
            
            # Exclude 'self' from arguments for cache key
            args_without_self = args[1:]

            # Remove transient or non-cacheable objects
            copy_kwargs = kwargs.copy()
            
            # Get the access_mapping to check for sensitive data access
            access_mapping = kwargs.get("access_mapping")
            city = copy_kwargs.get("city")
            store = copy_kwargs.get("store")
            
            # Add is_sensitive flag to cache key params if we have the necessary info
            is_sensitive = None
            if access_mapping:
                is_sensitive = deps.check_access(city, store, access_mapping, tenant=tenant)
                copy_kwargs["_is_sensitive"] = is_sensitive
                
            for key in ["request", "response", "db_conn", "http_client", "conn", "db_postgres", "access_mapping"]:
                copy_kwargs.pop(key, None)

            # Generate cache key
            cache_key = default_key_builder(
                func=func, 
                namespace=f"{namespace}:{class_name}", 
                args=args_without_self, 
                kwargs=copy_kwargs
            )

            # Check cache for the response
            _, cached_data = cache.check_cache(cache_key)
            if cached_data and (
                settings.FORCE_REDIS_CACHE or is_ttl_epoch_valid(cached_data.get("ttl_epoch", -1))
            ):
                return cached_data["response"]

            # Execute the original function
            response = func(*args, **kwargs)
            if response is None:
                return None

            # Store the response in cache
            cache.add_to_cache(
                key=cache_key,
                value={
                    "response": response,
                    "ttl_epoch": fetch_ttl_epoch(
                        expire, 
                        force_cache_burst_at_eod, 
                        force_cache_burst_seconds_after_midnight_list
                    ),
                },
                expire=ONE_DAY_IN_SECONDS
            )
            return response

        return wrapper
    return decorator


def fetch_ttl_epoch(
        expire: int, 
        force_cache_burst_at_eod: bool, 
        force_cache_burst_seconds_after_midnight_list: List[int] = None
    ):
    epoch_now = int(arrow.now().timestamp())
    compare_epochs = [epoch_now + expire]
    ist_midnight_epoch = get_eod_ttl_epoch()
    if force_cache_burst_at_eod:
        compare_epochs.append(ist_midnight_epoch)
    if force_cache_burst_seconds_after_midnight_list:
        compare_epochs.extend([ist_midnight_epoch + epoch for epoch in force_cache_burst_seconds_after_midnight_list])
    return min(filter(lambda x: (x >= epoch_now), compare_epochs))


def get_eod_ttl_epoch():
    return int(arrow.now('Asia/Kolkata').ceil('day').timestamp())


def is_ttl_epoch_valid(epoch: int):
    if epoch == -1:
        return False

    epoch_now = int(arrow.now().timestamp())
    return epoch >= epoch_now
