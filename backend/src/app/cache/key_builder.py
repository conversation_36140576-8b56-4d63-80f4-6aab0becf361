from asyncio.log import logger
import hashlib
from typing import Optional
from app.core.config import settings

from starlette.requests import Request
from starlette.responses import Response


def default_key_builder(
    func,
    namespace: Optional[str] = "",
    args: Optional[tuple] = None,
    kwargs: Optional[dict] = None,
):
    prefix = f"{settings.PROJECT_NAME}:{namespace}:"
    cache_key = (
        prefix
        + hashlib.md5(  # nosec:B303
            f"{func.__module__}:{func.__name__}:{args}:{kwargs}".encode()
        ).hexdigest()
    )
    return cache_key
