from typing import List, Dict, Optional
import arrow

from app.cache import cache, THREE_MINUTES

def set_cache_for_object(object_path: str, 
                         presigned_url: str, 
                         expire: int = THREE_MINUTES):
    is_cached = cache.add_to_cache(
        key=object_path,
        value={
            "presigned_url": presigned_url,
            "ttl_epoch": fetch_ttl_epoch(expire)
        },
        expire=expire
    )
    return is_cached


def get_cache_presigned_urls(object_paths : List[str]) -> Dict[str, Optional[Dict]]:
    return cache.get_multiple_keys(object_paths)


def fetch_ttl_epoch(expire: int):
    epoch_now = int(arrow.now().timestamp())
    return epoch_now + expire

def is_ttl_epoch_valid(epoch: int):
    if epoch == -1:
        return False

    epoch_now = int(arrow.now().timestamp())
    return epoch >= epoch_now