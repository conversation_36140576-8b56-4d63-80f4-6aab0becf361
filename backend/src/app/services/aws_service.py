import time
import boto3
import uuid
from botocore.config import Config
from botocore.exceptions import ClientError
from boto3.session import Session
from app.core.config import settings

class AWSClient:
    def __init__(self):
        self.s3_boto_client = None
        self.create_s3_connection()


    def create_s3_connection(self):
        if self.s3_boto_client:
            return
        self.s3_boto_client = boto3.client(
            "s3",
            settings.AWS_S3_REGION,
            config=Config(signature_version="s3v4", connect_timeout=30, read_timeout=30),
        )


    def generate_presigned_url(self, bucket_name, object_path, expires_in):
        if not bucket_name or not object_path or not expires_in: 
            return None

        try:
            self.create_s3_connection()

            presigned_url = self.s3_boto_client.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": bucket_name,
                    "Key": object_path,
                },
                ExpiresIn=expires_in,
            )
            return presigned_url
        except Exception as error:
            print(f"[Exception]: Unable to generate presigned_url for {object_path}. Failed with error: {error}")
            return None
