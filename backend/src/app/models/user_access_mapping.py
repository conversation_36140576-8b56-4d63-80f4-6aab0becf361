from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TIMESTAMP, String
from sqlalchemy.orm import relationship
from sqlalchemy.schema import UniqueConstraint
from app.db.base_class import Base
from sqlalchemy.sql import func
from sqlalchemy.sql import false
from sqlalchemy import text

class UserAccessMapping(Base):
    __tablename__ = "user_access_mapping"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    city_name = Column(String, nullable=False, server_default='all')
    store_id = Column(Integer, nullable=False, default=-1)
    is_sensitive = Column(Boolean(), nullable=False, server_default=false())
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.now())
    updated_at = Column(TIMESTAMP, nullable=False, server_default=func.now(), server_onupdate=func.now())
    tenant_id = Column(<PERSON><PERSON><PERSON>, <PERSON><PERSON>ey("tenant.id"), nullable=False)

    user = relationship("User", back_populates="user_access_mapping")
    
    __table_args__ = (
        UniqueConstraint('user_id', 'city_name', 'store_id', 'tenant_id', name='user_access_mapping_user_city_store_tenant_key'),
    )
