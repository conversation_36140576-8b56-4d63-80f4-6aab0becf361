from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Inte<PERSON>, String, TIMESTAMP, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.sql import false
from sqlalchemy.orm import relationship
from app.models.user_access_mapping import UserAccessMapping

from app.db.base_class import Base


class User(Base):
    __tablename__ = "user"

    id = Column(Integer, primary_key=True, index=True)
    full_name = Column(String, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    image_url = Column(String)
    is_allowed = Column(Boolean(), server_default=false())
    created_at = Column(TIMESTAMP, nullable=False, server_default=func.now())

    user_access_mapping = relationship("UserAccessMapping", back_populates="user")

    page_group_id = Column(Integer, ForeignKey("page_group.id"), nullable=True, server_default="1")
    page_group = relationship("PageGroup")
