from sqlalchemy import Column, Integer, String, ForeignKey, UniqueConstraint
from app.db.base_class import Base

class Page(Base):
    __tablename__ = "page"

    id = Column(Integer, primary_key=True, index=True)
    page_name = Column(String, nullable=False, unique=False)
    tenant_id = Column(Integer, ForeignKey("tenant.id"), nullable=False)

    __table_args__ = (
        UniqueConstraint('tenant_id', 'page_name', name='page_tenant_name_unique'),
    )
