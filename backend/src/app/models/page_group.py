from sqlalchemy import Column, Integer, String, ARRAY, ForeignKey, UniqueConstraint
from app.db.base_class import Base

class PageGroup(Base):
    __tablename__ = "page_group"

    id = Column(Integer, primary_key=True, index=True)
    group_name = Column(String, nullable=False, unique=False)
    page_ids = Column(ARRAY(Integer), nullable=False)
    tenant_id = Column(Integer, ForeignKey("tenant.id"), nullable=False)

    __table_args__ = (
        UniqueConstraint('tenant_id', 'group_name', name='page_group_tenant_name_unique'),
    )
