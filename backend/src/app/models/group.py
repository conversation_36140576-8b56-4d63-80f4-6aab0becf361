from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, UniqueConstraint, TIMESTAMP, text
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class Group(Base):
    __tablename__ = "groups"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    is_global = Column(Boolean, server_default=text('false'), nullable=False)
    created_by = Column(Integer, ForeignKey("user.id", ondelete="CASCADE"), nullable=False)
    created_at = Column(TIMESTAMP, server_default=text('now()'), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=text('now()'), nullable=False)

    mappings = relationship("GroupCityStoreMapping", back_populates="group", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('name', 'created_by', name='unique_group_name_per_user'),
    )

class GroupCityStoreMapping(Base):
    __tablename__ = "group_city_store_mapping"

    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(Integer, ForeignKey("groups.id", ondelete="CASCADE"), nullable=False)
    city = Column(String, nullable=False)
    store = Column(String, nullable=False)
    created_at = Column(TIMESTAMP, server_default=text('now()'), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=text('now()'), nullable=False)

    group = relationship("Group", back_populates="mappings")

    __table_args__ = (
        UniqueConstraint('group_id', 'city', 'store', name='unique_group_city_store'),
    )
