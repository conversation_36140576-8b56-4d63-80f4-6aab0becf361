from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from sqlalchemy.schema import UniqueConstraint

class UserTenantMapping(Base):
    __tablename__ = "user_tenant_mapping"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    tenant_id = Column(Integer, ForeignKey("tenant.id"), nullable=False)
    is_allowed = Column(Boolean, default=False, nullable=False)
    is_global_group_allowed = Column(Boolean, default=False, nullable=False)
    page_group_id = Column(Integer, ForeignKey("page_group.id"), nullable=True)

    # Relationships
    user = relationship("User")
    tenant = relationship("Tenant")
    page_group = relationship("PageGroup")

    __table_args__ = (
        UniqueConstraint('user_id', 'tenant_id', name='user_tenant_mapping_user_tenant_key'),
    )
