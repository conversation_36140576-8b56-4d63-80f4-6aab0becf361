from collections import defaultdict
from datetime import datetime, timedelta, timezone
import math
from decimal import Decimal
from app.schemas.order_metric import ConversionGroupMetric,OrderDatewiseMetric
from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING
from app.schemas.order_metric import Metric, LocationMetric
from typing import List, Dict, Any, Optional
from app.schemas.product_metric import ProductMetricTypes

IST = timezone(timedelta(hours=5, minutes=30))
UTC = timezone(timedelta(hours=0))


def handle_percentage_metrics(value):
    if isinstance(value, (float, int)) and not math.isnan(value) and not math.isinf(value):
        return round(float(value), 2)
    else:
        return 0.00


def process_metric_data(api_results: List[Dict], metrics: List[str]) -> Dict[str, List]:
    metric_data = {metric: [] for metric in metrics}
    for row in api_results:
        upto_date = datetime.strptime(row["snapshot_date"], "%Y-%m-%d")
        for metric in metrics:
            metric_value = row.get(metric, None)
            if metric_value is None or (isinstance(metric_value, (int, float)) and metric_value == 0):
                continue
            if isinstance(metric_value, float):
                metric_value = round(metric_value, 2)
            metric_data[metric].append({"date": upto_date, "metric": metric_value})
    return metric_data


def get_etl_snapshot_ts(api_results: List[Dict]) -> datetime:
    return max(
        datetime.fromtimestamp(int(d["etl_snapshot_ts_ist"]) / 1000000)
        for d in api_results
    )


def create_metrics_data(row: Dict, metrics: List[str]) -> List[Metric]:
    return [
        Metric(name=METRIC_NAME_MAPPING[metric], metric=round(row[metric], 2) if isinstance(row[metric], float) else row[metric], type=METRICS_TYPE_MAPPING[metric])
        for metric in metrics if metric in row and row[metric] is not None
    ]


def process_citywise_results(api_results: List[Dict], metrics: List[str], region_name: str = None) -> Dict:
    citywise_results = defaultdict(list)
    etl_snapshot_ts_ist_obj = get_etl_snapshot_ts(api_results)
    for row in api_results:
        metrics_data = create_metrics_data(row, metrics)
        # citywise_results[(row["date"], etl_snapshot_ts_ist_obj)].append(LocationMetric(type="city", name=row["city"], data=metrics_data))
        citywise_results[(row["snapshot_date"], etl_snapshot_ts_ist_obj)].append(LocationMetric(type="city", name=row["city_name"], data=metrics_data))
    return citywise_results


def process_zonewise_results(api_results: List[Dict], metrics: List[str], city_name: str) -> Dict:
    zonewise_results = defaultdict(list)
    etl_snapshot_ts_ist_obj = get_etl_snapshot_ts(api_results)
    for row in api_results:
        metrics_data = create_metrics_data(row, metrics)
        # zonewise_results[(row["date"], etl_snapshot_ts_ist_obj)].append(LocationMetric(type="zone", name=row["zone"], data=metrics_data))
        zonewise_results[(row["snapshot_date"], etl_snapshot_ts_ist_obj)].append(LocationMetric(type="zone", name=row["zone_name"], data=metrics_data))
    return zonewise_results


def get_date_str(is_yesterday: bool):
    today = datetime.now(IST)
    return (today - timedelta(days=1)).strftime('%Y-%m-%d') if is_yesterday else today.strftime('%Y-%m-%d')


def is_yesterday_date(date_str):
   date_obj = datetime.strptime(date_str, '%Y-%m-%d')
   today = datetime.now(IST)
   yesterday = today - timedelta(days=1)
   return date_obj.date() == yesterday.date()


def get_dates_in_ist_format(start_date: str, jump: int, num_of_outputs: int):
    # Parse the start date string into a datetime object
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')

    # Calculate the list of dates based on the jump and number of outputs
    dates = []
    for i in range(num_of_outputs):
        # Calculate the date based on the jump
        target_date = start_date_obj - timedelta(days=i * jump)
        # Format the date as 'YYYY-MM-DD'
        formatted_date = target_date.strftime('%Y-%m-%d')
        dates.append(formatted_date)

    return dates
    
def generate_insights_metrics_sql(metrics, num_weeks, timestamp_col,datatype, is_item_level = False):
    column_list = []
    
    if is_item_level:
        for metric in metrics:
            base_col = f"{metric}"
            if base_col in ("ars_created_ts_ist", "truncation_reason", "max_grn_ts_ist"):
                formatted_col = f"LASTWITHTIME({base_col},{timestamp_col},'STRING') as {base_col}"
            elif base_col == "current_availability" :
                formatted_col = f"LASTWITHTIME({base_col},{timestamp_col},'DOUBLE')*100 as {base_col}"
            else:
                formatted_col = f"LASTWITHTIME({base_col},{timestamp_col},'{datatype}') as {base_col}"
            column_list.append(formatted_col)

    else:
        for metric in metrics:
            for i in range(num_weeks):
                week_offset = i * 7
                base_col = f"{metric}_t{week_offset}"
                formatted_col = f"LASTWITHTIME({base_col},{timestamp_col},'{datatype}') as {base_col}"
                column_list.append(formatted_col)
    
    metrics_sql = ',\n'.join(column_list)
    
    return metrics_sql

def sort_by_conversion_drop_contribution(data: List[ConversionGroupMetric]) -> List[ConversionGroupMetric]:
    def get_conversion_drop_contribution(store: ConversionGroupMetric) -> float:
        for metric in store.metric:
            if metric.name == "Conv Δ Contri":
                # Assuming there's only one entry and it's for the current date
                return metric.data[0].metric
        return float('-inf')  # Return negative infinity if no value found for sorting

    sorted_data = sorted(data, key=get_conversion_drop_contribution, reverse=False)
    return sorted_data

def sort_conversion_group_metrics(
    data: List[ConversionGroupMetric],
    metrics_type: str = ""
) -> List[ConversionGroupMetric]:

    # Define maps to store metric data
    metric_map = {}
    overall_metric_today = Decimal(0)

    # Get the appropriate attribute for sorting
    attr = "grain"

    # Extract today's relevant metric and "Overall" values
    for group in data:
        key_name = getattr(group, attr)
        for metric in group.metric:
            if metric.name == "Search Impressions":
                metric_today = metric.data[0].metric  # Metric for today
                metric_map[key_name] = metric_today
                if key_name == "Overall" and metrics_type != ProductMetricTypes.search_spike:
                    overall_metric_today = metric_today
                break
    
    sortMetricName = "Search Conv %"
    if metrics_type == ProductMetricTypes.search_spike:
        sortMetricName = "Search Impressions"

    # Define sorting score map
    sorting_scores = {}

    for key_name in metric_map:
        if key_name == "Overall":
            continue  # Skip "Overall"

        metric_today = metric_map[key_name]
        sorting_delta = Decimal(0)

        # Calculate the delta based on the sorting type
        for group in data:
            if getattr(group, attr) == key_name:
                for metric in group.metric:
                    if metric.name == sortMetricName:
                        if len(metric.data) >= 2:
                            sorting_delta = metric.data[0].metric - metric.data[1].metric
                break

        # Calculate the sorting score
        if metrics_type == ProductMetricTypes.search_spike:
            sorting_scores[key_name] = sorting_delta
        elif overall_metric_today > 0:
            score = (metric_today / overall_metric_today) * sorting_delta
            sorting_scores[key_name] = score

    # Filter out "Overall" and sort by the calculated scores
    data_filtered = [group for group in data if getattr(group, attr) != "Overall"]

    sorted_data = sorted(
        data_filtered,
        key=lambda x: sorting_scores.get(getattr(x, attr), 0),
        reverse=(metrics_type == ProductMetricTypes.search_spike)  # Ascending order -> False
    )

    if metrics_type == ProductMetricTypes.search_spike:
        return sorted_data[:20]
    
    return sorted_data


def remove_metric(sorted_data: List[ConversionGroupMetric], conversion_funnel_metrics: bool) -> List[ConversionGroupMetric]:
    # Define the metric name to be removed based on the boolean input
    metric_to_remove = "Daily Active Users" 
    # if conversion_funnel_metrics else "Search Impressions"
    
    # Iterate through the data and remove the specified metric
    for group in sorted_data:
        group.metric = [metric for metric in group.metric if metric.name != metric_to_remove]
    
    return sorted_data

#function to calculate total_search_impressions by sunning for all ptype's
def add_overall_search_impressions(data: List[ConversionGroupMetric]) -> List[ConversionGroupMetric]:
    today_date = None
    
    for group in data:
        for metric in group.metric:
            if metric.name == "Search Impressions" and metric.data:
                today_date = metric.data[0].date
                break
        if today_date is not None:
            break
    
    if today_date is None:
        raise ValueError("Could not determine today's date - no Search Impressions data found")

    total_search_impressions = Decimal(0)
    
    for group in data:
        for metric in group.metric:
            if metric.name == "Search Impressions":
                for data_point in metric.data:
                    if data_point.date == today_date:
                        total_search_impressions += Decimal(data_point.metric)

    overall_metric = ConversionGroupMetric(
        key=data[0].key,
        grain="Overall",
        metric=[
            OrderDatewiseMetric(
                name="Search Impressions",
                data=[
                    {
                        "date": today_date,
                        "metric": total_search_impressions
                    }
                ],
                type="number"
            )
        ]
    )

    data.append(overall_metric)

    return data


def remove_abs_conv_drop(data: List[ConversionGroupMetric]) -> List[ConversionGroupMetric]:
    # remove items having "Converdion Drop %" as 100% or "Search Conv %" drop is more than 5%
    mutatedData = []
    for group in data:
        metric_to_append = True
        for metric in group.metric:
            if metric.name == "Conversion Drop %":
                for data in metric.data:
                    if abs(data.metric) == 100:
                        metric_to_append = False
                        break
            if metric.name == "Search Conv %":
                # drop is more than 5% i.e. (t_0 - t_7  < -5%)
                if metric.data[1].metric - metric.data[0].metric < 5.00:
                    metric_to_append = False
                    break
        if metric_to_append:
            mutatedData.append(group)

    return mutatedData

def remove_low_search_spike(data: List[ConversionGroupMetric]) -> List[ConversionGroupMetric]:
    #remove ptypes with search spike lower than 20%
    mutatedData = []
    for group in data:
        metric_to_append = True
        for metric in group.metric:
            if metric.name == "Search Spike %":
                for data in metric.data:
                    if data.metric < 20.00:
                        metric_to_append = False
        if metric_to_append:
            mutatedData.append(group)

    return mutatedData


def generate_dt_filters_custom_date(date_str: str, 
                                    column_name: str,
                                    num_intervals: int = 4, 
                                    interval_days: int = 7, 
                                    start_idx: int = 0) -> str:
    base_date = datetime.strptime(date_str, "%Y-%m-%d")
    filters = [
        f"({column_name} = '{(base_date - timedelta(days=i * interval_days)).strftime('%Y-%m-%d')}')"
        for i in range(start_idx, num_intervals + 1)
    ]
    return " OR\n".join(filters)


def extract_indices_from_dt_filters(dt_filters_str: str, indices: list) -> str:
    """
    Extracts specific indices from dt_filters_str and returns the formatted string.

    :param dt_filters_str: The original filter string joined by " OR\n".
    :param indices: A list of indices to extract.
    :return: A formatted string containing only the extracted filters.
    """
    dt_filters_list = dt_filters_str.split(" OR\n")
    filtered_dt_filters = [dt_filters_list[i] for i in indices if i < len(dt_filters_list)]    
    return " OR\n".join(filtered_dt_filters)


def generate_dynamic_dt_filters_with_backend_calc(
    date_column: str = "order_date",
    timestamp_column: str = "insert_timestamp",
    num_weeks: int = 5,
    period_length_days: int = 7,
    time_column_in_seconds: bool = False,
    less_than_time_in_utc_ms: Optional[int] = None,
    yesterday_metric: bool = False,
    start_date: Optional[str] = None,
) -> str:
    """
    Generates date filters with: 
    - Applying timestamp filters for today, else only date filters
    - IST date generation
    - Current time minus weekly offsets for comparisons
    - All calculations done in backend
    """
    DATE_FILTER = '%Y-%m-%d'
    IST_OFFSET = timedelta(hours=5, minutes=30) # IST is UTC + 5 hours and 30 minutes
    NOW_UTC = datetime.utcnow()                 # Get current time in UTC and convert to IST
    NOW_IST = NOW_UTC + IST_OFFSET
    MS_PER_DAY = 86400 * 1000
    TODAYS_DATE_STR = NOW_IST.strftime(DATE_FILTER)

    starting_ms = int(NOW_UTC.timestamp() * 1000)    # Current UTC in milliseconds
    if less_than_time_in_utc_ms: 
        starting_ms = less_than_time_in_utc_ms

    dt_filters = []

    # Calculate the reference date
    if start_date:
        start_date_ist = datetime.strptime(start_date, DATE_FILTER)
    else:
        if not yesterday_metric: 
            start_date_ist = NOW_IST.replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            start_date_ist = NOW_IST.replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1)

    apply_timestamp_filter = (start_date_ist.strftime(DATE_FILTER) == TODAYS_DATE_STR)
    for week_num in range(num_weeks):
        # date calculations
        days_offset = period_length_days * week_num
        target_date_ist = start_date_ist - timedelta(days=days_offset)
        date_str = target_date_ist.strftime(DATE_FILTER)

        # calculate offset in milliseconds
        offset_ms = MS_PER_DAY * days_offset
        cutoff_ms = starting_ms - offset_ms

        if apply_timestamp_filter: 
            if time_column_in_seconds:
                filter_str = f"({date_column} = '{date_str}' AND {timestamp_column} <= {cutoff_ms/1000})"
            else:
                filter_str = f"({date_column} = '{date_str}' AND {timestamp_column} <= {cutoff_ms})"
        else: 
            filter_str = f"({date_column} = '{date_str}')"
        dt_filters.append(filter_str)

    return " OR\n".join(dt_filters)


def get_todays_date_in_ist() -> str:
    """
    Returns today's date in IST timezone.
    """
    ist_offset = timedelta(hours=5, minutes=30)
    now_ist = datetime.now() + ist_offset
    return now_ist


def get_yesterdays_date_in_ist() -> str:
    """
    Returns yesterday's date in IST timezone.
    """
    ist_offset = timedelta(hours=5, minutes=30)
    now_ist = datetime.now() + ist_offset
    yesterday_ist = now_ist - timedelta(days=1)
    return yesterday_ist

