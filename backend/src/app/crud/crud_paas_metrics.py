
from typing import List, Dict
from pinotdb import connect

from app.core.metric_config_paas import DOCUMENT_PRINT_PRODUCT_IDS, PHOTO_PRINT_PRODUCT_IDS, PASSPORT_PHOTO_PRODUCT_IDS
from app import crud

class CRUDPaasMetrics():
    
    def _build_paas_filters(self,
                          city: List[str] = None,
                          store: List[str] = None,
                          print_type: str = None) -> Dict:
        """
        Build filters for PaaS metrics that can be passed to product metrics CRUD
        """
        # Convert store list items to strings if they are integers
        if store and isinstance(store, list):
            store = [str(s) for s in store]
            
        # Convert city list items to strings if needed
        if city and isinstance(city, list):
            city = [str(c) for c in city]
            
        # Initialize filters
        filters = {
            'l0_category': None,
            'l1_category': ['Print as a service'],
            'l2_category': None,
            'ptype': None,
            'pname': None,
            'brand': None,
            'city': city,
            'store': store,
            'pid': None
        }
        
        # Add print type filtering if specified
        if print_type and print_type != 'all':
            print_type_mapping = {
                'document': DOCUMENT_PRINT_PRODUCT_IDS,
                'photo': PHOTO_PRINT_PRODUCT_IDS,
                'passport': PASSPORT_PHOTO_PRODUCT_IDS
            }
            if print_type in print_type_mapping:
                filters['pid'] = [str(pid) for pid in print_type_mapping[print_type]]
        
        return filters
    
    def get_all_metrics(self,
                        conn: connect,
                        metrics: List = None,
                        date_str: str = None,
                        city: List[str] = None,
                        store: List[str] = None,
                        print_type: str = None,
                        is_daywise: bool = False,
                        current_hour: bool = True,):
        """
        Get all PaaS metrics using the product metrics CRUD
        """
        filters = self._build_paas_filters(city, store, print_type)
        
        result = crud.product_metrics.get_all_metrics(
            conn=conn,
            metrics=metrics,
            l0_category=filters['l0_category'],
            l1_category=filters['l1_category'],
            l2_category=filters['l2_category'],
            ptype=filters['ptype'],
            pname=filters['pname'],
            brand=filters['brand'],
            city=filters['city'],
            store=filters['store'],
            date_str=date_str,
            yesterday_metric=False,
            is_daywise=is_daywise,
            current_hour=current_hour, # will be used for today metrics, else won't matter
            pid=filters['pid'],
            number_of_weeks=5,
        )
        return result
    
    def get_hourly_metrics(self,
                          conn: connect,
                          metrics: List[str] = None,
                          city: List[str] = None,
                          store: List[str] = None,
                          print_type: str = None,
                          date_str: str = None,):
        """
        Get PaaS hourly metrics using the product metrics CRUD
        """
        filters = self._build_paas_filters(city, store, print_type)
        
        result = crud.product_metrics.get_all_metrics_hourwise(
            conn=conn,
            metrics=metrics,
            l0_category=filters['l0_category'],
            l1_category=filters['l1_category'],
            l2_category=filters['l2_category'],
            ptype=filters['ptype'],
            pname=filters['pname'],
            brand=filters['brand'],
            city=filters['city'],
            store=filters['store'],
            date_str=date_str,
            yesterday_metric=False,
            is_daywise=False,
            current_hour=False,
            pid=filters['pid']
        )
        return result
    
    def get_all_metrics_all_cities(self,
                                  conn: connect,
                                  metrics: List = None,
                                  date_str: str = None,
                                  city: List[str] = None,
                                  store: List[str] = None,
                                  print_type: str = None,
                                  yesterday_metric: bool = False):
        """
        Get PaaS metrics by city/store using the product metrics CRUD
        """
        filters = self._build_paas_filters(city, store, print_type)
        
        result = crud.product_metrics.get_all_metrics_all_cities(
            conn=conn,
            metrics=metrics,
            l0_category=filters['l0_category'],
            l1_category=filters['l1_category'],
            l2_category=filters['l2_category'],
            ptype=filters['ptype'],
            pname=filters['pname'],
            brand=filters['brand'],
            city=filters['city'],
            store=filters['store'],
            yesterday_metric=yesterday_metric,
            pid=filters['pid']
        )
        return result
        
    def get_complaints_data(self,
                           conn: connect,
                           city: List[str] = None,
                           store: List[str] = None,
                           print_type: str = None,
                           date_str: str = None,):
        """
        Get PaaS complaints data using the product metrics CRUD
        """
        # Build filters
        filters = self._build_paas_filters(city, store, print_type)
        
        # Call product metrics complaints with our filters
        result = crud.product_metrics.get_complaints(
            conn=conn,
            l0_category=filters['l0_category'],
            l1_category=filters['l1_category'],
            l2_category=filters['l2_category'],
            ptype=filters['ptype'],
            pname=filters['pname'],
            brand=filters['brand'],
            city=filters['city'],
            store=filters['store'],
            yesterday_metric=False,
            is_daywise=False,
            current_hour=True,
            pid=filters['pid'],
            date_str=date_str,
            number_of_weeks=5,
        )
        return result


paas_metrics = CRUDPaasMetrics()
