from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math
import httpx
from app.core.config import settings

from pinotdb import connect
from app.api import deps, utils

from .queries import get_distinct_order_ids_with_merchant_info_query

IST = timezone(timedelta(hours=5, minutes=30))
from app.schemas.complaints import Complaint

class CRUDComplaints():
    def get_complaint_images(self, conn: connect, 
                        http_client: httpx.Client,
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        start_date: str = None,
                        end_date: str = None,
                        pid: List = None) -> List[Complaint]:
        
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if l0_category:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l0_category', 'product_id', product_id) IN (%(l0_category)s)")
            params["l0_category"] = l0_category
        if l1_category:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l1_category', 'product_id', product_id) IN (%(l1_category)s)")
            params["l1_category"] = l1_category
        if l2_category:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l2_category', 'product_id', product_id) IN (%(l2_category)s)")
            params["l2_category"] = l2_category
        if ptype:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'product_type', 'product_id', product_id) IN (%(ptype)s)")
            params["ptype"] = ptype
        if pname:
            dynamic_where_clause.append("AND product_name IN (%(pname)s)")
            params["pname"] = pname
        if brand:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'brand_name', 'product_id', product_id) IN (%(brand)s)")
            params["brand"] = brand
        if city:
            dynamic_where_clause.append("AND LOOKUP('outlet_facility_mapping_v1', 'city_name', 'outlet_id', store_id) IN (%(city)s)")
            params["city"] = city
        if store:
            dynamic_where_clause.append("AND LOOKUP('outlet_facility_mapping_v1', 'merchant_id', 'outlet_id', store_id) IN (%(store)s)")
            params["store"] = store
        if pid:
            dynamic_where_clause.append("AND product_id IN (%(pid)s)")
            params["pid"] = pid
    
        query = get_distinct_order_ids_with_merchant_info_query(
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            start_date=start_date,
                                            end_date=end_date)
        
        curs.execute(query, params)

        order_info = {}
        for row in curs:
            order_info[str(row[0])] = {
                "merchant_id": str(row[1]),
                "frontend_merchant_name": str(row[2])
            }

        order_ids_list = list(order_info.keys())
        splitted_order_ids = []
        for start_idx in range(0, len(order_ids_list), 50):
            splitted_order_ids.append(order_ids_list[start_idx:min(start_idx + 50, len(order_ids_list))])

        complaints = []
        with http_client as client:
            for order_ids_chunk in splitted_order_ids:
                response = self.fetch_s3_urls(client, order_ids=order_ids_chunk)
                if response.status_code == 200: 
                    result = response.json()
                    for order_id, complaint_data in result.items():
                        merchant_info = order_info.get(order_id, {})
                        complaints.append(
                            Complaint(
                                complaint_images=complaint_data['media_data'],
                                order_id=order_id,
                                merchant_id=merchant_info.get("merchant_id"),
                                merchant_name=merchant_info.get("frontend_merchant_name")
                            )
                        )
        return complaints

    def fetch_s3_urls(self, client: httpx.Client, order_ids: List) -> None:
        url = settings.COMPLAINT_IMAGES_URL
        headers = {
            'Content-Type': 'application/json',
            'Authorization': settings.COMPLAINT_IMAGES_TOKEN
        }
        data = {
            "filter_type": "order_id",
            "values": order_ids
        }
        response = client.post(url, headers=headers, json=data)
        return response
    

complaints = CRUDComplaints()
