from typing import Any, Dict, Optional, Union

from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import Session

from app.core.config import settings
from app.schemas.user import UserBase
from app.models.user import User

class CRUDUser():
    def get_by_email(self, db: Session, *, email: str):
        return db.query(User).filter(User.email == email).first()

    def create(self, db: Session, *, obj_in: UserBase):
        db_obj = User(
            email=obj_in.email,
            full_name=obj_in.full_name,
            image_url=obj_in.image_url,
            is_allowed=settings.USER_REGISTRAION_ALLOWED
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db: Session, *, user_email: str, obj_in: UserBase):
        try:
            # Fetch the user by email
            db_obj = db.query(User).filter(User.email == user_email).one()
        except NoResultFound:
            raise ValueError("User not found")

        # Update the fields that are provided in obj_in
        obj_data = obj_in.dict(exclude_unset=True)
        for field, value in obj_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

user = CRUDUser()
