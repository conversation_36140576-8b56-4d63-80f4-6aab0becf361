from sqlalchemy.orm import Session
from app.models.user_tenant_mapping import UserTenantMapping

class CRUDUserTenantMapping:
    def get_tenant_mappings_by_user_id(self, db: Session, *, user_id: int):
        tenants_access_data = db.query(UserTenantMapping).filter(
            UserTenantMapping.user_id == user_id, 
            UserTenantMapping.is_allowed == True
        ).all()

        tenants_access_info = []
        for tenant_access_data in tenants_access_data:
            tenants_access_info.append({
                "tenant_id": tenant_access_data.tenant_id,
                "is_allowed": tenant_access_data.is_allowed,
                "page_group_id": tenant_access_data.page_group_id,
                "is_global_group_allowed": tenant_access_data.is_global_group_allowed,
            })
        return tenants_access_info

user_tenant_mapping = CRUDUserTenantMapping()
