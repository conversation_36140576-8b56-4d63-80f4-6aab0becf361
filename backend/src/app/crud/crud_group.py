from typing import List, Optional, Union, Dict, Any, Tuple
from sqlalchemy.orm import Session
from fastapi.encoders import jsonable_encoder
from fastapi import HTT<PERSON>Exception
from datetime import datetime, timezone, timedelta

from app.models.user import User
from app.models.group import Group, GroupCityStoreMapping
from app.schemas.group import GroupCreate, GroupUpdate, GroupWithMappings, GroupCityMapping
from app import crud
from app.core import const

class CRUDGroup:
    
    def create(self, db: Session, *, group: GroupCreate, user_id: int) -> Group:
        existing_group = db.query(Group).filter(
            Group.name == group.name,
            Group.created_by == user_id
        ).first()
        
        if existing_group:
            raise HTTPException(
                status_code=400,
                detail=f"Group with name '{group.name}' already exists for this user"
            )

        group_data = jsonable_encoder(group, exclude={'mappings'})
        db_obj = Group(**group_data, created_by=user_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)

        if group.mappings:
            for city_mapping in group.mappings:
                for store in city_mapping.stores:
                    db_mapping = GroupCityStoreMapping(
                        group_id=db_obj.id,
                        city=city_mapping.city,
                        store=store
                    )
                    db.add(db_mapping)
            db.commit()

        return db_obj
    

    def update(self, db: Session, *, db_group: Group, update_group: Union[GroupUpdate, Dict[str, Any]]) -> Group:
        update_data = update_group if isinstance(update_group, dict) else update_group.dict(exclude_unset=True)
        
        # Check for name uniqueness if name is being updated
        if "name" in update_data and update_data["name"] != db_group.name:
            existing_group = db.query(Group).filter(
                Group.name == update_data["name"],
                Group.created_by == db_group.created_by,
                Group.id != db_group.id
            ).first()
            
            if existing_group:
                raise HTTPException(
                    status_code=400,
                    detail=f"Group with name '{update_data['name']}' already exists for this user"
                )
        
        # Update basic fields
        for field in update_data:
            if field != 'mappings' and hasattr(db_group, field):
                setattr(db_group, field, update_data[field])
        
        db_group.updated_at = datetime.now(timezone(timedelta(hours=5, minutes=30)))
        db.add(db_group)
        db.commit()
        db.refresh(db_group)

        if "mappings" in update_data:
            # Delete existing mappings
            db.query(GroupCityStoreMapping).filter(
                GroupCityStoreMapping.group_id == db_group.id
            ).delete()
            
            # Add new mappings
            for city_mapping in update_data["mappings"]:
                for store in city_mapping["stores"]:
                    db_mapping = GroupCityStoreMapping(
                        group_id=db_group.id,
                        city=city_mapping["city"],
                        store=store
                    )
                    db.add(db_mapping)
            db.commit()

        return db_group


    def delete(self, db: Session, *, id: int) -> Group:
        obj = db.query(Group).get(id)
        db.delete(obj)
        db.commit()
        return obj


    def get_all(self, db: Session) -> List[Group]:
        return db.query(Group).all()


    def get_global_groups(self, db: Session) -> List[Group]:
        return db.query(Group).filter(Group.is_global == True).all()


    def get_user_groups(self, db: Session, user_id: int) -> List[Group]:
        return db.query(Group).filter(Group.created_by == user_id).all()


    def get_by_type(self, db: Session, *, group_type: str) -> List[Group]:
        return db.query(Group).filter(
            Group.type == group_type.upper()
        ).all()


    def get(self, db: Session, id: int) -> Optional[Group]:
        return db.query(Group).filter(Group.id == id).first()


    def get_with_mappings(self, db: Session, id: int) -> Optional[GroupWithMappings]:
        group = db.query(Group).filter(Group.id == id).first()
        
        if not group:
            return None
        
        mappings = db.query(GroupCityStoreMapping)\
            .filter(GroupCityStoreMapping.group_id == id)\
            .all()
        
        city_store_map: Dict[str, List[str]] = {}
        for mapping in mappings:
            if mapping.city not in city_store_map:
                city_store_map[mapping.city] = []
            city_store_map[mapping.city].append(mapping.store)
        
        return GroupWithMappings(
            id=group.id,
            name=group.name,
            type=group.type,
            is_global=group.is_global,
            created_by=group.created_by,
            mappings=[
                GroupCityMapping(city=city, stores=stores)
                for city, stores in city_store_map.items()
            ]
        )


    def get_accessible_groups(self, db: Session, db_user: User, tenant_id: int = const.BLINKIT_TENANT_ID) -> List[GroupWithMappings]:
        tenant_mappings = crud.user_tenant_mapping.get_tenant_mappings_by_user_id(
            db=db, user_id=db_user.id
        )
        tenant_access = next(
            (mapping for mapping in tenant_mappings if mapping["tenant_id"] == tenant_id),
            None
        )
        if not tenant_access:
            raise HTTPException(
                status_code=403,
                detail="User does not have access to this tenant"
            )

        access_mapping = crud.user_access_mapping.get_mappings_by_user_id(
            db=db,
            userId=db_user.id,
            tenants_access_info=[tenant_access]
        )
        
        tenant_name = const.TENANT_NAME_MAPPING[tenant_id]
        user_access_data = access_mapping[tenant_name]['userCityStoreAccessData']

        global_groups = self.get_global_groups(db)

        # [Will be showing only global grps for now]
        # user_groups = self.get_user_groups(db, user_id=db_user.id)
        
        accessible_global_groups = []
        for group in global_groups:
            group_with_mappings = self.get_with_mappings(db, group.id)
            if group_with_mappings:
                can_access = True
                
                if any(mapping.city_name == 'all' for mapping in user_access_data):
                    accessible_global_groups.append(group_with_mappings)
                    continue

                for mapping in group_with_mappings.mappings:
                    city_mappings = [m for m in user_access_data if m.city_name == mapping.city]
                    if not city_mappings:
                        can_access = False
                        break
                    
                    has_all_store_access = any(m.store_id == -1 for m in city_mappings)
                    if has_all_store_access:
                        continue

                    for store in mapping.stores:
                        store_id = int(store) if store != '-1' else -1
                        if store_id != -1 and not any(m.store_id == store_id for m in city_mappings):
                            can_access = False
                            break
                    
                    if not can_access:
                        break
                
                if can_access:
                    accessible_global_groups.append(group_with_mappings)


        seen_ids = set()
        combined_groups = []
        for group in accessible_global_groups:
            if group.id not in seen_ids:
                seen_ids.add(group.id)
                combined_groups.append(group)
        
        # [Will be showing only global grps for now]
        # for group in user_groups:
        #     if group.id not in seen_ids:
        #         group_with_mappings = self.get_with_mappings(db, group.id)
        #         if group_with_mappings:
        #             seen_ids.add(group.id)
        #             combined_groups.append(group_with_mappings)
        
        return combined_groups


    def get_city_store_from_group(
        self,
        db: Session,
        group_id: Optional[str],
        city: Optional[List[str]],
        store: Optional[List[str]]
    ) -> Tuple[Optional[List[str]], Optional[List[str]]]:
        if not group_id:
            return city, store
            
        group = self.get_with_mappings(db, int(group_id))
        if not group:
            return city, store
        
        group_cities = [mapping.city for mapping in group.mappings]
        group_stores = [store for mapping in group.mappings for store in mapping.stores if store != '-1']
        
        filtered_cities = group_cities if not city else list(set(city) & set(group_cities))
        filtered_stores = group_stores if not store else list(set(store) & set(group_stores))
        
        return filtered_cities, filtered_stores


group = CRUDGroup()
