from collections import defaultdict, OrderedDict
from datetime import datetime, timedelta, timezone
from typing import List, Union, Any, Dict
import httpx

from app.core.metric_config import WAREHOUSE_METRICS_CALC_MAPPING, WAREHOUSE_METRICS_TYPE_MAPPING, WAREHOUSE_METRICS_NAME_MAPPING
from app.schemas.order_metric import OrderDatewiseMetric, DatewiseMetric, WarehouseStoreMetric,\
    WarehouseStoreDateMetrics, OutletsList, OutletNameMapping, WarehouseSlotMetrics

from app.schemas.metric import Metric
from pinotdb import connect
from app.cache.decorator import cache_sync_function
from app.cache import ONE_HOUR_IN_SECONDS
from .queries import get_warehouse_mapping_query, get_warehouse_outbound_metrics, get_demand_ids,\
    get_warehouse_dispatch_slot_wise_metrics, get_availability_metrics, get_warehouse_activity_rates, get_warehouse_manpower_metrics


IST = timezone(timedelta(hours=5, minutes=30))
UTC = timezone(timedelta(hours=0))


@cache_sync_function(expire=ONE_HOUR_IN_SECONDS, cache_custom_key="warehouse_shift_mapping")
def get_outlet_shift_mapping(db_conn: connect=None):
    curs = db_conn.cursor()
    params = {}
    SHIFT_MAPPING_QUERY = """SELECT outlet_id,
        morning_shift_time,
        evening_shift_time
    FROM warehouse_shift_mapping_v1
    LIMIT 10000
    """
    curs.execute(SHIFT_MAPPING_QUERY, params)
    return {f"{row[0]}":[row[1], row[2]] for row in curs}


def build_warehouse_orders_shift_clause(
        conn: connect,
        outlets: List[Any], 
        previous_shift: bool=False,
        last_7_days: bool=False,
    ):
    where_clauses = []
    outlet_shifts = dict()
    current_timestamp_utc = datetime.now(UTC)
    outlet_shift_dict = get_outlet_shift_mapping(conn)
    # PAN INDIA Case
    if not outlets:
        outlets = list(outlet_shift_dict.keys())
    for outlet in outlets:
        shift1_start = datetime.strptime(outlet_shift_dict[outlet][0], '%H:%M').time()
        shift2_start = datetime.strptime(outlet_shift_dict[outlet][1], '%H:%M').time()
        current_timestamp = current_timestamp_utc
        current_timestamp_ist = current_timestamp + timedelta(minutes=330)
        # Morning Shift
        if current_timestamp_ist.time() >= shift1_start and current_timestamp_ist.time() < shift2_start:
            if previous_shift:
                shift_type = "evening"
                shift_start_ist_str = (current_timestamp_ist - timedelta(days=1)).strftime("%Y-%m-%d ") + outlet_shift_dict[outlet][1]
            else:
                shift_type = "morning"
                shift_start_ist_str = (current_timestamp_ist).strftime("%Y-%m-%d ") + outlet_shift_dict[outlet][0]
        # One Shift or Evening Shift
        else:
            if shift1_start == shift2_start:
                delta_time = timedelta(days=1)
            else:
                delta_time = datetime.strptime("2024-10-24 " + outlet_shift_dict[outlet][1], '%Y-%m-%d %H:%M') - datetime.strptime("2024-10-24 " + outlet_shift_dict[outlet][0], '%Y-%m-%d %H:%M')
                shift_type = "evening"
            if current_timestamp_ist.time() < shift1_start:
                shift_start_ist_str = (current_timestamp_ist - timedelta(days=1)).strftime("%Y-%m-%d ") + outlet_shift_dict[outlet][1]
                if previous_shift:
                    shift_type = "morning"
                    shift_start_ist_str = (current_timestamp_ist - timedelta(days=1) - delta_time).strftime("%Y-%m-%d ") + outlet_shift_dict[outlet][0]
            else:
                shift_start_ist_str = (current_timestamp_ist).strftime("%Y-%m-%d ") + outlet_shift_dict[outlet][1]
                if previous_shift:
                    shift_type = "morning"
                    shift_start_ist_str = (current_timestamp_ist - delta_time).strftime("%Y-%m-%d ") + outlet_shift_dict[outlet][0]
            if shift1_start == shift2_start:
                shift_type = "one"
        outlet_shifts[outlet] = shift_start_ist_str
        if shift_type == "morning":
            day0_min_epoch = int((datetime.strptime(shift_start_ist_str, '%Y-%m-%d %H:%M') - timedelta(minutes=330)).replace(hour=0, minute=0, tzinfo=timezone(timedelta(hours=0))).timestamp())
            day0_max_epoch = int((datetime.strptime(shift_start_ist_str, '%Y-%m-%d %H:%M') - timedelta(minutes=330)).replace(hour=12, minute=0, tzinfo=timezone(timedelta(hours=0))).timestamp())
        elif shift_type == "evening":
            day0_min_epoch = int((datetime.strptime(shift_start_ist_str, '%Y-%m-%d %H:%M') - timedelta(minutes=330)).replace(hour=12, minute=0, tzinfo=timezone(timedelta(hours=0))).timestamp())
            day0_max_epoch = int((datetime.strptime(shift_start_ist_str, '%Y-%m-%d %H:%M') - timedelta(minutes=330) + timedelta(days=1)).replace(hour=0, minute=0, tzinfo=timezone(timedelta(hours=0))).timestamp())
        else:
            day0_min_epoch = int((datetime.strptime(shift_start_ist_str, '%Y-%m-%d %H:%M') - timedelta(minutes=330)).replace(hour=0, minute=0, tzinfo=timezone(timedelta(hours=0))).timestamp())
            day0_max_epoch = int((datetime.strptime(shift_start_ist_str, '%Y-%m-%d %H:%M') - timedelta(minutes=330) + timedelta(days=1)).replace(hour=0, minute=0, tzinfo=timezone(timedelta(hours=0))).timestamp())
        cmp_epochs = [(day0_min_epoch, day0_max_epoch)]
        # WIP enable when past shift queries become easier 
        # for i in range(7,0,-1):
        #     break
        #     cmp_epochs.append((day0_min_epoch - 86400*i, day0_max_epoch - 86400*i))
        #     if not last_7_days:
        #         break
        epoch_cmp_str = " OR ".join([f"(insert_ts >= {min_epoch}000 AND insert_ts < {max_epoch}000)" for min_epoch, max_epoch in cmp_epochs])
        where_clauses.append(f"(outlet_id = {outlet} AND ({epoch_cmp_str}))")
    return outlet_shifts, " OR ".join(where_clauses)


class CRUDWarehouseMetrics():
    def get_warehouse_mapping(self, conn: connect)-> OutletsList:
        curs = conn.cursor()
        params = {}
        WAREHOUSE_LIST_QUERY = get_warehouse_mapping_query()
        curs.execute(WAREHOUSE_LIST_QUERY, params)
        results = []
        for row in curs:
            results.append(OutletNameMapping(outlet_id=str(row[0]),
                              outlet_name=str(row[1])))
        return OutletsList(filters=results)
    

    def get_dispatch_slot_metrics(
        self,
        conn: connect,
        outlet: List,
        previous_shift: bool = False
    )-> Dict[str, WarehouseSlotMetrics]:
        current_timestamp_ist = datetime.now(tz=IST)
        epoch_time = int(current_timestamp_ist.timestamp())
        curs = conn.cursor()
        params = {}

        # Get current shift of warehouse and orders(demand_ids) created for that shift
        current_outlet_shifts, orders_where_clause = build_warehouse_orders_shift_clause(conn, outlet, previous_shift)
        DEMAND_ID_FILTER_QUERY = get_demand_ids(orders_where_clause)
        curs.execute(DEMAND_ID_FILTER_QUERY, params)

        # Datewise demand_ids map
        # demand_id_dict = {"2024-12-01": "OBD_431075,OBD_431325,OBD_131275"}
        demand_id_dict = dict()
        for row in curs:
            demand_id_dict[row[1]] = row[0]

        dynamic_where_clause = []
        if outlet:
            dynamic_where_clause.append(
                "AND outlet_id IN (%(outlet_ids)s)"
            )
            params["outlet_ids"] = outlet

        # Calculate different activity rates(picking(picking zone wise)/packaging(overall outlet level)/sorting(overall outlet level)) based on last 30 minutes
        # row[0] - outlet_id
        # row[1] - picking_zone (only for picking events else 'null')
        metrics_sql = f"""{WAREHOUSE_METRICS_CALC_MAPPING['picked_qty']},
        {WAREHOUSE_METRICS_CALC_MAPPING['packaging_qty']},
        {WAREHOUSE_METRICS_CALC_MAPPING['sorting_qty']}
        """
        WAREHOUSE_ACTIVITY_RATE_QUERY = get_warehouse_activity_rates(metrics_sql, dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(WAREHOUSE_ACTIVITY_RATE_QUERY, params)
            

        warehouse_activity_rate_dict = {
            "picking_rate": dict(),
            "packaging_rate": dict(),
            "sorting_rate": dict()
        }

        for row in curs:
            if row[1] != 'null':
                warehouse_activity_rate_dict["picking_rate"][f"{row[0]}:{row[1]}"] = row[2]
            else:
                warehouse_activity_rate_dict["packaging_rate"][f"{row[0]}"] = row[3]
                warehouse_activity_rate_dict["sorting_rate"][f"{row[0]}"] = row[4]

        # Final datewise calculations
        warehouse_activity_metric_dict = dict()
        for date_str, demand_ids in demand_id_dict.items():
            params = {}
            dynamic_where_clause = []
            if outlet:
                dynamic_where_clause.append(
                    "AND outlet_id IN (%(outlet_ids)s)"
                )
                params["outlet_ids"] = outlet
            
            if demand_ids:
                dynamic_where_clause.append(
                    "demand_id IN (%(demand_id)s)"
                )
                params["demand_id"] = demand_ids
            
            dynamic_where_clause.append(
                "event_name IN (%(event_names)s)"
            )
            params["event_names"] = ['PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND', 'PICK_LIST_CREATED', 
                                     'PICK_LIST_CANCELLED', 'PICK_LIST_PARTIALLY_CANCELLED']
        
            # Calculating picking rate separately, as we need to find out the slowest picking time completion across each pick zone
            # The query results are ordered by dispatch time to calculate running pendencies 
            # (items left to be processed in the previous slot are also considered for upcoming slots) 
            # row[0] - outlet_id
            # row[1] - outlet_name
            # row[2] - dispatch_time
            # row[3] - picking_zone
            metrics_sql = f",{WAREHOUSE_METRICS_CALC_MAPPING['picking_pendency']}"    # row[4]
            DISPATCH_SLOT_METRICS_QUERY = get_warehouse_dispatch_slot_wise_metrics(
                metrics_sql, dynamic_where_clause=" AND ".join(dynamic_where_clause), is_slotwise=True, order_by_dispatch=True)
            curs.execute(DISPATCH_SLOT_METRICS_QUERY, params)

            picking_dispatch_metric_dict = dict()
            picking_running_pendency_dict = dict()
            for row in curs:
                if row[2] not in picking_dispatch_metric_dict:
                    picking_dispatch_metric_dict[row[2]] = {
                        "picking_pendency": 0,
                        "picking_etc": epoch_time
                    }
                if row[3] not in picking_running_pendency_dict:
                    picking_running_pendency_dict[row[3]] = 0
                picking_running_pendency_dict[row[3]] += row[4]
                if picking_running_pendency_dict[row[3]] and picking_running_pendency_dict[row[3]] > 0:
                    if f"{row[0]}:{row[3]}" in warehouse_activity_rate_dict["picking_rate"] and warehouse_activity_rate_dict["picking_rate"][f"{row[0]}:{row[3]}"] > 0:
                        # Calculating max picking completion time for each dispatch slot across all picking zones ((running pendency/picking rate)*60*30)
                        picking_dispatch_metric_dict[row[2]]["picking_etc"] = max(epoch_time + int((picking_running_pendency_dict[row[3]]*60*30)/warehouse_activity_rate_dict["picking_rate"][f"{row[0]}:{row[3]}"]), 
                                                                   picking_dispatch_metric_dict[row[2]]["picking_etc"])
                    # Adding picking 
                    picking_dispatch_metric_dict[row[2]]["picking_pendency"] += row[4]
            
            params = {}
            dynamic_where_clause = []

            if outlet:
                dynamic_where_clause.append(
                    "AND outlet_id IN (%(outlet_ids)s)"
                )
                params["outlet_ids"] = outlet
            
            if demand_ids:
                dynamic_where_clause.append(
                    "demand_id IN (%(demand_id)s)"
                )
                params["demand_id"] = demand_ids
            
            # row[0] - outlet_id
            # row[1] - outlet_name
            # row[2] - dispatch_time
            # row[3] - line_items
            # row[3] - line_items
            # row[4] - created_qty
            # row[5] - cancelled_qty
            # row[6] - picked_qty
            # row[7] - packaging_qty
            # row[8] - sorting_qty
            # row[9] - dispatch_qty
            # row[10] - on_time_picked_qty
            # row[11] - on_time_packed_qty
            # row[12] - on_time_sorted_qty
            # row[13] - on_time_dispatch_qty
            # row[14] - packaging_pendency
            # row[15] - irt
            # row[16] - raise_audit
            # row[17] - wave_created_qty
            metrics_sql = f""",{WAREHOUSE_METRICS_CALC_MAPPING['line_items']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['created_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['cancelled_qty']}

            ,{WAREHOUSE_METRICS_CALC_MAPPING['picked_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['packaging_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['sorting_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['dispatch_qty']}

            ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_picked_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_packed_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_sorted_qty']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_dispatch_qty']}

            ,{WAREHOUSE_METRICS_CALC_MAPPING['packaging_pendency']}

            ,{WAREHOUSE_METRICS_CALC_MAPPING['irt']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['raise_audit']}

            ,{WAREHOUSE_METRICS_CALC_MAPPING['wave_created_qty']}
            """
            # Calculating outbound warehouse slot-wise metrics
            DISPATCH_SLOT_METRICS_QUERY = get_warehouse_dispatch_slot_wise_metrics(metrics_sql, dynamic_where_clause=" AND ".join(dynamic_where_clause), order_by_dispatch=True)
            curs.execute(DISPATCH_SLOT_METRICS_QUERY, params)

            running_pendencies = {
                "packaging": 0,
                "sort": 0
            }

            upcoming_slot_found = False
            for row in curs:
                
                if row[2] not in warehouse_activity_metric_dict:
                    warehouse_activity_metric_dict[row[2]] = {"line_items": [], "created_qty":[], "cancelled_qty": [],
                        "pick_fill_rate": [], "packaging_fill_rate":[], "sort_fill_rate": [], "dispatch_fill_rate": [],
                        "pick_otif": [], "packaging_otif":[], "sort_otif": [], "dispatch_otif": [],
                        "picking_pendency": [], "packaging_pendency":[], "sort_pendency": [], "dispatch_pendency": [],
                        "irt": [], "raise_audit":[], "at_risk": False, "upcoming_slot": False}
                
                warehouse_activity_metric_dict[row[2]]["line_items"].append(DatewiseMetric(
                    date=date_str,
                    metric=row[3]))
                warehouse_activity_metric_dict[row[2]]["created_qty"].append(DatewiseMetric(
                    date=date_str,
                    metric=row[4]))
                warehouse_activity_metric_dict[row[2]]["cancelled_qty"].append(DatewiseMetric(
                    date=date_str,
                    metric=row[5]))

                # pick fill rate = picked_items/total_items
                warehouse_activity_metric_dict[row[2]]["pick_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[6]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))
                
                # packaging fill rate = packed_items/total_items
                warehouse_activity_metric_dict[row[2]]["packaging_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[7]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))
                
                # sort fill rate = sorted_items/total_items
                warehouse_activity_metric_dict[row[2]]["sort_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[8]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))
                
                # dispatch fill rate = dispatched_items/total_items
                warehouse_activity_metric_dict[row[2]]["dispatch_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[9]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))

                # pick otif = picked_items (within 90 minutes of dispatch time)/total_items
                warehouse_activity_metric_dict[row[2]]["pick_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[10]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))

                # packaging otif = packed_items (within 60 minutes of dispatch time)/total_items
                warehouse_activity_metric_dict[row[2]]["packaging_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[11]*100/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))
                
                # sort otif = sorted_items (within 30 minutes of dispatch time)/total_items
                warehouse_activity_metric_dict[row[2]]["sort_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[12]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))
                
                # dispatch otif = dispatched_items (within 30 minutes after dispatch time)/total_items
                warehouse_activity_metric_dict[row[2]]["dispatch_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[13]*100.0/row[17]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[17] else '-'))
                
                warehouse_activity_metric_dict[row[2]]["picking_pendency"].append(DatewiseMetric(
                    date=date_str,
                    metric=0))
                warehouse_activity_metric_dict[row[2]]["packaging_pendency"].append(DatewiseMetric(
                    date=date_str,
                    metric=max(row[14], 0)))

                # sort_pendency = packed_qty - sorted_qty
                warehouse_activity_metric_dict[row[2]]["sort_pendency"].append(DatewiseMetric(
                    date=date_str,
                    metric=max(row[7] - row[8], 0)))
                # dispatch_pendency = sorted_qty - dispatched_qty
                warehouse_activity_metric_dict[row[2]]["dispatch_pendency"].append(DatewiseMetric(
                    date=date_str,
                    metric=max(row[8] - row[9], 0)))
                
                warehouse_activity_metric_dict[row[2]]["irt"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(float(row[15]), 2)).replace('nan', '-').replace('inf', '-')))
                warehouse_activity_metric_dict[row[2]]["raise_audit"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(float(row[16]), 2)).replace('nan', '-').replace('inf', '-')))
                if row[2] in picking_dispatch_metric_dict:
                    warehouse_activity_metric_dict[row[2]]["picking_pendency"].append(DatewiseMetric(
                        date=date_str,
                        metric=picking_dispatch_metric_dict[row[2]]["picking_pendency"]))
                    
                    # setting slot at risk (if the largest expected picking time is more than 90 minutes before dispatch)
                    warehouse_activity_metric_dict[row[2]]["at_risk"] = (row[2] - picking_dispatch_metric_dict[row[2]]["picking_etc"] < 5400)
                
                # calculating running pendencies, if the previous slot had some pending items, they would be carried forward to upcoming slots as well
                running_pendencies["packaging"] += max(row[14], 0)
                running_pendencies["sort"] += max(row[7] - row[8], 0)

                # setting slot at risk (if expected packaging time is more than 60 minutes before dispatch)
                if f"{row[0]}" in warehouse_activity_rate_dict["packaging_rate"] and warehouse_activity_rate_dict["packaging_rate"][f"{row[0]}"]:
                    warehouse_activity_metric_dict[row[2]]["at_risk"] = (
                        warehouse_activity_metric_dict[row[2]]["at_risk"] or
                        row[2] - epoch_time + int(running_pendencies["packaging"]*60*30/warehouse_activity_rate_dict["packaging_rate"][f"{row[0]}"]) < 3600
                    )
                
                # setting slot at risk (if expected sorting time is more than 30 minutes before dispatch)
                if f"{row[0]}" in warehouse_activity_rate_dict["sorting_rate"] and warehouse_activity_rate_dict["sorting_rate"][f"{row[0]}"]:
                    warehouse_activity_metric_dict[row[2]]["at_risk"] = (
                        warehouse_activity_metric_dict[row[2]]["at_risk"] or
                        row[2] - epoch_time + int(running_pendencies["sort"]*60*30/warehouse_activity_rate_dict["sorting_rate"][f"{row[0]}"]) < 1800
                    )

                # need to show at risk for slots within 2 past hours and next 4 hours
                if row[2] - epoch_time > 4*3600 or epoch_time - row[2] > 2*3600:
                    warehouse_activity_metric_dict[row[2]]["at_risk"] = False
                if not upcoming_slot_found and row[2] > epoch_time:
                    warehouse_activity_metric_dict[row[2]]["upcoming_slot"] = True
                    upcoming_slot_found = True
        
        result = OrderedDict()
        for dispatch_time, warehouse_slot_metrics_dict in sorted(warehouse_activity_metric_dict.items()):
            result[f"{int(dispatch_time)}"] = WarehouseSlotMetrics(
                at_risk=warehouse_slot_metrics_dict["at_risk"],
                upcoming_slot=warehouse_slot_metrics_dict["upcoming_slot"],
                data=[
                    OrderDatewiseMetric(
                        name=WAREHOUSE_METRICS_NAME_MAPPING[metric_name],
                        type=WAREHOUSE_METRICS_TYPE_MAPPING[metric_name],
                        data=metrics
                    ) for metric_name, metrics in warehouse_slot_metrics_dict.items() if metric_name not in {"at_risk", "upcoming_slot"}
                ]
            )
        return result
    
    def get_manpower_metrics(self, conn: connect, outlet: List) -> WarehouseStoreDateMetrics:
        current_outlet_shifts, _ = build_warehouse_orders_shift_clause(conn, outlet, False)
        curs = conn.cursor()
        params = {}
        dynamic_where_clause = []
        if outlet:
            dynamic_where_clause.append(
                "AND outlet_id IN (%(outlet_ids)i)"
            )
            params["outlet_ids"] = int(outlet[0])
        
        
        metrics_sql = f"""
            ,{WAREHOUSE_METRICS_CALC_MAPPING['putters']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['pickers']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['packers']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['sorters']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['auditors']}"""
        WAREHOUSE_MANPOWER_QUERY = get_warehouse_manpower_metrics(metrics_sql, dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(WAREHOUSE_MANPOWER_QUERY, params)

        mid_results = defaultdict(lambda : {"putters": [], "pickers":[], "packers": [], "sorters": [], "auditors":[]})
        outlet_mapping = dict()
        for row in curs:
            outlet_mapping[row[0]] = row[1]
            mid_result_key = row[0]
            mid_results[mid_result_key]["putters"].append(DatewiseMetric(
                date=row[2],
                metric=row[3]
            ))
            mid_results[mid_result_key]["pickers"].append(DatewiseMetric(
                date=row[2],
                metric=row[4]
            ))
            mid_results[mid_result_key]["packers"].append(DatewiseMetric(
                date=row[2],
                metric=row[5]
            ))
            mid_results[mid_result_key]["sorters"].append(DatewiseMetric(
                date=row[2],
                metric=row[6]
            ))
            mid_results[mid_result_key]["auditors"].append(DatewiseMetric(
                date=row[2],
                metric=row[7]
            ))
        results = []
        for mid_result_key, result in sorted(mid_results.items()):
            outlet_name=outlet_mapping[mid_result_key]
            results.append(WarehouseStoreMetric(
                outlet_name=outlet_name,
                outlet_id=str(mid_result_key),
                frontend_merchant_name=outlet_name,
                shift=current_outlet_shifts[str(mid_result_key)],
                data=[OrderDatewiseMetric(
                        name=WAREHOUSE_METRICS_NAME_MAPPING[meric_key],type=WAREHOUSE_METRICS_TYPE_MAPPING[meric_key],
                        data=data) for meric_key, data in result.items()]))
        return WarehouseStoreDateMetrics(metrics=results)


    def get_availability_metrics(
        self,
        conn: connect,
        outlet: List = None,
        is_slot_wise: bool = False,
        slot: List = None,
        is_merchant_wise: bool = False,
        merchant_id: List = None
    ) -> Union[Dict[str, List[OrderDatewiseMetric]], List[OrderDatewiseMetric]]:
        curs = conn.cursor()
        params = {}
        metrics_sql = f"""
            ,{WAREHOUSE_METRICS_CALC_MAPPING['be_weighted_availability']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['fe_weighted_availability']}
            ,{WAREHOUSE_METRICS_CALC_MAPPING['fe_weighted_availability_post_sto']}"""
        dynamic_where_clause = []
        if outlet:
            dynamic_where_clause.append(
                "AND outlet_id IN (%(outlet_ids)s)"
            )
            params["outlet_ids"] = outlet

        if merchant_id:
            dynamic_where_clause.append(
                "AND fe_merchant_id IN (%(merchant_ids)s)"
            )
            params["merchant_ids"] = merchant_id
        
        date_str = None
        if slot:
            current_slot = slot[0]
            date_str, dispatch_slot = datetime.fromtimestamp(int(current_slot) + 330*60).strftime("%Y-%m-%d %H:%M:%S.000").split(" ")
            dynamic_where_clause.append(
                "AND dispatch_slot IN (%(dispatch_slot)s)"
            )
            params["dispatch_slot"] = [dispatch_slot]
        
        extra_column = False
        if is_slot_wise and outlet and date_str:
            extra_column = True
            WAREHOUSE_AVAILABILITY_QUERY = get_availability_metrics(
                metrics_sql, dynamic_where_clause=" ".join(dynamic_where_clause), extra_column=",dispatch_slot", table_name="be_fe_slot_availability", date_str=date_str)
        elif (is_merchant_wise or merchant_id) and outlet:
            extra_column = True
            WAREHOUSE_AVAILABILITY_QUERY = get_availability_metrics(
                metrics_sql, dynamic_where_clause=" ".join(dynamic_where_clause), extra_column=",fe_merchant_id", table_name="fe_store_availability")
        else:
            WAREHOUSE_AVAILABILITY_QUERY = get_availability_metrics(metrics_sql, dynamic_where_clause=" ".join(dynamic_where_clause))
        
        curs.execute(WAREHOUSE_AVAILABILITY_QUERY, params)
        mid_results = defaultdict(lambda : {"be_availability": [], "fe_availability":[], "fe_availability_post_sto": []})
        outlet_mapping = dict()
        for row in curs:
            outlet_mapping[row[0]] = row[1]
            mid_result_key = row[3] if extra_column else row[0]
            mid_results[mid_result_key]["be_availability"].append(DatewiseMetric(
                date=row[2],
                metric=str(round(float(row[3+extra_column]), 2)).replace('nan', '-').replace('inf', '-')
            ))
            mid_results[mid_result_key]["fe_availability"].append(DatewiseMetric(
                date=row[2],
                metric=str(round(float(row[4+extra_column]), 2)).replace('nan', '-').replace('inf', '-')
            ))
            mid_results[mid_result_key]["fe_availability_post_sto"].append(DatewiseMetric(
                date=row[2],
                metric=str(round(float(row[5+extra_column]), 2)).replace('nan', '-').replace('inf', '-')
            ))
        
        results_dict = OrderedDict()
        results_list = []
        for mid_result_key, result in sorted(mid_results.items()):
            # outlet_name=outlet_mapping[mid_result_key]
            temp_result = [OrderDatewiseMetric(
                    name=WAREHOUSE_METRICS_NAME_MAPPING[meric_key], 
                    type=WAREHOUSE_METRICS_TYPE_MAPPING[meric_key], 
                    data=data) for meric_key, data in result.items()]
            if is_slot_wise and outlet:
                results_list = temp_result
            else:
                results_dict[f"{mid_result_key}"] = temp_result
        return results_list or results_dict


    def get_outbound_metrics(self,
                             conn: connect,
                             outlet: List = None,
                             previous_shift: bool = False
        )-> WarehouseStoreDateMetrics:
        current_outlet_shifts, orders_where_clause = build_warehouse_orders_shift_clause(conn, outlet, previous_shift)
        curs = conn.cursor()
        params = {}
        DEMAND_ID_FILTER_QUERY = get_demand_ids(orders_where_clause)
        curs.execute(DEMAND_ID_FILTER_QUERY, params)
        
        demand_id_dict = dict()
        for row in curs:
            demand_id_dict[row[1]] = row[0]

        metrics_sql = f"""
        ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_picked_qty']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_packed_qty']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_sorted_qty']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['on_time_dispatch_qty']}
        
        ,{WAREHOUSE_METRICS_CALC_MAPPING['wave_created_qty']}
        
        ,{WAREHOUSE_METRICS_CALC_MAPPING['picked_qty']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['packaging_qty']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['sorting_qty']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['dispatch_qty']}

        ,{WAREHOUSE_METRICS_CALC_MAPPING['irt']}
        ,{WAREHOUSE_METRICS_CALC_MAPPING['raise_audit']}
        """
        
        mid_results = defaultdict(lambda : {"pick_otif": [], "pick_fill_rate": [], "packaging_otif":[], "packaging_fill_rate":[],
                                            "sort_otif": [], "sort_fill_rate": [], "dispatch_otif": [], "dispatch_fill_rate": [],
                                            "irt": [], "raise_audit": []})
        outlet_mapping = dict()
        for date_str, demand_ids in demand_id_dict.items():
            params = {}
            dynamic_where_clause = []

            if demand_ids:
                dynamic_where_clause.append(
                    "AND demand_id IN (%(demand_id)s)"
                )
                params["demand_id"] = demand_ids

            if outlet:
                dynamic_where_clause.append(
                    "outlet_id IN (%(outlet_ids)s)"
                )
                params["outlet_ids"] = outlet
            
            WAREHOUSE_DEMAND_QUERY = get_warehouse_outbound_metrics(metrics_sql, " AND ".join(dynamic_where_clause))
            curs.execute(WAREHOUSE_DEMAND_QUERY, params)
            for row in curs:
                outlet_mapping[row[0]] = row[1]  # outlet_name
                mid_result_key = row[0]  # outlet_id
                mid_results[mid_result_key]["pick_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[2]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[6] else '-'
                ))
                mid_results[mid_result_key]["packaging_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[3]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[6] else '-'
                ))
                mid_results[mid_result_key]["sort_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[4]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[6] else '-'
                ))
                mid_results[mid_result_key]["dispatch_otif"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[5]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[6] else '-'
                ))
                mid_results[mid_result_key]["pick_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[7]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[7] else '-'))
                mid_results[mid_result_key]["packaging_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[8]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[7] else '-'))
                mid_results[mid_result_key]["sort_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[9]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[7] else '-'))
                mid_results[mid_result_key]["dispatch_fill_rate"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(min(float(row[10]*100.0/row[6]), 100), 2)).replace('nan', '-').replace('inf', '-') if row[7] else '-'))
                mid_results[mid_result_key]["irt"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(float(row[11]), 2)).replace('nan', '-').replace('inf', '-')))
                mid_results[mid_result_key]["raise_audit"].append(DatewiseMetric(
                    date=date_str,
                    metric=str(round(float(row[12]), 2)).replace('nan', '-').replace('inf', '-')))


        results = []
        for mid_result_key, result in mid_results.items():
            outlet_name=outlet_mapping[mid_result_key]
            results.append(
                                WarehouseStoreMetric(
                                    outlet_name=outlet_name,
                                    outlet_id=str(mid_result_key),
                                    frontend_merchant_name=outlet_name,
                                    shift = current_outlet_shifts[mid_result_key],
                                    data=[OrderDatewiseMetric(
                                        name=WAREHOUSE_METRICS_NAME_MAPPING[meric_key],
                                        type=WAREHOUSE_METRICS_TYPE_MAPPING[meric_key],
                                        data=data) for meric_key, data in result.items()]))
        return WarehouseStoreDateMetrics(metrics=results)


warehouse_metrics = CRUDWarehouseMetrics()
