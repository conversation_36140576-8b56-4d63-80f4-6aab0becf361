from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math
import httpx
from app.core.config import settings
from app.cache import THREE_MINUTES, FIVE_MINUTES, THIRTY_MINUTES, TEN_MINUTES, NINE_MINUTES, TWENTY_NINE_MINUTES
from app.core.metric_config import ITEM_FIELD_FRONTEND_TO_BACKEND_MAPPING

from app.cache.decorator import cache_class_metrics
from pinotdb import connect
from app.api import deps, utils
from app.services import AWS_CLIENCT

from .queries import get_bad_stocks_query, get_all_field_list_query, get_filtered_product_fieldwise_query

IST = timezone(timedelta(hours=5, minutes=30))
from app.schemas.bad_stock import ItemBadStockImages
from app.schemas.product_metric import FilteredProductFieldwise
from app.cache.presigned_urls import get_cache_presigned_urls, set_cache_for_object, is_ttl_epoch_valid


class CRUDBadStock():
    BAD_STOCK_QUERY_TTL     = TEN_MINUTES # depends on table updates
    PRESIGNED_URL_S3_TTL    = THIRTY_MINUTES 
    PRESIGNED_URL_CACHE_TTL = TWENTY_NINE_MINUTES
    
    @cache_class_metrics(expire=BAD_STOCK_QUERY_TTL, 
                         namespace="bad_stock", 
                         force_cache_burst_at_eod=True)
    def get_bad_stocks_query_result(self, conn: connect, 
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        item_name: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        start_date: str = None,
                        end_date: str = None,
                        item_id: List = None,
                        er_id: List = None):
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if l0_category:
            dynamic_where_clause.append("AND LOOKUP('item_mapping_v1', 'l0_category', 'item_id', item_id) IN (%(l0_category)s)")
            params["l0_category"] = l0_category
        if l1_category:
            dynamic_where_clause.append("AND LOOKUP('item_mapping_v1', 'l1_category', 'item_id', item_id) IN (%(l1_category)s)")
            params["l1_category"] = l1_category
        if l2_category:
            dynamic_where_clause.append("AND LOOKUP('item_mapping_v1', 'l2_category', 'item_id', item_id) IN (%(l2_category)s)")
            params["l2_category"] = l2_category
        if ptype:
            dynamic_where_clause.append("AND LOOKUP('item_mapping_v1', 'p_type', 'item_id', item_id) IN (%(ptype)s)")
            params["ptype"] = ptype
        if item_name:
            dynamic_where_clause.append("AND LOOKUP('item_mapping_v1', 'item_name', 'item_id', item_id) IN (%(item_name)s)")
            params["item_name"] = item_name
        if brand:
            dynamic_where_clause.append("AND LOOKUP('item_mapping_v1', 'brand_name', 'item_id', item_id) IN (%(brand)s)")
            params["brand"] = brand
        if city:
            dynamic_where_clause.append("AND LOOKUP('outlet_facility_mapping_v1', 'city_name', 'outlet_id', site_id) IN (%(city)s)")
            params["city"] = city
        if store:
            dynamic_where_clause.append("AND LOOKUP('outlet_facility_mapping_v1', 'merchant_id', 'outlet_id', site_id) IN (%(store)s)")
            params["store"] = store
        if item_id:
            dynamic_where_clause.append("AND item_id IN (%(item_id)s)")
            params["item_id"] = item_id
        if er_id:
            dynamic_where_clause.append("AND er_id IN (%(er_id)s)")
            params["er_id"] = er_id
        query = get_bad_stocks_query(dynamic_where_clause=" ".join(dynamic_where_clause),
                                     start_date=start_date,
                                     end_date=end_date)
        
        curs.execute(query, params)

        bad_stock_result = []
        for row in curs:
            audit_date = str(row[0])
            item_name = str(row[1])
            merchant_id = str(row[2])
            merchant_name = str(row[3])
            images_str = str(row[4])
            bad_stock_result.append((audit_date, item_name, merchant_id, merchant_name, images_str))
        return bad_stock_result


    def get_bad_stock_images(self, conn: connect, 
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        item_name: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        start_date: str = None,
                        end_date: str = None,
                        item_id: List = None,
                        er_id: List = None) -> List[ItemBadStockImages]:
        
        bad_stock_query_result = self.get_bad_stocks_query_result(conn=conn, l0_category=l0_category, 
                                                                  l1_category=l1_category, l2_category=l2_category,
                                                                  ptype=ptype, item_name=item_name, 
                                                                  brand=brand, city=city, store=store, 
                                                                  start_date = start_date, end_date=end_date, 
                                                                  item_id=item_id, er_id=er_id)
        
        bad_stock_result = []
        for audit_date, item_name, merchant_id, merchant_name, images_str in bad_stock_query_result:
            images_path = images_str.split(",")
            presigned_urls = self.get_presigned_urls_from_image_path(object_paths = images_path)
            bad_stock_result.append(
                ItemBadStockImages(
                    audit_date=audit_date,
                    item_name=item_name,
                    merchant_id=merchant_id,
                    merchant_name=merchant_name,
                    media_urls = [item['presigned_url'] for item in presigned_urls]
                )
            )
        
        return bad_stock_result
    

    def get_presigned_urls_from_image_path(self, object_paths):
        BAD_STOCK_IMAGES_S3_BUCKET = settings.BAD_STOCK_S3_BUCKET

        # check all the objects in cache
        presigned_urls_data = get_cache_presigned_urls(object_paths)
        
        presigned_urls_result = []
        for object_path, presigned_url_data in presigned_urls_data.items():
            presigned_url = presigned_url_data.get('presigned_url') if presigned_url_data else None
            ttl_epoch = presigned_url_data.get('ttl_epoch') if presigned_url_data else None
            if not presigned_url or not ttl_epoch or not is_ttl_epoch_valid(ttl_epoch):
                # expired object
                presigned_url = AWS_CLIENCT.generate_presigned_url(bucket_name = BAD_STOCK_IMAGES_S3_BUCKET, 
                                                                   object_path = object_path,
                                                                   expires_in  = self.PRESIGNED_URL_S3_TTL)
                if presigned_url: 
                    is_cached = set_cache_for_object(object_path = object_path, 
                                                     presigned_url = presigned_url, 
                                                     expire = self.PRESIGNED_URL_CACHE_TTL)
            if presigned_url: 
                presigned_urls_result.append({
                    "object_path" : object_path,
                    "presigned_url": presigned_url
                })

        return presigned_urls_result


    def get_all_field_list(self, conn: connect, 
                           search_filter: List = None):
        curs = conn.cursor()
        
        result = []
        for field in search_filter: 
            if field not in ITEM_FIELD_FRONTEND_TO_BACKEND_MAPPING:
                continue
            db_field_name = ITEM_FIELD_FRONTEND_TO_BACKEND_MAPPING[field]
            query = get_all_field_list_query(field = db_field_name, 
                                             table_name = "item_mapping_v1")
            curs.execute(query)

            all_field_list = []
            for row in curs:
                all_field_list.append(row[0])
            result.append(FilteredProductFieldwise(field=field, filtered_values=all_field_list))

        return result


    def get_write_ahead_regex_expression(self, field_values: List) -> str:
        regex_exp = ""
        # '^abc.*|^def.*'
        for idx, val in enumerate(field_values):
            regex_exp += f'{"^" if idx == 0 else "|^"}{val}.*'
        return regex_exp


    def get_filtered_item_fieldwise(self, conn: connect, 
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        item_name: List = None,
                        brand: List = None,
                        search_filter: str = None,
                        item_id: List = None) -> FilteredProductFieldwise:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if l0_category:
            regex_exp = self.get_write_ahead_regex_expression(l0_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l0_category, (%(l0_category)s), 'i' )")
            params["l0_category"] = regex_exp
        if l1_category:
            regex_exp = self.get_write_ahead_regex_expression(l1_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l1_category, (%(l1_category)s), 'i' )")
            params["l1_category"] = regex_exp
        if l2_category:
            regex_exp = self.get_write_ahead_regex_expression(l2_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l2_category, (%(l2_category)s), 'i' )")
            params["l2_category"] = regex_exp
        if ptype:
            regex_exp = self.get_write_ahead_regex_expression(ptype)
            dynamic_where_clause.append("AND REGEXP_LIKE(p_type, (%(ptype)s), 'i' )")
            params["ptype"] = regex_exp
        if item_name:
            regex_exp = self.get_write_ahead_regex_expression(item_name)
            dynamic_where_clause.append("AND REGEXP_LIKE(item_name, (%(item_name)s), 'i' )")
            params["item_name"] = regex_exp
        if brand:
            regex_exp = self.get_write_ahead_regex_expression(brand)
            dynamic_where_clause.append("AND REGEXP_LIKE(brand_name, (%(brand)s), 'i' )")
            params["brand"] = regex_exp
        if item_id:
            regex_exp = self.get_write_ahead_regex_expression(item_id)
            dynamic_where_clause.append("AND REGEXP_LIKE(CAST(item_id AS STRING), (%(item_id)s), 'i' )")
            params["item_id"] = regex_exp

        if search_filter not in ITEM_FIELD_FRONTEND_TO_BACKEND_MAPPING:
            return FilteredProductFieldwise(field = search_filter, filtered_values=[])
        db_search_filter_name = ITEM_FIELD_FRONTEND_TO_BACKEND_MAPPING[search_filter]
        
        where_clause = ""
        if len(dynamic_where_clause) != 0:
            where_clause = " ".join(dynamic_where_clause)[4:]
        
        query = get_filtered_product_fieldwise_query(dynamic_where_clause=where_clause, 
                                                     search_filter=db_search_filter_name,
                                                     table_name = "item_mapping_v1")
        curs.execute(query, params)

        filtered_values = []

        for row in curs:
            value = row[0]
            filtered_values.append(value)
        
        return FilteredProductFieldwise(field = search_filter,
                                        filtered_values = filtered_values)
    

bad_stock = CRUDBadStock()
