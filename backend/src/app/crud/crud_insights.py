from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Union, Dict
from collections import defaultdict
import math

from pinotdb import connect
from app.api import deps
from .queries import get_funnel_conversion_metrics_query,get_ptype_metrics_query, get_funnel_conversion_metrics_query_custom
from app.schemas.order_metric import OrderDatewiseMetric, ConversionGroupMetric,HourlyDateMetrics,HourMetric,Metric,HourlyDateMetric, DatewiseMetric
from .utils import generate_insights_metrics_sql,remove_metric, add_overall_search_impressions, sort_conversion_group_metrics, sort_by_conversion_drop_contribution, remove_abs_conv_drop, remove_low_search_spike
from app.core.metric_config import METRICS_TYPE_MAPPING,METRIC_NAME_MAPPING,INSIGHTS_CONVERSION_METRICS_MAPPING,CONVERSION_METRICS_MAPPING,METRICS_TABLES_MAPPING,CONVERSION_COLUMNS_TO_QUERY, METRICS_TABLES_MAPPING_PTYPE,PTYPE_COLUMNS_TO_QUERY,METRICS_CALC_MAPPING_PTYPE,INSIGHTS_CONVERSION_COLUMNS_TO_QUERY,INSIGHTS_CONTRIBUTION_METRICS,INSIGHTS_CONTRIBUTION_COLUMNS_TO_QUERY, PTYPE_COLUMNS_FOR_PRODUCT_TO_QUERY,ITEM_COLUMNS_TO_QUERY, MERCHANDISING_METRICS_CALC_MAPPING_PTYPE
from app.schemas.product_metric import ProductMetricTypes

IST = timezone(timedelta(hours=5, minutes=30))

class CRUDInsightsMetrics():
    
    def get_daily_group_view(self, curs, custom_view=False, start_date=None) -> List[OrderDatewiseMetric]:
        results = []
        metric_lists = {name: [] for name in CONVERSION_METRICS_MAPPING.keys()}
        date_suffix_aggregates = {}

        for row_idx, row in enumerate(curs):
            ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
            if custom_view:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                ist_dates = {suffix: (start_date_obj - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') 
                            for suffix in ['t0', 't7', 't14', 't21', 't28']}
            else:
                ist_dates = {suffix: (ist_now - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') 
                            for suffix in ['t0', 't7', 't14', 't21', 't28']}
            
            for suffix, date_str in ist_dates.items():
                if date_str not in date_suffix_aggregates:
                    date_suffix_aggregates[date_str] = {
                        'sums': {metric: 0 for metric in CONVERSION_COLUMNS_TO_QUERY},
                        'suffix': suffix
                    }

                for base_metric in CONVERSION_COLUMNS_TO_QUERY:
                    col_index = 2 + CONVERSION_COLUMNS_TO_QUERY.index(base_metric) * 5 + ['t0', 't7', 't14', 't21', 't28'].index(suffix)
                    try:
                        value = float(row[col_index])
                        date_suffix_aggregates[date_str]['sums'][base_metric] += value
                    except (ValueError, TypeError, IndexError) as e:
                        continue

        for date_str, aggregates in date_suffix_aggregates.items():
            calculated_metrics = {}
            for metric_name, expression in CONVERSION_METRICS_MAPPING.items():
                metric_expression = expression
                for base_metric in CONVERSION_COLUMNS_TO_QUERY:
                    metric_expression = metric_expression.replace(
                        f"SUM({base_metric})",
                        str(aggregates['sums'][base_metric])
                    )

                try:
                    metric_value = Decimal(eval(metric_expression.split(" AS ")[0]))
                except ZeroDivisionError:
                    metric_value = Decimal(0)

                if metric_name not in calculated_metrics:
                    calculated_metrics[metric_name] = []
                date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
                calculated_metrics[metric_name].append(DatewiseMetric(date=date_obj, metric=round(metric_value, 2)))

            for metric_name, metrics_data in calculated_metrics.items():
                for datewisedata in metrics_data:
                    metric_lists[metric_name].append({"date": datewisedata.date, "metric": datewisedata.metric})

        for name, data in metric_lists.items():
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING[name],
                    data=data,
                    type=METRICS_TYPE_MAPPING[name]
                )
            )
        return results


    def get_hourly_group_view(self, curs, custom_view=False, start_date=None) -> HourlyDateMetrics:
        # Initialize data structure: {date: {hour: {suffix: {metric: sum}}}}
        date_hour_suffix_aggregates = defaultdict(
            lambda: defaultdict(
                lambda: defaultdict(
                    lambda: {metric: 0 for metric in CONVERSION_COLUMNS_TO_QUERY}
                )
            )
        )

        suffixes = ['t0', 't7']

        # Reference date for date_diff calculation
        ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
        reference_date = datetime.strptime(start_date, "%Y-%m-%d").date() if custom_view else ist_now.date()

        # First pass: Aggregate base metrics by date, hour, and suffix
        for row_num, row in enumerate(curs):
            # Calculate dates for each suffix
            if custom_view:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                ist_dates = {suffix: (start_date_obj - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') 
                            for suffix in suffixes}
            else:
                ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
                ist_dates = {suffix: (ist_now - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') 
                            for suffix in suffixes}

            hour = row[1]  # Hour is in column 1

            # Accumulate metrics for each suffix
            for suffix in suffixes:
                date_str = ist_dates[suffix]
                for i, metric in enumerate(CONVERSION_COLUMNS_TO_QUERY):
                    col_index = 3  # date (0), hour (1), and city (2)
                    col_index += CONVERSION_COLUMNS_TO_QUERY.index(metric) * len(suffixes)
                    col_index += suffixes.index(suffix)
                    try:
                        value = float(row[col_index])
                        date_hour_suffix_aggregates[date_str][hour][suffix][metric] += value
                    except (ValueError, TypeError, IndexError) as e:
                        continue

        # Second pass: Calculate conversions using aggregated sums
        date_metric_map = defaultdict(lambda: defaultdict(list))
        for date_str, hour_data in date_hour_suffix_aggregates.items():
            for hour, suffix_data in hour_data.items():
                for suffix, aggregates in suffix_data.items():
                    calculated_metrics = {}
                    
                    for metric_name, expression in CONVERSION_METRICS_MAPPING.items():
                        metric_expression = expression
                        for base_metric in CONVERSION_COLUMNS_TO_QUERY:
                            metric_expression = metric_expression.replace(
                                f"SUM({base_metric})",
                                str(aggregates[base_metric])
                            )
                        
                        try:
                            metric_value = Decimal(eval(metric_expression.split(" AS ")[0]))
                        except ZeroDivisionError:
                            metric_value = Decimal(0)
                        
                        calculated_metrics[metric_name] = metric_value

                    for metric_name, metric_value in calculated_metrics.items():
                        metric = Metric(
                            name=METRIC_NAME_MAPPING[metric_name],
                            metric=round(metric_value, 2),
                            type=METRICS_TYPE_MAPPING[metric_name]
                        )
                        date_metric_map[date_str][hour].append(metric)

        # Final packaging
        hourly_date_results = []
        for date_str, hour_metrics_dict in date_metric_map.items():
            hour_metrics = [
                HourMetric(hour=hour, data=metrics) 
                for hour, metrics in sorted(hour_metrics_dict.items())
            ]
            date_dt = datetime.strptime(date_str, "%Y-%m-%d").date()
            date_diff = (reference_date - date_dt).days

            hourly_date_metric = HourlyDateMetric(
                date=date_str,
                date_diff=date_diff,
                data=hour_metrics
            )
            hourly_date_results.append(hourly_date_metric)

        return HourlyDateMetrics(metrics=hourly_date_results)


    def get_daily_view(self,hourly_view,yesterday_view,curs) -> List[OrderDatewiseMetric]:
        metric_lists = {name: [] for name in CONVERSION_METRICS_MAPPING.keys()}
        results = []
        for row in curs:
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                CONVERSION_METRICS_MAPPING,
                CONVERSION_COLUMNS_TO_QUERY,
                is_ptype=False,
                is_hourly=hourly_view,
                insights_view=False,
                yesterday_view=yesterday_view
            )
            for metric_name, metrics_data in calculated_metrics.items():
                for datewisedata in metrics_data:
                    metric_lists[metric_name].append({"date": datewisedata.date, "metric": datewisedata.metric})
        for name, data in metric_lists.items():
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING[name],
                    data=data,
                    type=METRICS_TYPE_MAPPING[name]
                )
            )
        return results
    
    def get_hourly_view(self,hourly_view,yesterday_view,curs) -> HourlyDateMetrics:
        date_metric_map = defaultdict(lambda: defaultdict(list))
        ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
        for row in curs:
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                CONVERSION_METRICS_MAPPING,
                CONVERSION_COLUMNS_TO_QUERY,
                is_ptype=False,
                is_hourly=hourly_view,
                insights_view=False,
                yesterday_view=yesterday_view
            )
            hour_column = row[1]
            for metric_name, metrics_data in calculated_metrics.items():
                for datewisedata in metrics_data:
                    dt_str = datewisedata.date.strftime("%Y-%m-%d")
                    metric_value = datewisedata.metric
                    metric = Metric(
                        name=METRIC_NAME_MAPPING[metric_name],
                        metric=metric_value,
                        type=METRICS_TYPE_MAPPING[metric_name]
                    )
                    date_metric_map[dt_str][hour_column].append(metric)

        hourly_date_results = []
        for date_str, hour_metrics_dict in date_metric_map.items():
            hour_metrics = [
                HourMetric(hour=hour, data=metrics) for hour, metrics in hour_metrics_dict.items()
            ]
            date_dt = datetime.strptime(date_str, "%Y-%m-%d").date()
            date_diff = (ist_now.date() - date_dt).days

            hourly_date_metric = HourlyDateMetric(
                date=date_str,
                date_diff=date_diff,
                data=hour_metrics
            )

            hourly_date_results.append(hourly_date_metric)

        return HourlyDateMetrics(metrics=hourly_date_results)
    

    def get_insights_view(self,hourly_view,yesterday_view,store_response,metrics_sql,table_name_key,filter_hour,curs) -> List[ConversionGroupMetric]:
        results = defaultdict(list)
        for row in curs:
            key = row[1] if not hourly_view else row[2]
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                INSIGHTS_CONVERSION_METRICS_MAPPING,
                INSIGHTS_CONVERSION_COLUMNS_TO_QUERY,
                is_ptype=False,
                is_hourly=hourly_view,
                insights_view=True,
                yesterday_view=yesterday_view
            )
            for metric_name, metrics_data in calculated_metrics.items():
                results[key].append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING[metric_name], data=metrics_data, type=METRICS_TYPE_MAPPING[metric_name]))

            contribution_metrics = self.calculate_contribution_metrics(
                row,
                is_hourly=hourly_view,
                yesterday_view=yesterday_view
            )
            for metric_name, metrics_data in contribution_metrics.items():
                results[key].append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING[metric_name], data=metrics_data, type=METRICS_TYPE_MAPPING[metric_name]))

        final_results = [
            ConversionGroupMetric(
                key=key,
                grain=key,  # Adding the grain field with the key value
                metric=metrics
            )
            for key, metrics in results.items()
        ]
        #this is to move overall to the first element as per the requirement
        overall_metric = None
        for data in final_results:
            if data.key == "Overall":
                overall_metric = data
                final_results.remove(overall_metric)
                break
        
        sorted_results = sort_by_conversion_drop_contribution(final_results)
        if overall_metric is not None:
            sorted_results.insert(0,overall_metric)
        return sorted_results[:50]

    def calculate_contribution_metrics(self, row, is_hourly, yesterday_view):
        #in case of insights contribution metrics, we only need current data
        suffixes = ['t0']
        metrics_to_calculate = INSIGHTS_CONTRIBUTION_METRICS
        base_metrics = INSIGHTS_CONTRIBUTION_COLUMNS_TO_QUERY
        conv_contribution_metric = [base_metrics[0]]

        #to check if we dont have data for conv_contribution_metric and remove the row as sorting is based on this column
        for suffix in suffixes:
            for metric in conv_contribution_metric:
                column_index = 15 if is_hourly else 14 + base_metrics.index(metric) * len(suffixes) + suffixes.index(suffix)
                if row[column_index] == -999:
                    return {}
                
        #calculating date's based on suffixes
        ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
        ist_now = ist_now - timedelta(days=1) if yesterday_view else ist_now
        ist_dates = {suffix: (ist_now - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') for suffix in suffixes}

        result = {}

        for i in range(len(metrics_to_calculate)):
            metric_data = []
            for suffix in ['t0']:
                if is_hourly:
                    #index handling to take care non contribution metrics
                    column_index = 15 + i * len(suffixes) + suffixes.index(suffix)
                else:
                    #index handling to take care non contribution metrics
                    column_index = 14 + i * len(suffixes) + suffixes.index(suffix)

                try:
                    metric_value = Decimal(row[column_index])
                except ZeroDivisionError:
                    metric_value = Decimal(0)

                metric_data.append(DatewiseMetric(date=ist_dates[suffix],metric=round(metric_value, 2)))

            result[metrics_to_calculate[i]] = metric_data
        return result

    def calculate_metrics_with_dates(self, row, conversion_mapping, 
                                     base_metrics, is_ptype, is_hourly, 
                                     insights_view, yesterday_view, metrics_type = None,
                                     custom_view = False, start_date = None):
        ptype_metrics = []
        if metrics_type == ProductMetricTypes.product:
            ptype_metrics = ["search","availability","unique_impressions","cnt_inventory","merch_page_impr","merch_page_impr_atc","cat_grid_page_impr","cat_grid_page_impr_atc","cnt_be_inventory"]
        else:
            ptype_metrics = ['search','availability','unique_impressions']

        funnel_metrics = ["dau"]
        if insights_view or is_hourly:
            #only WoW in insights
            suffixes = ['t0', 't7']
            if is_ptype:
                #determines ordering on frontend, takes singular metrics apart from ones defined in metric config
                metrics_to_calculate = ["unique_impressions"] + list(conversion_mapping.keys()) + ["search"] + ["availability"]
                if metrics_type == ProductMetricTypes.product:
                    metrics_to_calculate.extend(["cnt_inventory","merch_page_impr","cat_grid_page_impr","cnt_be_inventory"])
            else:
                #determines ordering on frontend, takes singular metrics apart from ones defined in metric config
                metrics_to_calculate = ["dau"] + list(conversion_mapping.keys())
        else:
            #we show Wo4W comparisons apart from insights
            suffixes = ['t0', 't7', 't14', 't21', 't28']
            #in case of daily/hourly view we show only these two metrics
            metrics_to_calculate = ["dau_to_atc_conv", "cv_to_transact_conv"]
        

        #to check if we dont have search data for certain ptype's so we dont consider them
        if is_ptype:
            for suffix in suffixes:
                for metric in ["search", "search_atc"]:
                    column_index = 4 if is_hourly else 3
                    column_index += base_metrics.index(metric) * len(suffixes) + suffixes.index(suffix)
                    if row[column_index] == -1:
                        return {}

        #calculating date's based on suffixes
        if not custom_view:
            ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
            ist_now = ist_now - timedelta(days=1) if yesterday_view else ist_now
            ist_dates = {suffix: (ist_now - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') for suffix in suffixes}
        else:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            ist_dates = {suffix: (start_date_obj - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') for suffix in suffixes}

        result = {}

        for metric_name in metrics_to_calculate:
            if (metrics_type in [ProductMetricTypes.search_spike, ProductMetricTypes.absolute_conv_drop, ProductMetricTypes.contributed_conv_drop]) and metric_name == 'dau_to_atc_conv':
                continue
            metric_data = []
            if metric_name in conversion_mapping:
                #calculate metrics based on expression in metric config
                expression = conversion_mapping[metric_name]
                for suffix in suffixes:
                    metric_expression = expression
                    for base_metric in base_metrics:
                        if is_hourly:
                            #index handling to take care of key columns like city/store, date/hour
                            column_index = 4 if is_ptype else 3
                            column_index += base_metrics.index(base_metric) * len(suffixes) + suffixes.index(suffix)
                        else:
                            #index handling to take care of key columns like city/store, date/hour
                            column_index = 3 if is_ptype else 2
                            column_index += base_metrics.index(base_metric) * len(suffixes) + suffixes.index(suffix)
                        metric_expression = metric_expression.replace(
                            f"SUM({base_metric})",
                            str(row[column_index])
                        )

                    try:
                        metric_value = Decimal(eval(metric_expression.split(" AS ")[0]))  
                    except ZeroDivisionError:
                        metric_value = Decimal(0)
                    #to handle cases where we are not getting data in funnel metrics
                    if metric_value == 100.0 and not is_ptype:
                        continue
                    metric_data.append(DatewiseMetric(date=ist_dates[suffix],metric=round(metric_value, 2)))

            if (metric_name in ptype_metrics) or (metric_name in funnel_metrics):
                if metric_name == "unique_impressions":
                    metric_column = "dau"
                else:
                    metric_column = metric_name
                for suffix in suffixes:
                    if is_hourly:
                        #index handling to take care of key columns like city/store, date/hour
                        column_index = 4 if is_ptype else 3
                        column_index += base_metrics.index(metric_column) * len(suffixes) + suffixes.index(suffix)
                    else:
                        #index handling to take care of key columns like city/store, date/hour
                        column_index = 3 if is_ptype else 2
                        column_index += base_metrics.index(metric_column) * len(suffixes) + suffixes.index(suffix)
                    try:
                        metric_value = Decimal(row[column_index])
                    except ZeroDivisionError:
                        metric_value = Decimal(0)
                    
                    #in case of ptype metrics null value is -1 and in funnel metrics null value is -999
                    if (metric_value == -1 and is_ptype) or (metric_value == -999 and not is_ptype):
                        continue
                    metric_data.append(DatewiseMetric(date=ist_dates[suffix],metric=round(metric_value, 2)))
            result[metric_name] = metric_data

        if metrics_type == ProductMetricTypes.search_spike:  # (search_t0 - search_t7)/search_t7 * 100
            if is_hourly:
                metric_value = ((row[8] - row[9])/row[9])*100
            else:
                metric_value = ((row[7] - row[8])/row[8])*100
            result[ProductMetricTypes.search_spike] = [DatewiseMetric(date=ist_dates[suffixes[0]],metric=round(metric_value, 2))]

        
        # # (search_atc_t0/search_t0) - (search_atc_t7/search_t7)
        if metrics_type in [ProductMetricTypes.absolute_conv_drop, ProductMetricTypes.contributed_conv_drop]: # (search_atc_t0/search_t0) - (search_atc_t7/search_t7)
            if is_hourly:
                metric_value = (row[10]/row[8])*100 - (row[11]/row[9])*100
            else:
                metric_value = (row[9]/row[7])*100 - (row[10]/row[8])*100
            result['conv_drop'] = [DatewiseMetric(date=ist_dates[suffixes[0]],metric=round(metric_value, 2))]
        return result


    def get_funnel_conversion_metrics(
        self, 
        conn: connect, 
        filter_hour: int = -1, 
        city: List = None, 
        store: List = None, 
        zone: List = None,
        hourly_view: bool = False, 
        yesterday_view: bool = False, 
        insights_view: bool = False, 
        store_response: bool = False, 
        cities_view: bool = False, 
        stores_view: bool = False,
        group_view: bool = False,
    ) -> Union[List[OrderDatewiseMetric], HourlyDateMetrics, List[ConversionGroupMetric]]:
    
        if filter_hour != -1:
            hourly_view = True
        if store:
            store_response = True
        num_weeks = 2 if (insights_view or hourly_view) else 5
        timestamp_col = 'etl_snapshot_ts_ist_epoch'
        metrics_sql = generate_insights_metrics_sql(INSIGHTS_CONVERSION_COLUMNS_TO_QUERY if insights_view else CONVERSION_COLUMNS_TO_QUERY, num_weeks, timestamp_col, 'INT')
        if insights_view:
            #in case of store's we want to sort based on overall_conv_per_adj_diff if city is Overall
            if store_response == True and city == None:
                contribution_columns_to_query = INSIGHTS_CONTRIBUTION_COLUMNS_TO_QUERY.copy()
                contribution_columns_to_query[0] = "overall_conv_per_adj_diff"
            else:
                contribution_columns_to_query = INSIGHTS_CONTRIBUTION_COLUMNS_TO_QUERY
            contribution_metrics_sql = generate_insights_metrics_sql(contribution_columns_to_query, 1, timestamp_col, 'DOUBLE')
            metrics_sql = metrics_sql + "," + contribution_metrics_sql
        table_name_key = 'funnel_hourly' if hourly_view else 'funnel_daily'
        table_name = METRICS_TABLES_MAPPING[table_name_key]['store'] if store_response or zone else METRICS_TABLES_MAPPING[table_name_key]['city']

        curs = conn.cursor()
        dynamic_where_clause = []
        dynamic_where_clause.append("""AND city NOT IN ('NA', 'Not in service area')""")
        params = {}
        if city:
            dynamic_where_clause.append("""
            AND city IN (%(cities)s)""")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("""
            AND merchant_id IN (%(store)s)""")
            params["store"] = store
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone
        
        if not city:
            city = ['Overall']
        query = get_funnel_conversion_metrics_query(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            table_name=table_name,
            city=city,
            is_hourly=hourly_view,
            filter_hour=filter_hour,
            is_yesterday=yesterday_view,
            store_response=store_response,
            insights_view=insights_view
        )
        curs.execute(query, params)
        if insights_view:
            return self.get_insights_view(hourly_view,yesterday_view,store_response,metrics_sql,table_name_key,filter_hour,curs)
        if not hourly_view:
            if group_view: 
                return self.get_daily_group_view(curs)
            return self.get_daily_view(hourly_view,yesterday_view,curs)
        else:
            if group_view: 
                return self.get_hourly_group_view(curs)
            return self.get_hourly_view(hourly_view,yesterday_view,curs)
    
    def get_ptype_metrics(
        self, 
        conn: connect, 
        filter_hour: int = -1, 
        city: List = None, 
        store: List = None, 
        yesterday_view: bool = False,
        ptype_filter_list: List = None,
        metrics_type: str = None,
        l0_category: List = None
    ) -> List[ConversionGroupMetric]:
        
        timestamp_col = 'etl_snapshot_ts_ist_epoch'
        columns_to_query = PTYPE_COLUMNS_FOR_PRODUCT_TO_QUERY if ptype_filter_list else PTYPE_COLUMNS_TO_QUERY
        metrics_sql = generate_insights_metrics_sql(columns_to_query, 2, timestamp_col, 'INT')
        table_name_key = 'ptype_hourly' if filter_hour != -1 else 'ptype_daily'
        table_name = METRICS_TABLES_MAPPING_PTYPE[table_name_key]['store'] if store else METRICS_TABLES_MAPPING_PTYPE[table_name_key]['city']

        dynamic_where_clause = []
        params = {}
        if not city:
            city = ['Overall']
        dynamic_where_clause.append("""
        AND city IN (%(cities)s)""")
        params["cities"] = city

        if store:
            dynamic_where_clause.append("""
            AND merchant_id IN (%(store)s)""")
            params["store"] = store

        if ptype_filter_list:
            dynamic_where_clause.append("AND product_type IN (%(ptype_filter_list)s)")
            params["ptype_filter_list"] = ptype_filter_list
        
        if l0_category:
            dynamic_where_clause.append("AND l0_category IN (%(l0_category)s)")
            params["l0_category"] = l0_category

        #* only for product page api..
        stores_with_0_inventory = {}
        if city and not store and filter_hour == -1 and ptype_filter_list:
            stores_with_0_inventory = self.get_stores_with_zero_inventory(conn=conn, city=city, yesterday_view=yesterday_view, ptype=ptype_filter_list)

        curs = conn.cursor()
        
        query = get_ptype_metrics_query(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            table_name=table_name,
            store=store,
            filter_hour=filter_hour,
            is_yesterday=yesterday_view,
            metrics_type=metrics_type
        )

        curs.execute(query, params)

        conversion_mapping = METRICS_CALC_MAPPING_PTYPE
        if metrics_type == ProductMetricTypes.product:
            conversion_mapping = MERCHANDISING_METRICS_CALC_MAPPING_PTYPE

        results = defaultdict(list)
        for row in curs:
            key = row[1] if filter_hour == -1 else row[2]
            ptype = row[2] if filter_hour == -1 else row[3]
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                conversion_mapping,
                columns_to_query,
                is_ptype=True,
                is_hourly=True if filter_hour != -1 else False,
                insights_view=True,
                yesterday_view=yesterday_view,
                metrics_type=metrics_type
            )
            for metric_name, metrics_data in calculated_metrics.items():
                results[ptype].append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING[metric_name], data=metrics_data, type=METRICS_TYPE_MAPPING[metric_name]))
            #* only for product page api..
            if metrics_type == ProductMetricTypes.product and stores_with_0_inventory and ptype in stores_with_0_inventory:
                results[ptype].append(stores_with_0_inventory[ptype])
        
        final_results = [
            ConversionGroupMetric(
                key=key,
                grain=grain,
                metric=metrics
                )
                for grain, metrics in results.items()
            ]
        if final_results == [] or ptype_filter_list:
            return final_results
        
        final_results_with_overall_search = add_overall_search_impressions(final_results)
        
        if metrics_type == ProductMetricTypes.search_spike:
            final_results_with_overall_search = remove_low_search_spike(data=final_results_with_overall_search)
        if metrics_type == ProductMetricTypes.absolute_conv_drop:
            final_results_with_overall_search = remove_abs_conv_drop(data=final_results_with_overall_search)
            return remove_metric(final_results_with_overall_search[:20], False)
        
        sorted_final_results = remove_metric(sort_conversion_group_metrics(data=final_results_with_overall_search, metrics_type=metrics_type),False)

        return sorted_final_results[:50]


    def get_item_metrics(
        self, 
        conn: connect, 
        filter_hour: int = -1, 
        city: List = None, 
        store: List = None, 
        yesterday_view: bool = False, 
        product_type: str = None
    ) -> List[ConversionGroupMetric]:
        timestamp_col = 'etl_snapshot_ts_ist_epoch'
        item_columns = ITEM_COLUMNS_TO_QUERY['store'] if store else ITEM_COLUMNS_TO_QUERY['city']
        metrics_sql = generate_insights_metrics_sql(item_columns, 0, timestamp_col, 'INT', True)
        table_name = "daily_merchant_item_availability" if store else "daily_city_item_availability"

        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if not city:
            city = ['Overall']
        dynamic_where_clause.append("""AND city_name IN (%(cities)s)""")
        params["cities"] = city

        if store:
            dynamic_where_clause.append("AND lookup('outlet_facility_mapping_v1','merchant_id', 'outlet_id',outlet_id) in (%(store)s)")
            params["store"] = store

        if product_type:
            dynamic_where_clause.append("""AND product_type IN (%(product_type)s)""")
            params["product_type"] = product_type
        
        query = get_ptype_metrics_query(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            table_name=table_name,
            store=store,
            is_yesterday=yesterday_view,
            filter_hour=filter_hour,
            item_level_query=True
        )

        curs.execute(query, params)

        results = defaultdict(list)

        final_results = []
        start_from_idx = 0 if filter_hour == -1 else 2
        for row in curs:
            item_id = row[start_from_idx + 2]
            item_name = row[start_from_idx + 3]
            key = item_name
            calculated_metrics = {}
            start_idx = (5 if filter_hour != -1 else 4) + start_from_idx
            for idx, metric_value in enumerate(row[start_idx:]):
                if METRICS_TYPE_MAPPING[item_columns[idx]] == "string":
                    calculated_metrics[item_columns[idx]] = [DatewiseMetric(date=row[0],metric=metric_value)]
                else:
                    calculated_metrics[item_columns[idx]] = [DatewiseMetric(date=row[0],metric=round(metric_value, 2))]
            for metric_name, data in calculated_metrics.items():
                results[item_id].append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING[metric_name], data=data, type=METRICS_TYPE_MAPPING[metric_name]))
            final_results.append(ConversionGroupMetric(key=key, grain=str(item_id), metric=results[item_id]))

        if final_results == []:
            return final_results
        
        #* sorting in the query itself
        return final_results[:10]
    

    def get_stores_with_zero_inventory(
        self, 
        conn: connect,
        city: List = None, 
        yesterday_view: bool = False,
        ptype: List = None,
    ) -> Dict[str, OrderDatewiseMetric]:
        
        curs = conn.cursor()
        table_name = "daily_merchant_ptype_metrics"

        dynamic_where_clause = []
        params = {}
        dynamic_where_clause.append("""
        AND city IN (%(cities)s)""")
        params["cities"] = city

        if ptype:
            dynamic_where_clause.append("AND product_type IN (%(ptype)s)")
            params["ptype"] = ptype
        
        query = get_ptype_metrics_query(
            dynamic_where_clause=" ".join(dynamic_where_clause),
            table_name=table_name,
            metrics_sql="""count(distinct merchant_id) as stores_with_0_inventory """,
            is_yesterday=yesterday_view,
            is_stores_with_0_inventory=True
        )

        curs.execute(query, params)

        result = {}
        for row in  curs:
            result[row[2]] = (OrderDatewiseMetric(name=METRIC_NAME_MAPPING["stores_with_0_inventory"], data=[DatewiseMetric(date=row[0],metric=round(row[3], 2))], type=METRICS_TYPE_MAPPING["stores_with_0_inventory"]))
        return result
    

    def get_daily_view_custom(self,
                              hourly_view,
                              start_date,
                              curs) -> List[OrderDatewiseMetric]:
        metric_lists = {name: [] for name in CONVERSION_METRICS_MAPPING.keys()}
        results = []
        for row in curs:
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                CONVERSION_METRICS_MAPPING,
                CONVERSION_COLUMNS_TO_QUERY,
                is_ptype=False,
                is_hourly=hourly_view,
                insights_view=False,
                yesterday_view=False,
                custom_view = True,
                start_date=start_date
            )
            for metric_name, metrics_data in calculated_metrics.items():
                for datewisedata in metrics_data:
                    metric_lists[metric_name].append({"date": datewisedata.date, "metric": datewisedata.metric})
        for name, data in metric_lists.items():
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING[name],
                    data=data,
                    type=METRICS_TYPE_MAPPING[name]
                )
            )
        return results
    

    def get_hourly_view_custom(self, 
                               hourly_view, 
                               start_date, 
                               curs) -> HourlyDateMetrics:
        date_metric_map = defaultdict(lambda: defaultdict(list))
        ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)

        # !! CHECK WHERE WE WANT TO FIND DIFF FROM ist_now or from start_date
        # start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()

        for row in curs:
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                CONVERSION_METRICS_MAPPING,
                CONVERSION_COLUMNS_TO_QUERY,
                is_ptype=False,
                is_hourly=hourly_view,
                insights_view=False,
                yesterday_view=False,
                custom_view = True,
                start_date=start_date
            )
            hour_column = row[1]
            for metric_name, metrics_data in calculated_metrics.items():
                for datewisedata in metrics_data:
                    dt_str = datewisedata.date.strftime("%Y-%m-%d")
                    metric_value = datewisedata.metric
                    metric = Metric(
                        name=METRIC_NAME_MAPPING[metric_name],
                        metric=metric_value,
                        type=METRICS_TYPE_MAPPING[metric_name]
                    )
                    date_metric_map[dt_str][hour_column].append(metric)

        hourly_date_results = []
        for date_str, hour_metrics_dict in date_metric_map.items():
            hour_metrics = [
                HourMetric(hour=hour, data=metrics) for hour, metrics in hour_metrics_dict.items()
            ]
            date_dt = datetime.strptime(date_str, "%Y-%m-%d").date()
            # !! CHECK WHERE WE WANT TO FIND DIFF FROM ist_now or from start_date
            date_diff = (ist_now.date() - date_dt).days 

            hourly_date_metric = HourlyDateMetric(
                date=date_str,
                date_diff=date_diff,
                data=hour_metrics
            )

            hourly_date_results.append(hourly_date_metric)

        return HourlyDateMetrics(metrics=hourly_date_results)
    

    def get_funnel_conversion_metrics_custom(
        self, 
        conn: connect, 
        filter_hour: int = -1, 
        city: List = None, 
        store: List = None, 
        zone: List = None,
        hourly_view: bool = False, 
        store_response: bool = False, 
        start_date: str = None,
        group_view: bool = False,
    ) -> Union[List[OrderDatewiseMetric], HourlyDateMetrics, List[ConversionGroupMetric]]:

        if filter_hour != -1:
            hourly_view = True
        if store:
            store_response = True
        num_weeks = 2 if (hourly_view) else 5
        timestamp_col = 'etl_snapshot_ts_ist_epoch'
        metrics_sql = generate_insights_metrics_sql(CONVERSION_COLUMNS_TO_QUERY, num_weeks, timestamp_col, 'INT')
        table_name_key = 'funnel_hourly' if hourly_view else 'funnel_daily'
        table_name = METRICS_TABLES_MAPPING[table_name_key]['store'] if store_response or zone else METRICS_TABLES_MAPPING[table_name_key]['city']

        curs = conn.cursor()
        dynamic_where_clause = []
        dynamic_where_clause.append("""AND city NOT IN ('NA', 'Not in service area')""")
        params = {}
        if city:
            dynamic_where_clause.append("""
            AND city IN (%(cities)s)""")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("""
            AND merchant_id IN (%(store)s)""")
            params["store"] = store
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone
        
        if not city:
            city = ['Overall']
        query = get_funnel_conversion_metrics_query_custom(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            table_name=table_name,
            city=city,
            is_hourly=hourly_view,
            filter_hour=filter_hour,
            start_date=start_date,
            store_response=store_response,
        )
        curs.execute(query, params)
        if not hourly_view:
            if group_view: 
                return self.get_daily_group_view(curs=curs, custom_view=True, start_date=start_date)
            return self.get_daily_view_custom(hourly_view, start_date, curs)
        else:
            if group_view:
                return self.get_hourly_group_view(curs, custom_view=True, start_date=start_date)
            return self.get_hourly_view_custom(hourly_view, start_date, curs)


insights_metrics = CRUDInsightsMetrics()
