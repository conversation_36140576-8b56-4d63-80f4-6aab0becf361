from datetime import datetime, timezone, timedelta
from collections import defaultdict
from typing import List, Union

from pinotdb import connect

from app.schemas.metric import Metric
from .utils import get_dates_in_ist_format, get_date_str
from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING
from app.schemas.order_metric import Metric, StoresCityList, StoresNameMapping, StoresCityMapping, \
    LocationDateMetric, LocationDateMetrics, LocationMetric, StoreMetricsResponse,DatewiseMetric, StoreMetrics,OrderDatewiseMetric
from .queries import get_store_city_mapping_query,CITYWISE_STRESSED_STORES_COMPARISION_QUERY, STORES_CITY_MAPPING_QUERY, get_active_stores_count_v2, get_active_stores_count_custom_v2
from .crud_order_metrics import order_metrics
from .crud_surge_seen_metrics import surge_seen_metrics
from app.cache.decorator import cache_class_metrics
from app.cache import ONE_HOUR_IN_SECONDS

IST = timezone(timedelta(hours=5, minutes=30))
class CRUDStoreMetrics():
    def get_datewise_stressed_stores_count(self, conn: connect, base_days_diff: int = 0, comparative_days_diff: int = 7) -> LocationDateMetrics:
        curs = conn.cursor()
        curs.execute(CITYWISE_STRESSED_STORES_COMPARISION_QUERY, {"base_days_diff": base_days_diff, "comparative_days_diff": comparative_days_diff})
        stressed_store_ids_datewise = defaultdict(list)
        for row in curs:
            stressed_store_ids_datewise[row[0]].append(int(row[1]))
        curs.execute(STORES_CITY_MAPPING_QUERY)
        store_city_map = {}
        distinct_cities = set()
        for row in curs:
            store_city_map[row[0]] = row[1]
            distinct_cities.add(row[1])
        results = defaultdict(lambda: defaultdict(int, {k: 0 for k in distinct_cities}))
        for date, stressed_stores in stressed_store_ids_datewise.items():
            for store_id in stressed_stores:
                if store_id in store_city_map.keys():
                    results[date][store_city_map[store_id]] += 1

        city_stressed_stores = []
        for dt_str, city_stressed_store_metric in results.items():
            date_wise_city_stressed_store_metric = []
            for city, stressed_store_count in city_stressed_store_metric.items():
                metrics = [Metric(name="Stressed Stores", metric=stressed_store_count, type='number')]
                date_wise_city_stressed_store_metric.append(LocationMetric(type="city", name=city, data=metrics))
            city_stressed_stores.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=date_wise_city_stressed_store_metric))

        return LocationDateMetrics(metrics=city_stressed_stores)

    @cache_class_metrics(expire=ONE_HOUR_IN_SECONDS, 
                        namespace="store_metrics", 
                        force_cache_burst_at_eod=True)
    def get_stores_city_mapping(self, conn: connect) -> StoresCityList:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = []
        STORES_CITY_LIST_QUERY = get_store_city_mapping_query(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(STORES_CITY_LIST_QUERY, params)
        store_city_map = defaultdict(list)
        results = []
        for row in curs:
            store_city_map[(row[2],row[5])].append(StoresNameMapping(frontend_merchant_id=str(row[0]),
                                                            frontend_merchant_name=row[1],
                                                            backend_merchant_id=str(row[3]),
                                                            merchant_type=row[6],
                                                            is_longtail_outlet=row[7]
                                                            )
                                          )
        for (city, zone), stores_mapping in store_city_map.items():
            if zone != 'NA': 
                results.append(StoresCityMapping(city=city,zone=zone, data=stores_mapping))
        return StoresCityList(filters=results)

    def get_all_cities(self, conn: connect) -> List[str]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = []
        STORES_CITY_LIST_QUERY = get_store_city_mapping_query(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(STORES_CITY_LIST_QUERY, params)
        cities = set()
        for row in curs:
            cities.add(row[2])  # city is at index 2
        return list(cities)

    def get_active_stores_count_metric(self, conn: connect, city: List = None, zone: List = None, is_yesterday:bool = False,
                                       get_trend: bool = False) -> Union[int, List]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        active_stores_count = None
        if city:
            dynamic_where_clause.append("AND frontend_merchant_city_name IN (%(cities)s)")
            params["cities"] = city
        if zone:
            dynamic_where_clause.append("AND zone IN (%(zone)s) ")
            params["zone"] = zone
        ACTIVE_STORES_COUNT_QUERY = get_active_stores_count_v2(dynamic_where_clause=" ".join(dynamic_where_clause),
                                                            is_yesterday=is_yesterday,
                                                            get_trend=get_trend)
        curs.execute(ACTIVE_STORES_COUNT_QUERY, params)
        if not get_trend:
            for row in curs:
                active_stores_count = row[0]
            return active_stores_count
        else:
            total_active_stores = []
            express_active_stores = []
            longtail_active_stores = []
            results = []
            row = curs.fetchone()

            dates = get_dates_in_ist_format(start_date=get_date_str(is_yesterday), jump=7, num_of_outputs=5)
            i = 0
            for dt in dates:
                total_metric = row[i]
                express_metric = row[i + 1]
                longtail_metric = row[i + 2]

                total_active_stores.append({"date": dt, "metric": total_metric})
                express_active_stores.append({"date": dt, "metric": express_metric})
                longtail_active_stores.append({"date": dt, "metric": longtail_metric})
                i = i + 3

            results.append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING["active_stores_count"],
                                            data=total_active_stores,
                                            type=METRICS_TYPE_MAPPING["active_stores_count"]))
            results.append(OrderDatewiseMetric(name="Express Outlet Count",
                                            data=express_active_stores,
                                            type=METRICS_TYPE_MAPPING["active_stores_count"]))
            results.append(OrderDatewiseMetric(name="Longtail Outlet Count",
                                            data=longtail_active_stores,
                                            type=METRICS_TYPE_MAPPING["active_stores_count"]))

            return results

    def get_surge_eta_store_metrics(self, conn: connect, city: List = None) -> StoreMetricsResponse:
        # TODO: this function can be optimised based on metric config pattern again
        dynamic_where_clause = []
        params = {}
        results = []
        curs = conn.cursor()
    
        if city is not None:
            dynamic_where_clause.append("AND frontend_merchant_city_name IN (%(cities)s)")
            params["cities"] = city
    
        STORES_CITY_LIST_QUERY = get_store_city_mapping_query(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(STORES_CITY_LIST_QUERY, params)
    
        order_count_map = defaultdict(list)
        rain_order_map = defaultdict(list)
        billed_to_assigned_map = defaultdict(list)
        checkout_to_picker_assigned_map = defaultdict(list)
        surge_shown_map = defaultdict(list)
        surge_reason_map = defaultdict(set)
        store_name_mapping = defaultdict(list)
        frontend_merchant_ids = []
    
        for row in curs:
            frontend_merchant_id, frontend_merchant_name, _, backend_merchant_id, _, _, _ , _= row
            store_name_mapping[frontend_merchant_id].append(StoresNameMapping(frontend_merchant_id=str(frontend_merchant_id),
                                                                          frontend_merchant_name=frontend_merchant_name,
                                                                          backend_merchant_id=str(backend_merchant_id),
                                                                          merchant_type=row[6],
                                                                          is_longtail_outlet=row[7]))
            order_count_map[frontend_merchant_id]
            rain_order_map[frontend_merchant_id]
            billed_to_assigned_map[frontend_merchant_id]
            checkout_to_picker_assigned_map[frontend_merchant_id]
            surge_shown_map[str(frontend_merchant_id)]
            surge_reason_map[str(frontend_merchant_id)]
            frontend_merchant_ids.append(str(frontend_merchant_id))
        curs.close()
        
        order_metrics.get_store_wise_order_metrics(conn = conn,city = city,order_metrics_map = order_count_map, rain_order_map = rain_order_map, billed_to_assigned_map = billed_to_assigned_map, checkout_to_picker_assigned_map = checkout_to_picker_assigned_map)
        surge_seen_metrics.get_store_wise_surge_metrics(conn=conn,city=city,surge_shown_map=surge_shown_map,surge_reason_map=surge_reason_map,frontend_merchant_ids=frontend_merchant_ids)

        results = []
        for frontend_merchant_id, names in store_name_mapping.items():
            frontend_merchant_name = names[0].frontend_merchant_name
            backend_merchant_id = names[0].backend_merchant_id
            metrics = []

            order_count_metrics = order_count_map.get(frontend_merchant_id, [])
            rain_order_metrics = rain_order_map.get(frontend_merchant_id, [])
            billed_to_assigned_metrics = billed_to_assigned_map.get(frontend_merchant_id, [])
            checkout_to_picker_assigned_metrics = checkout_to_picker_assigned_map.get(frontend_merchant_id, [])
            surge_shown_metrics = surge_shown_map.get(frontend_merchant_id, [])
            surge_reason_metrics = surge_reason_map.get(frontend_merchant_id, [])

            order_count_metric = OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["order_count"],
                data=[DatewiseMetric(date=entry.date, metric=entry.metric) for entry in order_count_metrics],
                type=METRICS_TYPE_MAPPING["order_count"]  
            )

            rain_order_metric = OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["rain_order_percentage"],
                data=[DatewiseMetric(date=entry.date, metric=entry.metric) for entry in rain_order_metrics],
                type=METRICS_TYPE_MAPPING["rain_order_percentage"]  
            )

            billed_to_assigned_metric = OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["billed_to_assigned"],
                data=[DatewiseMetric(date=entry.date, metric=entry.metric) for entry in billed_to_assigned_metrics],
                type=METRICS_TYPE_MAPPING["billed_to_assigned"]  
            )

            checkout_to_picker_assigned_metric = OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["checkout_to_picker_assigned"],
                data=[DatewiseMetric(date=entry.date, metric=entry.metric) for entry in checkout_to_picker_assigned_metrics],
                type=METRICS_TYPE_MAPPING["checkout_to_picker_assigned"]  
            )

            surge_shown_metric = OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["surge_seen_percentage"],  
                data=[DatewiseMetric(date=entry.date, metric=entry.metric) for entry in surge_shown_metrics],
                type=METRICS_TYPE_MAPPING["surge_seen_percentage"]  
            )

            metrics.extend([order_count_metric, surge_shown_metric, rain_order_metric, billed_to_assigned_metric, checkout_to_picker_assigned_metric])

            store_metrics = StoreMetrics(
                frontend_merchant_id=str(frontend_merchant_id),
                frontend_merchant_name=frontend_merchant_name,
                backend_merchant_id=str(backend_merchant_id),
                surge_reason = surge_reason_metrics,
                metrics=metrics
            )

            results.append(store_metrics)

        store_metrics_response = StoreMetricsResponse(metrics=results)
        return store_metrics_response


    def get_active_stores_count_metric_custom(self, conn: connect, 
                                              city: List = None, start_date: str = None,
                                              get_trend: bool = False, zone: List = None) -> Union[int, List]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        active_stores_count = None
        if city:
            dynamic_where_clause.append("AND frontend_merchant_city_name IN (%(cities)s)")
            params["cities"] = city
        if zone:
            dynamic_where_clause.append("AND zone IN (%(zone)s) ")
            params["zone"] = zone
        ACTIVE_STORES_COUNT_QUERY = get_active_stores_count_custom_v2(dynamic_where_clause=" ".join(dynamic_where_clause),
                                                            start_date=start_date,
                                                            get_trend=get_trend)
        curs.execute(ACTIVE_STORES_COUNT_QUERY, params)
        if not get_trend:
            for row in curs:
                active_stores_count = row[0]
            return active_stores_count
        else:
            total_active_stores = []
            express_active_stores = []
            longtail_active_stores = []
            results = []
            row = curs.fetchone()
            dates = [
                (datetime.strptime(start_date, '%Y-%m-%d') - timedelta(days=i * 7)).strftime('%Y-%m-%d')
                for i in range(5)
            ]
            i = 0
            for dt in dates:
                total_metric = row[i]
                express_metric = row[i + 1]
                longtail_metric = row[i + 2]

                total_active_stores.append({"date": dt, "metric": total_metric})
                express_active_stores.append({"date": dt, "metric": express_metric})
                longtail_active_stores.append({"date": dt, "metric": longtail_metric})
                i = i + 3

            results.append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING["active_stores_count"],
                                            data=total_active_stores,
                                            type=METRICS_TYPE_MAPPING["active_stores_count"]))
            results.append(OrderDatewiseMetric(name="Express Outlet Count",
                                            data=express_active_stores,
                                            type=METRICS_TYPE_MAPPING["active_stores_count"]))
            results.append(OrderDatewiseMetric(name="Longtail Outlet Count",
                                            data=longtail_active_stores,
                                            type=METRICS_TYPE_MAPPING["active_stores_count"]))
            return results


store_metrics = CRUDStoreMetrics()
