from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math

from pinotdb import connect
from app.api import deps, utils

from app.core.metric_config import PRODUCT_METRICS_CALC_MAPPING, METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING
from .queries import get_complaints_query, get_all_field_list_query, get_filtered_product_fieldwise_query, get_distinct_cart_ids_in_whole_city_store_query, get_all_product_metrics_query, get_all_product_metrics_all_cities_query, get_all_product_metrics_hourwise_query, get_distinct_cart_ids_in_whole_city_store_query_citywise

IST = timezone(timedelta(hours=5, minutes=30))
from app.schemas.product_metric import ProductDatewiseMetric, ComplaintsDateMetric, FilteredProductFieldwise, CityAllMetrics, DateAllCityMetrics, DatewiseHourMetrics, HourMetrics, DatewiseMetric, ProductMetric


class CRUDProductMetrics():
    def _build_lookup_filters(self, filters_dict):
        dynamic_where_clause = []
        params = {}

        for field_name, (value, is_lookup, lookup_table, lookup_column_output, lookup_table_pk, current_table_lookup_colm) in filters_dict.items():
            if value:
                if is_lookup:
                    dynamic_where_clause.append(
                        f"AND LOOKUP('{lookup_table}', '{lookup_column_output}', '{lookup_table_pk}', {current_table_lookup_colm}) IN (%({field_name})s)"
                    )
                else:
                    dynamic_where_clause.append(f"AND {current_table_lookup_colm} IN (%({field_name})s)")

                params[field_name] = value

        return dynamic_where_clause, params


    def get_all_metrics(self, conn: connect,
                        metrics: List = None,
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        yesterday_metric: bool = False,
                        is_daywise: bool = False,
                        current_hour: bool = True,
                        pid: List = None,
                        date_str: str = None,
                        number_of_weeks: int = 5) -> List[ProductDatewiseMetric]:

        curs = conn.cursor()
        results = []

        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        
        filters_dict = {
            "l0_category": (l0_category, False, None, None, None, "l0_category"),
            "l1_category": (l1_category, False, None, None, None, "l1_category"),
            "l2_category": (l2_category, False, None, None, None, "l2_category"),
            "ptype": (ptype, False, None, None, None, "product_type"),
            "pname": (pname, False, None, None, None, "product_name"),
            "brand": (brand, False, None, None, None, "brand_name"),
            "city": (city, False, None, None, None, "city_name"),
            "store": (store, False, None, None, None, "merchant_id"),
            "pid": (pid, False, None, None, None, "item_product_id"),
        }
        
        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)
        
        query = get_all_product_metrics_query(metrics_sql=metrics_sql,
                                              dynamic_where_clause=" ".join(dynamic_where_clause),
                                              yesterday_metric=yesterday_metric,
                                              is_daywise=is_daywise,
                                              current_hour=current_hour,
                                              date_str=date_str,
                                              number_of_weeks=number_of_weeks)
        if not any(metric in {"asp", "new_transacting_users_count", "transacting_users_count"} for metric in metrics):
            query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + query
        curs.execute(query, params)

        metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)

        for row in curs:
            created_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[metric_name].append(DatewiseMetric(date=created_date, metric=metric_value))
        
        for metric_name, _ in metrics_items:
            results.append(ProductDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=METRICS_TYPE_MAPPING[metric_name]
            ))

        if "unique_carts" in metrics:
            curs.close()
            curs = conn.cursor()

            filters_dict = {
                "city": (city, False, None, None, None, "city_name"),
                "store": (store, False, None, None, None, "merchant_id"),
            }
            
            dynamic_where_clause, params = self._build_lookup_filters(filters_dict)

            distinct_cart_ids_query = get_distinct_cart_ids_in_whole_city_store_query(
                dynamic_where_clause=" ".join(dynamic_where_clause),
                yesterday_metric=yesterday_metric,
                is_daywise=is_daywise,
                current_hour=current_hour,
                number_of_weeks=number_of_weeks,
            )
            distinct_cart_ids_query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + distinct_cart_ids_query
            curs.execute(distinct_cart_ids_query, params)

            total_cart_ids: Dict[str, int] = defaultdict(int)
            for row in curs:
                total_cart_ids[row[0]] = row[1]
            results = self._convert_unique_cart_to_cart_pen(results, total_cart_ids)

        return results


    def get_complaints(self, conn: connect, 
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        yesterday_metric: bool = False,
                        is_daywise: bool = False,
                        current_hour: bool = True,
                        pid: List = None,
                        date_str: str = None, 
                        number_of_weeks: int = 5) -> List[ComplaintsDateMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if l0_category:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l0_category', 'product_id', product_id) IN (%(l0_category)s)")
            params["l0_category"] = l0_category
        if l1_category:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l1_category', 'product_id', product_id) IN (%(l1_category)s)")
            params["l1_category"] = l1_category
        if l2_category:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l2_category', 'product_id', product_id) IN (%(l2_category)s)")
            params["l2_category"] = l2_category
        if ptype:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'product_type', 'product_id', product_id) IN (%(ptype)s)")
            params["ptype"] = ptype
        if pname:
            dynamic_where_clause.append("AND product_name IN (%(pname)s)")
            params["pname"] = pname
        if brand:
            dynamic_where_clause.append("AND LOOKUP('product_l0_l1_l2_mapping_v1', 'brand_name', 'product_id', product_id) IN (%(brand)s)")
            params["brand"] = brand
        if city:
            dynamic_where_clause.append("AND LOOKUP('outlet_facility_mapping_v1', 'city_name', 'outlet_id', store_id) IN (%(city)s)")
            params["city"] = city
        if store:
            dynamic_where_clause.append("AND LOOKUP('outlet_facility_mapping_v1', 'merchant_id', 'outlet_id', store_id) IN (%(store)s)")
            params["store"] = store
        if pid:
            dynamic_where_clause.append("AND product_id IN (%(pid)s)")
            params["pid"] = pid

        query = get_complaints_query(dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric,
                                            is_daywise=is_daywise,
                                            current_hour=current_hour,
                                            date_str=date_str, 
                                            number_of_weeks=number_of_weeks)
        
        curs.execute(query, params)

        complaints_daywise_metrics = []

        for row in curs:
            complaint_date = datetime.strptime(row[0], "%Y-%m-%d")
            category_type = row[1]
            complaint_count = round(float(row[2]), 2)
            complaints_daywise_metrics.append(
                ComplaintsDateMetric(category_type=category_type,
                                     date=complaint_date,
                                     count=complaint_count))
            
        return complaints_daywise_metrics
    

    def get_all_field_list(self, conn: connect, 
                           search_filter: List = None):
        curs = conn.cursor()
        
        result = []
        for field in search_filter: 
            if field not in PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING:
                continue
            db_field_name = PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING[field]
            query = get_all_field_list_query(field=db_field_name,
                                             table_name="fact_order_item_details_v3")
            curs.execute(query)

            all_field_list = []
            for row in curs:
                all_field_list.append(row[0])
            result.append(FilteredProductFieldwise(field=field, filtered_values=all_field_list))

        return result
    

    def get_write_ahead_regex_expression(self, field_values: List) -> str:
        regex_exp = ""
        # '^abc.*|^def.*' - Match from the beginning of string
        for idx, val in enumerate(field_values):
            regex_exp += f'{"^" if idx == 0 else "|^"}{val}.*'
        return regex_exp


    def get_filtered_product_fieldwise(self, conn: connect, 
                                        l0_category: List = None,
                                        l1_category: List = None,
                                        l2_category: List = None,
                                        ptype: List = None,
                                        pname: List = None,
                                        brand: List = None,
                                        search_filter: str = None,
                                        pid: List = None) -> FilteredProductFieldwise:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if l0_category:
            regex_exp = self.get_write_ahead_regex_expression(l0_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l0_category, (%(l0_category)s), 'i' )")
            params["l0_category"] = regex_exp
        if l1_category:
            regex_exp = self.get_write_ahead_regex_expression(l1_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l1_category, (%(l1_category)s), 'i' )")
            params["l1_category"] = regex_exp
        if l2_category:
            regex_exp = self.get_write_ahead_regex_expression(l2_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l2_category, (%(l2_category)s), 'i' )")
            params["l2_category"] = regex_exp
        if ptype:
            regex_exp = self.get_write_ahead_regex_expression(ptype)
            dynamic_where_clause.append("AND REGEXP_LIKE(product_type, (%(ptype)s), 'i' )")
            params["ptype"] = regex_exp
        if pname:
            regex_exp = self.get_write_ahead_regex_expression(pname)
            dynamic_where_clause.append("AND REGEXP_LIKE(product_name, (%(pname)s), 'i' )")
            params["pname"] = regex_exp
        if brand:
            regex_exp = self.get_write_ahead_regex_expression(brand)
            dynamic_where_clause.append("AND REGEXP_LIKE(brand_name, (%(brand)s), 'i' )")
            params["brand"] = regex_exp
        if pid:
            regex_exp = self.get_write_ahead_regex_expression(pid)
            dynamic_where_clause.append("AND REGEXP_LIKE(CAST(item_product_id AS STRING), (%(pid)s), 'i' )")
            params["pid"] = regex_exp

        if search_filter not in PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING:
            return FilteredProductFieldwise(field = search_filter, filtered_values=[])
        db_search_filter_name = PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING[search_filter]
        
        where_clause = ""
        if len(dynamic_where_clause) != 0:
            where_clause = " ".join(dynamic_where_clause)[4:]
        
        query = get_filtered_product_fieldwise_query(dynamic_where_clause=where_clause,
                                                     search_filter=db_search_filter_name,
                                                     table_name="fact_order_item_details_v3")
        curs.execute(query, params)

        filtered_values = []

        for row in curs:
            value = row[0]
            filtered_values.append(value)
        
        return FilteredProductFieldwise(field = search_filter,
                                        filtered_values = filtered_values)


    def get_all_metrics_all_cities(self, conn: connect,
                        metrics: List = None,
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        yesterday_metric: bool = False,
                        city: List = None,
                        store: List = None,
                        pid: List = None) -> List[DateAllCityMetrics]:
        
        curs = conn.cursor()
        
        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        
        filters_dict = {
            "l0_category": (l0_category, False, None, None, None, "l0_category"),
            "l1_category": (l1_category, False, None, None, None, "l1_category"),
            "l2_category": (l2_category, False, None, None, None, "l2_category"),
            "ptype": (ptype, False, None, None, None, "product_type"),
            "pname": (pname, False, None, None, None, "product_name"),
            "brand": (brand, False, None, None, None, "brand_name"),
            "city": (city, False, None, None, None, "city_name"),
            "pid": (pid, False, None, None, None, "item_product_id"),
            "store": (store, False, None, None, None, "merchant_id"),
        }
        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)

        query = get_all_product_metrics_all_cities_query(metrics_sql = metrics_sql,
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric,
                                            is_store_query=True if city else False, )
        if not any(metric in {"asp", "new_transacting_users_count", "transacting_users_count"} for metric in metrics):
            query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + query
        curs.execute(query, params)

        # Process query results
        datewise_data = {}
        store_city_mapping = {}
        
        for row in curs:
            ordered_date = datetime.strptime(row[0], "%Y-%m-%d")
            city_name = row[1]
            if city:
                store_name = row[2]
                store_id = row[3]
                start_index = 4    
                store_city_mapping[store_id] = {
                    "city_name" : city_name,
                    "store_name" : store_name
                }
                city_store_key = store_id # store_id
            else:
                city_store_key = city_name # city_name
                start_index = 2
            for idx, (metric_name, _) in enumerate(metrics_items, start=start_index):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                if ordered_date not in datewise_data:
                    datewise_data[ordered_date] = {}
                if city_store_key not in datewise_data[ordered_date]:
                    datewise_data[ordered_date][city_store_key] = []
                datewise_data[ordered_date][city_store_key].append(
                    ProductMetric(metric=metric_value,
                                name=METRIC_NAME_MAPPING[metric_name],
                                type=METRICS_TYPE_MAPPING[metric_name])
                )

        datewise_city_store_metrics = []

        for ordered_date, city_store_data in datewise_data.items():
            ordered_date_data = []
            if city:
                for store_id, metric_list in city_store_data.items():
                    store_mapping_obj = store_city_mapping.get(store_id)
                    ordered_date_data.append(CityAllMetrics(
                        city=store_mapping_obj["city_name"],
                        frontend_merchant_name=store_mapping_obj["store_name"],
                        frontend_merchant_id=str(store_id),
                        data=metric_list
                    ))
                datewise_city_store_metrics.append(DateAllCityMetrics(date=ordered_date , data=ordered_date_data, date_diff=(datetime.now(IST) - ordered_date.replace(tzinfo=IST)).days))
            else:
                for city_name, metric_list in city_store_data.items():
                    ordered_date_data.append(CityAllMetrics(
                        city=city_name,
                        frontend_merchant_name=None,
                        frontend_merchant_id=None,
                        data=metric_list
                    ))
                datewise_city_store_metrics.append(DateAllCityMetrics(date=ordered_date , data=ordered_date_data, date_diff=(datetime.now(IST) - ordered_date.replace(tzinfo=IST)).days))

        # Handle cart penetration calculation
        if "unique_carts" in metrics:
            curs.close()
            curs = conn.cursor()
            
            # First, get total cart IDs across all categories for each city/store
            # We need to remove category filters for this query to get the total carts
            total_filters_dict = {
                "city": (city, False, None, None, None, "city_name"),
                "store": (store, False, None, None, None, "merchant_id"),
            }
            total_dynamic_where_clause, total_params = self._build_lookup_filters(total_filters_dict)

            distinct_cart_ids_query = get_distinct_cart_ids_in_whole_city_store_query_citywise(
                dynamic_where_clause=" ".join(total_dynamic_where_clause),
                yesterday_metric=yesterday_metric,
                is_store_query=True if city else False,
            )
            distinct_cart_ids_query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + distinct_cart_ids_query
            curs.execute(distinct_cart_ids_query, total_params)

            # Store total cart IDs by date and city/store
            total_cart_ids = {}
            for row in curs:
                date_str = row[0]
                if date_str not in total_cart_ids:
                    total_cart_ids[date_str] = {}
                
                if city and len(row) >= 4:  # Store-level data
                    store_id = row[2]
                    total_cart_ids[date_str][store_id] = row[3]
                elif not city and len(row) >= 3:  # City-level data
                    city_name = row[1]
                    total_cart_ids[date_str][city_name] = row[2]
            
            # Convert unique_carts to cart_pen for each date and city/store
            for date_metrics in datewise_city_store_metrics:
                date_str = date_metrics.date.strftime('%Y-%m-%d')
                if date_str in total_cart_ids:
                    for city_metrics in date_metrics.data:
                        key = int(city_metrics.frontend_merchant_id) if city else city_metrics.city
                        if key in total_cart_ids[date_str] and total_cart_ids[date_str][key] != 0:
                            # Find unique_carts metric and convert to cart_pen
                            for metric in city_metrics.data:
                                if metric.name == METRIC_NAME_MAPPING["unique_carts"]:
                                    # If no category filters are applied, cart_pen should be 100%
                                    if not any([l0_category, l1_category, l2_category, ptype, pname, brand, pid]):
                                        metric.metric = 100.0
                                    else:
                                        # Calculate cart penetration as percentage of filtered carts to total carts
                                        metric.metric = round((metric.metric / total_cart_ids[date_str][key]) * 100, 1)
                                    
                                    metric.name = METRIC_NAME_MAPPING["cart_pen"]
                                    metric.type = METRICS_TYPE_MAPPING["cart_pen"]
                                    break

        return datewise_city_store_metrics


    def get_all_metrics_hourwise(self, conn: connect,
                        metrics: List = None,
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        yesterday_metric: bool = False,
                        is_daywise: bool = False,
                        current_hour: bool = True,
                        date_str: str = None,
                        pid: List = None) -> List[DatewiseHourMetrics]:
        curs = conn.cursor()
        
        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]

        filters_dict = {
            "l0_category": (l0_category, False, None, None, None, "l0_category"),
            "l1_category": (l1_category, False, None, None, None, "l1_category"),
            "l2_category": (l2_category, False, None, None, None, "l2_category"),
            "ptype": (ptype, False, None, None, None, "product_type"),
            "pname": (pname, False, None, None, None, "product_name"),
            "brand": (brand, False, None, None, None, "brand_name"),
            "city": (city, False, None, None, None, "city_name"),
            "pid": (pid, False, None, None, None, "item_product_id"),
            "store": (store, False, None, None, None, "merchant_id"),
        }
        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)

        query = get_all_product_metrics_hourwise_query(metrics_sql = metrics_sql,
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric,
                                            date_str=date_str)
        if not any(metric in {"asp", "new_transacting_users_count", "transacting_users_count"} for metric in metrics):
            query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + query
        curs.execute(query, params)

        datewise_data = {}

        for row in curs:
            ordered_date = datetime.strptime(row[0], "%Y-%m-%d")
            hour = row[1]
            for idx, (metric_name, _) in enumerate(metrics_items, start=2):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                if ordered_date not in datewise_data:
                    datewise_data[ordered_date] = {}
                if hour not in datewise_data[ordered_date]:
                    datewise_data[ordered_date][hour] = []
                datewise_data[ordered_date][hour].append(
                    ProductMetric(metric=metric_value, 
                                name=METRIC_NAME_MAPPING[metric_name],
                                type=METRICS_TYPE_MAPPING[metric_name])
                    )

        datewise_hour_metrics = []
        for ordered_date, hour_data in datewise_data.items():
            ordered_date_data = []
            for hour, metric_list in hour_data.items():
                ordered_date_data.append(HourMetrics(
                    hour = int(hour),
                    data=metric_list
                ))
            datewise_hour_metrics.append(DatewiseHourMetrics(date=ordered_date , data=ordered_date_data))
        
        if "unique_carts" in metrics:
            curs.close()
            curs = conn.cursor()

            filters_dict = {
                "city": (city, False, None, None, None, "city_name"),
                "store": (store, False, None, None, None, "merchant_id"),
            }
            dynamic_where_clause, params = self._build_lookup_filters(filters_dict)

            distinct_cart_ids_query = get_distinct_cart_ids_in_whole_city_store_query(
                                                dynamic_where_clause=" ".join(dynamic_where_clause),
                                                yesterday_metric=yesterday_metric, isHourly = True,)
            distinct_cart_ids_query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + distinct_cart_ids_query
            curs.execute(distinct_cart_ids_query, params)

            total_cart_ids: Dict[str, Dict[int, int]] = defaultdict(lambda: defaultdict(int))
            for row in curs:
                total_cart_ids[row[0]][row[1]] = row[2]
            datewise_hour_metrics = self._convert_unique_cart_to_cart_pen(datewise_hour_metrics, total_cart_ids, isHourly = True)
        
        return datewise_hour_metrics


    def _convert_unique_cart_to_cart_pen(self, results, total_cart_ids, isHourly: bool = False):
        if isHourly:
            for result in results:
                date_str = result.date.strftime('%Y-%m-%d')
                for hourData in result.data:
                    hour_str = str(hourData.hour)
                    for data in hourData.data:
                        if date_str in total_cart_ids and total_cart_ids[date_str][hour_str] != 0:
                            data.metric = round((data.metric / total_cart_ids[date_str][hour_str]) * 100, 1)
                        else:
                            continue
                        data.name = METRIC_NAME_MAPPING["cart_pen"]
                        data.type = METRICS_TYPE_MAPPING["cart_pen"]
        else:
            for result in results:
                if result.name == 'Unique Carts':
                    for metric in result.data:
                        date_str = metric.date.strftime('%Y-%m-%d')
                        if date_str in total_cart_ids and total_cart_ids[date_str] != 0:
                            metric.metric = round((metric.metric / total_cart_ids[date_str]) * 100, 1)
                        else:
                            continue
                    result.name = METRIC_NAME_MAPPING["cart_pen"]
                    result.type = METRICS_TYPE_MAPPING["cart_pen"]
        return results


product_metrics = CRUDProductMetrics()