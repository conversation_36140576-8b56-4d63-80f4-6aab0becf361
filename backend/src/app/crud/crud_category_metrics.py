from datetime import datetime, timezone, timedelta
from typing import List, <PERSON>, Dict
from collections import defaultdict
import math
from decimal import Decimal

from pinotdb import connect
from app.api import deps, utils

from app.core.metric_config import PRODUCT_METRICS_CALC_MAPPING, METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING, CATEGORY_COLUMNS_TO_QUERY, METRICS_TABLES_MAPPING_CATEGORY, MERCHANDISING_METRICS_CALC_MAPPING_CATEGORY, CATEGORY_PAGE_NAME_CONVERT_MAPPING
from .queries import get_all_category_metrics_query, get_distinct_cart_ids_in_whole_city_store_query, get_category_metrics_query, get_all_category_metrics_query, get_l1_category_metrics_for_l0_query
from app.schemas.order_metric import OrderDatewiseMetric, ConversionGroupMetric,HourlyDateMetrics,HourMetric,Metric,HourlyDateMetric 
from .utils import generate_insights_metrics_sql, add_overall_search_impressions

IST = timezone(timedelta(hours=5, minutes=30))
from app.schemas.product_metric import ProductDatewiseMetric,  DatewiseMetric, L0CategoryProductMetrics, L1CategoryProductMetrics, L1CategoryMetrics


class CRUDCategoryMetrics():
    def get_l1_category_metrics(self, conn: connect,
                               l0_category: str,
                               metric: str = None,
                               city: List = None,
                               yesterday_metric: bool = False):
        """
        Get L1 category metrics for a given L0 category
        
        Args:
            conn: Database connection
            l0_category: L0 category to filter on
            metric: Metric to fetch
            city: Cities to filter on
            yesterday_metric: Whether to get yesterday's metrics
            
        Returns:
            Dictionary with L1 category metrics
        """
        total_cart_ids = None
        if metric in ('unique_carts', 'aov_contribution'):
            curs = conn.cursor()
            dynamic_where_clause = []
            params = {}

            # if l0_category:
            #     dynamic_where_clause.append("AND l0_category = %(l0_category)s")
            #     params["l0_category"] = l0_category
                
            if city:
                dynamic_where_clause.append("AND city_name IN (%(city)s)")
                params["city"] = city

            distinct_cart_ids_query = get_distinct_cart_ids_in_whole_city_store_query(
                                                dynamic_where_clause=" ".join(dynamic_where_clause),
                                                yesterday_metric=yesterday_metric,
                                                number_of_weeks=5)
            curs.execute(distinct_cart_ids_query, params)

            total_cart_ids: Dict[str, int] = defaultdict(int)
            for row in curs:
                total_cart_ids[row[0]] = row[1]

            curs.close()
        
        metrics = [metric]
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}

        category_results = {}

        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        
        if l0_category:
            dynamic_where_clause.append("AND l0_category = %(l0_category)s")
            params["l0_category"] = l0_category
            
        if city:
            dynamic_where_clause.append("AND city_name IN (%(city)s)")
            params["city"] = city

        query = get_l1_category_metrics_for_l0_query(
                                               metrics_sql=metrics_sql,
                                               dynamic_where_clause=" ".join(dynamic_where_clause),
                                               yesterday_metric=yesterday_metric,
                                               number_of_weeks=5)
        
        if "asp" not in metrics:
            query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + query
        curs.execute(query, params)

        metrics_data: Dict[str, Dict[str, List[Dict[str, float]]]] = defaultdict(lambda: defaultdict(list))

        for row in curs:
            created_date = datetime.strptime(row[0], "%Y-%m-%d")
            l1_category = row[1]
            for idx, (metric_name, _) in enumerate(metrics_items, start=2):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[l1_category][metric_name].append(DatewiseMetric(date=created_date, metric=metric_value))
        
        for l1_category, _ in metrics_data.items():
            results = []
            for metric_name, _ in metrics_items:
                results.append(ProductDatewiseMetric(
                    name=METRIC_NAME_MAPPING[metric_name],
                    data=metrics_data[l1_category][metric_name],
                    type=METRICS_TYPE_MAPPING[metric_name]
                ))
            if total_cart_ids:
                results = self._divide_metric_by_total_carts(results, total_cart_ids)
            category_results[l1_category] = results
            
        l1_category_metrics = []
        for l1_category, metrics in category_results.items():
            l1_category_metrics.append(L1CategoryProductMetrics(
                metrics=metrics,
                l1_category=l1_category
            ))

        return L1CategoryMetrics(data=l1_category_metrics, l0_category=l0_category)

    def get_all_metrics(self, conn: connect,
                        metric: str = None,
                        city: List = None,
                        yesterday_metric: bool = False,):
        
        total_cart_ids = None
        if metric in ('unique_carts', 'aov_contribution'):
            curs = conn.cursor()
            dynamic_where_clause = []
            params = {}

            if city:
                dynamic_where_clause.append("AND city_name IN (%(city)s)")
                params["city"] = city

            distinct_cart_ids_query = get_distinct_cart_ids_in_whole_city_store_query(
                                                dynamic_where_clause=" ".join(dynamic_where_clause),
                                                yesterday_metric=yesterday_metric,
                                                number_of_weeks=5)
            curs.execute(distinct_cart_ids_query, params)

            total_cart_ids: Dict[str, int] = defaultdict(int)
            for row in curs:
                total_cart_ids[row[0]] = row[1]

            curs.close()
        
        metrics = [metric]
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}

        category_results = {}

        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        if city:
            dynamic_where_clause.append("AND city_name IN (%(city)s)")
            params["city"] = city

        query = get_all_category_metrics_query(metrics_sql = metrics_sql,
                                                dynamic_where_clause=" ".join(dynamic_where_clause),
                                                yesterday_metric=yesterday_metric,
                                                number_of_weeks=5,)
        if "asp" not in metrics: 
            query = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            ''' + query
        curs.execute(query, params)

        metrics_data: Dict[str, Dict[str, List[Dict[str, float]]]] = defaultdict(lambda: defaultdict(list))

        for row in curs:
            created_date = datetime.strptime(row[0], "%Y-%m-%d")
            category = row[1]
            for idx, (metric_name, _) in enumerate(metrics_items, start=2):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[category][metric_name].append(DatewiseMetric(date=created_date, metric=metric_value))
        
        for category, _ in metrics_data.items():
            results = []
            for metric_name, _ in metrics_items:
                results.append(ProductDatewiseMetric(
                    name=METRIC_NAME_MAPPING[metric_name],
                    data=metrics_data[category][metric_name],
                    type=METRICS_TYPE_MAPPING[metric_name]
                ))
            if total_cart_ids:
                results = self._divide_metric_by_total_carts(results, total_cart_ids)
            category_results[category] = results
        return category_results


    def _divide_metric_by_total_carts(self, results, total_cart_ids):
        for result in results:
            if result.name in ('Unique Carts', 'AOV Contribution'):
                for metric in result.data:
                    date_str = metric.date.strftime('%Y-%m-%d')
                    if date_str in total_cart_ids and total_cart_ids[date_str] != 0:
                        if result.name == 'Unique Carts':
                            metric.metric = round((metric.metric / total_cart_ids[date_str]) * 100, 1)
                        else:
                            metric.metric = round((metric.metric / total_cart_ids[date_str]), 2)
                    else:
                        continue
                metric_to_convert = result.name
                result.name = METRIC_NAME_MAPPING[CATEGORY_PAGE_NAME_CONVERT_MAPPING[metric_to_convert]]
                result.type = METRICS_TYPE_MAPPING[CATEGORY_PAGE_NAME_CONVERT_MAPPING[metric_to_convert]]
        return results


    def calculate_metrics_with_dates(self, 
                                     row, 
                                     conversion_mapping, # "search_conversion": "100 * (SUM(search_atc) / SUM(search)) AS search_conversion",
                                     base_metrics,       # [ "dau", "atc", "search", "search_atc", "availability", "cnt_inventory" ]
                                     yesterday_view,):
        ptype_metrics = ["search", "availability", "unique_impressions", "cnt_inventory", "merch_page_impr",
                         "merch_page_impr_atc", "cat_grid_page_impr", "cat_grid_page_impr_atc", "cnt_be_inventory"]
        funnel_metrics = ["dau"]

        suffixes = ['t0', 't7']
        metrics_to_calculate = ["unique_impressions"] + list(conversion_mapping.keys()) + ["search"] + ["availability"]
        metrics_to_calculate.extend(["cnt_inventory"])

        # to check if we dont have search data for certain ptype's so we dont consider them
        # for suffix in suffixes:
        #     for metric in ["search", "search_atc"]:
        #         column_index = 3
        #         column_index += base_metrics.index(metric) * len(suffixes) + suffixes.index(suffix)
        #         if row[column_index] == -1:
        #             return {}

        # calculating date's based on suffixes
        ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
        ist_now = ist_now - timedelta(days=1) if yesterday_view else ist_now
        ist_dates = {suffix: (ist_now - timedelta(days=int(suffix[1:]))).strftime('%Y-%m-%d') for suffix in suffixes}

        result = {}

        for metric_name in metrics_to_calculate:
            metric_data = []
            if metric_name in conversion_mapping:
                # calculate metrics based on expression in metric config
                expression = conversion_mapping[metric_name]
                for suffix in suffixes:
                    metric_expression = expression
                    for base_metric in base_metrics:
                        # index handling to take care of key columns like city/store, date/hour
                        column_index = 3
                        column_index += base_metrics.index(base_metric) * len(suffixes) + suffixes.index(suffix)
                        metric_expression = metric_expression.replace(
                            f"SUM({base_metric})",
                            str(row[column_index])
                        )

                    try:
                        metric_value = Decimal(eval(metric_expression.split(" AS ")[0]))  
                    except ZeroDivisionError:
                        metric_value = Decimal(0)
                    metric_data.append(DatewiseMetric(date=ist_dates[suffix],metric=round(metric_value, 2)))

            if (metric_name in ptype_metrics) or (metric_name in funnel_metrics):
                if metric_name == "unique_impressions":
                    metric_column = "dau"
                else:
                    metric_column = metric_name
                for suffix in suffixes:
                    # index handling to take care of key columns like city/store, date/hour
                    column_index = 3
                    column_index += base_metrics.index(metric_column) * len(suffixes) + suffixes.index(suffix)
                    try:
                        metric_value = Decimal(row[column_index])
                    except ZeroDivisionError:
                        metric_value = Decimal(0)
                    
                    # in case of ptype metrics null value is -1 and in funnel metrics null value is -999
                    if (metric_value == -1):
                        continue
                    metric_data.append(DatewiseMetric(date=ist_dates[suffix],metric=round(metric_value, 2)))
            result[metric_name] = metric_data
        return result

    
    def get_category_insights_metrics(
        self, 
        conn: connect, 
        city: List = None, 
        yesterday_view: bool = False,
    ) -> List[ConversionGroupMetric]:
        
        timestamp_col = 'etl_snapshot_ts_ist_epoch'
        columns_to_query = CATEGORY_COLUMNS_TO_QUERY

        metrics_sql = generate_insights_metrics_sql(columns_to_query, 2, timestamp_col, 'INT')
        table_name_key = 'category_daily'
        table_name = METRICS_TABLES_MAPPING_CATEGORY[table_name_key]['city']

        dynamic_where_clause = []
        params = {}
        if not city:
            city = ['Overall']
        dynamic_where_clause.append(""" AND city IN (%(cities)s) """)
        params["cities"] = city

        curs = conn.cursor()
        
        query = get_category_metrics_query(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            table_name=table_name,
            is_yesterday=yesterday_view,
        )

        curs.execute(query, params)

        conversion_mapping = MERCHANDISING_METRICS_CALC_MAPPING_CATEGORY

        results = defaultdict(list)
        for row in curs:
            key = row[1]
            ptype = row[2]
            calculated_metrics = self.calculate_metrics_with_dates(
                row,
                conversion_mapping,
                columns_to_query,
                yesterday_view=yesterday_view,
            )
            for metric_name, metrics_data in calculated_metrics.items():
                results[ptype].append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING[metric_name], data=metrics_data, type=METRICS_TYPE_MAPPING[metric_name]))
        
        final_results = [
            ConversionGroupMetric(
                key=key,
                grain=grain,
                metric=metrics
                )
                for grain, metrics in results.items()
            ]
        if final_results == []:
            return final_results
        
        final_results_with_overall_search = add_overall_search_impressions(final_results)
        # sorted_final_results = remove_metric(sort_conversion_group_metrics(data=final_results_with_overall_search, metrics_type="product"),False)
        # return sorted_final_results[:100]
        return final_results_with_overall_search

category_metrics = CRUDCategoryMetrics()
