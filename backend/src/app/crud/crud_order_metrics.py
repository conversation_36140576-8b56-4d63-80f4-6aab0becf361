from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math

from pinotdb import connect

from app.api import deps,utils
import app.crud.utils as support_utils
from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING, CRITICAL_HAVING_CONDITION
from .queries import get_all_order_metrics_query, get_new_users_count, get_all_order_metrics_city_wise_query, get_order_metrics_hourly_query, get_all_order_metrics_days_comparative_city_wise_query, get_transacting_user_count_city_wise_query, get_cart_blocks_query, get_today_all_cities_dau_query, get_order_metrics_stores_current_view_query, get_today_hau_metrics_city_wise, get_ath_metrics, get_all_order_metrics_query_custom, get_new_users_count_custom, get_order_metrics_hourly_query_custom, get_orders_per_minute_query
from app.schemas.order_metric import HourlyDateMetrics, OrderDatewiseMetric, OrderMetricsFilter, Metric, LocationMetric, LocationDateMetric, LocationDateMetrics, HourMetric, HourlyDateMetric, DatewiseMetric, StoreMetric, StoreDateMetric, StoreDateMetrics, ConversionGroupMetric, OrdersPerMinute, OrderPerMinuteData
IST = timezone(timedelta(hours=5, minutes=30))


class CRUDOrderMetrics():
    def get_all_metrics(self, conn: connect, metrics: List = None, city: List = None, store: List = None,
                        status: List = None, app: str = None,
                        yesterday_metric: bool = False,
                        merchant_type: str = None,
                        zone: List = None,
                        merchant_ids: List = []) -> List[OrderDatewiseMetric]:

        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if status:
            dynamic_where_clause.append("AND current_status IN (%(status)s)")
            params["status"] = status
        if merchant_type:
            dynamic_where_clause.append("AND merchant_id IN (%(merchant_ids)s)")
            params["merchant_ids"] = merchant_ids

        required_dates = support_utils.get_dates_in_ist_format(support_utils.get_date_str(is_yesterday=yesterday_metric), 7, 5)
        params["required_dates"] = required_dates

        query = get_all_order_metrics_query(metrics_sql = metrics_sql,
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric)
        enable_multi_stage_engine = '''
            SET useMultistageEngine=true;
        '''
        if "new_transacting_users_count" in metrics:
            generic_params = '''
                SET enableNullHandling = True;
            '''
        else: 
            generic_params = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            '''
        
        query = generic_params + query

        if "true_fill_rate" in metrics:
            query = enable_multi_stage_engine + query

        curs.execute(query, params)

        metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)

        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                    # metric_value = 0.00  # Replace NaN or infinity with 0.0
                metrics_data[metric_name].append({"date": order_date, "metric": metric_value})

        results = [
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=METRICS_TYPE_MAPPING[metric_name]
            )
            for metric_name, _ in metrics_items
        ]
        return results
    
    def get_orders_per_minute(self,
                             conn: connect,
                             date_str: str,
                             city: List = None,
                             store: List = None,
                             zone: List = None) -> OrdersPerMinute:
        """
        Get the number of orders per minute for a specific date.
        
        Args:
            conn: Pinot database connection
            date_str: The date for which to get orders per minute in format 'YYYY-MM-DD'
            city: List of cities to filter by
            store: List of stores to filter by
            zone: List of zones to filter by
            
        Returns:
            OrdersPerMinute object containing list of OrderPerMinuteData
        """
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
            
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
            
        if zone:
            dynamic_where_clause.append("AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s)")
            params["zone"] = zone
        
        query = get_orders_per_minute_query(
            date_str=date_str,
            dynamic_where_clause=" ".join(dynamic_where_clause)
        )
        
        generic_params = '''
            SET enableNullHandling = True;
            SET serverReturnFinalResult = True;
        '''
        
        query = generic_params + query
        curs.execute(query, params)
        
        metrics = []
        for row in curs:
            minute_bucket = row[0]
            orders_per_minute = row[1]
            metrics.append(OrderPerMinuteData(
                minute_bucket=minute_bucket,
                orders_per_minute=orders_per_minute
            ))
        
        return OrdersPerMinute(metrics=metrics)

    def get_ath_metrics(self, conn: connect, city: List = None):
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if city:
            dynamic_where_clause.append("city in (%(cities)s)")
            params["cities"] = city
        query = get_ath_metrics(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(query, params)
        metrics_data: Dict[str, Dict[str, Union[float, str]]] = defaultdict(lambda: {"sum": 0, "max_date": None})
        results = []

        for row in curs:
            order_date = row[3] 
            metric_value = row[0]
            metric_name = row[1]
            metrics_data[metric_name]["sum"] += metric_value
            current_max_date = metrics_data[metric_name]["max_date"]
            if current_max_date is None or order_date > current_max_date:
                metrics_data[metric_name]["max_date"] = order_date

        if metrics_data["ath_gmv"]["max_date"] is not None:
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING["ath_gmv"],
                    data=[{"date": metrics_data["ath_gmv"]["max_date"], "metric": metrics_data["ath_gmv"]["sum"]}],
                    type=METRICS_TYPE_MAPPING["ath_gmv"]
                )
            )

        if metrics_data["ath_order_count"]["max_date"] is not None:
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING["ath_order_count"],
                    data=[{"date": metrics_data["ath_order_count"]["max_date"], "metric": metrics_data["ath_order_count"]["sum"]}],
                    type=METRICS_TYPE_MAPPING["ath_order_count"]
                )
            )
        return results


    # [DEPRICATED]
    def get_new_users_count_metrics(self, conn: connect,
                                    city: List = None,
                                    store: List = None,
                                    is_yesterday: bool = False) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if city:
            dynamic_where_clause.append("AND lookup('merchant_outlet_facility_mapping_v2', "
                                        "'frontend_merchant_city_name', 'frontend_merchant_id',"
                                        " merchant_id) IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        query = get_new_users_count(dynamic_where_clause=" ".join(dynamic_where_clause),
                                    is_yesterday=is_yesterday)
        curs.execute(query, params)
        metrics_data = []
        results = []
        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            metrics_data.append(DatewiseMetric(date=order_date, metric=row[1]))

        results.append(
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["new_transacting_users_count"],
                data=metrics_data,
                type=METRICS_TYPE_MAPPING["new_transacting_users_count"]
            )
        )
        return results

    def _get_city_wise_dau_metrics(self, conn: connect, past_days_diff: int = 7, app: str = None,
                                   city: List = None, date_str: str = None, hour: str = None, zone: List = None) -> dict:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {"past_days_diff": past_days_diff}
        if app:
            dynamic_where_clause.append(" AND app like %(app)")
            params["app"] = app
        if city:
            dynamic_where_clause.append(" AND city in (%(cities)s)")
            params["cities"] = city

        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone

        if hour:
            dynamic_where_clause.append(" AND hr IN (%(hour)s)")
            params["hour"] = hour
            curs.execute(get_today_hau_metrics_city_wise(" ".join(dynamic_where_clause),  is_store_query=True if city else False, date_str=date_str), params)
        else:
            curs.execute(get_today_all_cities_dau_query(" ".join(dynamic_where_clause), is_store_query=True if city else False, date_str=date_str), params)

        results_dau = defaultdict(lambda: defaultdict(int))
        window_end = []
        
        store_names = {}

        for row in curs:
            if city:
                results_dau[row[0]][row[5]] += row[2]
                if hour:
                    store_names[row[5]] = row[7]
                else: 
                    store_names[row[5]] = row[6]
            else:
                # Hack to combine all metrics of nearby cities in Chandigarh.
                if row[1] not in ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh'):
                    results_dau[row[0]][row[1]] += row[2]
                else:
                    results_dau[row[0]]['Chandigarh'] += row[2]
            window_end.append(row[3])
        max_window_end = max(window_end) if window_end else "now()"
        results_dau_conversion = defaultdict(lambda: defaultdict(int))
        dynamic_where_clause = []
        params = {"max_window_end": max_window_end, "past_days_diff": past_days_diff}
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if hour:
            dynamic_where_clause.append("AND hour(insert_timestamp, 'Asia/Kolkata') IN (%(hour)s)")
            params["hour"] = hour
        
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone

        curs.execute(get_transacting_user_count_city_wise_query(dynamic_where_clause=" ".join(dynamic_where_clause), 
                                                                is_store_query=True if city else False,
                                                                date_str=date_str,
                                                                hour=hour), params)
        for row in curs:
            if city:
                dau = results_dau.get(row[1], {}).get(row[3], None)
                if dau:
                    # If dau is not present in results, we are skipping for that city/date
                    dau_conversion = round((row[2] / dau) * 100, 2)
                    results_dau_conversion[row[1]][ row[3]] = dau_conversion
            else:
                dau = results_dau.get(row[1], {}).get(row[0], None)
                if dau:
                    # If dau is not present in results, we are skipping for that city/date
                    dau_conversion = round((row[2] / dau) * 100, 2)
                    results_dau_conversion[row[1]][row[0]] = dau_conversion
        
        # Initialize all cart blocks conversion dictionaries
        results_express_demand_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        results_express_serviceability_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        results_express_serviceability_ooh_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        results_express_serviceability_manual_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        # results_longtail_serviceability_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        results_longtail_serviceability_ooh_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        results_longtail_serviceability_manual_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        results_longtail_demand_based_cart_blocks_conversion = defaultdict(lambda: defaultdict(int))
        
        dynamic_where_clause = []
        params =  {"past_days_diff": past_days_diff}
        if city:
            dynamic_where_clause.append(" AND lookup('merchant_outlet_facility_mapping_v2', "
                                        "'frontend_merchant_city_name', 'frontend_merchant_id', "
                                        "merchant_id) in (%(cities)s)")
            params["cities"] = city
        if hour:
            dynamic_where_clause.append(""" AND hour("time"*1000, 'Asia/Kolkata') IN (%(hour)s)""")
            params["hour"] = hour

        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone

        # Define the IST timezone offset (+5:30)
        # will be removed once we start using date directly rather than yesterday_metric
        IST_OFFSET = timedelta(hours=5, minutes=30)
        IST = timezone(IST_OFFSET)
        is_yesterday = lambda date_str: datetime.strptime(date_str, '%Y-%m-%d').date() == \
                                        (datetime.now(IST) - timedelta(days=1)).date()
        curs.execute(get_cart_blocks_query(dynamic_where_clause=" ".join(dynamic_where_clause),
                                        get_all_cities=False if city else True,
                                        is_store_query=True if city else False,
                                        yesterday_metric=is_yesterday(date_str),
                                        max_window_end=max_window_end,
                                        hour=hour), params)
        for row in curs:
            if city:
                cart_block = results_dau.get(row[0], {}).get(row[4], None)
                if cart_block:
                    # If dau is not present in results, we are skipping for that city/date
                    cart_block_conversion = round((row[2] / cart_block) * 100, 2)
                    if row[1] == 'express_demand_based_block':
                        results_express_demand_based_cart_blocks_conversion[row[0]][row[4]] = cart_block_conversion
                    elif row[1] == 'express_serviceability_ooh_based_block':
                        results_express_serviceability_ooh_based_cart_blocks_conversion[row[0]][row[4]] = cart_block_conversion
                    elif row[1] == 'express_serviceability_manual_based_block':
                        results_express_serviceability_manual_based_cart_blocks_conversion[row[0]][row[4]] = cart_block_conversion
                    elif row[1] == 'longtail_serviceability_ooh_based_block':
                        results_longtail_serviceability_ooh_based_cart_blocks_conversion[row[0]][row[4]] = cart_block_conversion
                    elif row[1] == 'longtail_serviceability_manual_based_block':
                        results_longtail_serviceability_manual_based_cart_blocks_conversion[row[0]][row[4]] = cart_block_conversion
                    elif row[1] == 'longtail_demand_based_block':
                        results_longtail_demand_based_cart_blocks_conversion[row[0]][row[4]] = cart_block_conversion
            else:
                cart_block = results_dau.get(row[0], {}).get(row[3], None)
                if cart_block:
                    # If dau is not present in results, we are skipping for that city/date
                    cart_block_conversion = round((row[2] / cart_block) * 100, 2)
                    if row[1] == 'express_demand_based_block':
                        results_express_demand_based_cart_blocks_conversion[row[0]][row[3]] = cart_block_conversion
                    elif row[1] == 'express_serviceability_ooh_based_block':
                        results_express_serviceability_ooh_based_cart_blocks_conversion[row[0]][row[3]] = cart_block_conversion
                    elif row[1] == 'express_serviceability_manual_based_block':
                        results_express_serviceability_manual_based_cart_blocks_conversion[row[0]][row[3]] = cart_block_conversion
                    elif row[1] == 'longtail_serviceability_ooh_based_block':
                        results_longtail_serviceability_ooh_based_cart_blocks_conversion[row[0]][row[3]] = cart_block_conversion
                    elif row[1] == 'longtail_serviceability_manual_based_block':
                        results_longtail_serviceability_manual_based_cart_blocks_conversion[row[0]][row[3]] = cart_block_conversion
                    elif row[1] == 'longtail_demand_based_block':
                        results_longtail_demand_based_cart_blocks_conversion[row[0]][row[3]] = cart_block_conversion

        # Combine express serviceability blocks
        for date in set(results_express_serviceability_ooh_based_cart_blocks_conversion.keys()) | set(results_express_serviceability_manual_based_cart_blocks_conversion.keys()):
            # For each city in either dictionary for this date
            all_cities = set(results_express_serviceability_ooh_based_cart_blocks_conversion[date].keys()) | set(results_express_serviceability_manual_based_cart_blocks_conversion[date].keys())
            
            for city_name in all_cities:
                # Add the values from both dictionaries (defaultdict will return 0 if key doesn't exist)
                results_express_serviceability_based_cart_blocks_conversion[date][city_name] = round(
                    results_express_serviceability_ooh_based_cart_blocks_conversion[date][city_name] + 
                    results_express_serviceability_manual_based_cart_blocks_conversion[date][city_name],
                    2  # Round to 2 decimal places
                )

        # # Combine longtail serviceability blocks
        # for date in set(results_longtail_serviceability_ooh_based_cart_blocks_conversion.keys()) | set(results_longtail_serviceability_manual_based_cart_blocks_conversion.keys()):
        #     # For each city in either dictionary for this date
        #     all_cities = set(results_longtail_serviceability_ooh_based_cart_blocks_conversion[date].keys()) | set(results_longtail_serviceability_manual_based_cart_blocks_conversion[date].keys())
            
        #     for city_name in all_cities:
        #         # Add the values from both dictionaries (defaultdict will return 0 if key doesn't exist)
        #         results_longtail_serviceability_based_cart_blocks_conversion[date][city_name] = round(
        #             results_longtail_serviceability_ooh_based_cart_blocks_conversion[date][city_name] + 
        #             results_longtail_serviceability_manual_based_cart_blocks_conversion[date][city_name],
        #             2  # Round to 2 decimal places
        #         )

        return (results_dau, results_dau_conversion, results_express_demand_based_cart_blocks_conversion, 
                results_express_serviceability_based_cart_blocks_conversion, 
                results_express_serviceability_ooh_based_cart_blocks_conversion, 
                results_express_serviceability_manual_based_cart_blocks_conversion,
                # results_longtail_serviceability_based_cart_blocks_conversion,
                results_longtail_serviceability_ooh_based_cart_blocks_conversion,
                results_longtail_serviceability_manual_based_cart_blocks_conversion,
                results_longtail_demand_based_cart_blocks_conversion,
                store_names)


    def get_city_wise_dau_metrics(self, conn: connect, past_days_diff: int = 7, app: str = None, is_sensitive: bool = True, city: List = None, date_str: str = None, hour: str = None, zone: List = None) -> Union[StoreDateMetrics, LocationDateMetrics]:
        """
        Get only DAU related metrics for cities/stores
        """
        curs = conn.cursor()
        city_wise_dau_results, city_wise_dau_conversion_results, city_wise_demand_based_cart_block_conversion, \
            city_wise_express_serviceability_based_cart_blocks_conversion, city_wise_express_serviceability_ooh_based_cart_blocks_conversion, city_wise_express_serviceability_manual_based_cart_blocks_conversion, _, _, _, store_names = self._get_city_wise_dau_metrics(conn,
                                                                                                   past_days_diff=past_days_diff, app=app, city=city, date_str=date_str, hour=hour, zone=zone)
        if city is None:
            # City-wise DAU metrics
            results = []
            citywise_results = defaultdict(list)
            
            for date_str, cities_data in city_wise_dau_results.items():
                for city_name, dau_value in cities_data.items():
                    metrics = [
                        Metric(name=METRIC_NAME_MAPPING["dau"], metric=dau_value, type=METRICS_TYPE_MAPPING["dau"]),
                        Metric(name=METRIC_NAME_MAPPING["dau_conversion_percentage"], metric=city_wise_dau_conversion_results.get(date_str, {}).get(city_name, 0), type=METRICS_TYPE_MAPPING["dau_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["demand_based_cart_blocks_conversion_percentage"], metric=city_wise_demand_based_cart_block_conversion.get(date_str, {}).get(city_name, 0), type=METRICS_TYPE_MAPPING["demand_based_cart_blocks_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["serviceability_based_cart_blocks_conversion_percentage"], metric=city_wise_express_serviceability_based_cart_blocks_conversion.get(date_str, {}).get(city_name, 0), type=METRICS_TYPE_MAPPING["serviceability_based_cart_blocks_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["express_serviceability_ooh_based_cart_blocks_conversion_percentage"], metric=city_wise_express_serviceability_ooh_based_cart_blocks_conversion.get(date_str, {}).get(city_name, 0), type=METRICS_TYPE_MAPPING["express_serviceability_ooh_based_cart_blocks_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["express_serviceability_manual_based_cart_blocks_conversion_percentage"], metric=city_wise_express_serviceability_manual_based_cart_blocks_conversion.get(date_str, {}).get(city_name, 0), type=METRICS_TYPE_MAPPING["express_serviceability_manual_based_cart_blocks_conversion_percentage"])
                    ]
                    metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=metrics)
                    citywise_results[date_str].append(LocationMetric(type="city", name=city_name, data=metrics))
            
            for dt_str, result in citywise_results.items():
                results.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), 
                                                  etl_snapshot_ts_ist=None, 
                                                  date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, 
                                                  data=result))
            return LocationDateMetrics(metrics=results)
        else:
            # Store-wise DAU metrics
            results = []
            storewise_results = defaultdict(list)
            
            for date_str, stores_data in city_wise_dau_results.items():
                for store_id, dau_value in stores_data.items():
                    metrics = [
                        Metric(name=METRIC_NAME_MAPPING["dau"], metric=dau_value, type=METRICS_TYPE_MAPPING["dau"]),
                        Metric(name=METRIC_NAME_MAPPING["dau_conversion_percentage"], metric=city_wise_dau_conversion_results.get(date_str, {}).get(store_id, 0), type=METRICS_TYPE_MAPPING["dau_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["demand_based_cart_blocks_conversion_percentage"], metric=city_wise_demand_based_cart_block_conversion.get(date_str, {}).get(store_id, 0), type=METRICS_TYPE_MAPPING["demand_based_cart_blocks_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["serviceability_based_cart_blocks_conversion_percentage"], metric=city_wise_express_serviceability_based_cart_blocks_conversion.get(date_str, {}).get(store_id, 0), type=METRICS_TYPE_MAPPING["serviceability_based_cart_blocks_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["express_serviceability_ooh_based_cart_blocks_conversion_percentage"], metric=city_wise_express_serviceability_ooh_based_cart_blocks_conversion.get(date_str, {}).get(store_id, 0), type=METRICS_TYPE_MAPPING["express_serviceability_ooh_based_cart_blocks_conversion_percentage"]),
                        Metric(name=METRIC_NAME_MAPPING["express_serviceability_manual_based_cart_blocks_conversion_percentage"], metric=city_wise_express_serviceability_manual_based_cart_blocks_conversion.get(date_str, {}).get(store_id, 0), type=METRICS_TYPE_MAPPING["express_serviceability_manual_based_cart_blocks_conversion_percentage"])
                    ]
                    metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=metrics)

                    store_name = store_names.get(store_id, f"Store_{store_id}")  # Fallback to Store_{store_id} if name not found
                    storewise_results[date_str].append(StoreMetric(frontend_merchant_name=store_name, frontend_merchant_id=str(store_id), data=metrics))
            
            for dt_str, result in storewise_results.items():
                results.append(StoreDateMetric(etl_snapshot_ts_ist = None, date=datetime.strptime(dt_str, "%Y-%m-%d"), date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return StoreDateMetrics(metrics=results)


    def get_city_wise_current_date_metrics(self, conn: connect, past_days_diff: int = 7, app: str = None, is_sensitive: bool = True, city: List = None, date_str: str = None, hour: str = None, zone: List = None) -> Union[StoreDateMetrics, LocationDateMetrics]:
        """
        Get all metrics for cities/stores excluding DAU related metrics
        """
        curs = conn.cursor()

        dynamic_where_clause = []
        params = {"past_days_diff": past_days_diff}
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")

        if hour:
            dynamic_where_clause.append("AND hour(insert_timestamp, 'Asia/Kolkata') IN (%(hour)s)")
            params["hour"] = hour
        
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")            
            params["zone"] = zone

        query = get_all_order_metrics_city_wise_query(dynamic_where_clause=" ".join(dynamic_where_clause), is_store_level=True if city else False, date_str=date_str, hour=hour)
        curs.execute(query, params)
        
        if city is None:
            results = []
            citywise_results = defaultdict(list)
            for row in curs:
                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["gmv"], metric=row[2], type=METRICS_TYPE_MAPPING["gmv"]),
                    Metric(name=METRIC_NAME_MAPPING["order_count"], metric=row[3], type=METRICS_TYPE_MAPPING["order_count"]),
                    Metric(name=METRIC_NAME_MAPPING["aov"], metric=round(row[4], 2), type=METRICS_TYPE_MAPPING["aov"]),
                    Metric(name=METRIC_NAME_MAPPING["surge_shown_carts_percentage"], metric=round(row[5], 2), type=METRICS_TYPE_MAPPING["surge_shown_carts_percentage"]),
                    Metric(name=METRIC_NAME_MAPPING["rain_order_percentage"], metric=round(row[6], 2), type=METRICS_TYPE_MAPPING["rain_order_percentage"])
                ]
                metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=metrics)
                citywise_results[row[1]].append(LocationMetric(type="city", name=row[0], data=metrics))
            for dt_str, result in citywise_results.items():
                results.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return LocationDateMetrics(metrics = results)
        else:
            results = []
            storewise_results = defaultdict(list)
            for row in curs:
                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["gmv"], metric=row[3], type=METRICS_TYPE_MAPPING["gmv"]),
                    Metric(name=METRIC_NAME_MAPPING["order_count"], metric=row[4], type=METRICS_TYPE_MAPPING["order_count"]),
                    Metric(name=METRIC_NAME_MAPPING["aov"], metric=round(row[5], 2), type=METRICS_TYPE_MAPPING["aov"]),
                    Metric(name=METRIC_NAME_MAPPING["surge_shown_carts_percentage"], metric=round(row[6], 2), type=METRICS_TYPE_MAPPING["surge_shown_carts_percentage"]),
                    Metric(name=METRIC_NAME_MAPPING["rain_order_percentage"], metric=round(row[7], 2), type=METRICS_TYPE_MAPPING["rain_order_percentage"])
                ]
                metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,metrics_list=metrics)
                storewise_results[row[2]].append(StoreMetric(frontend_merchant_name=str(row[1]), frontend_merchant_id=str(row[0]), data=metrics))
            for dt_str, result in storewise_results.items():
                results.append(StoreDateMetric(
                    date=datetime.strptime(dt_str, "%Y-%m-%d"),
                    date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, 
                    data=result,
                    etl_snapshot_ts_ist=None
                ))
            return StoreDateMetrics(metrics = results)

    def get_city_wise_comparitive_date_metrics(self, conn: connect, 
                                               app: str = None, metrics: List = None, 
                                               yesterday_metric: bool = False, 
                                               status: str = None, 
                                               city_wise: str = None, 
                                               store_wise: str = None, 
                                               is_critical_metrics: bool = False, 
                                               city: str = None,
                                               custom_view: bool = False,
                                               start_date: str = None) -> Union[LocationDateMetrics, List[ConversionGroupMetric]]:
        curs = conn.cursor()
        dynamic_where_clause = []
        dynamic_having_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]
        # metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if status:
            dynamic_where_clause.append("AND current_status IN (%(status)s)")
            params["status"] = status
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if is_critical_metrics:
            having_condition = '\nAND'.join([CRITICAL_HAVING_CONDITION[key] for key in metrics if key in CRITICAL_HAVING_CONDITION])
            dynamic_having_clause.append(
                # stores having ppi is greater than 25 seconds or fill rate less than 99%
                f"""
                HAVING {having_condition}
                """
            )
        query = get_all_order_metrics_days_comparative_city_wise_query(
            dynamic_where_clause=" ".join(dynamic_where_clause), 
            dynamic_having_clause=" ".join(dynamic_having_clause), 
            metrics_sql=metrics_sql, 
            yesterday_metric=yesterday_metric, 
            city_wise=city_wise, 
            store_wise=store_wise, 
            is_critical_metrics=is_critical_metrics,
            custom_view = custom_view,
            start_date = start_date,
        )

        curs.execute(query, params)
        results = []
        if not is_critical_metrics:
            citywise_results = defaultdict(list)
            for row in curs:
                metric_value = row[2]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                    # metric_value = 0.00 
                metrics_data = [
                    Metric(name=METRIC_NAME_MAPPING[metrics[0]],metric=metric_value, type=METRICS_TYPE_MAPPING[metrics[0]]),
                ]
                citywise_results[row[0]].append(LocationMetric(type="city", name=row[1], data=metrics_data))
            for dt_str, result in citywise_results.items():
                results.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return LocationDateMetrics(metrics = results)
        else:
            results = []
            key_metrics_data = {}
            for row in curs:
                order_date = datetime.strptime(row[0], "%Y-%m-%d")
                key = row[1]
                metric_value = row[2]

                metric_value = round(float(metric_value), 2) if isinstance(
                    metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value) else 0.00

                if key not in key_metrics_data:
                    key_metrics_data[key] = {metric: [] for metric in metrics}

                for metric in metrics:
                    key_metrics_data[key][metric].append(
                        {"date": order_date, "metric": metric_value})

            for key, metrics_data in key_metrics_data.items():
                order_datewise_metrics = []
                for metric, data in metrics_data.items():
                    order_datewise_metrics.append(
                        OrderDatewiseMetric(
                            name=METRIC_NAME_MAPPING[metric],
                            data=data,
                            type=METRICS_TYPE_MAPPING[metric]
                        ))
                results.append(
                    ConversionGroupMetric(
                        key=key,
                        grain=None,
                        metric=order_datewise_metrics
                    ))

            return results

    def get_hourly_metrics(self, 
                           conn: connect, 
                           metrics: List = None, cities: List = None, 
                           app: str = None, stores: List = None, 
                           yesterday_metric: bool = False, 
                           status: List = None, 
                           merchant_type: str = None, 
                           merchant_ids: List = [], 
                           is_sensitive: bool = True,
                           zone: List = None,
                           custom_view: bool = False,
                           start_date: str = None, ) -> HourlyDateMetrics:
        curs = conn.cursor()
        metrics_calc = [METRICS_CALC_MAPPING[metric] for metric in metrics]
        metrics_calc_sql = ",\n".join(metrics_calc)
        dynamic_where_clause = []
        params = {}
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if status:
            dynamic_where_clause.append("AND current_status IN (%(status)s)")
            params["status"] = status
        if cities:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = cities
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone
        if stores:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = stores
        if merchant_type:
            dynamic_where_clause.append("AND merchant_id IN (%(merchant_ids)s)")
            params["merchant_ids"] = merchant_ids

        if not custom_view:
            query = get_order_metrics_hourly_query(metrics_sql=metrics_calc_sql, 
                                               dynamic_where_clause=" ".join(dynamic_where_clause),
                                               yesterday_metric=yesterday_metric,) 
        else:
            query = get_order_metrics_hourly_query_custom(metrics_sql=metrics_calc_sql, 
                                               dynamic_where_clause=" ".join(dynamic_where_clause),
                                               start_date = start_date) 
        query = '''
            SET enableNullHandling = True;
            SET serverReturnFinalResult = True;
        ''' + query
        curs.execute(query, params)
        datewise_results = []
        hourly_results = defaultdict(list)
        for row in curs:
            result_metrics, idx = [], 0
            for metric in metrics:
                metric_value = row[idx+2]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    metric_value = '-'  # Replace NaN or infinity with -
                result_metrics.append(Metric(name=METRIC_NAME_MAPPING[metric], metric=metric_value, type=METRICS_TYPE_MAPPING[metric]))
                idx += 1
            result_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,metrics_list=result_metrics)
            hourly_results[row[1]].append(HourMetric(hour=row[0], data=result_metrics))

        for dt_str, result in hourly_results.items():
            datewise_results.append(HourlyDateMetric(date= datetime.strptime(dt_str, "%Y-%m-%d"), 
                                                     date_diff= (datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, # !! CHECK: diff is from now not `start_date` in case of custom_view and also yesterday
                                                     data=result))
        return HourlyDateMetrics(metrics= datewise_results)


    def get_store_wise_order_metrics(self, conn: connect, city: List = None, order_metrics_map: defaultdict(list) = None, rain_order_map: defaultdict(list) = None, billed_to_assigned_map: defaultdict(list) = None, checkout_to_picker_assigned_map: defaultdict(list) = None):
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if city is not None:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        order_count_query = get_order_metrics_stores_current_view_query(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(order_count_query, params)
        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            order_count = DatewiseMetric(date = order_date, metric = row[1])
            checkout_to_picker_assigned_order = DatewiseMetric(date = order_date, metric = round(row[2],2))
            assigned_before_billing_order = DatewiseMetric(date = order_date, metric = round(row[3],2))
            rain_order = DatewiseMetric(date = order_date, metric = round(row[4],2))
            frontend_merchant_id = row[5]
            order_metrics_map[frontend_merchant_id].append(order_count)
            rain_order_map[frontend_merchant_id].append(rain_order)
            billed_to_assigned_map[frontend_merchant_id].append(assigned_before_billing_order)
            checkout_to_picker_assigned_map[frontend_merchant_id].append(checkout_to_picker_assigned_order)
        curs.close()

    def find_models_in_list(self, models: List, model_name: str) -> List:
        return [model for model in models if model.name == model_name]

    def calculate_ratios_based_on_two_models_input(self,
                                                   first_model,
                                                   second_model,
                                                   model_name) -> List:
        # Sort both lists based on date
        first_model.sort(key=lambda x: x.date)
        second_model.sort(key=lambda x: x.date)

        ratio_data = []
        for first_entry, second_entry in zip(first_model, second_model):
            if first_entry.date != second_entry.date:
                return [OrderDatewiseMetric(data=ratio_data,
                                    type=METRICS_TYPE_MAPPING[model_name],
                                    name=METRIC_NAME_MAPPING[model_name])] 
            first_count = first_entry.metric
            second_count = second_entry.metric
            if second_count != 0:
                ratio = first_count / second_count
            else:
                ratio = 0
            ratio_data.append({"date": first_entry.date,
                               "metric": round(ratio, 2) if METRICS_TYPE_MAPPING[model_name] == 'number' else ratio})
        return [OrderDatewiseMetric(data=ratio_data,
                                    type=METRICS_TYPE_MAPPING[model_name],
                                    name=METRIC_NAME_MAPPING[model_name])]
    
    def get_merchant_ids(self, conn: connect, city: List = None, merchant_type: str = None) -> []:
        curs = conn.cursor()
        dynamic_where_clause = "AND type in (%(merchant_type)s)"
        params = {"merchant_type": merchant_type}

        if city:
            dynamic_where_clause += "AND frontend_merchant_city_name IN (%(city)s)"
            params["city"] = city

        query = f"""SELECT distinct frontend_merchant_id
            FROM merchant_outlet_facility_mapping_v2
            WHERE frontend_merchant_city_name NOT IN ('Not in service area')
            {dynamic_where_clause}
            limit 10000
            """
        curs.execute(query, params)

        merchant_ids = []
        for row in curs:
            merchant_ids.append(str(row[0]))
        return merchant_ids


    def get_all_metrics_custom(self, conn: connect, metrics: List = None, 
                               city: List = None, store: List = None,
                               status: List = None, app: str = None,
                               merchant_type: str = None, merchant_ids: List = [],
                               start_date: str = None, 
                               zone: List = None,
                            ) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        if zone:
            dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
            params["zone"] = zone
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if status:
            dynamic_where_clause.append("AND current_status IN (%(status)s)")
            params["status"] = status
        if merchant_type:
            dynamic_where_clause.append("AND merchant_id IN (%(merchant_ids)s)")
            params["merchant_ids"] = merchant_ids
        
        required_dates = support_utils.get_dates_in_ist_format(start_date, 7, 5)
        params["required_dates"] = required_dates

        query = get_all_order_metrics_query_custom(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            start_date=start_date
        )
        enable_multi_stage_engine = '''
            SET useMultistageEngine=true;
        '''
        if "new_transacting_users_count" in metrics:
            generic_params = '''
                SET enableNullHandling = True;
            '''
        else: 
            generic_params = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            '''
        query = generic_params + query

        if "true_fill_rate" in metrics:
            query = enable_multi_stage_engine + query

        curs.execute(query, params)

        metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)

        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[metric_name].append({"date": order_date, "metric": metric_value})

        results = [
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=METRICS_TYPE_MAPPING[metric_name]
            )
            for metric_name, _ in metrics_items
        ]
        return results


    # [DEPRICATED]
    def get_new_users_count_metrics_custom(self, conn: connect,
                                    city: List = None,
                                    store: List = None,
                                    start_date: str = None, 
                                ) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if city:
            dynamic_where_clause.append("AND lookup('merchant_outlet_facility_mapping_v2', "
                                        "'frontend_merchant_city_name', 'frontend_merchant_id',"
                                        " merchant_id) IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        query = get_new_users_count_custom(dynamic_where_clause=" ".join(dynamic_where_clause),
                                           start_date=start_date)
        curs.execute(query, params)
        metrics_data = []
        results = []
        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            metrics_data.append(DatewiseMetric(date=order_date, metric=row[1]))

        results.append(
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING["new_transacting_users_count"],
                data=metrics_data,
                type=METRICS_TYPE_MAPPING["new_transacting_users_count"]
            )
        )
        return results


order_metrics = CRUDOrderMetrics()
