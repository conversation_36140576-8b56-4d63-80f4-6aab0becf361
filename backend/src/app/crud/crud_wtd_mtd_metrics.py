from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import List, Dict

from .utils import process_citywise_results, process_metric_data, process_zonewise_results, get_etl_snapshot_ts
from app.core.config import settings
from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING
from app.schemas.mtd_metric import MTDMetric
from app.schemas.wtd_metric import WTDMetric
from app.schemas.order_metric import LocationDateMetric, LocationDateMetrics
from .queries import get_wtd_mtd_query

from pinotdb import connect

IST = timezone(timedelta(hours=5, minutes=30))


class CRUDWtdMtdMetrics():
    def fetch_data(self, conn: connect,
                   zone: str = None,
                   city: str = None,
                   metric_type: str = None,
                   isTrends: bool = None) -> List[dict]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}

        if isTrends:
            if city:
                dynamic_where_clause.append("city_name IN (%(cities)s)")
                params["cities"] = city
            if zone:
                dynamic_where_clause.append("AND zone_name IN (%(zones)s)")
                params["zones"] = zone
            if not city and not zone:
                dynamic_where_clause.append("zone_name = 'Overall'")
        else:
            if city:
                dynamic_where_clause.append("city_name IN (%(cities)s)")
                params["cities"] = city
            else:
                dynamic_where_clause.append("city_name = 'Overall'")

            if zone:
                dynamic_where_clause.append("AND zone_name IN (%(zones)s)")
                params["zones"] = zone
            else:
                dynamic_where_clause.append("AND zone_name = 'Overall'")
        
        query = get_wtd_mtd_query(metric_type=metric_type,
                                  dynamic_where_clause=" ".join(dynamic_where_clause),)
        curs.execute(query, params)
        
        column_names = [desc[0] for desc in curs.description]
        results = [dict(zip(column_names, row)) for row in curs]
        return results


    def get_all_metrics(self, 
                        conn: connect, 
                        metrics: List = None, 
                        zone: str = None,
                        city: str = None, 
                        metric_type: str = None, ):
        results = self.fetch_data(conn, zone, city, metric_type)
        metric_data = process_metric_data(results, metrics)
        etl_snapshot_ts_ist_obj = get_etl_snapshot_ts(results)

        if metric_type == 'mtd': 
            return [MTDMetric(name=METRIC_NAME_MAPPING[metric], 
                          etl_snapshot_ts_ist=etl_snapshot_ts_ist_obj, 
                          data=metric_data.get(metric,[]), 
                          type=METRICS_TYPE_MAPPING[metric])
                    for metric in metrics if metric_data.get(metric,{})]
        else:
            return [WTDMetric(name=METRIC_NAME_MAPPING[metric], 
                          etl_snapshot_ts_ist=etl_snapshot_ts_ist_obj, 
                          data=metric_data.get(metric,[]), 
                          type=METRICS_TYPE_MAPPING[metric])
                    for metric in metrics if metric_data.get(metric,{})]


    def get_city_zone_metrics(self, conn: connect, 
                        metrics: List = None, 
                        zone: str = None, 
                        city:str = None, 
                        metric_type: str = None, 
                        isTrends: bool = False) -> LocationDateMetrics:
        results = self.fetch_data(conn, zone, city, metric_type, isTrends)

        if isTrends and not city:
            processed_results = process_citywise_results(results, metrics, zone)
        else:
            processed_results = process_zonewise_results(results, metrics, city)
        
        results = []
        for (dt_str, etl_snapshot_ts_ist_obj), result in processed_results.items():
            date_obj = datetime.strptime(dt_str, "%Y-%m-%d")
            results.append(LocationDateMetric(date=date_obj, 
                                          etl_snapshot_ts_ist=etl_snapshot_ts_ist_obj, 
                                          date_diff=(datetime.now(IST) - date_obj.replace(tzinfo=IST)).days, 
                                          data=result))
        return LocationDateMetrics(metrics=results)


wtd_mtd_metrics = CRUDWtdMtdMetrics()
