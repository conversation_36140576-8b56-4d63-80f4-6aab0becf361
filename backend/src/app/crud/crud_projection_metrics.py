from datetime import timezone, timedelta
from typing import List
from pinotdb import connect
from app.core.config import settings
from app.schemas.current_rate_metric import ProjectionMetrics, Metric, CurrentRateDatewiseMetric, CurrentRateMetric
from .queries import get_projection_metrics_query, get_store_city_mapping_query, get_actual_order_metrics, get_projection_metrics_event_query
from datetime import datetime
from app.core.metric_config import METRIC_NAME_MAPPING,METRICS_TYPE_MAPPING
import json


IST = timezone(timedelta(hours=5, minutes=30))

class CRUDProjectionMetrics():
    def _get_outlet_ids(self, conn: connect, store: List = None, zone: List = None) -> List[str]:
        dynamic_where_clause = []
        params = {}
        curs = conn.cursor()

        if zone:
            dynamic_where_clause.append("AND zone IN (%(zone)s)")
            params["zone"] = zone
        if store:
            dynamic_where_clause.append("AND frontend_merchant_id IN (%(stores)s)")
            params["stores"] = store
        STORES_CITY_LIST_QUERY = get_store_city_mapping_query(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(STORES_CITY_LIST_QUERY, params)
        outlet_ids = [str(row[4]) for row in curs]
        curs.close()
        
        return outlet_ids
    
    def get_projection_metrics_daily(self, conn: connect, city: List = None, store: List = None, zone: List = None, projection_days: int = 15, today_projection: bool = None, start_date: str = None) -> List[CurrentRateDatewiseMetric]:
        results = []
        dynamic_where_clause = []
        params = {}

        # Handle edge case for `purnia` city
        if city:
            city = [c if c.lower() != 'purnia' else 'Purnia' for c in city]
        print(city)

        dynamic_where_clause.append("AND city IN (%(cities)s)")
        params["cities"] = city
        if store: 
            outlet_ids = self._get_outlet_ids(conn, store=store)
            dynamic_where_clause.append("AND outlet_id IN (%(outlets)s)")
            params["outlets"] = outlet_ids
        elif zone:
            outlet_ids = self._get_outlet_ids(conn,zone=zone)
            dynamic_where_clause.append("AND outlet_id IN (%(outlets)s)")
            params["outlets"] = outlet_ids  
        else: 
            dynamic_where_clause.append("AND outlet_id = 'Overall'")

        projection_query = get_projection_metrics_query(dynamic_where_clause=" ".join(dynamic_where_clause), projection_days=projection_days, today_projection=today_projection, start_date=start_date)
        curs = conn.cursor()
        curs.execute(projection_query, params)
        projected_order_count, projected_gmv = [],[]
        if today_projection or start_date:
            for row in curs:
                current_date = start_date if start_date else datetime.now(IST).date()
                projected_order_count.append({"date": current_date, "metric": int(row[0])})
                projected_gmv.append({"date": current_date, "metric": row[1]})
                
            dynamic_where_clause = []
            params = {}
            
            if not (len(city) == 1 and city[0] == 'Overall'):
                dynamic_where_clause.append("AND city_name IN (%(cities)s)")
                params["cities"] = city
            if store:
                dynamic_where_clause.append("AND merchant_id IN (%(store)s)")
                params["store"] = store
            if zone: 
                dynamic_where_clause.append(" AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s) ")
                params["zone"] = zone

            curs.execute(get_actual_order_metrics(dynamic_where_clause=" ".join(dynamic_where_clause), start_date=start_date), params)
            for row in curs:
                projection_date = datetime.strptime(row[0], "%Y-%m-%d")
                projected_order_count.append({"date": projection_date, "metric": int(row[1])})
                projected_gmv.append({"date": projection_date, "metric": row[2]})
        else:
            for row in curs:
                projection_date = datetime.strptime(row[0], "%Y-%m-%d")
                projected_order_count.append({"date": projection_date, "metric": int(row[1])})
                projected_gmv.append({"date": projection_date, "metric": row[2]})
        
        results.append(CurrentRateDatewiseMetric(name=METRIC_NAME_MAPPING["projected_gmv"], data=projected_gmv, type=METRICS_TYPE_MAPPING["projected_gmv"]))
        results.append(CurrentRateDatewiseMetric(name=METRIC_NAME_MAPPING["projected_order_count"], data=projected_order_count, type=METRICS_TYPE_MAPPING["projected_order_count"]))
        return results


    def get_projection_metrics_daily_events(self, 
                                            conn: connect, 
                                            city: List = None, 
                                            store: List = None,
                                            projection_days: int = 15, ):
        results = []
        dynamic_where_clause = []
        params = {}
        dynamic_where_clause.append("AND city IN (%(cities)s)")
        params["cities"] = city
        if store is None:
            dynamic_where_clause.append("AND outlet_id = 'Overall'")
        else:
            outlet_ids = self._get_outlet_ids(conn, store)
            dynamic_where_clause.append("AND outlet_id IN (%(outlets)s)")
            params["outlets"] = outlet_ids

        projection_query = get_projection_metrics_event_query(dynamic_where_clause=" ".join(dynamic_where_clause), 
                                                              projection_days=projection_days,)
        curs = conn.cursor()
        curs.execute(projection_query, params)
        results = []
        for row in curs:
            projection_date = row[0]
            event_name = ""
            if row[1]:
                try:
                    unescaped_data = row[1].replace('\\"', '"').strip('"')
                    parsed_data = json.loads(unescaped_data)
                    if isinstance(parsed_data, list) and parsed_data:
                        event_name = parsed_data[0].get("item")
                except Exception as e:
                    event_name = ""
            results.append({
                "date" : projection_date,
                "event_name" : event_name,
                "is_event" : True if event_name != "" else False
            })
        return results


    def get_projection_metrics_bucket(self, conn: connect, 
                                      city: List = None, store: List = None,
                                      monthly_projection: bool = None, 
                                      weekly_projection: bool = None, 
                                      yesterday_projection: bool = None,
                                      custom_view: bool = None,
                                      zone: List = None,
                                      start_date: str = None):
        dynamic_where_clause = []
        params = {}
        dynamic_where_clause.append("AND city IN (%(cities)s)")
        params["cities"] = city
        if store is None and zone is None:
            dynamic_where_clause.append("AND outlet_id = 'Overall'")
        elif zone:
            outlet_ids = self._get_outlet_ids(conn,zone=zone)
            dynamic_where_clause.append("AND outlet_id IN (%(outlets)s)")
            params["outlets"] = outlet_ids
        else:
            outlet_ids = self._get_outlet_ids(conn,store=store)
            dynamic_where_clause.append("AND outlet_id IN (%(outlets)s)")
            params["outlets"] = outlet_ids
        projection_query = get_projection_metrics_query(dynamic_where_clause=" ".join(dynamic_where_clause),
                                                        weekly_projection=weekly_projection,
                                                        monthly_projection=monthly_projection, 
                                                        yesterday_projection=yesterday_projection,
                                                        start_date = start_date,)
        curs = conn.cursor()
        curs.execute(projection_query, params)
        
        if custom_view:
            projected_order_count = []
            projected_gmv = []
            results = []
            for row in curs:
                projection_date = datetime.strptime(row[0], "%Y-%m-%d")
                projected_order_count.append({"date": projection_date, "metric": int(row[1])})
                projected_gmv.append({"date": projection_date, "metric": row[2]})
            results.append(CurrentRateDatewiseMetric(name=METRIC_NAME_MAPPING["projected_gmv"], data=projected_gmv, type=METRICS_TYPE_MAPPING["projected_gmv"]))
            results.append(CurrentRateDatewiseMetric(name=METRIC_NAME_MAPPING["projected_order_count"], data=projected_order_count, type=METRICS_TYPE_MAPPING["projected_order_count"]))
        else:
            for row in curs:
                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["projected_order_count"], metric=int(row[0]), type=METRICS_TYPE_MAPPING["projected_order_count"]),
                    Metric(name=METRIC_NAME_MAPPING["projected_gmv"], metric=row[1], type=METRICS_TYPE_MAPPING["projected_gmv"])
                ]
            results = ProjectionMetrics(metrics=metrics,weekly_projection=weekly_projection,monthly_projection=monthly_projection,yesterday_projection=yesterday_projection)
        return results
            
projection_metrics = CRUDProjectionMetrics()

