from typing import List
from datetime import datetime, timedelta

from .utils import get_date_str, is_yesterday_date, generate_dt_filters_custom_date, extract_indices_from_dt_filters, generate_dynamic_dt_filters_with_backend_calc, get_yesterdays_date_in_ist, get_todays_date_in_ist
from ..common import product_metric as product_metric_query_utils
from app.schemas.product_metric import ProductMetricTypes

def generate_dt_filters(date_str,
                        date_column="dt",
                        timestamp_column=None, num_weeks=5,
                        less_than_time="now()/1000", period_length=7, timestamp_column_in_seconds: bool = False,
                        is_yesterday: bool = False, skip_week: List[int]=[]):
    # TODO:make this function more generic
    date_format = '%Y-%m-%d'
    dt_filters = []

    # IST is UTC + 5 hours and 30 minutes
    ist_offset = timedelta(hours=5, minutes=30)
    # Convert date_str to datetime object
    date_ist = datetime.strptime(date_str, date_format)
    # Get current time in IST
    now_ist = datetime.now() + ist_offset
    if is_yesterday and date_ist.date() == now_ist.date():
        date_ist -= timedelta(days=1)

    if date_ist.date() == now_ist.date():
        for i in range(num_weeks):
            order_date = (date_ist - timedelta(days=period_length*i)).strftime(date_format)
            if i in skip_week:
                continue
            if timestamp_column:
                dt_filters.append(
                    f"({date_column} = '{order_date}' AND {timestamp_column} <= "
                    f"FromEpochSeconds({less_than_time} - 86400*{period_length*i}))" if not timestamp_column_in_seconds
                    else f"({date_column} = '{order_date}' AND {timestamp_column} <="
                         f" ({less_than_time} - 86400*{period_length*i}))"
                )
            else:
                dt_filters.append(
                    f"({date_column} = '{order_date}')")
    else:
        for i in range(num_weeks):
            if i in skip_week:
                continue
            order_date = (date_ist - timedelta(days=period_length*i)).strftime(date_format)
            dt_filters.append(f"({date_column} = '{order_date}')")

    return " OR\n".join(dt_filters)


def get_all_order_metrics_query(metrics_sql,dynamic_where_clause: str,
                                yesterday_metric: bool = False,
                                is_hourly: bool = False) -> str:
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'order_date',
                                                                    timestamp_column='insert_timestamp',
                                                                    num_weeks=5,
                                                                    yesterday_metric=yesterday_metric)

    # Add promo metrics date condition
    if any(metric in metrics_sql for metric in ["promo_orders", "promo_percentage", "promo_charge", "new_transacting_users_count"]):
        dynamic_where_clause += " AND order_date >= '2025-04-14'"
    
    # Add assortment_type metrics date condition
    # These metrics are only available from 2025-09-02 onwards
    if any(metric_type in metrics_sql for metric_type in ["express_", "longtail_", "super_longtail_", "unicorn_"]):
        dynamic_where_clause += " AND order_date >= '2025-09-02'"

    hour_col = ""
    grp_by_hr = ""
    if is_hourly:
        hour_col += "hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,"
        grp_by_hr += ", hour(insert_timestamp, 'Asia/Kolkata')"
    all_orders_metric_query = f"""
        SELECT {hour_col}
        order_date,
            {metrics_sql}
        FROM fact_order_details_v6
        WHERE ({dt_filters})
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        GROUP BY order_date {grp_by_hr}
        LIMIT 50000
    """
    return all_orders_metric_query


def get_all_order_metrics_query_custom(metrics_sql, 
                                       dynamic_where_clause: str,
                                       start_date: str = None,
                                       is_hourly: bool = False) -> str:
    base_date = datetime.strptime(start_date, '%Y-%m-%d')
    dt_filters = """
        (order_date = '{date_0}') OR
        (order_date = '{date_7}') OR
        (order_date = '{date_14}') OR
        (order_date = '{date_21}') OR
        (order_date = '{date_28}')
    """.format(
        date_0=(base_date).strftime('%Y-%m-%d'),
        date_7=(base_date - timedelta(days=7)).strftime('%Y-%m-%d'),
        date_14=(base_date - timedelta(days=14)).strftime('%Y-%m-%d'),
        date_21=(base_date - timedelta(days=21)).strftime('%Y-%m-%d'),
        date_28=(base_date - timedelta(days=28)).strftime('%Y-%m-%d')
    )
    
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    # Add promo metrics date condition
    if any(metric in metrics_sql for metric in ["promo_orders", "promo_percentage", "promo_charge", "new_transacting_users_count"]):
        dynamic_where_clause += " AND order_date >= '2025-04-14'"
    
    # Add assortment_type and union order metrics date condition
    # These metrics are only available from 2025-09-02 onwards
    if any(metric_type in metrics_sql for metric_type in ["express_", "longtail_", "super_longtail_", "unicorn_", "union_"]):
        dynamic_where_clause += " AND order_date >= '2025-09-02'"

    hour_col = ""
    grp_by_hr = ""
    if is_hourly:
        hour_col += "order_hour,"
        grp_by_hr += ", order_hour"
    all_orders_metric_query = f"""
        SELECT {hour_col}
            order_date,
            {metrics_sql}
        FROM fact_order_details_v6
        WHERE ({dt_filters})
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        and {cutoff_date_condition}
        {dynamic_where_clause}
        GROUP BY order_date {grp_by_hr}
        LIMIT 50000
    """
    return all_orders_metric_query


def get_orders_per_minute_query(date_str: str, dynamic_where_clause: str = "") -> str:
    """
    Get the orders per minute query.
    This query returns the number of orders placed per minute for a specific date.
    
    Args:
        date_str: The date for which to get orders per minute in format 'YYYY-MM-DD'
        dynamic_where_clause: Additional WHERE conditions for filtering
        
    Returns:
        SQL query string
    """
    opm_query = f"""
        SELECT 
            DATETIMECONVERT(
                insert_timestamp,
                '1:MILLISECONDS:EPOCH',
                '1:MINUTES:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm tz(Asia/Kolkata)',
                '1:MINUTES'
            ) AS minute_bucket,
            COUNT(DISTINCT(cart_id)) AS orders_per_minute
        FROM fact_order_details_v6
        WHERE order_date = '{date_str}'
        AND org_channel_id in ('1', '2')
        AND city_name not in ('Not in service area')
        {dynamic_where_clause}
        GROUP BY minute_bucket
        ORDER BY minute_bucket
        LIMIT 100000
    """
    return opm_query


def get_all_order_metrics_city_wise_query(dynamic_where_clause: str, is_store_level: bool, date_str, hour: str = None) -> str:
    if is_store_level:
        select_store_or_city = f"""merchant_id as frontend_merchant_id, 
                                    merchant_name as frontend_merchant_name,
                                """
        group_by = "GROUP BY 1,2,3"
    else:
        select_store_or_city = "CASE WHEN city_name not in ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali','Chandigarh')  THEN city_name ELSE 'Chandigarh' END  as city,"
        group_by = "GROUP BY 1,2"

    if hour:
        hour_column = ",hour(insert_timestamp, 'Asia/Kolkata') AS order_hour"
        group_by += ", hour(insert_timestamp, 'Asia/Kolkata')"
    else:
        hour_column = ""
        
    dt_filters = generate_dt_filters(
        date_str=date_str, date_column='order_date', timestamp_column='insert_timestamp', num_weeks=2
    )

    return f"""
        SELECT
            --city_name as city,
            {select_store_or_city}
            order_date,
            SUM(total_cost) as gmv,
            COUNT(DISTINCT(cart_id)) as order_count
            --,sum(delivery_cost) as dc_revenue
            ,SUM(total_cost)/COUNT(DISTINCT(cart_id)) as aov
            ,100 * (DISTINCTCOUNT(CASE WHEN surge_amount > 0 THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) as surge_shown_carts_percentage,
            100 * SUM(CASE WHEN serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / count(id) as rain_order_percentage
            {hour_column}
        FROM fact_order_details_v6
        WHERE (
        ({dt_filters})
        )
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        and city_name not in ('null', 'Not in service area')
        {group_by}
        LIMIT 50000
        """


def get_transacting_user_count_city_wise_query(dynamic_where_clause: str, is_store_query: bool, date_str: str, hour: str = None) -> str:
    columns: str = """
                CASE WHEN city_name not in ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali','Chandigarh')  THEN city_name ELSE 'Chandigarh' END  as city,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                COUNT(DISTINCT(customer_id)) as transacting_users_count
    """
    group_by_clause: str = "GROUP BY 2,1"

    if is_store_query:
        columns: str = """city_name as city,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                COUNT(DISTINCT(customer_id)) as transacting_users_count,
                merchant_id as frontend_merchant_id
        """
        group_by_clause: str = "GROUP BY 2,1,4"
    
    hr_column = """, hour(insert_timestamp, 'Asia/Kolkata') AS order_hour""" if hour else ""
    group_by_clause += """, hour(insert_timestamp, 'Asia/Kolkata')""" if hour else ""

    dt_filters = generate_dt_filters(
        date_str=date_str, date_column='order_date', timestamp_column='insert_timestamp', num_weeks=2
    )

    return f"""
            SELECT 
            {columns}
            {hr_column}
            FROM fact_order_details_v6
            WHERE
            (
            ({dt_filters})
            )
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
            {dynamic_where_clause}
            {group_by_clause}
            LIMIT 50000
            """


def get_all_order_metrics_days_comparative_city_wise_query(dynamic_where_clause: str, 
                                                           dynamic_having_clause: str, metrics_sql, 
                                                           yesterday_metric: bool = False, 
                                                           city_wise: bool = False, 
                                                           store_wise: bool = False, 
                                                           is_critical_metrics: bool = False,
                                                           custom_view: bool = False,
                                                           start_date: str = None, ) -> str:

    table_name = "fact_order_details_v6"

    select_columns_arr = ["order_date"]
    group_by_columns_arr = ["order_date"]

    if city_wise:
        if (yesterday_metric and not is_critical_metrics) or (custom_view and not is_critical_metrics):
            select_columns_arr.extend(["CASE WHEN city_name not in ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali','Chandigarh')  THEN city_name ELSE 'Chandigarh' END  as city"])
        elif is_critical_metrics:
            select_columns_arr.extend(["""city_name as city"""])
        group_by_columns_arr.extend(["city"])
    if store_wise:
        group_by_columns_arr.extend(["merchant_name"])
        select_columns_arr.extend(["merchant_name as frontend_merchant_name"])

    if not custom_view:
        dt_filters = generate_dt_filters(date_str=get_date_str(yesterday_metric), 
                                        date_column='order_date', 
                                        timestamp_column='insert_timestamp', 
                                        num_weeks=2)
    else: 
        dt_filters = generate_dt_filters_custom_date(date_str=start_date, 
                                        column_name='order_date', 
                                        num_intervals=1)
    select_columns = ',\n'.join(col for col in select_columns_arr)
    group_by_clause = "group by " + ', '.join(col for col in group_by_columns_arr)

    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    query = f"""
    SELECT {select_columns},
        {metrics_sql}
        FROM {table_name}
        WHERE ({dt_filters})
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        and {cutoff_date_condition}
        {dynamic_where_clause}
        {group_by_clause}
        {dynamic_having_clause}
        LIMIT 50000
    """
    return query


def get_order_metrics_hourly_query(metrics_sql, dynamic_where_clause, yesterday_metric: bool = False)-> str :
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'order_date',
                                                                    timestamp_column='insert_timestamp',
                                                                    num_weeks=2,
                                                                    yesterday_metric=yesterday_metric)

    ORDER_METRICS_HOURLY_QUERY = f"""
        SELECT 
            order_hour,
            order_date,
            {metrics_sql}
        FROM 
            fact_order_details_v6
        WHERE 
            ({dt_filters})
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
            {dynamic_where_clause}
        GROUP BY 
            order_hour, order_date
        LIMIT 50000
        """
    return ORDER_METRICS_HOURLY_QUERY


def get_order_metrics_hourly_query_custom(metrics_sql, dynamic_where_clause, start_date)-> str :
    base_date = datetime.strptime(start_date, '%Y-%m-%d')
    dt_filters = """
        (order_date = '{date_0}') OR
        (order_date = '{date_7}') 
    """.format(
        date_0=(base_date).strftime('%Y-%m-%d'),
        date_7=(base_date - timedelta(days=7)).strftime('%Y-%m-%d'),
    )
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    ORDER_METRICS_HOURLY_QUERY = f"""
        SELECT order_hour,
               order_date,
               {metrics_sql}
        FROM fact_order_details_v6
        WHERE ({dt_filters})
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        AND {cutoff_date_condition}
        GROUP BY order_hour, order_date
        LIMIT 50000
        """
    return ORDER_METRICS_HOURLY_QUERY


ORDER_METRICS_HOURLY_CITIES_FILTER_QUERY: str = """
            SELECT hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                {metrics_sql}
            FROM fact_order_details_v6
            WHERE
            (insert_timestamp >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            OR
            (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp <= FromEpochSeconds(now()/1000 - 86400*7))) AND city_name IN (%(cities)s)
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
            GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
            LIMIT 50000
            """


def get_today_city_dau_query(dynamic_where_clause: str, is_store_query: bool, yesterday_metric: bool = False) -> str:
    table: str = "city_daily_active_users_v2"
    columns: str = """
            city,
            dt,
            LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
            max(window_end),
            app
        """
    group_by_query: str = """
            GROUP BY app, city, dt
        """
    if is_store_query:
        table = "merchant_daily_active_users_v2"
        columns: str = """
                    city,
                    dt,
                    LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
                    max(window_end),
                    app,
                    merchant_id
                """
        group_by_query: str = """
                        GROUP BY app, city, merchant_id, dt
                """
    
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                    timestamp_column='window_end',
                                                                    num_weeks=5,
                                                                    yesterday_metric=yesterday_metric)

    CITY_DAU_QUERY = f"""
                        SELECT {columns}
                        FROM {table}
                        WHERE ({dt_filters})
                        {dynamic_where_clause}
                        {group_by_query}
                        LIMIT 100000 
                    """
    return CITY_DAU_QUERY


def get_high_priority_users_demand_block_perc_query(dynamic_where_clause: str, table_name: str) -> str:
    columns: str = """
            date_ist,
            LASTWITHTIME(high_p_demand_block_perc, window_end_epoch, 'DOUBLE') AS high_p_demand_block_perc,
            LASTWITHTIME(high_p_ooh_block_perc, window_end_epoch, 'DOUBLE') AS high_p_ooh_block_perc,
            LASTWITHTIME(high_p_manual_block_perc, window_end_epoch, 'DOUBLE') AS high_p_manual_block_perc,
            LASTWITHTIME(high_p_user_perc, window_end_epoch, 'DOUBLE') AS high_p_user_perc
        """
    group_by_query: str = """
            GROUP BY date_ist
        """
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'date_ist',
                                                                    timestamp_column='window_end_epoch',
                                                                    num_weeks=5,)

    query = f"""
            SELECT {columns}
            FROM {table_name}
            WHERE ({dt_filters})
            {dynamic_where_clause}
            {group_by_query}
            LIMIT 100000 
    """
    return query


def get_high_priority_users_demand_block_perc_query_custom(dynamic_where_clause: str, table_name: str, start_date: str) -> str:
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    start_date_timestamp = int(start_date_obj.timestamp() * 1000)

    columns: str = """
            date_ist,
            LASTWITHTIME(high_p_demand_block_perc, window_end_epoch, 'DOUBLE') AS high_p_demand_block_perc,
            LASTWITHTIME(high_p_ooh_block_perc, window_end_epoch, 'DOUBLE') AS high_p_ooh_block_perc,
            LASTWITHTIME(high_p_manual_block_perc, window_end_epoch, 'DOUBLE') AS high_p_manual_block_perc,
            LASTWITHTIME(high_p_user_perc, window_end_epoch, 'DOUBLE') AS high_p_user_perc
        """
    group_by_query: str = """
            GROUP BY date_ist
        """

    dt_filters = f"""
        (window_start_epoch = dateTrunc('DAY', {start_date_timestamp}, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end_epoch <= dateTrunc('DAY', {start_date_timestamp} + 86400 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start_epoch = dateTrunc('DAY', {start_date_timestamp} - 86400 * 7 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end_epoch <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 6 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start_epoch = dateTrunc('DAY', {start_date_timestamp} - 86400 * 14 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end_epoch <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 13 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start_epoch = dateTrunc('DAY', {start_date_timestamp} - 86400 * 21 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end_epoch <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 20 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start_epoch = dateTrunc('DAY', {start_date_timestamp} - 86400 * 28 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end_epoch <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 27 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"date_ist >= '{cutoff_date}'"

    query = f"""
            SELECT {columns}
            FROM {table_name}
            WHERE ({dt_filters})
            {dynamic_where_clause}
            {group_by_query}
            HAVING {cutoff_date_condition}
            LIMIT 100000 
    """
    return query


def get_today_all_cities_dau_query(dynamic_where_clause: str,
                                   is_store_query: bool = False,
                                   date_str: str = None, hour: str = None) -> str:
    table: str = "city_daily_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
        max(window_end),
        app
    """
    group_by_query: str = """
                GROUP BY app, city,
         DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
        """
    if is_store_query:
        table = "merchant_daily_active_users_v2"
        columns: str = """
               DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
               city,
               LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
               max(window_end),
               app,
               merchant_id,
               lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_name', 'frontend_merchant_id', merchant_id) as frontend_merchant_name
                """
        group_by_query: str = """
                        GROUP BY app, city,merchant_id,
                 DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS'),
                 frontend_merchant_name
                """
    
    hr_column = """, hour(window_start, 'Asia/Kolkata') AS order_hour""" if hour else ""
    group_by_query += """, hour(window_start, 'Asia/Kolkata')""" if hour else ""

    if date_str is None:
        date_str = get_date_str(is_yesterday=False)
        
    dt_filters = """
        (window_start = dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        OR 
        (window_start >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end <= FromEpochSeconds(now()/1000 - 86400*7)))
    """ if not is_yesterday_date(date_str) else """
        (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR
        (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*8), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """

    return f"""
    SELECT 
    {columns}
    {hr_column}
    FROM {table}
    WHERE (
        ({dt_filters})
        {dynamic_where_clause}
    )
    {group_by_query}
    LIMIT 100000
    """


def get_today_hau_metrics_city_wise(dynamic_where_clause: str, is_store_query: bool, date_str: str = None) -> str:
    table: str = "city_hourly_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
        max(window_end),
        app,
        hr as hour
    """
    group_by_query: str = """
            GROUP BY order_date,
            hr,
            city,
            app
    """
    if is_store_query:
        table = "merchant_hourly_active_users_v2"
        columns = """
                DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
                city,
                LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
                max(window_end),
                app,
                merchant_id,
                hr as hour,
                lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_name', 'frontend_merchant_id', merchant_id) as frontend_merchant_name
            """
        group_by_query  = """
                    GROUP BY order_date,
                    hr,
                    city,
                    app,
                    merchant_id,
                    frontend_merchant_name
            """
        
    if date_str is None:
        date_str = get_date_str(is_yesterday=False)

    dt_filters = """
        (window_start >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        OR ( window_start >= dateTrunc('DAY', now() - 86400000*%(past_days_diff)i, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= now() - 300000 - 86400000*%(past_days_diff)i ))
        AND (window_end - window_start) <= 3600000
    """ if not is_yesterday_date(date_str) else """
        ((window_start >= dateTrunc('DAY', now() - 86400000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start >= dateTrunc('DAY', now() - 86400000*8, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now() - 86400000 * 7, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')))
        AND (window_end - window_start) <= 3600000
    """
        
    
    return f"""
    SELECT {columns}
    FROM {table}
    WHERE 
        {dt_filters}
        {dynamic_where_clause}
    {group_by_query}
    LIMIT 100000
    """


def get_yesterday_dau_metrics(dynamic_where_clause: str, is_store_query: bool = False) -> str:
    table: str = "city_daily_active_users_v2"
    columns: str = """city,
                      dt,
                      LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
                      max(window_end),
                      app """
    group_by_query: str = """GROUP BY app, city, dt """
    if is_store_query:
        table = "merchant_daily_active_users_v2"
        columns: str = """ city,
                           dt,
                           LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
                           max(window_end),
                           app,
                           merchant_id """
        group_by_query: str = """ GROUP BY app, city, merchant_id, dt """
    
    dt_filters = """
    (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*8), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*15), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*22), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*29), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """
    YESTERDAY_DAU_METRICS =  f"""
                        SELECT {columns}
                        FROM {table}
                        WHERE ({dt_filters})
                        {dynamic_where_clause}
                        {group_by_query}
                        LIMIT 100000 
                    """
    return YESTERDAY_DAU_METRICS


def get_custom_dau_metrics_query(dynamic_where_clause: str, 
                                 is_store_query: bool = False,
                                 start_date: str = None) -> str:
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    start_date_timestamp = int(start_date_obj.timestamp() * 1000)

    table: str = "city_daily_active_users_v2"
    columns: str = """
            city,
            dt,
            LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
            max(window_end),
            app
        """
    group_by_query: str = """
            GROUP BY app, city, dt
        """
    if is_store_query:
        table = "merchant_daily_active_users_v2"
        columns: str = """
                    city,
                    dt,
                    LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
                    max(window_end),
                    app,
                    merchant_id
                """
        group_by_query: str = """
                    GROUP BY app, city, merchant_id, dt
                """

    dt_filters = f"""
        (window_start = dateTrunc('DAY', {start_date_timestamp}, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} + 86400 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', {start_date_timestamp} - 86400 * 7 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 6 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', {start_date_timestamp} - 86400 * 14 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 13 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', {start_date_timestamp} - 86400 * 21 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 20 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start = dateTrunc('DAY', {start_date_timestamp} - 86400 * 28 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 27 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"

    CUSTOM_DAU_METRICS = f"""
                        SELECT {columns}
                        FROM {table}
                        WHERE ({dt_filters})
                        {dynamic_where_clause}
                        {group_by_query}
                        HAVING {cutoff_date_condition}
                        LIMIT 100000 
                    """
    return CUSTOM_DAU_METRICS


def get_yesterday_hau_metrics(dynamic_where_clause: str, is_store_query: bool) -> str:
    table: str = "city_hourly_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        hour(window_start, 'Asia/Kolkata') AS hour,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
        app
    """
    group_by_query: str = """
            GROUP BY order_date,
            hour,
            city,
            app
    """
    if is_store_query:
        table = "merchant_hourly_active_users_v2"
        columns = """
                DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
                hour(window_start, 'Asia/Kolkata') AS hour,
                city,
                LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
                app,
                merchant_id
            """
        group_by_query  = """
                    GROUP BY order_date,
                    hour,
                    city,
                    app,
                    merchant_id
            """
        
    return f"""
    SELECT {columns}
    FROM {table}
    WHERE ((window_start >= dateTrunc('DAY', now() - 86400000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start >= dateTrunc('DAY', now() - 86400000*8, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now() - 86400000 * 7, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')))
        AND (window_end - window_start) <= 3600000
        {dynamic_where_clause}
        {group_by_query}
    LIMIT 100000
    """


def get_hau_metrics_custom(dynamic_where_clause: str, 
                           is_store_query: bool,
                           start_date: str) -> str:
    table: str = "city_hourly_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        hour(window_start, 'Asia/Kolkata') AS hour,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
        app
    """
    group_by_query: str = """
            GROUP BY order_date,
            hour,
            city,
            app
    """
    if is_store_query:
        table = "merchant_hourly_active_users_v2"
        columns = """
                DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
                hour(window_start, 'Asia/Kolkata') AS hour,
                city,
                LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
                app,
                merchant_id
            """
        group_by_query  = """
                    GROUP BY order_date,
                    hour,
                    city,
                    app,
                    merchant_id
            """
    
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    start_date_timestamp = int(start_date_obj.timestamp() * 1000)

    dt_filters = f'''
        (window_start >= dateTrunc('DAY', {start_date_timestamp}, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} + 86400 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 7 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', {start_date_timestamp} - 86400 * 6 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    '''
    
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    return f"""
    SELECT {columns}
    FROM {table}
    WHERE 
        ({dt_filters})
        AND (window_end - window_start) <= 3600000
        {dynamic_where_clause}
        {group_by_query}
    HAVING {cutoff_date_condition}
    LIMIT 100000
    """


CITY_LEVEL_AVAILABILITY_METRICS_QUERY: str = """
             SELECT
              l2,
              city,
              hour,
              cum_weighted_availability,
              weighted_availability,
              date_
              --,ToDateTime(date_,'YYYY-MM-dd') as availability_date
            FROM city_hourly_weighted_availability
            WHERE  (
                date_ = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' )
                OR date_ = DATETIMECONVERT(FromEpochSeconds((now()/1000) - 86400*%(past_days_diff)i), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' )
                )
                AND hour = %(max_hour)i
                limit 100000
            """


CITYWISE_STRESSED_STORES_COMPARISION_QUERY = """
SELECT DATETIMECONVERT(eventTimestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS eventDate,
       storeID,
       lastwithtime(isStoreDisrupted, eventTimestamp, 'STRING') AS stressed
FROM store_manpower_metrics
WHERE (eventDayEpochIST = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*%(base_days_diff)i), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
    AND eventTimestamp < FromEpochSeconds(now()/1000 - 86400*%(base_days_diff)i))
  OR (eventDayEpochIST = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*%(comparative_days_diff)i), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
    AND eventTimestamp < FromEpochSeconds(now()/1000 - 86400*%(comparative_days_diff)i))
GROUP BY eventDate,
         storeID
HAVING stressed = 'true'
LIMIT 1000000
"""


STORES_CITY_MAPPING_QUERY = """
SELECT DISTINCT merchant_id,
                city_name
FROM fact_order_details_v6
WHERE org_channel_id in ('1', '2')
and city_name not in ('Not in service area')
LIMIT 1000000
"""


def get_transacting_user_count_dau_conversion_query(dynamic_where_clause: str,
                                                    yesterday_metric: bool,
                                                    max_window_end: int) -> str:
    
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'order_date',
                                                                    timestamp_column='insert_timestamp',
                                                                    num_weeks=5,
                                                                    less_than_time_in_utc_ms=max_window_end,
                                                                    yesterday_metric=yesterday_metric)

    QUERY = f""" 
        SELECT 
            order_date,
            COUNT(DISTINCT customer_id) as transacting_users_count
        FROM fact_order_details_v6
        WHERE 
            ({dt_filters})
            AND org_channel_id in ('1', '2')
            AND city_name not in ('Not in service area')
            {dynamic_where_clause}
        GROUP BY order_date
        LIMIT 50000"""
    return QUERY


def get_yesterday_transacting_user_count_dau_conversion_query(dynamic_where_clause: str) -> str:
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'order_date',
                                                                timestamp_column='insert_timestamp',
                                                                num_weeks=5,
                                                                yesterday_metric=True)

    query = f"""
        SELECT 
            order_date,
            COUNT(DISTINCT customer_id) as transacting_users_count
        FROM 
            fact_order_details_v6
        WHERE ({dt_filters})
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        GROUP BY order_date
        LIMIT 50000
    """
    return query


def get_custom_transacting_user_count_dau_conversion_query(dynamic_where_clause: str,
                                                           start_date: str = None) -> str:

    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'order_date',
                                                                timestamp_column='insert_timestamp',
                                                                num_weeks=5,
                                                                start_date=start_date)

    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    query = f"""
        SELECT 
            order_date,
            COUNT(DISTINCT customer_id) as transacting_users_count
        FROM 
            fact_order_details_v6
        WHERE ({dt_filters})
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
            {dynamic_where_clause}
        GROUP BY order_date
        HAVING {cutoff_date_condition}
        LIMIT 50000
    """
    return query


def get_transacting_user_count_hau_conversion_query(metrics_sql: str,
                                                    max_window_end: int,
                                                    dynamic_where_clause: str) -> str:
    return f"""
        SELECT hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
            DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
            {metrics_sql}
        FROM fact_order_details_v6
        WHERE
        ((insert_timestamp >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp <= {max_window_end})
		 OR
		 (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp <= FromEpochSeconds({max_window_end}/1000 - 86400*7)))
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
        LIMIT 50000
        """


ORDER_METRICS_PROJECTED_HOURLY_QUERY: str = """
            SELECT hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                {metrics_sql}
            FROM fact_order_details_v6
            WHERE
            ((insert_timestamp >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('HOUR', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*6), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*13), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*20), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*27), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')))
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
            GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
            LIMIT 50000
            """

HAU_PROJECTED_HOURLY_QUERY : str = """
SELECT hour(window_start, 'Asia/Kolkata') AS hour,
    DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
       city,
       LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
       max(window_end),
       app
FROM city_hourly_active_users_v2
WHERE 
( window_start >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
 AND window_end <= dateTrunc('HOUR', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
  OR
( window_start >= dateTrunc('DAY', now() - 86400000*7, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
  AND window_end <= dateTrunc('DAY', now() - 86400000*6, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
  OR
( window_start >= dateTrunc('DAY', now() - 86400000*14, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
  AND window_end <= dateTrunc('DAY', now() - 86400000*13, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
  OR
( window_start >= dateTrunc('DAY', now() - 86400000*21, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
  AND window_end <= dateTrunc('DAY', now() - 86400000*20, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
  OR
( window_start >= dateTrunc('DAY', now() - 86400000*28, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
  AND window_end <= dateTrunc('DAY', now() - 86400000*27, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
)
 AND (window_end - window_start) <= 3600000
GROUP BY order_date,
         hour,
         city,
         app
LIMIT 100000
"""

def get_order_metrics_hourly_projected_query(
    metrics_sql: str, dynamic_where_clause: str,
): 
    query = f"""
            SELECT 
                hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                {metrics_sql}
            FROM fact_order_details_v6
            WHERE
                ((insert_timestamp >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('HOUR', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
                (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*6), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
                (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*13), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
                (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*20), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
                (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*27), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')))
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
            {dynamic_where_clause}
            GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
            LIMIT 50000
        """
    return query


def get_prjection_hourly_query(dynamic_where_clause: str) -> str:
    query = f'''
        select 
            hr as hour, 
            dt as order_date, 
            SUM(proj_carts) as proj_carts 
        from 
            projection_hourly_city_metrics
        where 
            1 = 1 
            {dynamic_where_clause}
        group by 1,2 
		limit 100000
    '''
    return query


def get_cart_blocks_query(dynamic_where_clause: str,
                          max_window_end: int = None,
                          yesterday_metric: bool = False,
                          get_all_cities: bool = False,
                          is_store_query: bool = False,
                          hour: str = None) -> str:
    processed_max_window_end = None if max_window_end == "now()" else max_window_end
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                    timestamp_column='time',
                                                                    num_weeks=2,
                                                                    yesterday_metric=yesterday_metric,
                                                                    less_than_time_in_utc_ms=processed_max_window_end,
                                                                    time_column_in_seconds=True)

    dt_filters = dt_filters.replace('time', '"time"')
    group_by_str: str = "group by dt, block_type"
    cities_filter: str = ""

    if get_all_cities:
        dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                        timestamp_column='time',
                                                                        num_weeks=2,
                                                                        yesterday_metric=yesterday_metric,
                                                                        less_than_time_in_utc_ms=processed_max_window_end,
                                                                        time_column_in_seconds=True)
        dt_filters = dt_filters.replace('time', '"time"')

        group_by_str = "group by dt, block_type, city"
        cities_filter = """, 
                        CASE
                            WHEN
                            lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_city_name', 'frontend_merchant_id', merchant_id)
                                IN ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh') THEN 'Chandigarh'
                            ELSE
                            lookup('merchant_outlet_facility_mapping_v2',
                                'frontend_merchant_city_name',
                                'frontend_merchant_id',
                                merchant_id)
                            END AS city"""
        if hour:
            group_by_str += """, order_hour"""
            cities_filter += """,hour("time"*1000, 'Asia/Kolkata') AS order_hour"""

    if is_store_query:
        dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                        timestamp_column='time',
                                                                        num_weeks=2,
                                                                        yesterday_metric=yesterday_metric,
                                                                        less_than_time_in_utc_ms=processed_max_window_end,
                                                                        time_column_in_seconds=True)
        dt_filters = dt_filters.replace('time', '"time"')
        
        cities_filter = """,   
                CASE 
                    WHEN lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_city_name', 'frontend_merchant_id', merchant_id)
                    IN('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh')
                    THEN 'Chandigarh'
                    ELSE lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_city_name', 'frontend_merchant_id', merchant_id)
                END AS city,
                merchant_id as frontend_merchant_id
        """
        group_by_str = "group by dt, block_type, city, frontend_merchant_id"
    
    cart_blocked_query = f"""
        SELECT 
            dt,
            CASE        
                WHEN reason IN ('OUTSIDE_OPERATING_HOURS')
                AND is_express_serviceable = false
                AND express_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'express_serviceability_ooh_based_block'

                WHEN reason IN (
                    'DISRUPTION_DEFAULT', 'DISRUPTION_POLYGON_THROTTLE', 'DISRUPTION_FESTIVAL', 
                    'SERVICEABILITY_ROLLOUT_BLOCK', 'MAX_POSSIBLE_DISTANCE_BREACHED'
                )
                AND is_express_serviceable = false
                AND express_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'express_serviceability_manual_based_block'
                
                WHEN reason IN (
                    'MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED',
                    'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 
                    'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED'
                )
                AND is_express_serviceable = false
                AND express_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'express_demand_based_block'

                WHEN reason IN ('OUTSIDE_OPERATING_HOURS')
                AND is_express_serviceable = true
                AND is_longtail_serviceable = false
                AND longtail_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'longtail_serviceability_ooh_based_block'

                WHEN reason IN (
                    'DISRUPTION_DEFAULT', 'DISRUPTION_POLYGON_THROTTLE', 'DISRUPTION_FESTIVAL', 
                    'SERVICEABILITY_ROLLOUT_BLOCK', 'MAX_POSSIBLE_DISTANCE_BREACHED'
                )
                AND is_express_serviceable = true
                AND is_longtail_serviceable = false
                AND longtail_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'longtail_serviceability_manual_based_block'

                WHEN reason IN (
                    'MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED',
                    'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 
                    'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED'
                )
                AND is_express_serviceable = true
                AND is_longtail_serviceable = false
                AND longtail_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'longtail_demand_based_block'

            END AS block_type,
            count(distinct user_id) AS blocked_users
            {cities_filter}
        FROM serviceability_checkouts_block_info_v1
        WHERE ({dt_filters})
        AND express_merchant_id NOT IN ('28759', '31149')
        AND reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH')
        AND block_type = 'COMPLETE'
        AND NOT serviceable
        AND downstream_service = 'Assembly'
        {dynamic_where_clause}
        {group_by_str}
        ORDER BY dt
        LIMIT 50000
    """
    return cart_blocked_query


def get_cart_blocks_query_custom(dynamic_where_clause: str,
                          get_all_cities: bool = False,
                          is_store_query: bool = False,
                          hour: str = None,
                          start_date: str = None) -> str:
    dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                    timestamp_column='time',
                                                                    num_weeks=2,
                                                                    start_date=start_date,
                                                                    time_column_in_seconds=True)
    dt_filters.replace('time', '"time"')

    group_by_str: str = "group by dt, block_type"
    cities_filter: str = ""

    if get_all_cities:
        dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                    timestamp_column='time',
                                                                    num_weeks=2,
                                                                    start_date=start_date,
                                                                    time_column_in_seconds=True)
        dt_filters.replace('time', '"time"')

        group_by_str = "group by dt, block_type, city"
        cities_filter = """,
            CASE
            WHEN
            lookup('merchant_outlet_facility_mapping_v2',
                'frontend_merchant_city_name',
                'frontend_merchant_id',
                merchant_id)
            IN('Panchkula',
            'Kharar',
            'Zirakpur',
            'Mohali',
            'Chandigarh')
            THEN
            'Chandigarh'
            ELSE
            lookup('merchant_outlet_facility_mapping_v2',
                'frontend_merchant_city_name',
                'frontend_merchant_id',
                merchant_id)
            END
            AS
            city"""
        if hour:
            group_by_str += """, order_hour"""
            cities_filter += """,hour("time"*1000, 'Asia/Kolkata') AS order_hour"""

    if is_store_query:
        dt_filters: str = generate_dynamic_dt_filters_with_backend_calc(date_column = 'dt',
                                                                    timestamp_column='time',
                                                                    num_weeks=2,
                                                                    start_date=start_date,
                                                                    time_column_in_seconds=True)
        dt_filters.replace('time', '"time"')

        group_by_str = "group by dt, block_type, city, frontend_merchant_id"
        cities_filter = """,
            CASE
            WHEN
            lookup('merchant_outlet_facility_mapping_v2',
                'frontend_merchant_city_name',
                'frontend_merchant_id',
                merchant_id)
            IN('Panchkula',
            'Kharar',
            'Zirakpur',
            'Mohali',
            'Chandigarh')
            THEN
            'Chandigarh'
            ELSE
            lookup('merchant_outlet_facility_mapping_v2',
                'frontend_merchant_city_name',
                'frontend_merchant_id',
                merchant_id)
            END
            AS
            city,
            merchant_id as frontend_merchant_id
            """
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"

    cart_blocked_query = f"""
        SELECT dt,
            CASE        
                WHEN reason IN ('OUTSIDE_OPERATING_HOURS')
                AND is_express_serviceable = false
                AND express_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'express_serviceability_ooh_based_block'

                WHEN reason IN (
                    'DISRUPTION_DEFAULT', 'DISRUPTION_POLYGON_THROTTLE', 'DISRUPTION_FESTIVAL', 
                    'SERVICEABILITY_ROLLOUT_BLOCK', 'MAX_POSSIBLE_DISTANCE_BREACHED'
                )
                AND is_express_serviceable = false
                AND express_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'express_serviceability_manual_based_block'
                
                WHEN reason IN (
                    'MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED',
                    'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 
                    'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED'
                )
                AND is_express_serviceable = false
                AND express_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'express_demand_based_block'

                WHEN reason IN ('OUTSIDE_OPERATING_HOURS')
                AND is_express_serviceable = true
                AND is_longtail_serviceable = false
                AND longtail_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'longtail_serviceability_ooh_based_block'

                WHEN reason IN (
                    'DISRUPTION_DEFAULT', 'DISRUPTION_POLYGON_THROTTLE', 'DISRUPTION_FESTIVAL', 
                    'SERVICEABILITY_ROLLOUT_BLOCK', 'MAX_POSSIBLE_DISTANCE_BREACHED'
                )
                AND is_express_serviceable = true
                AND is_longtail_serviceable = false
                AND longtail_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'longtail_serviceability_manual_based_block'

                WHEN reason IN (
                    'MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED',
                    'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 
                    'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED'
                )
                AND is_express_serviceable = true
                AND is_longtail_serviceable = false
                AND longtail_merchant_id = REGEXP_REPLACE(merchant_id, '^0+', '')
                THEN 'longtail_demand_based_block'

            END AS block_type,
            count(distinct user_id) AS blocked_users
            {cities_filter}
        FROM serviceability_checkouts_block_info_v1
        WHERE ({dt_filters})
        AND express_merchant_id NOT IN ('28759', '31149')
        AND reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH')
        AND block_type = 'COMPLETE'
        AND NOT serviceable
        AND downstream_service = 'Assembly'
        {dynamic_where_clause}
        {group_by_str}
        HAVING {cutoff_date_condition}
        ORDER BY dt
        LIMIT 50000
    """
    return cart_blocked_query


def get_store_city_mapping_query(dynamic_where_clause: str) -> str:
    columns = """
    frontend_merchant_id,
    frontend_merchant_name, 
    frontend_merchant_city_name,
    backend_merchant_id,
    pos_outlet_id,
    zone,
    type,
    is_longtail_outlet
    """
    return f"""select {columns}
                from merchant_outlet_facility_mapping_v2
                where  (frontend_merchant_city_name not in ('Not in service area'))
                {dynamic_where_clause}
                limit 1000000"""


# def get_active_stores_count(dynamic_where_clause: str, is_yesterday:bool = False, get_trend: bool = False) -> str:
#     if not is_yesterday and not get_trend:
#         query = f"""select count(distinct(frontend_merchant_id))
#                     from merchant_outlet_facility_mapping_v2
#                     where is_active = true
#                     {dynamic_where_clause}
#                     limit 1"""
#         return query
#     elif get_trend:
#         time_intervals = [7, 14, 21, 28] if not is_yesterday else [8, 15, 22, 29]
#         trend_queries = [
#             f"SUM(CASE WHEN live_date <= DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*{t}), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') THEN 1 ELSE 0 END) AS lt_t{t}"
#             for t in time_intervals]
#         t0_condition = "COUNT(DISTINCT frontend_merchant_id) AS t0" if not is_yesterday else f"SUM(CASE WHEN live_date <= DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*1), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') THEN 1 ELSE 0 END) AS lt_t1"
#         query = f"""SELECT {t0_condition}, {', '.join(trend_queries)}
#                             FROM merchant_outlet_facility_mapping_v2
#                             WHERE is_active = TRUE
#                             {dynamic_where_clause}
#                             LIMIT 1"""
#         return query

def get_active_stores_count_v2(dynamic_where_clause: str, is_yesterday:bool = False, get_trend: bool = False) -> str:
    if not is_yesterday and not get_trend:
        query = f"""select COUNT(DISTINCT pos_outlet_id) AS total_stores_count
                    from merchant_outlet_facility_mapping_v2
                    where dim_is_outlet_active = 1
                    AND tenant_name = 'BLINKIT'
                    {dynamic_where_clause}
                    limit 1"""
        return query
    elif get_trend:
        base_date = get_todays_date_in_ist()
        if is_yesterday:
            base_date = get_yesterdays_date_in_ist()

        time_intervals = [7, 14, 21, 28] if not is_yesterday else [8, 15, 22, 29]
        
        trend_queries = []
        t0_condition = [
            "COUNT(DISTINCT pos_outlet_id) AS totat_lt_t0",
            "SUM( CASE WHEN is_express_outlet = TRUE THEN 1 ELSE 0 END) AS express_lt_t0",
            "SUM( CASE WHEN is_longtail_outlet = TRUE THEN 1 ELSE 0 END) AS longtail_lt_t0"
        ]
        
        for t in time_intervals:
            cutoff_date = (base_date - timedelta(days=t)).strftime('%Y-%m-%d')
            trend_queries.append(
                f"SUM(CASE WHEN live_date <= '{cutoff_date}' THEN 1 ELSE 0 END) AS total_lt_t{t}"
            )
            trend_queries.append(
                f"SUM(CASE WHEN is_express_outlet = TRUE AND live_date <= '{cutoff_date}' THEN 1 ELSE 0 END) AS express_lt_t{t}"
            )
            trend_queries.append(
                f"SUM(CASE WHEN is_longtail_outlet = TRUE AND live_date <= '{cutoff_date}' THEN 1 ELSE 0 END) AS longtail_lt_t{t}"
            )

        query = f"""SELECT {', '.join(t0_condition)}, {', '.join(trend_queries)}
                                FROM merchant_outlet_facility_mapping_v2
                                where dim_is_outlet_active = 1
                                AND tenant_name = 'BLINKIT'
                                {dynamic_where_clause}
                                ORDER BY 1 DESC
                                LIMIT 1"""
        return query

def get_active_stores_count_custom_v2(dynamic_where_clause: str, 
                                   start_date: str = None, 
                                   get_trend: bool = False) -> str:
    base_date = datetime.strptime(start_date, '%Y-%m-%d')

    if not get_trend:
        query = f"""select COUNT(DISTINCT pos_outlet_id) AS total_stores_count
                    from merchant_outlet_facility_mapping_v2
                    where dim_is_outlet_active = 1
                    AND tenant_name = 'BLINKIT'
                    {dynamic_where_clause}
                    limit 1"""
        return query

    time_intervals = [7, 14, 21, 28]
    trend_queries = []
    t0_condition = [
            f"SUM(CASE WHEN live_date <= '{(base_date).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS totat_lt_t0",
            f"SUM(CASE WHEN is_express_outlet = TRUE AND live_date <= '{(base_date).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS express_lt_t0",
            f"SUM(CASE WHEN is_longtail_outlet = TRUE AND live_date <= '{(base_date).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS longtail_lt_t0"
    ]
    for t in time_intervals:
        trend_queries.append(f"SUM(CASE WHEN live_date <= '{(base_date - timedelta(days=t)).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS total_lt_t{t}")
        trend_queries.append(f"SUM(CASE WHEN is_express_outlet = TRUE AND live_date <= '{(base_date - timedelta(days=t)).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS express_lt_t{t}")
        trend_queries.append(f"SUM(CASE WHEN is_longtail_outlet = TRUE AND live_date <= '{(base_date - timedelta(days=t)).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS longtail_lt_t{t}")

    query = f"""SELECT {', '.join(t0_condition)}, {', '.join(trend_queries)}
                            FROM merchant_outlet_facility_mapping_v2
                            WHERE dim_is_outlet_active = 1
                            AND tenant_name = 'BLINKIT'
                            {dynamic_where_clause}
                            LIMIT 1"""
    return query

# def get_active_stores_count_custom(dynamic_where_clause: str, 
#                                    start_date: str = None, 
#                                    get_trend: bool = False) -> str:
#     base_date = datetime.strptime(start_date, '%Y-%m-%d')

#     if not get_trend:
#         return f"""select count(distinct(frontend_merchant_id))
#                     from merchant_outlet_facility_mapping_v2
#                     where is_active = true
#                     {dynamic_where_clause}
#                     limit 1"""

#     time_intervals = [7, 14, 21, 28]
#     trend_queries = [
#         f"SUM(CASE WHEN live_date <= '{(base_date - timedelta(days=t)).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS lt_t{t}"
#         for t in time_intervals
#     ]

#     t0_condition = f"SUM(CASE WHEN live_date <= '{(base_date).strftime('%Y-%m-%d')}' THEN 1 ELSE 0 END) AS lt_t0"

#     return f"""SELECT {t0_condition}, {', '.join(trend_queries)}
#                             FROM merchant_outlet_facility_mapping_v2
#                             WHERE is_active = TRUE
#                             {dynamic_where_clause}
#                             LIMIT 1"""


def get_new_users_count(dynamic_where_clause: str, is_yesterday: bool = False) -> str:
    dt_filter = generate_dt_filters(date_str=get_date_str(is_yesterday=is_yesterday),
                                    timestamp_column="event_time_epoch",
                                    is_yesterday=is_yesterday,
                                    timestamp_column_in_seconds=True)
    return f"""SELECT dt, COUNT (DISTINCT CASE WHEN is_first_order_completed = false THEN user_id ELSE -1 END)-1 as
     new_transacting_users_count from is_first_order_completed_events 
                        where ({dt_filter})
                        {dynamic_where_clause}
                        group by dt
                        LIMIT 100"""


def get_new_users_count_custom(dynamic_where_clause: str, start_date: str = None) -> str:
    dt_filter = generate_dt_filters_custom_date(
        date_str=start_date,
        column_name="dt"
    )
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"

    return f"""
        SELECT dt, 
            COUNT (DISTINCT CASE WHEN is_first_order_completed = false THEN user_id ELSE -1 END)-1 as new_transacting_users_count
        from is_first_order_completed_events 
        where ({dt_filter})
              {dynamic_where_clause}
              and {cutoff_date_condition}
        group by dt
        LIMIT 100
    """


def get_ath_metrics(dynamic_where_clause: str)->str:
    return f"""SELECT SUM(metric_value),
                CASE
                    WHEN metric_type='Cart Volume' THEN 'ath_order_count'
                    WHEN metric_type='GMV' THEN 'ath_gmv'
                END AS metric_key,
                CASE 
                    WHEN city NOT IN ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh') THEN city 
                    ELSE 'Chandigarh' 
                END AS city,
                dt
                from ath_metrics
                where {dynamic_where_clause}
                group by metric_key,city,dt limit 1000"""


def get_rider_login_hours_current_max_epoch_query() -> str:
    base_date = get_todays_date_in_ist()
    query = f"""
        SELECT 
            max(DATETIMECONVERT(updated_at, '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS:EPOCH', '1:MILLISECONDS')) AS max_epoch
        FROM 
            rider_login_time_metrics_v2
        WHERE 
            "date" = '{base_date.strftime('%Y-%m-%d')}'
        LIMIT 1
    """
    return query


def get_rider_login_metrics_query(dynamic_where_clause: str = '', 
        yesterday_metric: bool = False, 
        hourly_metrics: bool = False,
        get_all_cities: bool = False,
        max_current_epoch:str = 'now()',
        get_all_stores_for_city: bool = False,
        hour: str = None)-> str:
    
    dt_filters: str = f"""
        updated_at >= DATETIMECONVERT(dateTrunc('DAY', now() , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
        OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
        AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*7 ), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        """ if not yesterday_metric else """
        (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at < DATETIMECONVERT(dateTrunc('DAY', now() , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*8 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
    """
    
    hr_column: str = """, cast(hour_ as int) AS hour""" if hourly_metrics or hour else ""
     
    city_column = """, CASE
            WHEN city IN ('Panchkula',
                            'Kharar',
                            'Zirakpur',
                            'Mohali',
                            'Chandigarh') THEN 'Chandigarh'
            ELSE city
        END AS city_name
    """ if get_all_cities and not get_all_stores_for_city else ""

    store_column = """, frontend_merchant_name,
			            frontend_merchant_id
                    """ if get_all_stores_for_city else ""

    group_by_str: str = "group by order_date"

    group_by_str += ", city_name" if get_all_cities else ""
    group_by_str += ", hour" if hourly_metrics or hour else ""
    group_by_str += ", frontend_merchant_name,frontend_merchant_id" if get_all_stores_for_city else ""

    if not get_all_cities and not hourly_metrics and not get_all_stores_for_city:
        dt_filters += f"""
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*14 ), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*21 ), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*28 ), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')) 
        """ if not yesterday_metric else """
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*15 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*22 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*29 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28 ), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        """
    
    rider_login_metric = f"""
        SELECT "date" AS order_date,
            sum(total_active_mins)/60 AS login_hrs
            {city_column}
            {store_column}
            {hr_column}
        FROM rider_login_time_metrics_v2
        WHERE ({dt_filters})
        {dynamic_where_clause}
        {group_by_str}
        LIMIT 100000
    """
    return rider_login_metric


def get_rider_login_metrics_query_custom(dynamic_where_clause: str = '', 
        hourly_metrics: bool = False,
        get_all_cities: bool = False,
        get_all_stores_for_city: bool = False,
        hour: str = None,
        start_date: str = None)-> str:
    
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    start_date_timestamp = int(start_date_obj.timestamp() * 1000)

    dt_filters: str = f"""
        (updated_at >= DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp}, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at < DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} + 86400 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 7 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at < DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 6 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
    """

    hr_column: str = """, cast(hour_ as int) AS hour""" if hourly_metrics or hour else ""
     
    city_column = """, CASE
            WHEN city IN ('Panchkula',
                            'Kharar',
                            'Zirakpur',
                            'Mohali',
                            'Chandigarh') THEN 'Chandigarh'
            ELSE city
        END AS city_name
    """ if get_all_cities and not get_all_stores_for_city else ""

    store_column = """, frontend_merchant_name,
			            frontend_merchant_id
                    """ if get_all_stores_for_city else ""

    group_by_str: str = "group by order_date"
    group_by_str += ", city_name" if get_all_cities else ""
    group_by_str += ", hour" if hourly_metrics or hour else ""
    group_by_str += ", frontend_merchant_name,frontend_merchant_id" if get_all_stores_for_city else ""

    if not get_all_cities and not hourly_metrics and not get_all_stores_for_city:
        dt_filters += f"""
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 14 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 13 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 21 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 20 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 28 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', {start_date_timestamp} - 86400 * 27 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        """
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    rider_login_metric = f"""
        SELECT "date" AS order_date,
            sum(total_active_mins)/60 AS login_hrs
            {city_column}
            {store_column}
            {hr_column}
        FROM rider_login_time_metrics_v2
        WHERE ({dt_filters})
        {dynamic_where_clause}
        {group_by_str}
        HAVING {cutoff_date_condition}
        LIMIT 100000
    """
    return rider_login_metric


def get_new_rider_metrics_query(dynamic_where_clause: str = '', 
        dynamic_where_clause_today: str = '',
        yesterday_metric: bool = False, 
        hourly_metrics: bool = False,
        get_all_cities: bool = False,
        get_all_stores_for_city: bool = False,
        hour: str = None)-> List[str]:

    dt_filters: str = """
    (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
        AND fod < FromEpochSeconds(now()/1000 - 86400*7))
    """ if not yesterday_metric else """
    (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
        AND fod < dateTrunc('DAY', FromEpochSeconds(now()/1000), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*8), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
        AND fod < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """

    city_column: str = """, CASE
            WHEN city_name IN ('Panchkula',
                            'Kharar',
                            'Zirakpur',
                            'Mohali',
                            'Chandigarh') THEN 'Chandigarh'
            ELSE city_name
        END AS city
    """ if get_all_cities and not get_all_stores_for_city else ""

    city_column_today: str = """, CASE
            WHEN city_name IN ('Panchkula',
                                    'Kharar',
                                    'Zirakpur',
                                    'Mohali',
                                    'Chandigarh') THEN 'Chandigarh'
            ELSE city_name
            END AS city
    """ if get_all_cities and not get_all_stores_for_city else ""

    store_column = """
                ,lookup('merchant_outlet_facility_mapping_v2','frontend_merchant_name','frontend_merchant_id',front_end_merchant_id) as frontend_merchant_name,
                front_end_merchant_id as frontend_merchant_id
            """ if get_all_stores_for_city else ""
    
    store_column_today = """
                , merchant_name as frontend_merchant_name,
		        merchant_id as frontend_merchant_id
            """ if get_all_stores_for_city else ""

    hr_column_fod: str = """, hour(fod, 'Asia/Kolkata') AS hour""" if hourly_metrics or hour else ""
    hr_column_today: str = """, hour(insert_timestamp, 'Asia/Kolkata') AS hour""" if hourly_metrics or hour else ""

    group_by_str: str = "group by order_date"
    group_by_str_today: str = "group by order_date,rider_login"

    group_by_str += ", city" if get_all_cities else ""
    group_by_str_today += ", city" if get_all_cities else ""

    group_by_str += ", hour" if hourly_metrics  or hour else ""
    group_by_str_today += ", hour" if hourly_metrics or hour else ""

    group_by_str += ", frontend_merchant_name ,frontend_merchant_id" if get_all_stores_for_city else ""
    group_by_str_today += ", frontend_merchant_name ,frontend_merchant_id" if get_all_stores_for_city else ""

    if not get_all_cities and not hourly_metrics and not get_all_stores_for_city:
        dt_filters += """
        OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < FromEpochSeconds(now()/1000 - 86400*14))
        OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < FromEpochSeconds(now()/1000 - 86400*21))
        OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < FromEpochSeconds(now()/1000 - 86400*28))
        """ if not yesterday_metric else """
        OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*15), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*22), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (fod >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*29), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        """

    new_rider_hist_qry: str = f"""
    SELECT 
        DATETIMECONVERT(fod + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
        count(delivery_fe_id) AS new_rider_logins
        {city_column}
        {store_column}
        {hr_column_fod}
    FROM 
        rider_fod_metrics_v2 
    WHERE 
        ({dt_filters})
        {dynamic_where_clause}
    {group_by_str}
    LIMIT 100000
    """

    todays_date = get_todays_date_in_ist().strftime('%Y-%m-%d')
    new_rider_today_qry: str = f"""
    SELECT 
        order_date,
        count(DISTINCT delivery_fe_id) AS new_rider_logins, 
        CASE 
            WHEN lookup('rider_fod_metrics_v2', 'front_end_merchant_id', 'delivery_fe_id', delivery_fe_id) = 'null' 
                THEN 'new_rider' 
            ELSE 'NA' 
        END AS rider_login
        {city_column_today}
        {store_column_today}
        {hr_column_today}
    FROM 
        fact_order_details_v6
    WHERE 
        order_date = '{todays_date}'
        {dynamic_where_clause_today}
        AND current_status = 'DELIVERED' 
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
    {group_by_str_today}
        HAVING rider_login='new_rider'
    LIMIT 100
    """

    return new_rider_hist_qry, new_rider_today_qry


def get_new_rider_metrics_query_custom(dynamic_where_clause: str = '', 
        hourly_metrics: bool = False,
        get_all_cities: bool = False,
        get_all_stores_for_city: bool = False,
        hour: str = None,
        start_date: str = None)-> List[str]:

    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    start_date_timestamp = int(start_date_obj.timestamp() * 1000)

    dt_filters: str = f"""
    (fod >= dateTrunc('DAY', {start_date_timestamp}, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
        AND fod < dateTrunc('DAY', {start_date_timestamp} + 86400 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    OR (fod >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 7 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
        AND fod < dateTrunc('DAY', {start_date_timestamp} - 86400 * 6 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """

    city_column: str = """, CASE
            WHEN city_name IN ('Panchkula',
                            'Kharar',
                            'Zirakpur',
                            'Mohali',
                            'Chandigarh') THEN 'Chandigarh'
            ELSE city_name
        END AS city
    """ if get_all_cities and not get_all_stores_for_city else ""

    store_column = """
                ,lookup('merchant_outlet_facility_mapping_v2','frontend_merchant_name','frontend_merchant_id',front_end_merchant_id) as frontend_merchant_name,
                front_end_merchant_id as frontend_merchant_id
            """ if get_all_stores_for_city else ""
    
    hr_column_fod: str = """, hour(fod, 'Asia/Kolkata') AS hour""" if hourly_metrics or hour else ""

    group_by_str: str = "group by order_date"
    group_by_str += ", city" if get_all_cities else ""
    group_by_str += ", hour" if hourly_metrics  or hour else ""
    group_by_str += ", frontend_merchant_name ,frontend_merchant_id" if get_all_stores_for_city else ""

    if not get_all_cities and not hourly_metrics and not get_all_stores_for_city:
        dt_filters += f"""
        OR (fod >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 14 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < dateTrunc('DAY', {start_date_timestamp} - 86400 * 13 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (fod >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 21 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < dateTrunc('DAY', {start_date_timestamp} - 86400 * 20 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (fod >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 28 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND fod < dateTrunc('DAY', {start_date_timestamp} - 86400 * 27 * 1000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        """
    
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    new_rider_hist_qry: str = f"""
    SELECT 
        DATETIMECONVERT(fod + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
        count(delivery_fe_id) AS new_rider_logins
        {city_column}
        {store_column}
        {hr_column_fod}
    FROM 
        rider_fod_metrics_v2 
    WHERE 
        ({dt_filters})
        {dynamic_where_clause}
    {group_by_str}
    HAVING {cutoff_date_condition}
    LIMIT 100000
    """

    return new_rider_hist_qry


def get_storewise_surge_seen_metrics_query(where_clause: str) -> str:
    return f"""
SELECT 
    frontend_merchant_id, 
    100 * (LASTWITHTIME(total_surge_carts, window_end_epoch, 'LONG') / LASTWITHTIME(total_carts, window_end_epoch, 'LONG')) AS surge_shown_percent, 
    LASTWITHTIME(rider_surge_carts, window_end_epoch, 'LONG') AS rider_surge_carts, 
    LASTWITHTIME(picker_surge_carts, window_end_epoch, 'LONG') AS picker_surge_carts,  
    LASTWITHTIME(rain_surge_carts, window_end_epoch, 'LONG') AS rain_surge_carts 
FROM ticktock_serviceability_surge_info 
{where_clause}
GROUP BY frontend_merchant_id 
LIMIT 10000;
"""

def get_surge_seen_metrics_query(metrics_sql: str, dynamic_where_clause: str, yesterday_metric: bool, is_hourly: bool, is_city_filter: bool) -> str:
    dt_filters: str = """
        (window_start_epoch >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR
        (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end_epoch <= FromEpochSeconds(now()/1000 - 86400*7))
    """ if not yesterday_metric else """
        (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*1), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_start_epoch < dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR
        (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*8), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_start_epoch < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """
    city_store_col = ""
    hour_col = ""
    grp_by_clause = ""
    table_name = ""
    if is_hourly:
        hour_col += "hour(window_end_epoch, 'Asia/Kolkata') AS hr,"
        grp_by_clause += ", hour(window_end_epoch, 'Asia/Kolkata')"
        if is_city_filter:
            grp_by_clause += ", city_name"
            city_store_col += "city_name"
            table_name = "ticktock_serviceability_city_hourly_surge_info"
        else:
            grp_by_clause += ", frontend_merchant_id"
            city_store_col += "frontend_merchant_id"
            table_name = "ticktock_serviceability_hourly_surge_info"
    else:
        dt_filters += """
            OR
            (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end_epoch <= FromEpochSeconds(now()/1000 - 86400*14))
            OR
            (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end_epoch <= FromEpochSeconds(now()/1000 - 86400*21))
            OR
            (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end_epoch <= FromEpochSeconds(now()/1000 - 86400*28))
        """ if not yesterday_metric else """
            OR
            (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*15), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_start_epoch < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*22), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_start_epoch < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (window_start_epoch >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*29), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_start_epoch < dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        """
        if is_city_filter:
            grp_by_clause += ", city_name"
            city_store_col += "city_name"
            table_name = "ticktock_serviceability_city_daily_surge_info"
        else:
            grp_by_clause += ", frontend_merchant_id"
            city_store_col += "frontend_merchant_id"
            table_name = "ticktock_serviceability_daily_surge_info"

    return f"""
        SELECT {city_store_col},   
            ToDateTime(window_start_epoch, 'yyyy-MM-dd', 'Asia/Kolkata') AS dt,
            {hour_col}
            {metrics_sql} 
        FROM {table_name}
        WHERE ({dt_filters})
        {dynamic_where_clause}
        GROUP BY dt {grp_by_clause}
        LIMIT 1000000;
        """


def get_surge_seen_metrics_query_custom(metrics_sql: str, 
                                        dynamic_where_clause: str, 
                                        is_hourly: bool,
                                        start_date: str,
                                        is_city_filter: bool) -> str:
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
    start_date_timestamp = int(start_date_obj.timestamp() * 1000)

    dt_filters: str = f"""
        (window_start_epoch >= dateTrunc('DAY', {start_date_timestamp} , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND window_start_epoch < dateTrunc('DAY', {start_date_timestamp} + 86400 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR
        (window_start_epoch >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 7 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
            AND window_start_epoch < dateTrunc('DAY', {start_date_timestamp} - 86400 * 6 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """
    city_store_col = ""
    hour_col = ""
    grp_by_clause = ""
    table_name = ""
    if is_hourly:
        hour_col += "hour(window_end_epoch, 'Asia/Kolkata') AS hr,"
        grp_by_clause += ", hour(window_end_epoch, 'Asia/Kolkata')"
        if is_city_filter:
            grp_by_clause += ", city_name"
            city_store_col += "city_name"
            table_name = "ticktock_serviceability_city_hourly_surge_info"
        else:
            grp_by_clause += ", frontend_merchant_id"
            city_store_col += "frontend_merchant_id"
            table_name = "ticktock_serviceability_hourly_surge_info"
    else:
        dt_filters += f"""
            OR
            (window_start_epoch >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 14 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
                AND window_start_epoch < dateTrunc('DAY', {start_date_timestamp} - 86400 * 13 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (window_start_epoch >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 21 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
                AND window_start_epoch < dateTrunc('DAY', {start_date_timestamp} - 86400 * 20 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
            OR
            (window_start_epoch >= dateTrunc('DAY', {start_date_timestamp} - 86400 * 28 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
                AND window_start_epoch < dateTrunc('DAY', {start_date_timestamp} - 86400 * 27 * 1000 , 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        """
        if is_city_filter:
            grp_by_clause += ", city_name"
            city_store_col += "city_name"
            table_name = "ticktock_serviceability_city_daily_surge_info"
        else:
            grp_by_clause += ", frontend_merchant_id"
            city_store_col += "frontend_merchant_id"
            table_name = "ticktock_serviceability_daily_surge_info"
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"

    return f"""
        SELECT {city_store_col},   
            ToDateTime(window_start_epoch, 'yyyy-MM-dd', 'Asia/Kolkata') AS dt,
            {hour_col}
            {metrics_sql} 
        FROM {table_name}
        WHERE ({dt_filters})
        {dynamic_where_clause}
        GROUP BY dt {grp_by_clause}
        HAVING {cutoff_date_condition}
        LIMIT 1000000;
        """


def get_order_metrics_stores_current_view_query(dynamic_where_clause: str) -> str:
    return f"""
        SELECT order_date,
            COUNT(DISTINCT(cart_id)) as order_count,
            100 * SUM(CASE WHEN (picker_assigned_timestamp - checkout_timestamp) <= 10000 then 1 else 0 end) / count(id) as "percentage_checkout_to_picker_assigned",
            100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 THEN 1 ELSE 0 END) / count(id) AS "percentage_order_assigned_before_billing",
            100 * SUM(CASE WHEN serviceability_reason = 'DISRUPTION_RAINS' THEN 1 ELSE 0 END) / count(id) AS "percentage_rain_order",
            merchant_id
        FROM fact_order_details_v6
        WHERE (
            (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') AND insert_timestamp >= FromEpochSeconds(now()/1000 - 900))
            OR 
            (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*7), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
            AND insert_timestamp >= FromEpochSeconds(now()/1000 - 86400*7 - 900) AND insert_timestamp <= FromEpochSeconds(now()/1000 - 86400*7))
        )
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        GROUP BY order_date,merchant_id
        LIMIT 50000
    """


def get_projection_metrics_query(
    dynamic_where_clause: str = '',
    today_projection: bool = False, 
    yesterday_projection: bool = False, 
    weekly_projection: bool = False,
    monthly_projection: bool = False,
    projection_days: int = 15,
    start_date: str = None
) -> str:
    base_query = f"""SELECT 
        CAST(SUM(proj_carts) AS int) AS projected_order_count,
        SUM(proj_gmv) AS projected_gmv
    FROM 
        projection_metrics_v2 
    WHERE 
        (proj_carts != 0 OR proj_gmv != 0)
        {dynamic_where_clause}"""
    
    if today_projection: # today query
        query = f"{base_query} AND dt = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') limit 10000"
    elif yesterday_projection: # YDAY query
        query = f"{base_query} AND dt = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*1), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') limit 10000"
    elif weekly_projection: # Weekly query
        query = (
            f"{base_query} AND ("
            "CASE "
            "WHEN dayOfWeek(FromEpochSeconds(now()/1000),'Asia/Kolkata') = 1 THEN "
            "week(FromDateTime(dt, 'yyyy-MM-dd')) = week(FromEpochSeconds(now()/1000 - 86400*1),'Asia/Kolkata') "
            "ELSE "
            "week(FromDateTime(dt, 'yyyy-MM-dd')) = week(FromEpochSeconds(now()/1000), 'Asia/Kolkata') "
            "AND dt < DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') "
            "END "
            ")"
            "LIMIT 10000"
        )
    elif monthly_projection: # Monthy query
        query = (
            f"{base_query} AND ("
            "CASE "
            "WHEN dayOfMonth(FromEpochSeconds(now()/1000),'Asia/Kolkata') = 1 THEN "
            "month(FromDateTime(dt, 'yyyy-MM-dd')) = month(FromEpochSeconds(now()/1000 - 86400*1),'Asia/Kolkata') "
            "ELSE "
            "month(FromDateTime(dt, 'yyyy-MM-dd')) = month(FromEpochSeconds(now()/1000), 'Asia/Kolkata') "
            "AND dt < DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') "
            "END "
            ")"
            "LIMIT 10000"
        )
    elif start_date:
        query = f"{base_query} AND dt='{start_date}' limit 10000"
    else:  # Projection Days query
        query = f"""SELECT 
            dt, 
            CAST(proj_carts AS INT) AS projected_order_count,
            proj_gmv AS projected_gmv
        FROM 
            projection_metrics_v2
        WHERE
            dt > DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*{projection_days}), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
            AND dt <= DATETIMECONVERT(FromEpochSeconds(now()/1000 + 86400*{projection_days}), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
            AND (proj_carts != 0 OR proj_gmv != 0) 
            {dynamic_where_clause}
        ORDER BY 1 limit 100"""

    return query


def get_projection_metrics_event_query(
    dynamic_where_clause: str = '',
    projection_days: int = 15,
) -> str:
    query = f"""
        SELECT 
            dt, 
            event_names
        FROM 
            projection_metrics_v2
        WHERE
            dt > DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*{projection_days}), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
            AND dt <= DATETIMECONVERT(FromEpochSeconds(now()/1000 + 86400*{projection_days}), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
            AND (proj_carts != 0 OR proj_gmv != 0) 
            {dynamic_where_clause}
        ORDER BY 1 limit 100
    """
    return query

def get_actual_order_metrics(dynamic_where_clause, start_date: str = None) -> str:
    if start_date:
        dt_filters = generate_dt_filters_custom_date(date_str=start_date,
                                                     column_name='order_date', 
                                                     num_intervals=4,
                                                     interval_days=7,
                                                     start_idx=1)
    else:
        dt_filters = """(
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*7), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS'))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*14), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS'))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*21), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS'))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*28), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS'))
        )"""
        
    return f"""
        SELECT order_date,
            COUNT(DISTINCT(cart_id)) as order_count,
            SUM(total_cost) as gmv
        FROM fact_order_details_v6
        WHERE ({dt_filters})
        and org_channel_id in ('1', '2')
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        GROUP BY order_date
        LIMIT 50000
    """


def get_max_hour_minute_funnel_conversion_metrics(table_name: str):
    return f"""
        SELECT max(snapshot_hr_mm)
        FROM {table_name}
        WHERE snapshot_date_ist = DATETIMECONVERT(now(), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS')
    """


def get_funnel_conversion_metrics_query(
    metrics_sql: str,
    dynamic_where_clause: str,
    table_name: str,
    city: List[str],
    is_yesterday: bool = False,
    is_hourly: bool = False,
    filter_hour: int = -1,
    store_response: bool = False,
    insights_view: bool = False
) -> str:
    # Define the grain column and group-by clause based on whether it's hourly
    if not is_hourly:
        grain_column = "snapshot_date_ist,"
        group_by_clause = "group by snapshot_date_ist"
    else:
        grain_column = "snapshot_date_ist, hour(updated_till_epoch * 1000, 'Asia/Kolkata') AS hour_column,"
        group_by_clause = "group by snapshot_date_ist, hour_column"

    hourly_check = ""
    if is_hourly and filter_hour != -1:
        lower_bound = filter_hour * 100
        
        upper_bound = 2345 if filter_hour == 23 else (filter_hour + 1) * 100
        
        hourly_check = f"and (snapshot_hr_mm > {lower_bound} and snapshot_hr_mm <= {upper_bound})"


    # Define key column and group-by clause based on city and store response
    if store_response==False:
        if city:
            group_by_clause += ", city"
            if insights_view:
                key_column = """
                    city,
                """
            else:
                key_column = """
                    CASE 
                        WHEN city IN ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh') THEN 'Chandigarh'
                        ELSE city
                    END AS city,
                """
    else:
        key_column = "merchant_name,"
        group_by_clause += ", merchant_name"

    # Determine the filter date based on whether it's yesterday
    ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
    filter_date = (ist_now - timedelta(days=1)).strftime('%Y-%m-%d') if is_yesterday else ist_now.strftime('%Y-%m-%d')

    # Generate the date filters
    dt_filters = generate_dt_filters(
        date_str=filter_date,
        date_column='snapshot_date_ist',
        num_weeks=1
    )

    # Construct the final SQL query with the required components
    funnel_conversion_metrics_query = f"""
        SELECT {grain_column}
            {key_column}
            {metrics_sql}
            FROM {table_name}
            WHERE (
                ({dt_filters})
            )
            {dynamic_where_clause}
            {hourly_check}
            {group_by_clause}
            LIMIT 100000
    """

    return funnel_conversion_metrics_query


def get_funnel_conversion_metrics_query_custom(
    metrics_sql: str,
    dynamic_where_clause: str,
    table_name: str,
    city: List[str],
    is_hourly: bool = False,
    filter_hour: int = -1,
    store_response: bool = False,
    start_date: str = None,
) -> str:
    # Define the grain column and group-by clause based on whether it's hourly
    if not is_hourly:
        grain_column = "snapshot_date_ist,"
        group_by_clause = "group by snapshot_date_ist"
    else:
        grain_column = "snapshot_date_ist, hour(updated_till_epoch * 1000, 'Asia/Kolkata') AS hour_column,"
        group_by_clause = "group by snapshot_date_ist, hour_column"

    hourly_check = ""
    if is_hourly and filter_hour != -1:
        lower_bound = filter_hour * 100
        upper_bound = 2345 if filter_hour == 23 else (filter_hour + 1) * 100
        hourly_check = f"and (snapshot_hr_mm > {lower_bound} and snapshot_hr_mm <= {upper_bound})"

    # Define key column and group-by clause based on city and store response
    if store_response==False:
        if city:
            group_by_clause += ", city"
            key_column = """
                CASE 
                    WHEN city IN ('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh') THEN 'Chandigarh'
                    ELSE city
                END AS city,
            """
    else:
        key_column = "merchant_name,"
        group_by_clause += ", merchant_name"

    # Generate the date filters
    dt_filters = generate_dt_filters_custom_date(
        date_str=start_date,
        column_name='snapshot_date_ist',
        num_intervals=1
    )
    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"snapshot_date_ist >= '{cutoff_date}'"

    # Construct the final SQL query with the required components
    funnel_conversion_metrics_query = f"""
        SELECT {grain_column}
            {key_column}
            {metrics_sql}
            FROM {table_name}
            WHERE (
                ({dt_filters})
            )
            AND {cutoff_date_condition}
            {dynamic_where_clause}
            {hourly_check}
            {group_by_clause}
            LIMIT 100000
    """

    return funnel_conversion_metrics_query


def get_ptype_metrics_query(
    metrics_sql: str,
    dynamic_where_clause: str,
    table_name: str,
    store: str = None,
    is_yesterday: bool = False,
    filter_hour: int = -1,
    item_level_query: bool = False,
    is_stores_with_0_inventory: bool = False,
    metrics_type: str = None
) -> str:
    
    # Define the grain column and group-by clause based on whether it's hourly
    if filter_hour == -1:
        grain_column = "snapshot_date_ist,"
        group_by_clause = "group by snapshot_date_ist"
    else:
        grain_column = "snapshot_date_ist, hour(updated_till_epoch * 1000, 'Asia/Kolkata') AS hour_column,"
        group_by_clause = "group by snapshot_date_ist, hour_column"

    hourly_check = ""
    if filter_hour != -1:
        lower_bound = filter_hour * 100
        upper_bound = 2345 if filter_hour == 23 else (filter_hour + 1) * 100
        hourly_check = f"and (snapshot_hr_mm > {lower_bound} and snapshot_hr_mm <= {upper_bound})"

    order_by_clause = ""
    # Define key column and group-by clause based on city and store response
    if item_level_query:
        if store:
            key_column = """outlet_id, item_id, item_name, """
            group_by_clause += """, outlet_id, item_id, item_name"""
            order_by_clause = "order by top_ptype_item_rank asc"
        else:
            key_column = """city_name, item_id, item_name, """
            group_by_clause += ", city_name, item_id, item_name"
            order_by_clause = "order by stores_with_0_inventory desc"
    else:
        if store:
            key_column = "merchant_name, product_type,"
            group_by_clause += ", merchant_name, product_type"
        else:
            key_column = """
                city, product_type,
            """
            group_by_clause += ", city, product_type"
    
    if metrics_type == ProductMetricTypes.absolute_conv_drop:
        order_by_clause = "order by search_t0 desc"

    avoid_null_columns = ""
    if metrics_type == ProductMetricTypes.search_spike:
        avoid_null_columns = "and search_t7 IS NOT NULL and search_t7 != 0 "
    elif metrics_type in [ProductMetricTypes.absolute_conv_drop, ProductMetricTypes.contributed_conv_drop]:
        avoid_null_columns = "and search_t0 IS NOT NULL and search_t0 != 0 and search_t7 IS NOT NULL and search_t7 != 0"

    stores_with_0_inventory = ""
    if is_stores_with_0_inventory:
        stores_with_0_inventory = "and cnt_inventory_t0 = 0"
    # Determine the filter date based on whether it's yesterday
    ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
    filter_date = (ist_now - timedelta(days=1)).strftime('%Y-%m-%d') if is_yesterday else ist_now.strftime('%Y-%m-%d')

    # Generate the date filters
    dt_filters = generate_dt_filters(
        date_str=filter_date,
        date_column='snapshot_date_ist',
        num_weeks=1
    )
    # Construct the final SQL query with the required components
    ptype_metrics_query = f"""
        SELECT {grain_column}
            {key_column}
            {metrics_sql}
            FROM {table_name}
            WHERE (
                ({dt_filters})
            )
            {dynamic_where_clause}
            {hourly_check}
            {avoid_null_columns}
            {stores_with_0_inventory}
            {group_by_clause}
            {order_by_clause}
            LIMIT 100000
    """

    return ptype_metrics_query


def get_keyword_metrics_query(
    table_name: str,
    dynamic_where_clause: str,
    start_date: str = None,
) -> str:
    
    # Define the grain and columns
    grain_column = "snapshot_date_ist, "
    key_column = "search_keyword, "
    merchant_column = "merchant_id, " if "merchant" in table_name else ""
    group_by_clause = f"group by snapshot_date_ist, search_keyword{', merchant_id' if 'merchant' in table_name else ''}"
    
    # Define the metrics to select
    metrics_sql = """
        LASTWITHTIME(total_searches, etl_snapshot_ts_ist_epoch, 'DOUBLE') AS t0_searches,
        LASTWITHTIME(total_atc, etl_snapshot_ts_ist_epoch, 'DOUBLE') AS t0_atc,
        LASTWITHTIME(t0_conversion_percent, etl_snapshot_ts_ist_epoch, 'DOUBLE') AS t0_conversion_percent,
        LASTWITHTIME(spike_percent, etl_snapshot_ts_ist_epoch, 'DOUBLE') AS spike_percent_7d,
        LASTWITHTIME(t7_total_searches, etl_snapshot_ts_ist_epoch, 'DOUBLE') AS t7_searches
    """
            
    # Generate the date filters
    dt_filters = generate_dt_filters(
        date_str=start_date,
        date_column='snapshot_date_ist',
        num_weeks=1
    )
    
    # Construct the final SQL query
    keyword_metrics_query = f"""
        SELECT
            {grain_column}
            {key_column}
            {merchant_column}
            {metrics_sql}
        FROM
            {table_name}
        WHERE
            (
                ({dt_filters})
            )
            {dynamic_where_clause}
        {group_by_clause}
        LIMIT 50000
    """
    
    return keyword_metrics_query


def get_category_metrics_query(
    metrics_sql: str,
    dynamic_where_clause: str,
    table_name: str,
    is_yesterday: bool = False,
) -> str:
    
    grain_column = "snapshot_date_ist, "
    group_by_clause = "group by snapshot_date_ist , city, l0_category"
    
    order_by_clause = ""
    key_column = " city, l0_category, "
    
    ist_now = datetime.utcnow() + timedelta(hours=5, minutes=30)
    filter_date = (ist_now - timedelta(days=1)).strftime('%Y-%m-%d') if is_yesterday else ist_now.strftime('%Y-%m-%d')

    dt_filters = generate_dt_filters(
        date_str=filter_date,
        date_column='snapshot_date_ist',
        num_weeks=1
    )
    l0category_metrics_query = f"""
        SELECT {grain_column}
            {key_column}
            {metrics_sql}
            FROM {table_name}
            WHERE (
                ({dt_filters})
            )
            {dynamic_where_clause}
            {group_by_clause}
            {order_by_clause}
            LIMIT 100000
    """
    return l0category_metrics_query


def get_active_time_metrics_query(metrics_sql: str, dynamic_where_clause: str, city: list = None,
                                  store: list = None, yesterday_metric: bool = False, is_hourly: bool = False,
                                  city_wise: bool = False, store_wise: bool = False,
                                  custom_view: bool = False, start_date: str = None, ) -> str:

    table_name = "storeops_activity_metrics"

    select_columns_arr = ["dt"]
    group_by_columns_arr = ["dt"]

    if city:
        group_by_columns_arr.extend(["frontend_merchant_city_name"])
        # select_columns_arr.extend(["frontend_merchant_city_name"])
    if store:
        group_by_columns_arr.extend(["frontend_merchant_name", "frontend_merchant_id"])
        # select_columns_arr.extend(["frontend_merchant_name", "frontend_merchant_id"])
    if is_hourly:
        group_by_columns_arr.extend(["hour"])
        select_columns_arr.insert(0, "hour")
    if city_wise:
        group_by_columns_arr.extend(["frontend_merchant_city_name"])
        select_columns_arr.extend(["frontend_merchant_city_name"])
    if store_wise:
        group_by_columns_arr.extend(["frontend_merchant_name"])
        select_columns_arr.extend(["frontend_merchant_name"])

    if not custom_view:
        dt_filters = generate_dt_filters(date_str=get_date_str(yesterday_metric), date_column='dt', num_weeks=2 if (is_hourly or city_wise or store_wise) else 5)
    else: 
        num_weeks=2 if (is_hourly or city_wise or store_wise) else 5
        dt_filters = generate_dt_filters_custom_date(date_str=start_date, column_name='dt', num_intervals= num_weeks-1)

    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"
    select_columns = ',\n'.join(col for col in select_columns_arr)
    group_by_clause = "group by " + ', '.join(col for col in group_by_columns_arr)

    active_time_metrics_query = f"""
    SELECT {select_columns},
        {metrics_sql}
        FROM {table_name}
        WHERE ({dt_filters})
        and {cutoff_date_condition}
        {dynamic_where_clause}
        {group_by_clause}
        limit 1000000
    """
    return active_time_metrics_query


def get_complaints_metrics_query(metrics_sql: str, dynamic_where_clause: str,
                                 city: list = None, store: list = None,
                                 yesterday_metric: bool = False, is_hourly: bool = False,
                                 city_wise: bool = False, store_wise: bool = False,
                                 custom_view: bool = False, start_date: str = None, ) -> str:

    table_name = "fact_order_complaints"

    select_columns_arr = ["dt"]
    group_by_columns_arr = ["dt"]

    if is_hourly:
        group_by_columns_arr.extend(["hr"])
        select_columns_arr.insert(0, "hr")
    if city_wise:
        group_by_columns_arr.extend(
            ["lookup('outlet_facility_mapping_v1','city_name','outlet_id', store_id)"])
        select_columns_arr.extend(
            ["lookup('outlet_facility_mapping_v1','city_name','outlet_id', store_id) as frontend_merchant_city_name"])
    if not custom_view:
        dt_filters = generate_dt_filters(date_str=get_date_str(yesterday_metric), date_column='dt', timestamp_column='created_at', num_weeks=2 if (is_hourly or city_wise or store_wise) else 5)
    else:
        num_weeks=2 if (is_hourly or city_wise or store_wise) else 5
        dt_filters = generate_dt_filters_custom_date(date_str=start_date, column_name='dt', num_intervals= num_weeks-1)
    select_columns = ',\n'.join(col for col in select_columns_arr)
    group_by_clause = "group by " + \
        ', '.join(col for col in group_by_columns_arr)

    cutoff_date = (datetime.now() - timedelta(days=29)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"

    complaints_metrics_query = f"""
    SELECT {select_columns},
        {metrics_sql}
        FROM {table_name}
        WHERE ({dt_filters})
        and {cutoff_date_condition}
        {dynamic_where_clause}
        AND store_id is not null
        {group_by_clause}
        limit 1000000
    """
    return complaints_metrics_query


def get_wms_outlet_metrics_query(dynamic_where_clause: str, date_str: str, metrics_sql: str):

    dt_filters = generate_dt_filters(
        date_str, 
        date_column='expected_dispatch_date', 
        timestamp_column='epoch_expected_dispatch_time', 
        num_weeks=8, 
        less_than_time="now()/1000 - 1800", 
        period_length=1
    )

    cities_filter = """,CASE
        WHEN
        lookup('outlet_facility_mapping_v1',
               'city_name',
               'outlet_id',
               destination_outlet_id)
        IN('Panchkula', 'Kharar', 'Zirakpur', 'Mohali', 'Chandigarh')
        THEN 'Chandigarh'
        ELSE
        lookup('outlet_facility_mapping_v1',
               'city_name',
               'outlet_id',
               destination_outlet_id)
        END AS frontend_merchant_city_name"""

    wms_container_outlet_metrics_query = f"""
    SELECT destination_outlet_id,
        outlet_id,
        {metrics_sql}
        {cities_filter},    
        epoch_expected_dispatch_time,
        expected_dispatch_date
    FROM wms_container_events_v1
    WHERE 
    ({dt_filters})
    {dynamic_where_clause}
    GROUP BY destination_outlet_id,
            outlet_id,
            epoch_expected_dispatch_time,
            expected_dispatch_date,
            frontend_merchant_city_name
    ORDER BY on_time_dispatch_qty DESC
    LIMIT 90000000
    """
    return wms_container_outlet_metrics_query


def get_sto_demand_metrics_query(dynamic_where_clause: str):
    city_column: str = """, CASE
            WHEN frontend_merchant_city_name IN ('Panchkula',
                            'Kharar',
                            'Zirakpur',
                            'Mohali',
                            'Chandigarh') THEN 'Chandigarh'
            ELSE frontend_merchant_city_name
        END AS frontend_merchant_city_name
    """
    
    return f"""SELECT dispatch_time AS epoch_expected_dispatch_time
       ,frontend_outlet_id AS destination_outlet_id
       ,merchant_id
       ,frontend_merchant_name
       ,outlet_id
       ,lookup('outlet_facility_mapping_v1',
               'outlet_name',
               'outlet_id',
               outlet_id) AS outlet_name
       ,total_demand_quantity
       {city_column}
    FROM sto_items_demand_quantity_v1
    WHERE demand_date >= DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400*7), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS')
    AND demand_date <= DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS')
    AND dispatch_time < (now()/1000 - 1800)
    {dynamic_where_clause}
    LIMIT 900000
    """

def get_demand_ids(dynamic_where_clause: str):
    # determining the shift start-date according to order creation timestamp 0-12 UTC -> morning shift 12-0 UTC -> evening shift
    dt_column = """
        ,CASE WHEN hour(insert_ts) < 12 
        THEN DATETIMECONVERT(dateTrunc('day', insert_ts, 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
        ELSE DATETIMECONVERT(dateTrunc('day', insert_ts, 'MILLISECONDS') + 43200000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS') 
        END AS dt
    """

    return f"""SELECT ARRAY_AGG(demand_id, 'STRING', true) AS demand_ids
            {dt_column}
            FROM warehouse_outbound_events_v2 WHERE event_name = 'ORDER_CREATED' AND NOT is_packaging
            AND ({dynamic_where_clause})
            GROUP BY 2
            LIMIT 10000
        """

def get_availability_metrics(
        metrics_sql: str, 
        dynamic_where_clause: str, 
        table_name="warehouse_be_fe_availability", 
        extra_column: str="",
        date_str: str="") -> str:
    dt_filters = generate_dt_filters(date_str=date_str if date_str else get_date_str(is_yesterday=False), 
                                     date_column='date_ist', 
                                     num_weeks=8, 
                                     period_length=1,
                                     skip_week=[2,4,5,6])
    return f"""SELECT
        outlet_id
        ,lookup('outlet_facility_mapping_v1',
               'outlet_name',
               'outlet_id',
               outlet_id) AS outlet_name
        ,date_ist
        {extra_column}
        {metrics_sql}
        FROM {table_name}
        WHERE ({dt_filters})
        {dynamic_where_clause}
        GROUP BY outlet_id, outlet_name, date_ist {extra_column}
        ORDER BY date_ist DESC
        LIMIT 50000
        """

def get_warehouse_manpower_metrics(metrics_sql: str, dynamic_where_clause: str):
    dt_filters = generate_dt_filters(date_str=get_date_str(is_yesterday=False), 
                                     date_column='date_ist', 
                                     num_weeks=8, 
                                     period_length=1,
                                     skip_week=[2,4,5,6])
    return f"""SELECT
    outlet_id
    ,lookup('outlet_facility_mapping_v1',
               'outlet_name',
               'outlet_id',
               outlet_id) AS outlet_name
    ,date_ist
    {metrics_sql}
    FROM warehouse_manpower_metrics
    WHERE ({dt_filters})
    {dynamic_where_clause}
    GROUP BY 1,2,3
    ORDER BY date_ist DESC
    LIMIT 50000
    """

def get_warehouse_activity_rates(metrics_sql: str, dynamic_where_clause: str):
    return f""" SELECT
        outlet_id,
        CASE WHEN event_name in ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND') THEN picking_zone ELSE 'null' END AS picking_zone,
        {metrics_sql}
        FROM warehouse_outbound_events_v2
        WHERE insert_ts >= now() - 30*60*1000 AND NOT is_packaging
        AND event_name in ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND', 'PACKAGING_ACTIVITY_COMPLETED', 'CONTAINER_SORTED')
        {dynamic_where_clause}
        GROUP BY outlet_id, picking_zone
        LIMIT 100000
    """


def get_warehouse_dispatch_slot_wise_metrics(metrics_sql: str, dynamic_where_clause: str, is_slotwise=False, order_by_dispatch=False):
    slot_column = ""
    group_by_clause = "GROUP BY 1,2,3"
    order_by_clause = "ORDER BY 3" if order_by_dispatch else ""
    if is_slotwise:
        slot_column = """,CASE WHEN event_name in ('PICK_LIST_ITEM_NOT_FOUND', 'PICK_LIST_PICK_ITEM', 
            'PICK_LIST_ITEM_LOCATION_NOT_FOUND', 'PICK_LIST_CREATED', 'PICK_LIST_CANCELLED', 
            'PICK_LIST_PARTIALLY_CANCELLED', 'PICK_LIST_COMPLETED') THEN picking_zone ELSE 'null' END AS picking_zone"""
        group_by_clause = "GROUP BY 1,2,3,4"

    return f"""SELECT
        outlet_id
        ,lookup('outlet_facility_mapping_v1',
               'outlet_name',
               'outlet_id',
               outlet_id) AS outlet_name
        ,dispatch_time/1000 AS dispatch_time
        {slot_column}
        {metrics_sql}
        FROM warehouse_outbound_events_v2
        WHERE NOT is_packaging
        {dynamic_where_clause}
        {group_by_clause}
        {order_by_clause}
        LIMIT 100000
    """

def get_warehouse_outbound_metrics(metrics_sql: str, dynamic_where_clause: str):
    return f"""SELECT 
            outlet_id
            ,lookup('outlet_facility_mapping_v1',
               'outlet_name',
               'outlet_id',
               outlet_id) AS outlet_name
            {metrics_sql}
        FROM warehouse_outbound_events_v2 
        WHERE event_name IN ('ORDER_CREATED', 'PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND', 'PACKAGING_ACTIVITY_COMPLETED', 'CONTAINER_SORTED', 'CONTAINER_DISPATCHED', 'PICK_LIST_CREATED') 
        AND NOT is_packaging
        {dynamic_where_clause}
        GROUP BY 1,2
        LIMIT 10000
        """


def get_warehouse_mapping_query() -> str:
    return f"""SELECT outlet_id,
            lookup('outlet_facility_mapping_v1', 'outlet_name', 'outlet_id', outlet_id) AS outlet_name
        FROM warehouse_shift_mapping_v1
        WHERE lookup('outlet_facility_mapping_v1', 'outlet_name', 'outlet_id', outlet_id) <> 'null'
        LIMIT 100000"""


def get_all_category_metrics_query(metrics_sql,
                                   dynamic_where_clause: str,
                                   yesterday_metric: bool = False,
                                   number_of_weeks: int = 5) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
                            date_column='order_date', 
                            timestamp_column='insert_timestamp', 
                            yesterday_metric=yesterday_metric, 
                            number_of_days=7,  number_of_week=number_of_weeks)

    dt_filters: str = extract_indices_from_dt_filters(dt_filters, [0,1,-1])

    date_column = "order_date"
    table_name = "fact_order_item_details_v3"
    select_l0_category = "l0_category as mapped_l0_category"
    group_by_mapped_l0_category = "mapped_l0_category"

    all_l0_category_metric_query = f"""
        SELECT 
            {date_column},
            {select_l0_category},
            {metrics_sql}
        FROM 
            {table_name}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
        GROUP BY 
            {date_column}, 
            {group_by_mapped_l0_category}
        LIMIT 50000
    """
    return all_l0_category_metric_query


def get_l1_category_metrics_for_l0_query(metrics_sql, 
                                         dynamic_where_clause, 
                                         yesterday_metric=False, 
                                         number_of_weeks=5):
    """
    Query to get L1 category metrics for a given L0 category
    
    Args:
        metrics_sql: SQL for the metrics to fetch
        dynamic_where_clause: Additional WHERE clauses for filtering
        yesterday_metric: Whether to get yesterday's metrics
        number_of_weeks: Number of weeks to fetch data for
    """
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
                            date_column='order_date', 
                            timestamp_column='insert_timestamp', 
                            yesterday_metric=yesterday_metric, 
                            number_of_days=7,  number_of_week=number_of_weeks)
    dt_filters: str = extract_indices_from_dt_filters(dt_filters, [0,1,-1])
    date_column = "order_date"
    l1_category_column = "l1_category"
    table_name = "fact_order_item_details_v3"
    
    query = f"""
        SELECT 
            {date_column},
            {l1_category_column},
            {metrics_sql}
        FROM 
            {table_name}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
        GROUP BY 
            {date_column}, 
            {l1_category_column}
        LIMIT 50000
    """
    return query


def get_all_product_metrics_query(metrics_sql,
                                  dynamic_where_clause: str,
                                  yesterday_metric: bool = False,
                                  is_daywise: bool = False,
                                  current_hour: bool = True,
                                  dates: List[str] = None,
                                  date_str: str = None,
                                  number_of_weeks: int = 3) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(date_column='order_date',
                                                                                     timestamp_column='insert_timestamp',
                                                                                     timestamp_column_in_seconds=False,
                                                                                     is_daywise=is_daywise,
                                                                                     current_hour=current_hour,
                                                                                     yesterday_metric=yesterday_metric,
                                                                                     number_of_days=7,
                                                                                     number_of_week=number_of_weeks,
                                                                                     dates=dates,
                                                                                     date_str=date_str)

    date_column = "order_date"
    table_name = "fact_order_item_details_v3"

    all_products_metric_query = f"""
        SELECT 
            {date_column},
            {metrics_sql}
        FROM 
            {table_name}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
        GROUP BY 
            {date_column}
        LIMIT 50000
    """
    return all_products_metric_query


def get_complaints_query(dynamic_where_clause: str,
                         yesterday_metric: bool = False,
                         is_daywise: bool = False,
                         current_hour: bool = True,
                         date_str: str = None, 
                         number_of_weeks: str = 3) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(date_column='dt', 
                                                                                     timestamp_column='created_at', 
                                                                                     timestamp_column_in_seconds=False, 
                                                                                     is_daywise=is_daywise, 
                                                                                     current_hour=current_hour, 
                                                                                     yesterday_metric=yesterday_metric, 
                                                                                     number_of_days=7,  
                                                                                     number_of_week=number_of_weeks,
                                                                                     date_str=date_str)

    date_column = "dt"
    table_name = "fact_order_complaints"

    all_complaints_metric_query = f"""
        SELECT 
            {date_column},
            complaint_type,
            DISTINCTCOUNT(complaint_id)
        FROM {table_name}
        WHERE ({dt_filters})
            {dynamic_where_clause}
            AND product_id is not null
        GROUP BY 
            complaint_type, 
            {date_column}
        LIMIT 50000
    """
    return all_complaints_metric_query


def get_all_field_list_query(field: str, table_name: str)-> str:
    all_field_list_query = f"""
        SELECT DISTINCT({field})
        FROM {table_name}
        LIMIT 50000
    """
    return all_field_list_query


def get_filtered_product_fieldwise_query(dynamic_where_clause: str, search_filter: str, table_name: str) -> str:
    filtered_product_fieldwise_query = f"""
        SELECT 
            DISTINCT({search_filter})
        FROM {table_name}
        {'WHERE ' + dynamic_where_clause if dynamic_where_clause else ''}
        LIMIT 100
    """
    return filtered_product_fieldwise_query



def get_all_product_metrics_all_cities_query(metrics_sql,
                                  dynamic_where_clause: str,
                                  yesterday_metric: bool = False,
                                  is_store_query: bool = False,
                                  dates: List[str] = None) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(date_column='order_date', 
                                                                                     timestamp_column='insert_timestamp', 
                                                                                     timestamp_column_in_seconds=False, 
                                                                                     is_daywise=False, 
                                                                                     current_hour=True, 
                                                                                     yesterday_metric=yesterday_metric, 
                                                                                     number_of_week=2,
                                                                                     dates=dates)

    selected_columns = ''' order_date, city_name '''
    group_by = ''' city_name , order_date '''

    table_name = "fact_order_item_details_v3"

    if is_store_query:
        selected_columns = '''
            order_date, 
            city_name, 
            (LOOKUP('merchant_outlet_facility_mapping_v2', 'frontend_merchant_name', 'frontend_merchant_id', merchant_id)) as frontend_merchant_name, 
            merchant_id '''
        group_by = '''
            merchant_id,
            (LOOKUP('merchant_outlet_facility_mapping_v2', 'frontend_merchant_name', 'frontend_merchant_id', merchant_id)),
            city_name,
            order_date '''

    all_products_metric_all_cities_query = f"""
        SELECT 
            {selected_columns},
            {metrics_sql}
        FROM 
            {table_name}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
        GROUP BY 
            {group_by}
        LIMIT 50000
    """
    return all_products_metric_all_cities_query



def get_all_product_metrics_hourwise_query(metrics_sql,
                                  dynamic_where_clause: str,
                                  yesterday_metric: bool = False,
                                  dates: List[str] = None,
                                  date_str: str = None) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(date_column='order_date', 
                                                                                     timestamp_column='insert_timestamp', 
                                                                                     timestamp_column_in_seconds=False, 
                                                                                     is_daywise=True, 
                                                                                     current_hour=False, 
                                                                                     yesterday_metric=yesterday_metric, 
                                                                                     number_of_days=3,
                                                                                     dates=dates,
                                                                                     date_str=date_str)
    static_columns = '''order_date, order_hour'''
    table_name = "fact_order_item_details_v3"

    all_products_metric_hourly_query = f"""
        SELECT 
            {static_columns},
            {metrics_sql}
        FROM 
            {table_name}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
        GROUP BY {static_columns}
        LIMIT 50000
    """
    return all_products_metric_hourly_query


def get_distinct_order_ids_with_merchant_info_query(dynamic_where_clause: str,
                         start_date=str,
                         end_date=str) -> str:

    date_column = "dt"
    table_name = "fact_order_complaints"

    distinct_order_ids_query = f"""
        SELECT 
            DISTINCT(order_id) as order_id,
            LOOKUP('outlet_facility_mapping_v1', 'merchant_id', 'outlet_id', store_id) as merchant_id,
            LOOKUP('outlet_facility_mapping_v1', 'frontend_merchant_name', 'outlet_id', store_id) as frontend_merchant_name
        FROM 
            {table_name}
        WHERE 
            ({date_column} >= '{start_date}' AND {date_column} <= '{end_date}')
            {dynamic_where_clause}
            AND store_id is not null
        ORDER BY order_id
        LIMIT 200
    """
    return distinct_order_ids_query


def get_bad_stocks_query(dynamic_where_clause: str,
                         start_date=str,
                         end_date=str) -> str:

    date_column = "audit_date"
    table_name = "storeops_audit_bad_stock_images"
    select_columns = '''
        audit_date,
        LOOKUP('item_mapping_v1', 'item_name', 'item_id', item_id) as item_name,
        LOOKUP('outlet_facility_mapping_v1', 'merchant_id', 'outlet_id', site_id) as merchant_id,
        LOOKUP('outlet_facility_mapping_v1', 'frontend_merchant_name', 'outlet_id', site_id) as merchant_name,
        images
    '''
    image_empty_condition = " AND images <> '' "

    bad_stocks_query = f"""
        SELECT 
            {select_columns}
        FROM 
            {table_name}
        WHERE 
            ({date_column} >= '{start_date}' AND {date_column} <= '{end_date}')
            {dynamic_where_clause}
            {image_empty_condition}
        LIMIT 100
    """
    return bad_stocks_query



def get_distinct_cart_ids_in_whole_city_store_query(dynamic_where_clause: str,
                                                    yesterday_metric: bool = False,
                                                    is_daywise: bool = False,
                                                    current_hour: bool = True,
                                                    isHourly: bool = False,
                                                    dates: List[str] = None,
                                                    number_of_weeks: int = 3) -> str:
    dt_filters: str = ""
    if isHourly:
        dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(date_column='order_date', 
                                                                                         timestamp_column='insert_timestamp', 
                                                                                         timestamp_column_in_seconds=False, 
                                                                                         is_daywise=True, 
                                                                                         current_hour=False, 
                                                                                         yesterday_metric=yesterday_metric, 
                                                                                         number_of_days=3,
                                                                                         dates=dates)
    else:
        dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(date_column='order_date', 
                                                                                         timestamp_column='insert_timestamp', 
                                                                                         timestamp_column_in_seconds=False, 
                                                                                         is_daywise=is_daywise, 
                                                                                         current_hour=current_hour,
                                                                                         yesterday_metric=yesterday_metric, 
                                                                                         number_of_days=7,  
                                                                                         number_of_week=number_of_weeks,
                                                                                         dates=dates)

    order_by_columns: str =  '''order_date, order_hour''' if isHourly else '''order_date'''

    table_name = "fact_order_item_details_v3"

    distinct_cart_ids_query = f"""
        SELECT 
            {order_by_columns},
            COUNT(DISTINCT cart_id) as cart_ids
        FROM 
            {table_name}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
        GROUP BY 
            {order_by_columns}
        LIMIT 50000
    """
    return distinct_cart_ids_query

def get_distinct_cart_ids_in_whole_city_store_query_citywise(dynamic_where_clause: str,
                                                       yesterday_metric: bool = False,
                                                       is_daywise: bool = False,
                                                       current_hour: bool = True,
                                                       isHourly: bool = False,
                                                       is_store_query: bool = False,
                                                       dates: List[str] = None) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
        date_column='order_date', 
        timestamp_column='insert_timestamp', 
        timestamp_column_in_seconds=False, 
        is_daywise=is_daywise, 
        current_hour=current_hour, 
        yesterday_metric=yesterday_metric, 
        number_of_days=7,  
        number_of_week=3,
        dates=dates
    )
    
    if is_store_query:
        # For store-level query, return date, city, store_id, and count
        select_columns = """
            order_date,
            city_name,
            merchant_id,
            COUNT(DISTINCT cart_id) as total_cart_ids
        """
        group_by = "GROUP BY order_date, city_name, merchant_id"
    else:
        # For city-level query, return date, city, and count
        select_columns = """
            order_date,
            city_name,
            COUNT(DISTINCT cart_id) as total_cart_ids
        """
        group_by = "GROUP BY order_date, city_name"
    
    if isHourly:
        select_columns = """
            order_date,
            order_hour,
            COUNT(DISTINCT cart_id) as total_cart_ids
        """
        group_by = "GROUP BY order_date, order_hour"
    
    query = f"""
        SELECT 
            {select_columns}
        FROM 
            fact_order_item_details_v3
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            and org_channel_id in ('1', '2')
            and city_name not in ('Not in service area')
        {group_by}
        LIMIT 50000
    """
    
    return query


def get_wtd_mtd_query(metric_type: str, dynamic_where_clause: str):
    if metric_type == "mtd":
        table_name = "agg_mtd_sonar_key_business_metrics_v3"
    elif metric_type == "wtd":
        table_name = "agg_wtd_sonar_key_business_metrics_v3"

    query = f"""
        SELECT * 
        FROM 
            {table_name}
        {("WHERE " + (dynamic_where_clause)) if dynamic_where_clause else ""}
        LIMIT 100000
    """
    return query


def get_freebie_order_percentage(dynamic_where_clause: str, yesterday_metric: bool = False) -> str:
    dt_filters: str = """
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*7 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND datetime_created <= FromEpochSeconds(now()/1000 - 86400*7))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*14 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND datetime_created <= FromEpochSeconds(now()/1000 - 86400*14))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*21 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND datetime_created <= FromEpochSeconds(now()/1000 - 86400*21))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*28 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND datetime_created <= FromEpochSeconds(now()/1000 - 86400*28))
    """ if not yesterday_metric else """
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*8 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*15 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*22 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        -- OR
        -- (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*29 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
    """

    return f"""SELECT order_date,
        (SUM(CASE WHEN product_type IN ('Free Gift', 'Special Offer') THEN 1 ELSE 0 END) / COUNT(DISTINCT cart_id)) * 100 AS freebie_order_percentage
        FROM fact_order_item_details_v3
        where ({dt_filters})
        {dynamic_where_clause}
        group by order_date
        LIMIT 100"""
