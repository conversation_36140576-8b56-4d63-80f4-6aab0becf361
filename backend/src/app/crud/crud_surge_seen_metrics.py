from datetime import datetime, timezone, timedelta
from typing import List, Dict, Union
from collections import defaultdict
import math
from pinotdb import connect


from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING, SURGE_SEEN_METRICS, SURGE_SEEN_METRICS_HOURLY
from .queries import get_storewise_surge_seen_metrics_query, get_store_city_mapping_query, get_surge_seen_metrics_query, get_surge_seen_metrics_query_custom
from app.schemas.order_metric import DatewiseMetric, OrderDatewiseMetric, HourlyDateMetrics, HourMetric, HourlyDateMetric, Metric

IST = timezone(timedelta(hours=5, minutes=30))


class CRUDSurgeSeenMetrics():    
    def get_store_wise_surge_metrics(self, conn: connect, city: List = None, surge_shown_map: defaultdict(list) = None,surge_reason_map: defaultdict(list) = None,frontend_merchant_ids: List[str] = None):
        where_clause = []
        params = {}
        if city is not None:
            where_clause.append("WHERE frontend_merchant_id IN (%(stores)s)")
            params["stores"] = frontend_merchant_ids
        surge_shown_query = get_storewise_surge_seen_metrics_query(where_clause=" ".join(where_clause))
        curs = conn.cursor()
        curs.execute(surge_shown_query, params)
        current_date = datetime.now(IST).date()
        for row in curs:
            frontend_merchant_id = row[0]
            surge_shown = DatewiseMetric(date = current_date, metric = round(row[1],2))
            if row[4] > 0 :
                surge_reason_map[frontend_merchant_id].add("Rain")
            if row[3] > 0:
                surge_reason_map[frontend_merchant_id].add("Picker")
            if row[2] > 0:
                surge_reason_map[frontend_merchant_id].add("Rider")
            surge_shown_map[frontend_merchant_id].append(surge_shown)
        curs.close()    

    def get_daily_view(self, curs) -> List[OrderDatewiseMetric]:
        # Create dictionaries dynamically for each metric
        metric_instances = {metric: defaultdict(float) for metric in SURGE_SEEN_METRICS}

        # Populate the dicts with table data, row[0] is store id.
        for row in curs:
            dt = datetime.strptime(row[1], "%Y-%m-%d")
            for idx, metric in enumerate(SURGE_SEEN_METRICS, start=2):
                metric_instances[metric][dt] += row[idx]

        # Define metric mappings for easier access
        metrics_to_calculate = {
            "surge_seen_percentage": "surge_cart_instances",
            "rider_surge_seen_percentage": "rider_surge_cart_instances",
            "picker_surge_seen_percentage": "picker_surge_cart_instances",
            "rain_surge_seen_percentage": "rain_surge_cart_instances",
        }

        # Create list dynamically for each metric
        results = {key: [] for key in metrics_to_calculate}

        # Iterate over each date and calculate the percentages for all metrics
        for dt in metric_instances["total_cart_instances"]:
            total = metric_instances["total_cart_instances"][dt]

            for metric_name, instance_name in metrics_to_calculate.items():
                if total > 0:
                    percentage = (metric_instances[instance_name][dt] / total) * 100
                else:
                    percentage = 0

                # Append the result for each metric
                results[metric_name].append({"date": dt, "metric": round(percentage, 2)})

        # Return the list of OrderDatewiseMetric objects dynamically
        return [
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric], 
                data=results[metric], 
                type=METRICS_TYPE_MAPPING[metric]
            )
            for metric in metrics_to_calculate
        ]

    
    def get_hourly_view(self, curs) -> HourlyDateMetrics:
        surge_seen_hourly_results = []
        
        # Initialize defaultdicts for total and surge instances
        total_instances = defaultdict(lambda: defaultdict(int))
        surge_instances = defaultdict(lambda: defaultdict(int))

        # Populate the instances with cursor data
        for row in curs:
            dt = datetime.strptime(row[1], "%Y-%m-%d")
            hr = int(row[2])
            total_instances[dt][hr] += row[3]
            surge_instances[dt][hr] += row[4]

        # Iterate through the total instances and calculate the surge seen percentage
        for dt, hourly_metric in total_instances.items():
            hourly_results = []

            for hr, total in hourly_metric.items():
                surge = surge_instances[dt][hr]

                # Calculate surge seen percentage (surge / total) * 100
                if total > 0:
                    surge_seen_percentage = (surge / total) * 100
                else:
                    surge_seen_percentage = 0

                # Append hourly metrics with the calculated surge seen percentage
                hourly_results.append(
                    HourMetric(
                        hour=hr,
                        data=[
                            Metric(
                                name=METRIC_NAME_MAPPING["surge_seen_percentage"],
                                metric=round(surge_seen_percentage, 2),
                                type=METRICS_TYPE_MAPPING["surge_seen_percentage"]
                            ),
                        ]
                    )
                )
            # Append the hourly results to the hau_datewise_results
            surge_seen_hourly_results.append(
                HourlyDateMetric(
                    date=dt,
                    date_diff=(datetime.now(IST) - dt.replace(tzinfo=IST)).days, # !! CHECK: this diff is from now, do we want diff from start_date in custom dt filters 
                    data=hourly_results
                )
            )
        
        return HourlyDateMetrics(metrics=surge_seen_hourly_results)


    def get_surge_seen_metrics(self, conn: connect, city: List = None, store: List = None, zone: List = None, is_hourly: bool = False,
                        yesterday_metric: bool = False) -> Union[List[OrderDatewiseMetric], HourlyDateMetrics]:        
        curs = conn.cursor()
        dynamic_where_clause = []
        metrics = []
        params = {}

        if is_hourly:
            metrics = SURGE_SEEN_METRICS_HOURLY
        else:
            metrics = SURGE_SEEN_METRICS

        
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)

        if store is not None and len(store) > 0:
            is_city_filter = False
            dynamic_where_clause.append(
                "AND LOOKUP('merchant_outlet_facility_mapping', 'frontend_merchant_city_name', 'frontend_merchant_id', frontend_merchant_id ) IN (%(city)s)"
            )
            dynamic_where_clause.append("AND frontend_merchant_id IN (%(stores)s)")
            params["stores"] = store
            params["city"] = city  # Ensure city is set when store is not None
        elif zone is not None:
            is_city_filter = False
            dynamic_where_clause.append(
                "AND LOOKUP('merchant_outlet_facility_mapping', 'frontend_merchant_city_name', 'frontend_merchant_id', frontend_merchant_id ) IN (%(city)s)"
            )
            dynamic_where_clause.append("AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', frontend_merchant_id) IN (%(zone)s)")
            params["zone"] = zone
            params["city"] = city
        else:
            is_city_filter = True
            if city is not None:
                dynamic_where_clause.append("AND city_name IN (%(city)s)")
                params["city"] = city

        surge_seen_query = get_surge_seen_metrics_query(metrics_sql=metrics_sql,dynamic_where_clause=" ".join(dynamic_where_clause),
                                                        yesterday_metric=yesterday_metric,is_hourly=is_hourly,is_city_filter=is_city_filter)
        curs.execute(surge_seen_query, params)

        if is_hourly:
            return self.get_hourly_view(curs)
        else:
            return self.get_daily_view(curs)


    def get_surge_seen_metrics_custom(self, conn: connect, 
                                      city: List = None, store: List = None, zone: List = None,
                                      is_hourly: bool = False,
                                      start_date: str = None) \
                -> Union[List[OrderDatewiseMetric], HourlyDateMetrics]:
        curs = conn.cursor()
        dynamic_where_clause = []
        metrics = []
        params = {}

        if is_hourly:
            metrics = SURGE_SEEN_METRICS_HOURLY
        else:
            metrics = SURGE_SEEN_METRICS

        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)

        if store is not None and len(store) > 0:
            is_city_filter = False
            dynamic_where_clause.append(
                "AND LOOKUP('merchant_outlet_facility_mapping', 'frontend_merchant_city_name', 'frontend_merchant_id', frontend_merchant_id ) IN (%(city)s)"
            )
            dynamic_where_clause.append("AND frontend_merchant_id IN (%(stores)s)")
            params["stores"] = store
            params["city"] = city  # Ensure city is set when store is not None
        elif zone is not None:
            is_city_filter = False
            dynamic_where_clause.append(
                "AND LOOKUP('merchant_outlet_facility_mapping', 'frontend_merchant_city_name', 'frontend_merchant_id', frontend_merchant_id ) IN (%(city)s)"
            )
            dynamic_where_clause.append("AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', frontend_merchant_id) IN (%(zone)s)")
            params["zone"] = zone
            params["city"] = city
        else:
            is_city_filter = True
            if city is not None:
                dynamic_where_clause.append("AND city_name IN (%(city)s)")
                params["city"] = city

        surge_seen_query = get_surge_seen_metrics_query_custom(metrics_sql=metrics_sql,
                                                               dynamic_where_clause=" ".join(dynamic_where_clause),
                                                               is_hourly=is_hourly,
                                                               start_date=start_date,
                                                               is_city_filter=is_city_filter)
        curs.execute(surge_seen_query, params)

        if is_hourly:
            return self.get_hourly_view(curs)
        else:
            return self.get_daily_view(curs)


surge_seen_metrics = CRUDSurgeSeenMetrics()
