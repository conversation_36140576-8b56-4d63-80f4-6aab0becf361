from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import List, Union
from app.core.metric_config import METRIC_NAME_MAPPING,METRICS_TYPE_MAPPING

from pinotdb import connect


from app.schemas.order_metric import OrderDatewiseMetric, HourlyDateMetrics, Metric, HourMetric, HourlyDateMetric, \
    LocationDateMetric, LocationDateMetrics, LocationMetric, StoreMetric, StoreDateMetric, StoreDateMetrics
from .queries.rider_metrics import get_rider_login_metrics_query, get_rider_login_hours_current_max_epoch_query

IST = timezone(timedelta(hours=5, minutes=30))

class CRUDRiderMetrics():
    def get_rider_login_metrics(self, conn: connect, city: List = None, store: List = None, yesterday_metric: bool = False,
                               hourly_metrics: bool = False, get_all_cities: bool = False, get_all_stores_for_city: bool = False, hour: str = None, date_str: str = None) -> \
            Union[List[OrderDatewiseMetric], HourlyDateMetrics, LocationDateMetric, StoreDateMetrics]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}

        max_epoch_query = get_rider_login_hours_current_max_epoch_query()
        curs.execute(max_epoch_query, params)
        for row in curs:
            try:
                max_current_epoch = str(int(row[0]))
            except Exception as e:
                max_current_epoch = "now()"

        if city:
            dynamic_where_clause.append(
                "AND city in (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append(
                "AND frontend_merchant_id in (%(stores)s)")
            params["stores"] = store
        if hour:
            dynamic_where_clause.append(" AND cast(hour_ as int) in (%(hour)s)")
            params["hour"] = hour

        query = get_rider_login_metrics_query(dynamic_where_clause=" ".join(dynamic_where_clause),
                                     yesterday_metric=yesterday_metric,
                                     hourly_metrics=hourly_metrics,
                                     get_all_cities=get_all_cities,
                                     max_current_epoch=max_current_epoch,
                                     get_all_stores_for_city=get_all_stores_for_city,
                                     hour=hour,
                                     date_str=date_str)
        curs.execute(query, params)

        if not hourly_metrics and not get_all_cities and not get_all_stores_for_city:
            results = []
            rider_login_hrs = []
            for row in curs:
                order_date = datetime.strptime(row[0], "%Y-%m-%d")
                rider_login_hrs.append({"date": order_date, "metric": round(row[1], 0)})

            results.append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING["rider_login_hrs"],
                                               data=rider_login_hrs,
                                               type=METRICS_TYPE_MAPPING["rider_login_hrs"]))
            return results

        elif hourly_metrics:
            datewise_results = []
            hourly_results = defaultdict(list)
            for row in curs:
                result_metrics = [
                    Metric(name=METRIC_NAME_MAPPING["rider_login_hrs"], metric=round(row[1], 0),
                           type=METRICS_TYPE_MAPPING["rider_login_hrs"])]

                hourly_results[row[0]].append(HourMetric(hour=row[2], data=result_metrics))
            for dt_str, result in hourly_results.items():
                datewise_results.append(HourlyDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), date_diff=(
                        datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days,
                                                         data=result))
            return HourlyDateMetrics(metrics=datewise_results)

        elif get_all_cities:
            results = []
            citywise_results = defaultdict(list)
            for row in curs:
                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["rider_login_hrs"], metric=round(row[1], 0),
                           type=METRICS_TYPE_MAPPING["rider_login_hrs"])
                ]
                citywise_results[row[0]].append(LocationMetric(type="city", name=row[2], data=metrics))
            for dt_str, result in citywise_results.items():
                results.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days,data=result))
            return LocationDateMetrics(metrics=results)
        
        elif get_all_stores_for_city:
            results = []
            storewise_results = defaultdict(list)
            for row in curs:
                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["rider_login_hrs"], metric=round(row[1], 0),
                           type=METRICS_TYPE_MAPPING["rider_login_hrs"])
                ]
                storewise_results[row[0]].append(StoreMetric(frontend_merchant_name=row[2], frontend_merchant_id=str(row[3]), data=metrics))
            for dt_str, result in storewise_results.items():
                results.append(StoreDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days,data=result))
            return StoreDateMetrics(metrics=results)
        
rider_metrics = CRUDRiderMetrics()