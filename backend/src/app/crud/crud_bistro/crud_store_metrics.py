from datetime import timezone, timedelta
from collections import defaultdict
from typing import List, Union

from pinotdb import connect

from app.schemas.order_metric import StoresCityList, StoresNameMapping, StoresCityMapping, OrderDatewiseMetric
from .queries.store_metrics import get_store_city_mapping_query, get_active_stores_count
from app.core.metric_config_bistro import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING
from app.crud.utils import get_dates_in_ist_format, get_date_str


class CRUDStoreMetrics():

    def get_stores_city_mapping(self, conn: connect) -> StoresCityList:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = []
        STORES_CITY_LIST_QUERY = get_store_city_mapping_query(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(STORES_CITY_LIST_QUERY, params)
        store_city_map = defaultdict(list)
        results = []
        for row in curs:
            store_city_map[(row[2])].append(StoresNameMapping(frontend_merchant_id=str(row[0]),
                                                            frontend_merchant_name=row[1],
                                                            backend_merchant_id=str(row[3]),
                                                            merchant_type=row[5],
                                                            is_longtail_outlet=None
                                                            )
                                          )
        for city, stores_mapping in store_city_map.items():
            results.append(StoresCityMapping(city=city, zone=None, data=stores_mapping))
        return StoresCityList(filters=results)
    
    
    def get_active_stores_count_metric(self, conn: connect, city: List = None, is_yesterday:bool = False, date_str: str = None,
                                    ) -> Union[int, List]:

        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if city:
            dynamic_where_clause.append("AND frontend_merchant_city_name IN (%(cities)s)")
            params["cities"] = city
        ACTIVE_STORES_COUNT_QUERY = get_active_stores_count(dynamic_where_clause=" ".join(dynamic_where_clause),
                                                            is_yesterday=is_yesterday ,date_str=date_str)
        curs.execute(ACTIVE_STORES_COUNT_QUERY, params)

        total_active_stores = []

        results = []
        row = curs.fetchone()

        dates = get_dates_in_ist_format(start_date=date_str, jump=7, num_of_outputs=5)

        i = 0
        for dt in dates:
            total_metric = row[i]

            total_active_stores.append({"date": dt, "metric": total_metric})
            i = i + 1

        results.append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING["active_stores_count"],
                                        data=total_active_stores,
                                        type=METRICS_TYPE_MAPPING["active_stores_count"]))

        return results
    
store_metrics = CRUDStoreMetrics()


