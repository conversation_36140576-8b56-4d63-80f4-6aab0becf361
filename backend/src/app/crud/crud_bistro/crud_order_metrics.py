from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math
from pinotdb import connect
from app.core.metric_config_bistro import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING
from .queries.order_metrics import get_all_order_metrics_query, get_order_metrics_hourly_query, get_new_user_metrics_query, get_all_order_metrics_city_wise_query, get_bistro_ath_metrics
from .queries.dau_metrics import get_today_hau_metrics_city_wise, get_today_all_cities_dau_query, get_transacting_user_count_city_wise_query
from app.schemas.order_metric import OrderDatewiseMetric, HourlyDateMetrics, Metric, HourMetric, HourlyDateMetric, LocationDateMetric, StoreDateMetrics, LocationDateMetrics, StoreMetric, StoreDateMetric, LocationMetric

IST = timezone(timedelta(hours=5, minutes=30))


class CRUDOrderMetrics():
    def get_all_metrics(self, conn: connect, 
                        metrics: List = None, city: List = None, store: List = None,
                        yesterday_metric: bool = False,
                        date_str: str = None) -> List[OrderDatewiseMetric]:

        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        query = get_all_order_metrics_query(metrics_sql = metrics_sql,
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric,
                                            date_str=date_str)
        if "new_transacting_users_count" in metrics:
            generic_params = '''
                SET enableNullHandling = True;
            '''
        else: 
            generic_params = '''
                SET enableNullHandling = True;
                SET serverReturnFinalResult = True;
            '''

        query = generic_params + query
        curs.execute(query, params)

        metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)

        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[metric_name].append({"date": order_date, "metric": metric_value})

        results = [
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=METRICS_TYPE_MAPPING[metric_name]
            )
            for metric_name, _ in metrics_items
        ]
        return results
    

    def get_hourly_metrics(self, conn: connect, metrics: List, cities: List = None, app: str = None, stores: List = None, yesterday_metric: bool = False, status: List = None, date_str: str = None) -> HourlyDateMetrics:
        curs = conn.cursor()
        metrics_calc = [METRICS_CALC_MAPPING[metric] for metric in metrics]
        metrics_calc_sql = ",\n".join(metrics_calc)
        dynamic_where_clause = []
        params = {}
        # if app:
        #     if app.casefold() == "blinkit".casefold():
        #         dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
        #     else:
        #         dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if status:
            dynamic_where_clause.append("AND current_status IN (%(status)s)")
            params["status"] = status
        if cities:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = cities
        if stores:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = stores

        query = get_order_metrics_hourly_query(metrics_sql=metrics_calc_sql, dynamic_where_clause=" ".join(dynamic_where_clause),yesterday_metric=yesterday_metric, date_str=date_str)
        query = '''
            SET enableNullHandling = True;
            SET serverReturnFinalResult = True;
        ''' + query
        curs.execute(query, params)
        datewise_results = []
        hourly_results = defaultdict(list)
        for row in curs:
            result_metrics, idx = [], 0
            for metric in metrics:
                metric_value = row[idx+2]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    metric_value = '-'  # Replace NaN or infinity with -
                result_metrics.append(Metric(name=METRIC_NAME_MAPPING[metric], metric=metric_value, type=METRICS_TYPE_MAPPING[metric]))
                idx += 1
            hourly_results[row[1]].append(HourMetric(hour=row[0], data=result_metrics))

        for dt_str, result in hourly_results.items():
            datewise_results.append(HourlyDateMetric(date= datetime.strptime(dt_str, "%Y-%m-%d"), date_diff= (datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
        return HourlyDateMetrics(metrics= datewise_results)

#[Deprecated]
    def get_ntu_metrics(self, conn: connect, 
                        city: List = None, store: List = None, 
                        yesterday_metric: bool = False,
                        date_str: str = None) -> List[OrderDatewiseMetric]:
        curs_hist = conn.cursor()
        curs_today = conn.cursor()
        dynamic_where_clause = []
        dynamic_where_clause_today = []
        params = {}
        if city:
            dynamic_where_clause.append(
                 "AND city_name IN (%(cities)s)")
            dynamic_where_clause_today.append(
                "AND city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append(
                "AND front_end_merchant_id IN (%(stores)s)")
            dynamic_where_clause_today.append(
                "AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        
        query_hist, query_today = get_new_user_metrics_query(
                    dynamic_where_clause=" ".join(dynamic_where_clause), 
                    dynamic_where_clause_today=" ".join(dynamic_where_clause_today),
                    yesterday_metric=yesterday_metric,
                    date_str=date_str)

        curs_hist.execute(query_hist, params)
        
        today_date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
        if date_str == today_date_str:
            curs_today.execute(query_today, params)
        
        results = []
        new_user_logins = []
        for row in curs_hist:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            new_user_logins.append({"date": order_date, "metric": round(row[1], 0)})

        if date_str == today_date_str:
            for row in curs_today:
                order_date = datetime.strptime(row[0], "%Y-%m-%d")
                new_user_logins.append({"date": order_date, "metric": round(row[1], 0)})
        
        results.append(OrderDatewiseMetric(name=METRIC_NAME_MAPPING["new_transacting_users_count"],
                                            data=new_user_logins,
                                            type=METRICS_TYPE_MAPPING["new_transacting_users_count"]))
        return results


    def _get_city_wise_dau_metrics(self, 
                                   conn: connect, 
                                   past_days_diff: int = 7, 
                                   city: List = None, 
                                   date_str: str = None, 
                                   hour: str = None) -> dict:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {"past_days_diff": past_days_diff}
        if city:
            dynamic_where_clause.append(" AND city in (%(cities)s)")
            params["cities"] = city

        if hour:
            dynamic_where_clause.append(" AND hr IN (%(hour)s)")
            params["hour"] = hour
            query = get_today_hau_metrics_city_wise(" ".join(dynamic_where_clause),  
                                                    is_store_query=True if city else False, 
                                                    date_str=date_str)
            curs.execute(query , params)
        else:
            query = get_today_all_cities_dau_query(" ".join(dynamic_where_clause), 
                                                   is_store_query=True if city else False, 
                                                   date_str=date_str)
            curs.execute(query, params)

        results_dau = defaultdict(lambda: defaultdict(int))
        window_end = []

        for row in curs:
            if city:
                results_dau[row[0]][row[5]] += row[2]
            else:
                results_dau[row[0]][row[1]] += row[2]
            window_end.append(row[3])
        max_window_end = max(window_end) if window_end else "now()"
        results_dau_conversion = defaultdict(lambda: defaultdict(int))
        dynamic_where_clause = []
        params = {"max_window_end": max_window_end, "past_days_diff": past_days_diff}
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if hour:
            dynamic_where_clause.append("AND hour(insert_timestamp, 'Asia/Kolkata') IN (%(hour)s)")
            params["hour"] = hour
        query = get_transacting_user_count_city_wise_query(dynamic_where_clause=" ".join(dynamic_where_clause), 
                                                                is_store_query=True if city else False,
                                                                date_str=date_str,
                                                                hour=hour)
        curs.execute(query, params)
        for row in curs:
            if city:
                dau = results_dau.get(row[1], {}).get(row[3], None)
                if dau:
                    # If dau is not present in results, we are skipping for that city/date
                    dau_conversion = round((row[2] / dau) * 100, 2)
                    results_dau_conversion[row[1]][ row[3]] = dau_conversion
            else:
                dau = results_dau.get(row[1], {}).get(row[0], None)
                if dau:
                    # If dau is not present in results, we are skipping for that city/date
                    dau_conversion = round((row[2] / dau) * 100, 2)
                    results_dau_conversion[row[1]][row[0]] = dau_conversion
        
        return results_dau, results_dau_conversion


    def get_city_wise_current_date_metrics(self, 
                                           conn: connect, 
                                           past_days_diff: int = 7, 
                                           city: List = None, 
                                           date_str: str = None, 
                                           hour: str = None) -> Union[StoreDateMetrics, LocationDateMetrics]:
        curs = conn.cursor()
        city_wise_dau_results, city_wise_dau_conversion_results = self._get_city_wise_dau_metrics(conn,
                                                                                                  past_days_diff=past_days_diff, 
                                                                                                  city=city, 
                                                                                                  date_str=date_str, 
                                                                                                  hour=hour)

        dynamic_where_clause = []
        params = {"past_days_diff": past_days_diff}
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if hour:
            dynamic_where_clause.append("AND order_hour IN (%(hour)s)")
            params["hour"] = hour
        
        query = get_all_order_metrics_city_wise_query(dynamic_where_clause=" ".join(dynamic_where_clause), 
                                                      is_store_level=True if city else False, 
                                                      date_str=date_str, hour=hour)
        curs.execute(query, params) 
        if city is None:
            results = []
            citywise_results = defaultdict(list)
            for row in curs:
                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["gmv"], metric=row[2], type=METRICS_TYPE_MAPPING["gmv"]),
                    Metric(name=METRIC_NAME_MAPPING["order_count"], metric=row[3], type=METRICS_TYPE_MAPPING["order_count"]),
                    Metric(name=METRIC_NAME_MAPPING["cancellation_percentage"], metric=round(row[4], 2), type=METRICS_TYPE_MAPPING["cancellation_percentage"]),
                    Metric(name=METRIC_NAME_MAPPING["aov"], metric=round(row[5], 2), type=METRICS_TYPE_MAPPING["aov"]),
                    Metric(name=METRIC_NAME_MAPPING["dau"], metric=city_wise_dau_results.get(row[1], {}).get(row[0],0), type=METRICS_TYPE_MAPPING["dau"]),
                    Metric(name=METRIC_NAME_MAPPING["dau_conversion_percentage"], metric=city_wise_dau_conversion_results.get(row[1], {}).get(row[0],0), type=METRICS_TYPE_MAPPING["dau_conversion_percentage"]),
                    Metric(name=METRIC_NAME_MAPPING["percentage_orders_delivered_in_15mins"], metric=round(row[6], 2), type=METRICS_TYPE_MAPPING["percentage_orders_delivered_in_15mins"]),
                ]
                citywise_results[row[1]].append(LocationMetric(type="city", name=row[0], data=metrics))
            for dt_str, result in citywise_results.items():
                results.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return LocationDateMetrics(metrics = results)
        else:
            results = []
            storewise_results = defaultdict(list)
            for row in curs:
                # Handle NaN and infinity values
                percentage_orders_delivered_in_15mins_metric_value = row[7]
                if isinstance(percentage_orders_delivered_in_15mins_metric_value, (float, int)) and not math.isnan(percentage_orders_delivered_in_15mins_metric_value) and not math.isinf(percentage_orders_delivered_in_15mins_metric_value):
                    percentage_orders_delivered_in_15mins_metric_value = round(float(percentage_orders_delivered_in_15mins_metric_value), 2)
                else:
                    percentage_orders_delivered_in_15mins_metric_value = '-'  # Replace NaN or infinity with -

                metrics = [
                    Metric(name=METRIC_NAME_MAPPING["gmv"], metric=row[3], type=METRICS_TYPE_MAPPING["gmv"]),
                    Metric(name=METRIC_NAME_MAPPING["order_count"], metric=row[4], type=METRICS_TYPE_MAPPING["order_count"]),
                    Metric(name=METRIC_NAME_MAPPING["cancellation_percentage"], metric=round(row[5], 2), type=METRICS_TYPE_MAPPING["cancellation_percentage"]),
                    Metric(name=METRIC_NAME_MAPPING["aov"], metric=round(row[6], 2), type=METRICS_TYPE_MAPPING["aov"]),
                    Metric(name=METRIC_NAME_MAPPING["dau"], metric=city_wise_dau_results.get(row[2], {}).get(row[0],0), type=METRICS_TYPE_MAPPING["dau"]),
                    Metric(name=METRIC_NAME_MAPPING["dau_conversion_percentage"], metric=city_wise_dau_conversion_results.get(row[2], {}).get(row[0],0), type=METRICS_TYPE_MAPPING["dau_conversion_percentage"]),
                    Metric(name=METRIC_NAME_MAPPING["percentage_orders_delivered_in_15mins"], metric=percentage_orders_delivered_in_15mins_metric_value, type=METRICS_TYPE_MAPPING["percentage_orders_delivered_in_15mins"]),
                ]
                storewise_results[row[2]].append(StoreMetric(frontend_merchant_name=row[1], frontend_merchant_id=str(row[0]), data=metrics))
            for dt_str, result in storewise_results.items():
                results.append(StoreDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return StoreDateMetrics(metrics = results)
        
    def get_ath_metrics(self, conn: connect, city: List = None):
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if city:
            dynamic_where_clause.append("city in (%(cities)s)")
            params["cities"] = city
        query = get_bistro_ath_metrics(dynamic_where_clause=" ".join(dynamic_where_clause))
        curs.execute(query, params)
        metrics_data: Dict[str, Dict[str, Union[float, str]]] = defaultdict(lambda: {"sum": 0, "max_date": None})
        results = []

        for row in curs:
            order_date = row[3]
            metric_value = row[0]
            metric_name = row[1]
            metrics_data[metric_name]["sum"] += metric_value
            current_max_date = metrics_data[metric_name]["max_date"]
            if current_max_date is None or order_date > current_max_date:
                metrics_data[metric_name]["max_date"] = order_date

        if metrics_data["ath_gmv"]["max_date"] is not None:
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING["ath_gmv"],
                    data=[{"date": metrics_data["ath_gmv"]["max_date"], "metric": metrics_data["ath_gmv"]["sum"]}],
                    type=METRICS_TYPE_MAPPING["ath_gmv"]
                )
            )

        if metrics_data["ath_order_count"]["max_date"] is not None:
            results.append(
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING["ath_order_count"],
                    data=[{"date": metrics_data["ath_order_count"]["max_date"], "metric": metrics_data["ath_order_count"]["sum"]}],
                    type=METRICS_TYPE_MAPPING["ath_order_count"]
                )
            )
        return results


order_metrics = CRUDOrderMetrics()
