from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math

from pinotdb import connect
from app.api import deps, utils

from app.core.metric_config_bistro import RATING_METRICS_NAME_MAPPING, RATING_METRICS_TYPE_MAPPING
from .queries.rating_metrics import get_ratings_query, build_rating_lookup_filters, get_ratings_metrics_city_wise_query, get_station_ratings_query


IST = timezone(timedelta(hours=5, minutes=30))
from app.schemas.product_metric import ProductDatewiseMetric, CategoryProductDatewiseMetric, DatewiseMetric, RatingsDateMetricItemWise
from app.schemas.order_metric import OrderDatewiseMetric, StoreDateMetric, StoreDateMetrics, LocationDateMetrics, Metric, StoreMetric, LocationMetric, LocationDateMetric

class CRUDRatingMetrics():

    def rating_metrics(self, conn: connect, 
                   l0_category: List = None,
                   l1_category: List = None,
                   l2_category: List = None,
                   ptype: List = None,
                   pname: List = None,
                   brand: List = None,
                   yesterday_metric: bool = False,
                   is_daywise: bool = False,
                   current_hour: bool = True,
                   pid: List = None, 
                   city: List = None,
                   store: List = None,
                   date_str: str = None,
                   category_wise: bool = False,) -> Union[List[RatingsDateMetricItemWise], List[OrderDatewiseMetric]]:
        curs = conn.cursor()

        rating_filters_dict = {
            "l0_category": {
                "value": l0_category,
                "type": "join",
                "lookup_table": "product_l0_l1_l2_mapping_v1",
                "lookup_column": "l0_category"
            },
            "l1_category": {
                "value": l1_category,
                "type": "join",
                "lookup_table": "product_l0_l1_l2_mapping_v1",
                "lookup_column": "l1_category"
            },
            "l2_category": {
                "value": l2_category,
                "type": "join",
                "lookup_table": "product_l0_l1_l2_mapping_v1",
                "lookup_column": "l2_category"
            },
            "ptype": {
                "value": ptype,
                "type": "join",
                "lookup_table": "product_l0_l1_l2_mapping_v1",
                "lookup_column": "product_type"
            },
            "pname": {
                "value": pname,
                "type": "join",
                "lookup_table": "product_l0_l1_l2_mapping_v1",
                "lookup_column": "product_name"
            },
            "pid": {
                "value": pid,
                "type": "simple",
                "main_column": "product_id",
            },
            "brand": {
                "value": brand,
                "type": "join",
                "lookup_table": "product_l0_l1_l2_mapping_v1",
                "lookup_column": "brand_name"
            },
            "city": {
                "value": city,
                "type": "subquery",
                "main_column": "merchant_id",
                "lookup_table": "merchant_outlet_facility_mapping_v2",
                "lookup_column": "frontend_merchant_city_name",
                "lookup_pk": "frontend_merchant_id",
                "lookup_cast_type": "VARCHAR"
            },
            "store": {
                "value": store,
                "type": "simple",
                "main_column": "merchant_id",
            }
        }

        dynamic_where_clause, params = build_rating_lookup_filters(rating_filters_dict)
        
        has_product_filter = any([l0_category, l1_category, l2_category, ptype, pname, brand, pid])
        has_merchant_filter = any([city, store])

        query = get_ratings_query(
            dynamic_where_clause=" ".join(dynamic_where_clause),
            yesterday_metric=yesterday_metric,
            is_daywise=is_daywise,
            current_hour=current_hour,
            category_wise=category_wise,
            has_product_filter=has_product_filter,
            has_merchant_filter=has_merchant_filter,
            date_str=date_str
        )
        curs.execute(query, params)

        if category_wise:
            return self._process_category_wise_ratings(curs)
        else:
            return self._process_overall_ratings(curs)


    def _process_category_wise_ratings(self, curs):
        metrics_data = defaultdict(lambda: defaultdict(list))
        ratings_summary = defaultdict(lambda: defaultdict(lambda: {"total_count": 0, "sum_ratings": 0, "ratings": defaultdict(int)}))
        
        for row in curs:
            rating_date = datetime.strptime(row[0], "%Y-%m-%d")
            category_type = row[1]
            rating_value = int(row[2])
            rating_count = int(row[3])
            summary = ratings_summary[category_type][rating_date]
            summary["total_count"] += rating_count
            summary["sum_ratings"] += rating_value * rating_count
            summary["ratings"][rating_value] += rating_count

        return self._format_category_wise_metrics(metrics_data, ratings_summary)


    def _process_overall_ratings(self, curs):
        metrics_data = defaultdict(list)
        ratings_summary = defaultdict(lambda: {"total_count": 0, "sum_ratings": 0, "ratings": defaultdict(int)})
        
        for row in curs:
            rating_date = datetime.strptime(row[0], "%Y-%m-%d")
            rating_value = int(row[1])
            rating_count = int(row[2])
            summary = ratings_summary[rating_date]
            summary["total_count"] += rating_count
            summary["sum_ratings"] += rating_value * rating_count
            summary["ratings"][rating_value] += rating_count
        
        return self._format_overall_metrics(metrics_data, ratings_summary)


    def _format_category_wise_metrics(self, metrics_data, ratings_summary):
        for category, date_metrics in ratings_summary.items():
            for rating_date, summary in date_metrics.items():
                total_count = summary["total_count"]
                if total_count == 0:
                    continue
                
                average_rating = round(summary["sum_ratings"] / total_count, 2)
                metrics_data[category]["total_count"].append(DatewiseMetric(date=rating_date, metric=total_count))
                metrics_data[category]["average_rating"].append(DatewiseMetric(date=rating_date, metric=average_rating))
                
                for rating in range(1, 6):
                    if summary["ratings"][rating] > 0:
                        metrics_data[category][f"rating_{rating}_count"].append(
                            DatewiseMetric(date=rating_date, metric=summary["ratings"][rating])
                        )

        category_results = []
        for category, _ in metrics_data.items():
            results = []
            for metric_name in metrics_data[category].keys():
                results.append(ProductDatewiseMetric(
                    name=RATING_METRICS_NAME_MAPPING[metric_name],
                    data=metrics_data[category][metric_name],
                    type=RATING_METRICS_TYPE_MAPPING[metric_name]
                ))
            category_results.append(
                CategoryProductDatewiseMetric(
                    item_name=category,
                    data=results
                )
            )
        
        return category_results


    def _format_overall_metrics(self, metrics_data, ratings_summary):
        for rating_date, summary in ratings_summary.items():
            total_count = summary["total_count"]
            if total_count == 0:
                continue
            
            average_rating = round(summary["sum_ratings"] / total_count, 2)
            metrics_data["total_count"].append(DatewiseMetric(date=rating_date, metric=total_count))
            metrics_data["average_rating"].append(DatewiseMetric(date=rating_date, metric=average_rating))
            
            for rating in range(1, 6):
                if summary["ratings"][rating] > 0:
                    metrics_data[f"rating_{rating}_count"].append(
                        DatewiseMetric(date=rating_date, metric=summary["ratings"][rating])
                    )

        results = []
        for metric_name in metrics_data.keys():
            results.append(OrderDatewiseMetric(
                name=RATING_METRICS_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=RATING_METRICS_TYPE_MAPPING[metric_name]
            ))
        
        return results

    
    def get_city_wise_ratings_metrics(self, 
                                conn: connect, 
                                past_days_diff: int = 7, 
                                city: List = None, 
                                hour: str = None,
                                date_str: str = None,) -> Union[StoreDateMetrics, LocationDateMetrics]:
        curs = conn.cursor()
        
        params = {"past_days_diff": past_days_diff}
        dynamic_where_clause = []

        rating_filters_dict = {
            "city": {
                "value": city,
                "type": "join",
                "lookup_table": "merchant_outlet_facility_mapping_v2",
                "lookup_column": "frontend_merchant_city_name"
            },
            "hour": {
                "value": hour,
                "type": "join",
                "lookup_table": "bistro_fact_order_details_v2",
                "lookup_column": "order_hour"
            },
        }
        dynamic_where_clause, params = build_rating_lookup_filters(rating_filters_dict)

        query = get_ratings_metrics_city_wise_query(
            date_str=date_str,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            is_store_level=True if city else False,
            hour=hour,
            past_days_diff=past_days_diff
        )
        
        curs.execute(query, params)
        
        if city is None:
            results = []
            citywise_results = defaultdict(list)
            
            for row in curs:
                date_str = row[0]
                city_name = row[1]
                avg_rating = round(float(row[2]), 2) if row[2] is not None else None
                total_ratings = int(row[3]) if row[3] is not None else 0
                rating_1_count = int(row[4]) if row[4] is not None else 0
                rating_2_count = int(row[5]) if row[5] is not None else 0
                rating_3_count = int(row[6]) if row[6] is not None else 0
                rating_4_count = int(row[7]) if row[7] is not None else 0
                rating_5_count = int(row[8]) if row[8] is not None else 0
                
                metrics = [
                    Metric(name=RATING_METRICS_NAME_MAPPING["average_rating"], metric=avg_rating, type=RATING_METRICS_TYPE_MAPPING["average_rating"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["total_count"], metric=total_ratings, type=RATING_METRICS_TYPE_MAPPING["total_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_1_count"], metric=rating_1_count, type=RATING_METRICS_TYPE_MAPPING["rating_1_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_2_count"], metric=rating_2_count, type=RATING_METRICS_TYPE_MAPPING["rating_2_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_3_count"], metric=rating_3_count, type=RATING_METRICS_TYPE_MAPPING["rating_3_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_4_count"], metric=rating_4_count, type=RATING_METRICS_TYPE_MAPPING["rating_4_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_5_count"], metric=rating_5_count, type=RATING_METRICS_TYPE_MAPPING["rating_5_count"])
                ]
                
                citywise_results[date_str].append(LocationMetric(type="city", name=city_name, data=metrics))
            
            for dt_str, result in citywise_results.items():
                dt_obj = datetime.strptime(dt_str, "%Y-%m-%d")
                date_diff = (datetime.now(IST) - dt_obj.replace(tzinfo=IST)).days
                results.append(LocationDateMetric(date=dt_obj, etl_snapshot_ts_ist=None, date_diff=date_diff, data=result))
            
            return LocationDateMetrics(metrics=results)

        else:
            results = []
            storewise_results = defaultdict(list)
            
            for row in curs:
                date_str = row[0]
                store_id = row[1]
                store_name = row[2]
                avg_rating = round(float(row[3]), 2) if row[3] is not None else None
                total_ratings = int(row[4]) if row[4] is not None else 0
                rating_1_count = int(row[5]) if row[5] is not None else 0
                rating_2_count = int(row[6]) if row[6] is not None else 0
                rating_3_count = int(row[7]) if row[7] is not None else 0
                rating_4_count = int(row[8]) if row[8] is not None else 0
                rating_5_count = int(row[9]) if row[9] is not None else 0
                
                metrics = [
                    Metric(name=RATING_METRICS_NAME_MAPPING["average_rating"], metric=avg_rating, type=RATING_METRICS_TYPE_MAPPING["average_rating"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["total_count"], metric=total_ratings, type=RATING_METRICS_TYPE_MAPPING["total_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_1_count"], metric=rating_1_count, type=RATING_METRICS_TYPE_MAPPING["rating_1_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_2_count"], metric=rating_2_count, type=RATING_METRICS_TYPE_MAPPING["rating_2_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_3_count"], metric=rating_3_count, type=RATING_METRICS_TYPE_MAPPING["rating_3_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_4_count"], metric=rating_4_count, type=RATING_METRICS_TYPE_MAPPING["rating_4_count"]),
                    Metric(name=RATING_METRICS_NAME_MAPPING["rating_5_count"], metric=rating_5_count, type=RATING_METRICS_TYPE_MAPPING["rating_5_count"])
                ]

                storewise_results[date_str].append(StoreMetric(
                    frontend_merchant_name=store_name,
                    frontend_merchant_id=str(store_id),
                    data=metrics
                ))
            
            for dt_str, result in storewise_results.items():
                dt_obj = datetime.strptime(dt_str, "%Y-%m-%d")
                date_diff = (datetime.now(IST) - dt_obj.replace(tzinfo=IST)).days
                results.append(StoreDateMetric(date=dt_obj, etl_snapshot_ts_ist=None, date_diff=date_diff, data=result))
            
            return StoreDateMetrics(metrics=results)

    def get_station_ratings_metrics(self, conn: connect, 
                            city: List = None,
                            store: List = None,
                            station: List = None,
                            yesterday_metric: bool = False,
                            date_str: str = None) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()

        station_filters_dict = {
            "store": {
                "value": store,
                "type": "simple",
                "main_column": "merchant_id",
            },
            "city": {
                "value": city,
                "type": "subquery",
                "main_column": "merchant_id",
                "lookup_table": "merchant_outlet_facility_mapping_v2",
                "lookup_column": "frontend_merchant_city_name",
                "lookup_pk": "frontend_merchant_id",
                "lookup_cast_type": "VARCHAR"
            },
            "station": {
                "value": station,
                "type": "join",
                "lookup_table": "analytics_bistro_product_station_mapping_v2",
                "lookup_column": "station_id"
            }
        }

        dynamic_where_clause, params = build_rating_lookup_filters(station_filters_dict)
        
        query = get_station_ratings_query(
            dynamic_where_clause=" ".join(dynamic_where_clause),
            yesterday_metric=yesterday_metric,
            date_str=date_str,
        )
        curs.execute(query, params)
        
        return self._process_overall_ratings(curs)

rating_metrics = CRUDRatingMetrics()
