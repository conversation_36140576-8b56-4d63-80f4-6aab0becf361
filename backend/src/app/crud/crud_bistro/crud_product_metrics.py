from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math

from pinotdb import connect
from app.api import deps, utils

from app.core.metric_config_bistro import PRODUCT_METRICS_CALC_MAPPING, METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING
from .queries.product_metrics import (
    get_all_product_metrics_query,
    get_all_product_metrics_without_item_grouping_query,
    get_distinct_cart_ids_in_whole_city_store_query,
    get_complaints_query,
    get_all_field_list_query,
    get_filtered_product_fieldwise_query
)


IST = timezone(timedelta(hours=5, minutes=30))
from app.schemas.product_metric import ProductDatewiseMetric, CategoryProductDatewiseMetric, ComplaintsDateMetric, FilteredProductFieldwise, CityAllMetrics, DateAllCityMetrics, DatewiseHourMetrics, HourMetrics, DatewiseMetric, ProductMetric, ComplaintsDateMetricItemWise, RatingsDateMetricItemWise, ComplaintsDateMetricTypeWise
from app.schemas.order_metric import OrderDatewiseMetric

class CRUDProductMetrics():

    def _build_lookup_filters(self, filters_dict):
        dynamic_where_clause = []
        params = {}
        
        for field_name, (value, is_lookup, lookup_table, lookup_column_output, lookup_table_pk, current_table_lookup_colm) in filters_dict.items():
            if value:  # Only add filter if value is provided
                if is_lookup:
                    dynamic_where_clause.append(
                        f"AND LOOKUP('{lookup_table}', '{lookup_column_output}', '{lookup_table_pk}', {current_table_lookup_colm}) IN (%({field_name})s)"
                    )
                else:
                    dynamic_where_clause.append(f"AND {current_table_lookup_colm} IN (%({field_name})s)")
                
                params[field_name] = value

        return dynamic_where_clause, params


    def get_all_metrics(self, conn: connect, 
                        metrics: List = None, 
                        l0_category: List = ['Bistro'], # NOTE: Hardcoded here 
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        yesterday_metric: bool = False,
                        is_daywise: bool = False,
                        current_hour: bool = True,
                        date_str: str = None,
                        pid: List = None) -> List[CategoryProductDatewiseMetric]:
        curs = conn.cursor()

        filters_dict = {
            "city": (city, True, "merchant_outlet_facility_mapping_v2", "frontend_merchant_city_name", "frontend_merchant_id", "merchant_id"),
            "store": (store, False, None, None, None, "merchant_id"),
        }
        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)

        distinct_cart_ids_query = get_distinct_cart_ids_in_whole_city_store_query(
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric,
                                            is_daywise=is_daywise,
                                            current_hour=current_hour)
        curs.execute(distinct_cart_ids_query, params)

        total_cart_ids: Dict[str, int] = defaultdict(int)
        for row in curs:
            total_cart_ids[row[0]] = row[1]

        curs.close()


        curs = conn.cursor()
        category_results = []

        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]

        filters_dict = {
            "l0_category": (l0_category, True, "product_l0_l1_l2_mapping_v1", "l0_category", "product_id", "item_product_id"),
            "l1_category": (l1_category, True, "product_l0_l1_l2_mapping_v1", "l1_category", "product_id", "item_product_id"),
            "l2_category": (l2_category, True, "product_l0_l1_l2_mapping_v1", "l2_category", "product_id", "item_product_id"),
            "ptype": (ptype, True, "product_l0_l1_l2_mapping_v1", "product_type", "product_id", "item_product_id"),
            "pname": (pname, True, "product_l0_l1_l2_mapping_v1", "product_name", "product_id", "item_product_id"),
            "brand": (brand, True, "product_l0_l1_l2_mapping_v1", "brand_name", "product_id", "item_product_id"),
            "city": (city, True, "merchant_outlet_facility_mapping_v2", "frontend_merchant_city_name", "frontend_merchant_id", "merchant_id"),
            "store": (store, False, None, None, None, "merchant_id"),
            "pid": (pid, False, None, None, None, "item_product_id"),
        }
        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)
        
        query = get_all_product_metrics_query(metrics_sql = metrics_sql,
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            yesterday_metric=yesterday_metric,
                                            is_daywise=is_daywise,
                                            current_hour=current_hour,
                                            date_str=date_str)
        curs.execute(query, params)

        metrics_data: Dict[str, Dict[str, List[Dict[str, float]]]] = defaultdict(lambda: defaultdict(list))

        for row in curs:
            created_date = datetime.strptime(row[0], "%Y-%m-%d")
            category = row[1]
            for idx, (metric_name, _) in enumerate(metrics_items, start=2):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[category][metric_name].append(DatewiseMetric(date=created_date, metric=metric_value))
        
        for category, _ in metrics_data.items():
            results = []
            for metric_name, _ in metrics_items:
                results.append(ProductDatewiseMetric(
                    name=METRIC_NAME_MAPPING[metric_name],
                    data=metrics_data[category][metric_name],
                    type=METRICS_TYPE_MAPPING[metric_name]
                ))
            results = self._convert_unique_cart_to_cart_pen(results, total_cart_ids)
            category_results.append(
                CategoryProductDatewiseMetric(
                    item_name=category,
                    data=results
                )
            )

        return category_results


    def _convert_unique_cart_to_cart_pen(self, results, total_cart_ids):
        for result in results:
            if result.name == 'Unique Carts':
                for metric in result.data:
                    date_str = metric.date.strftime('%Y-%m-%d')
                    if date_str in total_cart_ids and total_cart_ids[date_str] != 0:
                        metric.metric = round((metric.metric / total_cart_ids[date_str]) * 100, 1)
                    else:
                        continue
                result.name = METRIC_NAME_MAPPING["cart_pen"]
                result.type = METRICS_TYPE_MAPPING["cart_pen"]
        return results
    

    def complaints_metrics(self, conn: connect, 
                        l0_category: List = None,
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        yesterday_metric: bool = False,
                        is_daywise: bool = False,
                        current_hour: bool = True,
                        pid: List = None, 
                        date_str: str = None,
                        category_wise: bool = False,) -> Union[List[ComplaintsDateMetricItemWise], List[ComplaintsDateMetricTypeWise]]:
        curs = conn.cursor()

        filters_dict = {
            "l0_category": (l0_category, True, "product_l0_l1_l2_mapping_v1", "l0_category", "product_id", "product_id"),
            "l1_category": (l1_category, True, "product_l0_l1_l2_mapping_v1", "l1_category", "product_id", "product_id"),
            "l2_category": (l2_category, True, "product_l0_l1_l2_mapping_v1", "l2_category", "product_id", "product_id"),
            "ptype": (ptype, True, "product_l0_l1_l2_mapping_v1", "product_type", "product_id", "product_id"),
            "pname": (pname, True, "product_l0_l1_l2_mapping_v1", "product_name", "product_id", "product_id"),
            "brand": (brand, True, "product_l0_l1_l2_mapping_v1", "brand_name", "product_id", "product_id"),
            "city": (city, True, "outlet_facility_mapping_v1", "city_name", "outlet_id", "store_id"),
            "store": (store, True, "outlet_facility_mapping_v1", "merchant_id", "outlet_id", "store_id"),
            "pid": (pid, False, None, None, None, "product_id"),
        }

        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)

        dynamic_where_clause.append("AND tenant IN (%(tenant)s)")
        params["tenant"] = "BISTRO"

        query = get_complaints_query(dynamic_where_clause=" ".join(dynamic_where_clause),
                                     yesterday_metric=yesterday_metric,
                                     is_daywise=is_daywise,
                                     current_hour=current_hour, 
                                     date_str=date_str,
                                     category_wise=category_wise)
        curs.execute(query, params)

        complaints_daywise_metrics = []

        if category_wise:
            for row in curs:
                complaint_date = datetime.strptime(row[0], "%Y-%m-%d")
                category_type = row[1]
                complaint_type = row[2]
                complaint_count = round(float(row[3]), 2)
                complaints_daywise_metrics.append(ComplaintsDateMetricItemWise(
                    complaint_type=complaint_type,
                    item_name=category_type,
                    date=complaint_date,
                    count=complaint_count
                ))
        else:
            for row in curs:
                complaint_date = datetime.strptime(row[0], "%Y-%m-%d")
                complaint_type = row[1]
                complaint_count = round(float(row[2]), 2)
                complaints_daywise_metrics.append(ComplaintsDateMetricTypeWise(
                    complaint_type=complaint_type,
                    date=complaint_date,
                    count=complaint_count
                ))

        return complaints_daywise_metrics
        
    
    def get_write_ahead_regex_expression(self, field_values: List) -> str:
        regex_exp = ""
        # '.*abc.*|.*def.*'  - Match anywhere in string
        for idx, val in enumerate(field_values):
            regex_exp += f'{"" if idx == 0 else "|"}.*{val}.*'
        return regex_exp


    def get_all_field_list(self, conn: connect,
                           search_filter: List = None):
        """
        Get complete list of all fields in search_filter directly from product_l0_l1_l2_mapping_v1 table
        """
        curs = conn.cursor()
        
        result = []
        for field in search_filter:
            if field not in PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING:
                continue
            db_field_name = PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING[field]
            query = get_all_field_list_query(field=db_field_name,
                                             table_name="product_l0_l1_l2_mapping_v1")
            curs.execute(query)

            all_field_list = []
            for row in curs:
                if row[0] is not None:
                    all_field_list.append(row[0])
            result.append(FilteredProductFieldwise(field=field, filtered_values=all_field_list))

        return result
    
    
    def get_metrics_without_item_grouping(self, conn: connect,
                        metrics: List = None,
                        l0_category: List = ['Bistro'], # NOTE: Hardcoded here
                        l1_category: List = None,
                        l2_category: List = None,
                        ptype: List = None,
                        pname: List = None,
                        brand: List = None,
                        city: List = None,
                        store: List = None,
                        yesterday_metric: bool = False,
                        is_daywise: bool = False,
                        current_hour: bool = True,
                        date_str: str = None,
                        pid: List = None) -> List[OrderDatewiseMetric]:
        """
        Get product metrics without grouping by item, aggregated across all items
        """
        curs = conn.cursor()

        filters_dict = {
            "l0_category": (l0_category, True, "product_l0_l1_l2_mapping_v1", "l0_category", "product_id", "item_product_id"),
            "l1_category": (l1_category, True, "product_l0_l1_l2_mapping_v1", "l1_category", "product_id", "item_product_id"),
            "l2_category": (l2_category, True, "product_l0_l1_l2_mapping_v1", "l2_category", "product_id", "item_product_id"),
            "ptype": (ptype, True, "product_l0_l1_l2_mapping_v1", "product_type", "product_id", "item_product_id"),
            "pname": (pname, True, "product_l0_l1_l2_mapping_v1", "product_name", "product_id", "item_product_id"),
            "brand": (brand, True, "product_l0_l1_l2_mapping_v1", "brand_name", "product_id", "item_product_id"),
            "city": (city, True, "merchant_outlet_facility_mapping_v2", "frontend_merchant_city_name", "frontend_merchant_id", "merchant_id"),
            "store": (store, False, None, None, None, "merchant_id"),
            "pid": (pid, False, None, None, None, "item_product_id"),
        }
        dynamic_where_clause, params = self._build_lookup_filters(filters_dict)
        
        selected_sql = [PRODUCT_METRICS_CALC_MAPPING[key] for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, PRODUCT_METRICS_CALC_MAPPING[key]) for key in metrics if key in PRODUCT_METRICS_CALC_MAPPING]
        
        query = get_all_product_metrics_without_item_grouping_query(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            yesterday_metric=yesterday_metric,
            is_daywise=is_daywise,
            current_hour=current_hour,
            date_str=date_str
        )
        
        curs.execute(query, params)
        
        metrics_data = defaultdict(list)
        
        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[metric_name].append({"date": order_date, "metric": metric_value})
        
        results = [
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=METRICS_TYPE_MAPPING[metric_name]
            )
            for metric_name, _ in metrics_items
        ]
        
        return results

    def get_filtered_product_fieldwise(self, conn: connect,
                                        l0_category: List = None,
                                        l1_category: List = None,
                                        l2_category: List = None,
                                        ptype: List = None,
                                        pname: List = None,
                                        brand: List = None,
                                        search_filter: str = None,
                                        pid: List = None) -> FilteredProductFieldwise:
        """
        Get search_filter field value based on filters directly from product_l0_l1_l2_mapping_v1 table
        """
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        
        # Apply filters directly to the product_l0_l1_l2_mapping_v1 table fields
        if l0_category:
            regex_exp = self.get_write_ahead_regex_expression(l0_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l0_category, (%(l0_category)s), 'i')")
            params["l0_category"] = regex_exp
        if l1_category:
            regex_exp = self.get_write_ahead_regex_expression(l1_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l1_category, (%(l1_category)s), 'i')")
            params["l1_category"] = regex_exp
        if l2_category:
            regex_exp = self.get_write_ahead_regex_expression(l2_category)
            dynamic_where_clause.append("AND REGEXP_LIKE(l2_category, (%(l2_category)s), 'i')")
            params["l2_category"] = regex_exp
        if ptype:
            regex_exp = self.get_write_ahead_regex_expression(ptype)
            dynamic_where_clause.append("AND REGEXP_LIKE(product_type, (%(ptype)s), 'i')")
            params["ptype"] = regex_exp
        if pname:
            regex_exp = self.get_write_ahead_regex_expression(pname)
            dynamic_where_clause.append("AND REGEXP_LIKE(product_name, (%(pname)s), 'i')")
            params["pname"] = regex_exp
        if brand:
            regex_exp = self.get_write_ahead_regex_expression(brand)
            dynamic_where_clause.append("AND REGEXP_LIKE(brand_name, (%(brand)s), 'i')")
            params["brand"] = regex_exp
        if pid:
            regex_exp = self.get_write_ahead_regex_expression(pid)
            dynamic_where_clause.append("AND REGEXP_LIKE(CAST(product_id AS STRING), (%(pid)s), 'i')")
            params["pid"] = regex_exp

        if search_filter not in PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING:
            return FilteredProductFieldwise(field = search_filter, filtered_values=[])
        db_search_filter_name = PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING[search_filter]
        
        dynamic_where_clause.append("AND l0_category = 'Bistro'")
        dynamic_where_clause.append("AND l1_category NOT IN ('Ingredients')")

        where_clause = ""
        if len(dynamic_where_clause) != 0:
            where_clause = " ".join(dynamic_where_clause)[4:]
        
        query = get_filtered_product_fieldwise_query(dynamic_where_clause=where_clause,
                                                      search_filter=db_search_filter_name,
                                                      table_name="product_l0_l1_l2_mapping_v1")
        curs.execute(query, params)

        filtered_values = []

        for row in curs:
            if row[0] is not None:
                filtered_values.append(row[0])
        
        return FilteredProductFieldwise(field = search_filter,
                                        filtered_values = filtered_values)


product_metrics = CRUDProductMetrics()
