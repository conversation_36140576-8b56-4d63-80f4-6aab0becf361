from datetime import datetime, timedelta, timezone
from collections import defaultdict
from typing import List

from pinotdb import connect
from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING
from .queries.hau_metrics import get_transacting_user_count_hau_conversion_query, get_today_hau_metrics_city_wise, get_yesterday_hau_metrics, get_order_metrics_hourly_query


from app.schemas.order_metric import HourMetric, HourlyDateMetric, HourlyDateMetrics, Metric

IST = timezone(timedelta(hours=5, minutes=30))

class CRUDHAUMetrics():

    def _calculate_hau_conversion(self, dt_hourly_results, curs, cities, max_window_end, date_str=None, app=None, store=None):
        hau_datewise_results = []
        metrics_calc_sql = METRICS_CALC_MAPPING["transacting_users_count"]
        dynamic_where_clause = []
        params = {}
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if cities:
            params["cities"] = cities
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
        if store:
            params["stores"] = store
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")

        curs.execute(
            get_transacting_user_count_hau_conversion_query(
                    metrics_sql=metrics_calc_sql,
                    max_window_end=max_window_end,
                    dynamic_where_clause=" ".join(dynamic_where_clause),
                    date_str=date_str), 
            params
        )
        
        transacting_user_count_hourly_results = defaultdict(lambda: defaultdict(int))
        for row in curs:
            transacting_user_count_hourly_results[row[1]][row[0]] += row[2]
            
        for dt_str, hourly_metric in dt_hourly_results.items():
            hourly_results = []
            for hr, metric in hourly_metric.items():
                transacting_user_count_hourly = transacting_user_count_hourly_results[dt_str][int(hr)]
                if metric == 0:
                    hau_conversion_metric = 0
                else:
                    hau_conversion_metric = round((transacting_user_count_hourly / metric) * 100, 2)
                hourly_results.append(
                    HourMetric(hour=int(hr), data=[
                        Metric(
                            name=METRIC_NAME_MAPPING["hau"],
                            metric=metric,
                            type=METRICS_TYPE_MAPPING["hau"]),
                        Metric(
                            name=METRIC_NAME_MAPPING["hau_conversion_percentage"],
                            metric=hau_conversion_metric,
                            type=METRICS_TYPE_MAPPING["hau_conversion_percentage"])
                        ]
                    )
                )
            hau_datewise_results.append(
                HourlyDateMetric(
                    date=datetime.strptime(dt_str, "%Y-%m-%d"),
                    date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days,
                    data=hourly_results)
                )
        return hau_datewise_results


    def get_hourly_active_users_current_date_metrics(self, conn: connect, past_days_diff: int = 7, cities: List = None, app: str = None, store: str = None, date_str: str = None) -> HourlyDateMetrics:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {"past_days_diff": past_days_diff}
        if store:
            dynamic_where_clause.append(" AND merchant_id in (%(stores)s)")
            params["stores"] = store
        elif cities:
            dynamic_where_clause.append(" AND city in (%(cities)s)")
            params["cities"] = cities
        if app:
            dynamic_where_clause.append(" AND app like %(app)s")
            params["app"] = app
            
        curs.execute(
            get_today_hau_metrics_city_wise(
                " ".join(dynamic_where_clause),
                is_store_query=True if store else False,
                date_str=date_str
            ), 
            params
        )
        
        window_end = []
        dt_hourly_results = defaultdict(lambda: defaultdict(int))
        for row in curs:
            if store:
                dt_hourly_results[row[0]][int(row[6])] += row[2]
            else:
                dt_hourly_results[row[0]][row[5]] += row[2]
            window_end.append(row[3])
            
        if window_end:
            max_window_end = max(window_end)
            hau_datewise_results = self._calculate_hau_conversion(
                dt_hourly_results, 
                curs, 
                cities, 
                max_window_end, 
                date_str=date_str,
                store=store
            )
            return HourlyDateMetrics(metrics=hau_datewise_results)
        else:
            return HourlyDateMetrics(metrics=[])

    # This method can be removed since we're now handling all cases with get_hourly_active_users_current_date_metrics
    def get_yesterday_hourly_active_users_metrics(self, conn: connect, app: str = None, city: List = None, store: List = None) -> HourlyDateMetrics:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if app:
            dynamic_where_clause.append(" AND app like %(app)s")
            params["app"] = app

        if store:
            dynamic_where_clause.append(" AND merchant_id in (%(stores)s)")
            params["stores"] = store
        elif city:
            dynamic_where_clause.append("AND city in (%(cities)s)")
            params["cities"] = city
        curs.execute(get_yesterday_hau_metrics(" ".join(dynamic_where_clause), is_store_query=True if store else False), params)
        datewise_results = []
        dt_hourly_results = defaultdict(lambda: defaultdict(int))
        for row in curs:
            dt_hourly_results[row[0]][row[1]] += row[3]
        metrics_calc_sql = METRICS_CALC_MAPPING["transacting_users_count"]
        dynamic_where_clause = []
        params = {}
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")

        if store:
            dynamic_where_clause.append(" AND merchant_id in (%(stores)s)")
            params["stores"] = store
        elif city:
            dynamic_where_clause.append(" AND city_name in (%(cities)s)")
            params["cities"] = city
        curs.execute(get_order_metrics_hourly_query(metrics_sql=metrics_calc_sql, dynamic_where_clause=" ".join(dynamic_where_clause),yesterday_metric=True), params)
        transacting_user_count_hourly_results = defaultdict(lambda: defaultdict(int))
        for row in curs:
            transacting_user_count_hourly_results[row[1]][row[0]] += row[2]
        for dt_str, hourly_metric in dt_hourly_results.items():
            hourly_results = []
            for hr, metric in hourly_metric.items():
                transacting_user_count_hourly = transacting_user_count_hourly_results[dt_str][hr]
                if metric == 0:
                    hau_conversion_metric = 0
                else:
                    hau_conversion_metric = round((transacting_user_count_hourly / metric) * 100, 2)
                hourly_results.append(
                    HourMetric(hour=hr, 
                    data=[
                        Metric(
                            name=METRIC_NAME_MAPPING["hau"],
                            metric=metric, 
                            type=METRICS_TYPE_MAPPING["hau"]),
                        Metric(
                            name=METRIC_NAME_MAPPING["hau_conversion_percentage"],
                            metric=hau_conversion_metric,
                            type=METRICS_TYPE_MAPPING["hau_conversion_percentage"]),
                    ]
                )   
            )
            datewise_results.append(HourlyDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), date_diff=(datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=hourly_results))
        return HourlyDateMetrics(metrics=datewise_results)

hau_metrics = CRUDHAUMetrics()
