from datetime import datetime, timezone, timedelta
from collections import defaultdict
from typing import List, Union, Dict
import math

from pinotdb import connect
from .queries.station_metrics import get_station_mapping_query, get_station_metrics_query, get_station_complaints_query, get_station_metrics_by_station_query
from app.schemas.order_metric import OrderDatewiseMetric, LocationDateMetric, LocationDateMetrics, LocationMetric, Metric
from app.schemas.product_metric import ComplaintsDateMetricTypeWise
from app.core.metric_config_bistro import STATION_METRICS_CALC_MAPPING, STATION_METRIC_NAME_MAPPING, STATION_METRICS_TYPE_MAPPING

IST = timezone(timedelta(hours=5, minutes=30))

class CRUDStationMetrics():
    def _build_lookup_filters(self, filters_dict):
        dynamic_where_clause = []
        params = {}
        
        for field_name, (value, is_lookup, lookup_table, lookup_column_output, lookup_table_pk, current_table_lookup_colm) in filters_dict.items():
            if value:  # Only add filter if value is provided
                if is_lookup:
                    dynamic_where_clause.append(
                        f"AND LOOKUP('{lookup_table}', '{lookup_column_output}', '{lookup_table_pk}', {current_table_lookup_colm}) IN (%({field_name})s)"
                    )
                else:
                    dynamic_where_clause.append(f"AND {current_table_lookup_colm} IN (%({field_name})s)")
                params[field_name] = value

        return dynamic_where_clause, params

    def get_station_mapping(self, conn: connect) -> List[Dict[str, Union[str, int]]]:
        curs = conn.cursor()
        query = get_station_mapping_query()
        curs.execute(query)
        results = []
        for row in curs:
            results.append({"station_id": row[0], "station_name": row[1]})
        return results

    def get_station_metrics(self, conn: connect, 
                            metrics: List = None,
                            city: List = None,
                            store: List = None,
                            station: str = None,
                            date_str: str = None) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        
        selected_sql = [STATION_METRICS_CALC_MAPPING[key] for key in metrics if key in STATION_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, STATION_METRICS_CALC_MAPPING[key]) for key in metrics if key in STATION_METRICS_CALC_MAPPING]

        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        if city:
            dynamic_where_clause.append("AND lookup('merchant_outlet_facility_mapping_v2','frontend_merchant_city_name','frontend_merchant_id',merchant_id) in (%(cities)s)")
            params["cities"] = city
        if station:
            dynamic_where_clause.append("AND station_id = %(station)s")
            params["station"] = station

        query = get_station_metrics_query(metrics_sql=metrics_sql,
                                        dynamic_where_clause=" ".join(dynamic_where_clause),
                                        date_str=date_str)

        curs.execute(query, params)

        metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)

        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                    metrics_data[metric_name].append({"date": order_date, "metric": metric_value})

        results = [
            OrderDatewiseMetric(
                name=STATION_METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=STATION_METRICS_TYPE_MAPPING[metric_name]
            )
            for metric_name, _ in metrics_items
        ]
        return results

    def get_station_complaints(self, conn: connect,
                            city: List = None,
                            store: List = None,
                            station: List = None,
                            date_str: str = None) -> List[ComplaintsDateMetricTypeWise]:
        """
        Get complaints count by station for a specific store and time period
        """
        curs = conn.cursor()

        query, params = get_station_complaints_query(
            city=city,
            store=store,
            station=station,
            date_str=date_str
        )
        
        curs.execute(query, params)

        station_complaints = []
        for row in curs:
            complaint_date = datetime.strptime(row[0], "%Y-%m-%d")
            complaint_type = row[1]
            complaint_count = round(float(row[2]), 2)
            
            station_complaints.append(ComplaintsDateMetricTypeWise(
                complaint_type=complaint_type,
                date=complaint_date,
                count=complaint_count
            ))

        return station_complaints

    def get_station_metrics_by_station(self, conn: connect,
                                    metrics: List = None,
                                    city: List = None,
                                    store: List = None,
                                    station: List = None,
                                    date_str: str = None,
                                    past_days_diff: int = 7) -> LocationDateMetrics:
        """
        Get metrics grouped by station for a specific store and time period
        """
        curs = conn.cursor()
        params = {"past_days_diff": past_days_diff}
        
        # Default to KPT if no metrics specified
        if not metrics:
            metrics = ["station_avg_kpt"]

        # Build dynamic where clause
        dynamic_where_clause = []
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        if city:
            dynamic_where_clause.append("AND lookup('merchant_outlet_facility_mapping_v2','frontend_merchant_city_name','frontend_merchant_id',merchant_id) IN (%(cities)s)")
            params["cities"] = city
        if station:
            dynamic_where_clause.append("AND station_id IN (%(stations)s)")
            params["stations"] = station
        
        # Get selected metrics SQL
        selected_sql = [STATION_METRICS_CALC_MAPPING[key] for key in metrics if key in STATION_METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, STATION_METRICS_CALC_MAPPING[key]) for key in metrics if key in STATION_METRICS_CALC_MAPPING]
        
        query = get_station_metrics_by_station_query(metrics_sql=metrics_sql,
                                                    dynamic_where_clause=" ".join(dynamic_where_clause),
                                                    date_str=date_str)
        
        curs.execute(query, params)
        
        # Group data by date and station
        datewise_results = defaultdict(list)
        
        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            station_id = str(row[1])  # Convert LONG station_id to string
            
            # Build metrics for this station
            station_metrics = []
            for idx, (metric_name, _) in enumerate(metrics_items, start=2):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    metric_value = '-'  # Replace NaN or infinity with -
                
                metric_display_name = STATION_METRIC_NAME_MAPPING.get(metric_name, metric_name)
                station_metrics.append(
                    Metric(
                        name=metric_display_name,
                        metric=metric_value,
                        type=STATION_METRICS_TYPE_MAPPING.get(metric_name, "number")
                    )
                )
            
            # Add station data to the date group
            datewise_results[row[0]].append(
                LocationMetric(
                    type="station",
                    name=station_id,  # Use station_id as name
                    data=station_metrics
                )
            )
        
        # Create final results structure
        results = []
        for dt_str, station_data in datewise_results.items():
            order_date = datetime.strptime(dt_str, "%Y-%m-%d")
            date_diff = (datetime.now(IST) - order_date.replace(tzinfo=IST)).days
            
            results.append(
                LocationDateMetric(
                    date=order_date,
                    etl_snapshot_ts_ist=None,
                    date_diff=date_diff,
                    data=station_data
                )
            )
        
        return LocationDateMetrics(metrics=results)

station_metrics = CRUDStationMetrics()
