from datetime import datetime, timed<PERSON>ta
from typing import List
from collections import defaultdict

from pinotdb import connect

from .queries.dau_metrics import get_transacting_user_count_dau_conversion_query, get_today_city_dau_query, get_yesterday_dau_metrics, get_yesterday_transacting_user_count_dau_conversion_query, get_cart_blocks_query


from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING
from app.schemas.order_metric import OrderDatewiseMetric

class CRUDDAUMetrics():

    def _calculate_dau_conversion(self, results_dau, city, curs, max_window_end, app=None, store=None, yesterday_metric=False, date_str: str = None):
        results_dau_conversion = []
        dynamic_where_clause = []
        params = {}
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        curs.execute(get_transacting_user_count_dau_conversion_query(dynamic_where_clause=" ".join(dynamic_where_clause), yesterday_metric=yesterday_metric, max_window_end=max_window_end, date_str=date_str), params)
        for row in curs:
            transacting_user_count = row[1]
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            dau_list = [result for result in results_dau if result["date"] == order_date]
            if dau_list:
                dau = dau_list[0]["metric"]
                dau_conversion = round((transacting_user_count / dau) * 100, 2)
                results_dau_conversion.append({"date": order_date, "metric": dau_conversion})
        return results_dau_conversion

    def _calculate_cart_blocks_conversion(self, results_dau, city, curs, max_window_end, store=None, yesterday_metric=False, date_str: str = None):
        results_demand_based_cart_blocks_conversion = []
        results_express_serviceability_based_cart_blocks_conversion = []
        results_express_serviceability_ooh_based_cart_blocks_conversion = []
        results_express_serviceability_manual_based_cart_blocks_conversion = []

        dynamic_where_clause = []
        params = {}
        if city:
            dynamic_where_clause.append("AND lookup('merchant_outlet_facility_mapping_v2','frontend_merchant_city_name','frontend_merchant_id', express_merchant_id) in (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND express_merchant_id in (%(stores)s)")
            params["stores"] = store
        query=get_cart_blocks_query(dynamic_where_clause=" ".join(dynamic_where_clause), yesterday_metric=yesterday_metric, max_window_end=max_window_end, date_str=date_str)
        curs.execute(query, params)
        for row in curs:
            block_count = row[2]
            dau_date = datetime.strptime(row[0], "%Y-%m-%d")
            dau_list = [result for result in results_dau if result["date"] == dau_date]
            if dau_list:
                dau = dau_list[0]["metric"]
                block_count_conversion = round((block_count / dau) * 100, 2)
                if row[1] == 'express_demand_based_block':
                    results_demand_based_cart_blocks_conversion.append({"date": dau_date, "metric": block_count_conversion})  
                elif row[1] == 'express_serviceability_ooh_based_block':
                    results_express_serviceability_ooh_based_cart_blocks_conversion.append({"date": dau_date, "metric": block_count_conversion})   
                elif row[1] == 'express_serviceability_manual_based_block':
                    results_express_serviceability_manual_based_cart_blocks_conversion.append({"date": dau_date, "metric": block_count_conversion})    

        ooh_by_date = {item["date"]: item["metric"] for item in results_express_serviceability_ooh_based_cart_blocks_conversion}
        manual_by_date = {item["date"]: item["metric"] for item in results_express_serviceability_manual_based_cart_blocks_conversion}
        
        all_dates = set(ooh_by_date.keys()) | set(manual_by_date.keys())
        results_express_serviceability_based_cart_blocks_conversion = [
            {
                "date": date,
                "metric": round((ooh_by_date.get(date, 0) + manual_by_date.get(date, 0)), 2)
            }
            for date in all_dates
        ]
        return results_demand_based_cart_blocks_conversion, results_express_serviceability_based_cart_blocks_conversion, results_express_serviceability_ooh_based_cart_blocks_conversion, results_express_serviceability_manual_based_cart_blocks_conversion

    def get_dau_metrics(self, conn: connect, city: List = None, app: str = None, store: List = None, yesterday_metric: bool = False, date_str: str = None) -> List[OrderDatewiseMetric]:
      
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        elif city:
            dynamic_where_clause.append("AND city IN (%(cities)s)")
            params["cities"] = city
        if app:
            dynamic_where_clause.append("AND app like %(app)s")
            params["app"] = app

        query = get_today_city_dau_query(
            dynamic_where_clause=" ".join(dynamic_where_clause),
            is_store_query=True if store else False,
            yesterday_metric=yesterday_metric,
            date_str=date_str
        )
        # print(query)        

        curs.execute(query, params)
        results_dau = []
        dau_metrics = defaultdict(int)
        window_end = []
        for row in curs:
            order_date = datetime.strptime(row[1], "%Y-%m-%d")
            dau_metrics[order_date] += row[2]
            window_end.append(row[3])
        for dt, dau in dau_metrics.items():
            results_dau.append({"date": dt, "metric": dau})
        if window_end:
            max_window_end = max(window_end)
        else:
            return [[], [], [], [], [], []]

        results_dau_conversion = self._calculate_dau_conversion(results_dau, city, curs, max_window_end, app, store, yesterday_metric=yesterday_metric, date_str=date_str)
        results_demand_based_cart_blocks_conversion, results_express_serviceability_based_cart_blocks_conversion, results_express_serviceability_ooh_based_cart_blocks_conversion, results_express_serviceability_manual_based_cart_blocks_conversion = self._calculate_cart_blocks_conversion(results_dau, city, curs, max_window_end, store, yesterday_metric=yesterday_metric, date_str=date_str)
        return [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["dau"], data=results_dau, type=METRICS_TYPE_MAPPING["dau"]),], \
               [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["dau_conversion_percentage"], data=results_dau_conversion, type=METRICS_TYPE_MAPPING["dau_conversion_percentage"]),], \
               [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["demand_based_cart_blocks_conversion_percentage"], data=results_demand_based_cart_blocks_conversion, type=METRICS_TYPE_MAPPING["demand_based_cart_blocks_conversion_percentage"]),],\
               [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["serviceability_based_cart_blocks_conversion_percentage"], data=results_express_serviceability_based_cart_blocks_conversion, type=METRICS_TYPE_MAPPING["serviceability_based_cart_blocks_conversion_percentage"]),],\
               [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["express_serviceability_ooh_based_cart_blocks_conversion_percentage"], data=results_express_serviceability_ooh_based_cart_blocks_conversion, type=METRICS_TYPE_MAPPING["express_serviceability_ooh_based_cart_blocks_conversion_percentage"]),],\
               [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["express_serviceability_manual_based_cart_blocks_conversion_percentage"], data=results_express_serviceability_manual_based_cart_blocks_conversion, type=METRICS_TYPE_MAPPING["express_serviceability_manual_based_cart_blocks_conversion_percentage"]),]


    def get_yesterday_dau_metrics(self, conn: connect, app: str = None, city: List = None, store: List = None) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        if app:
            dynamic_where_clause.append("AND app like %(app)s")
            params["app"] = app

        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        elif city:
            dynamic_where_clause.append(
                "AND city in (%(cities)s)")
            params["cities"] = city
        curs.execute(get_yesterday_dau_metrics(" ".join(dynamic_where_clause), is_store_query=True if store else False), params)
        results_dau = []
        dau_metrics = defaultdict(int)
        for row in curs:
            order_date = datetime.strptime(row[1], "%Y-%m-%d")
            dau_metrics[order_date] += row[2]
        for dt, dau in dau_metrics.items():
            results_dau.append({"date": dt, "metric": dau})
        results_dau_conversion = []
        dynamic_where_clause = []
        params = {}
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append("AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
                 
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        elif city:
            dynamic_where_clause.append(
                "AND city_name in (%(cities)s)")
            params["cities"] = city
        curs.execute(get_yesterday_transacting_user_count_dau_conversion_query(dynamic_where_clause=" ".join(dynamic_where_clause)), params)
        for row in curs:
            transacting_user_count = row[1]
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            dau_list = [result for result in results_dau if result["date"] == order_date]
            if dau_list:
                dau = dau_list[0]["metric"]
                dau_conversion = round((transacting_user_count / dau) * 100, 2)
                results_dau_conversion.append({"date": order_date, "metric": dau_conversion})

        return [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["dau"], data=results_dau, type=METRICS_TYPE_MAPPING["dau"]),],\
               [OrderDatewiseMetric(name=METRIC_NAME_MAPPING["dau_conversion_percentage"], data=results_dau_conversion, type=METRICS_TYPE_MAPPING["dau_conversion_percentage"]),]


dau_metrics = CRUDDAUMetrics()
