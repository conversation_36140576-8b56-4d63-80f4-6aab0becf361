from datetime import datetime, timedelta

def get_rider_login_hours_current_max_epoch_query() -> str:
    return """SELECT max(DATETIMECONVERT(updated_at, '1:SECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS:EPOCH', '1:MILLISECONDS')) AS max_epoch
    FROM bistro_rider_login_time_metrics_v1
    WHERE updated_at >= DATETIMECONVERT(dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
    LIMIT 1
    """


def get_rider_login_metrics_query(dynamic_where_clause: str = '', 
        yesterday_metric: bool = False, 
        hourly_metrics: bool = False,
        get_all_cities: bool = False,
        max_current_epoch:str = 'now()',
        get_all_stores_for_city: bool = False,
        hour: str = None,
        date_str: str = None)-> str:
    
    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

    is_today = date_str==(datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

    day_diff = int((datetime.now() + timedelta(hours=5, minutes=30) - datetime.strptime(date_str, '%Y-%m-%d')).days)
    
    dt_filters: str = f"""
        updated_at >= DATETIMECONVERT(dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
        OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*7), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        """ if is_today else f"""
        (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(7+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
            AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(7+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
    """

    
    hr_column: str = """, cast(hour_ as int) AS hour""" if hourly_metrics or hour else ""
     
    city_column = """, CASE
            WHEN city IN ('Panchkula',
                            'Kharar',
                            'Zirakpur',
                            'Mohali',
                            'Chandigarh') THEN 'Chandigarh'
            ELSE city
        END AS city_name
    """ if get_all_cities and not get_all_stores_for_city else ""

    store_column = """, frontend_merchant_name,
			            frontend_merchant_id
                    """ if get_all_stores_for_city else ""

    group_by_str: str = "group by order_date"

    group_by_str += ", city_name" if get_all_cities else ""
    group_by_str += ", hour" if hourly_metrics or hour else ""
    group_by_str += ", frontend_merchant_name,frontend_merchant_id" if get_all_stores_for_city else ""

    if not get_all_cities and not hourly_metrics and not get_all_stores_for_city:
        dt_filters += f"""
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*14), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*21), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at <= DATETIMECONVERT(FromEpochSeconds({max_current_epoch}/1000 - 86400*28), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')) 
        """ if is_today else f"""
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(14+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(14+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(21+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(21+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
            OR (updated_at >= DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(28+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS')
                AND updated_at < DATETIMECONVERT(dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(28+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'), '1:MILLISECONDS:EPOCH', '1:MILLISECONDS:SIMPLE_DATE_FORMAT:yyyy-MM-dd HH:mm:ss tz(Asia/Kolkata)', '1:MILLISECONDS'))
        """

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"
    
    rider_login_metric = f"""
        SELECT "date" AS order_date,
            sum(total_active_mins)/60 AS login_hrs
            {city_column}
            {store_column}
            {hr_column}
        FROM bistro_rider_login_time_metrics_v1
        WHERE ({dt_filters})
        {dynamic_where_clause}
        {group_by_str}
        HAVING {cutoff_date_condition}
        LIMIT 100000
    """
    return rider_login_metric