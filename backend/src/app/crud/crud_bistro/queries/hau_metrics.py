from app.crud.utils import get_date_str, is_yesterday_date
from datetime import datetime, timedelta

def get_transacting_user_count_hau_conversion_query(metrics_sql: str, max_window_end: int, dynamic_where_clause: str, date_str: str = None) -> str:

        # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"

    today_date = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    is_today = date_str == today_date 
    
    if is_today:
        dt_filters = f"""
            ((insert_timestamp >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
              AND insert_timestamp <= {max_window_end})
             OR
             (insert_timestamp >= dateTrunc('DAY',FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') 
              AND insert_timestamp <= FromEpochSeconds({max_window_end}/1000 - 86400*7)))
        """
    else:
        selected_date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        selected_date_timestamp_ist = int((selected_date_obj - timedelta(hours=5, minutes=30)).timestamp() * 1000)
        
        dt_filters = f"""
            ((insert_timestamp >= {selected_date_timestamp_ist}
            AND insert_timestamp <= {selected_date_timestamp_ist + 86400 * 1000})
            OR
            (insert_timestamp >= {selected_date_timestamp_ist - 86400 * 7 * 1000}
            AND insert_timestamp <= {selected_date_timestamp_ist - 86400 * 6 * 1000}))
        """

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"        
    
    return f"""
        SELECT hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
            DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
            {metrics_sql}
        FROM bistro_fact_order_details_v2
        WHERE {dt_filters}
        and org_channel_id = 4
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        AND {cutoff_date_condition}
        GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), order_date
        LIMIT 50000
    """


def get_today_hau_metrics_city_wise(dynamic_where_clause: str, is_store_query: bool, date_str: str = None) -> str:
    table: str = "bistro_city_hourly_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
        max(window_end),
        app,
        hr as hour
    """
    group_by_query: str = """
            GROUP BY order_date,
            hr,
            city,
            app
    """
    if is_store_query:
        table = "bistro_merchant_hourly_active_users_v2"
        columns = """
            DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
            city,
            LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
            max(window_end),
            app,
            merchant_id,
            hr as hour
        """
        group_by_query  = """
            GROUP BY order_date,
            hr,
            city,
            app,
            merchant_id
        """
    
    today_date = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    is_today = date_str == today_date 
    
    if is_today:
        dt_filters = """
            (
                (window_start >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
                OR 
                (
                    window_start >= dateTrunc('DAY', now() - 86400000*%(past_days_diff)i, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
                    AND window_end <= now() - 300000 - 86400000*%(past_days_diff)i
                )
            )
        """
    else:
        selected_date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        selected_date_timestamp_ist = int((selected_date_obj - timedelta(hours=5, minutes=30)).timestamp() * 1000)

        dt_filters = f"""
            (
                (window_start >= {selected_date_timestamp_ist}
                AND window_end <= {selected_date_timestamp_ist + 86400 * 1000})
                OR
                (window_start >= {selected_date_timestamp_ist - 86400 * 7 * 1000}
                AND window_end <= {selected_date_timestamp_ist - 86400 * 6 * 1000})
            )
        """

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') >= '{cutoff_date}'"
        
    return f"""
    SELECT {columns}
    FROM {table}
    WHERE 
        {dt_filters}
        {dynamic_where_clause}
        AND {cutoff_date_condition}
    {group_by_query}
    LIMIT 100000
    """



def get_yesterday_hau_metrics(dynamic_where_clause: str, is_store_query: bool) -> str:
    table: str = "bistro_city_hourly_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        hour(window_start, 'Asia/Kolkata') AS hour,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
        app
    """
    group_by_query: str = """
            GROUP BY order_date,
            hour,
            city,
            app
    """
    if is_store_query:
        table = "bistro_merchant_hourly_active_users_v2"
        columns = """
                DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
                hour(window_start, 'Asia/Kolkata') AS hour,
                city,
                LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
                app,
                merchant_id
            """
        group_by_query  = """
                    GROUP BY order_date,
                    hour,
                    city,
                    app,
                    merchant_id
            """
        
    return f"""
    SELECT {columns}
    FROM {table}
    WHERE ((window_start >= dateTrunc('DAY', now() - 86400000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start >= dateTrunc('DAY', now() - 86400000*8, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now() - 86400000 * 7, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')))
        {dynamic_where_clause}
        {group_by_query}
    LIMIT 100000
    """


def get_order_metrics_hourly_query(metrics_sql, dynamic_where_clause, yesterday_metric: bool = False)-> str :
    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"
    
    dt_filters: str = """
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*7 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= FromEpochSeconds(now()/1000 - 86400*7))
    """ if not yesterday_metric else """
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000 - 86400), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*8 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
    """
    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    ORDER_METRICS_HOURLY_QUERY = f"""
        SELECT hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
            order_date,
            {metrics_sql}
        FROM bistro_fact_order_details_v2
        WHERE ({dt_filters})
        and org_channel_id = 4
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        {DUMMY_MERCHANT_REMOVE_FILTER}
        AND {cutoff_date_condition}
        GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), order_date
        LIMIT 50000
        """
    return ORDER_METRICS_HOURLY_QUERY