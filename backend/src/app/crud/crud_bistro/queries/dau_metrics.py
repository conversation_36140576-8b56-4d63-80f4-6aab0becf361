from datetime import datetime, timedelta
from app.crud.utils import get_date_str, is_yesterday_date

def generate_dt_filters(date_str=None,
                        date_column="dt",
                        timestamp_column=None, num_weeks=5,
                        less_than_time="now()/1000", period_length=7, timestamp_column_in_seconds: bool = False,
                        is_yesterday: bool = False):
    # TODO:make this function more generic
    date_format = '%Y-%m-%d'
    dt_filters = []

    # IST is UTC + 5 hours and 30 minutes
    ist_offset = timedelta(hours=5, minutes=30)
    # Convert date_str to datetime object
    date_ist = datetime.strptime(date_str, date_format)
    # Get current time in IST
    now_ist = datetime.now() + ist_offset
    if is_yesterday and date_ist.date() == now_ist.date():
        date_ist -= timedelta(days=1)

    if date_ist.date() == now_ist.date():
        for i in range(num_weeks):
            order_date = (date_ist - timedelta(days=period_length*i)).strftime(date_format)
            if timestamp_column:
                dt_filters.append(
                    f"({date_column} = '{order_date}' AND {timestamp_column} <= "
                    f"FromEpochSeconds({less_than_time} - 86400*{period_length*i}))" if not timestamp_column_in_seconds
                    else f"({date_column} = '{order_date}' AND {timestamp_column} <="
                         f" ({less_than_time} - 86400*{period_length*i}))"
                )
            else:
                dt_filters.append(
                    f"({date_column} = '{order_date}')")
    else:
        for i in range(num_weeks):
            order_date = (date_ist - timedelta(days=period_length*i)).strftime(date_format)
            dt_filters.append(f"({date_column} = '{order_date}')")

    return " OR\n".join(dt_filters)


def generate_dt_filters_custom_date(date_str: str, 
                                   date_column: str,
                                   num_weeks: int = 5, 
                                   period_length: int = 7) -> str:
    """
    Simplified version for generating filters for non-today dates.
    """
    date_format = '%Y-%m-%d'
    dt_filters = []

    # Convert date_str to datetime object
    base_date = datetime.strptime(date_str, date_format)

    # Generate filters for the given number of intervals
    for i in range(num_weeks):
        order_date = (base_date - timedelta(days=period_length * i)).strftime(date_format)
        dt_filters.append(f"({date_column} = '{order_date}')")

    return " OR\n".join(dt_filters)



def get_transacting_user_count_dau_conversion_query(dynamic_where_clause: str,
                                                    yesterday_metric: bool,
                                                    max_window_end: int,
                                                    date_str: str = None) -> str:

    today_date = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    is_today= date_str == today_date


    if is_today:
        dt_filters = generate_dt_filters(date_str=date_str, date_column='order_date',
        timestamp_column='insert_timestamp',
        num_weeks=5,
        less_than_time=max_window_end/1000,
        is_yesterday=yesterday_metric)
    else:
        dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                   date_column='order_date',
                                                   num_weeks=5,
                                                   period_length=7)


    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    QUERY = f"""
    SELECT DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
        COUNT(DISTINCT customer_id) as transacting_users_count
    FROM bistro_fact_order_details_v2
    WHERE ({dt_filters})
    and org_channel_id = 4
    and city_name not in ('Not in service area')
    and {cutoff_date_condition}
    {dynamic_where_clause}
    {DUMMY_MERCHANT_REMOVE_FILTER}
    GROUP BY DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
    LIMIT 50000"""
    return QUERY


def get_today_city_dau_query(dynamic_where_clause: str, 
                      is_store_query: bool, 
                      yesterday_metric: bool = False,
                      date_str: str = None) -> str:
    table: str = "bistro_city_daily_active_users"
    columns: str = """
            city,
       DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
       LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
       max(window_end),
       app
        """
    group_by_query: str = """
                GROUP BY app, city, DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
        """
    if is_store_query:
        table = "bistro_merchant_daily_active_users"
        columns: str = """
                    city,
               DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
               LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
               max(window_end),
               app,
               merchant_id
                """
        group_by_query: str = """
                        GROUP BY app, city, merchant_id, DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
                """
    
    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

    day_diff = int((datetime.now() + timedelta(hours=5, minutes=30) - datetime.strptime(date_str, '%Y-%m-%d')).days)
    
    if date_str == (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d'):
        dt_filters = generate_dt_filters(date_str=date_str,
                                       date_column='dt',
                                       timestamp_column='window_end',
                                       num_weeks=5)
    else:
        dt_filters = f"""
        (
            window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        )
        OR (
            window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+7)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+7-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        )
        OR (
            window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+14)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+14-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        )
        OR (
            window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+21)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+21-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        )
        OR (
            window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+28)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+28-1)), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        )
        """

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') >= '{cutoff_date}'"

    query = f"""
                        SELECT {columns}
                        FROM {table}
                        WHERE ({dt_filters}
                        AND {cutoff_date_condition})
                        {dynamic_where_clause}
                        {group_by_query}
                        LIMIT 100000 
                    """
    return query


def get_yesterday_dau_metrics(dynamic_where_clause: str, is_store_query: bool = False, date_str: str = None) -> str:
    table: str = "bistro_city_daily_active_users"
    columns: str = """
            city,
       DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
       LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
       max(window_end),
       app
        """
    group_by_query: str = """
                GROUP BY app, city, DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
        """
    if is_store_query:
        table = "bistro_merchant_daily_active_users"
        columns: str = """
                    city,
               DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
               LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
               max(window_end),
               app,
               merchant_id
                """
        group_by_query: str = """
                        GROUP BY app, city, merchant_id, DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
                """
    dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                date_column='dt',
                                                num_weeks=5,
                                                period_length=7)
    YESTERDAY_DAU_METRICS =  f"""
                        SELECT {columns}
                        FROM {table}
                        WHERE ({dt_filters})
                        {dynamic_where_clause}
                        {group_by_query}
                        LIMIT 100000 
                    """
    return YESTERDAY_DAU_METRICS


def get_yesterday_transacting_user_count_dau_conversion_query(dynamic_where_clause: str) -> str:
    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"

    query = f"""
    SELECT DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
        COUNT(DISTINCT customer_id) as transacting_users_count
    FROM bistro_fact_order_details_v2
    WHERE (
    (insert_timestamp >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    OR
    (insert_timestamp >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*8), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    OR
    (insert_timestamp >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*15), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    OR
    (insert_timestamp >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*22), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    OR
    (insert_timestamp >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*29), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND insert_timestamp < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    )
    and org_channel_id = 4
    and city_name not in ('Not in service area')
    {dynamic_where_clause}
    {DUMMY_MERCHANT_REMOVE_FILTER}
    GROUP BY DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
    LIMIT 50000
"""
    return query


def get_today_hau_metrics_city_wise(dynamic_where_clause: str, 
                                    is_store_query: bool, 
                                    date_str: str = None) -> str:
    table: str = "bistro_city_hourly_active_users_v2"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
        max(window_end),
        app,
        hr as hour
    """
    group_by_query: str = """
            GROUP BY 
                order_date,
                hr,
                city,
                app
    """
    if is_store_query:
        table = "bistro_merchant_hourly_active_users_v2"
        columns = """
                DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
                city,
                LASTWITHTIME(unique_device_count, window_end, 'LONG') AS hau,
                max(window_end),
                app,
                merchant_id,
                hr as hour
            """
        group_by_query  = """
            GROUP BY 
                order_date,
                hr,
                city,
                app,
                merchant_id
            """
        
    if date_str is None:
        date_str = get_date_str(is_yesterday=False)

    dt_filters = """
        (window_start >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        OR ( window_start >= dateTrunc('DAY', now() - 86400000*%(past_days_diff)i, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= now() - 300000 - 86400000*%(past_days_diff)i ))
        AND (window_end - window_start) <= 3600000
    """ if not is_yesterday_date(date_str) else """
        ((window_start >= dateTrunc('DAY', now() - 86400000, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR (window_start >= dateTrunc('DAY', now() - 86400000*8, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
            AND window_end <= dateTrunc('DAY', now() - 86400000 * 7, 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')))
        AND (window_end - window_start) <= 3600000
    """

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') >= '{cutoff_date}'"

    return f"""
        SELECT {columns}
        FROM {table}
        WHERE 
            {dt_filters}
            {dynamic_where_clause}
            AND {cutoff_date_condition}
        {group_by_query}
        LIMIT 100000
    """

def get_today_all_cities_dau_query(dynamic_where_clause: str,
                                   is_store_query: bool = False,
                                   date_str: str = None, hour: str = None) -> str:
    table: str = "bistro_city_daily_active_users"
    columns: str = """
        DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
        city,
        LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
        max(window_end),
        app
    """
    group_by_query: str = """
        GROUP BY 
            app, 
            city,
            DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
    """
    if is_store_query:
        table = "bistro_merchant_daily_active_users"
        columns: str = """
            DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
            city,
            LASTWITHTIME(unique_device_count, window_end, 'LONG') AS dau,
            max(window_end),
            app,
            merchant_id
        """
        group_by_query: str = """
            GROUP BY 
                app, 
                city,
                merchant_id,
                DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS')
        """
    
    hr_column = """, hour(window_start, 'Asia/Kolkata') AS order_hour """ if hour else ""
    group_by_query += """, hour(window_start, 'Asia/Kolkata') """ if hour else ""

    if date_str is None:
        date_str = get_date_str(is_yesterday=False)

    dt_filters = """
        (window_start = dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS')
        OR 
        (window_start >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end <= FromEpochSeconds(now()/1000 - 86400*7)))
    """ if not is_yesterday_date(date_str) else """
        (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end <= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
        OR
        (window_start = dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*8), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS') AND window_end <= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'MILLISECONDS'))
    """

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"DATETIMECONVERT(window_start + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') >= '{cutoff_date}'"

    return f"""
        SELECT 
        {columns}
        {hr_column}
        FROM {table}
        WHERE (
            ({dt_filters})
            {dynamic_where_clause}
            AND {cutoff_date_condition}
        )
        {group_by_query}
        LIMIT 100000
    """

def get_transacting_user_count_city_wise_query(dynamic_where_clause: str, 
                                               is_store_query: bool, 
                                               date_str: str,
                                               hour: str = None) -> str:
    columns: str = """
                city_name as city,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                COUNT(DISTINCT(customer_id)) as transacting_users_count
    """
    group_by_clause: str = "GROUP BY 2,1"

    if is_store_query:
        columns: str = """city_name as city,
                DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') as order_date,
                COUNT(DISTINCT(customer_id)) as transacting_users_count,
                merchant_id as frontend_merchant_id
        """
        group_by_clause: str = "GROUP BY 2,1,4"
    
    hr_column = """, order_hour """ if hour else ""
    group_by_clause += """, order_hour """ if hour else ""

    dt_filters = generate_dt_filters(
        date_str=date_str, date_column='order_date', timestamp_column='insert_timestamp', num_weeks=2
    )

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"DATETIMECONVERT(insert_timestamp + 19800000, '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') >= '{cutoff_date}'"

    return f"""
        SELECT 
            {columns}
            {hr_column}
        FROM 
            bistro_fact_order_details_v2
        WHERE
            (
            ({dt_filters})
            )
            and org_channel_id = 4
            and city_name not in ('Not in service area')
            {dynamic_where_clause}
            and {cutoff_date_condition}
        {group_by_clause}
        
        LIMIT 50000
    """

def get_cart_blocks_query(dynamic_where_clause: str,
                          max_window_end: int = None,
                          yesterday_metric: bool = False,
                          get_all_cities: bool = False,
                          is_store_query: bool = False,
                          date_str: str = None,
                          hour: str = None) -> str:
    
    is_today=date_str==(datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    #calculate day diff between today and date_str in int 
    day_diff = int((datetime.now() + timedelta(hours=5, minutes=30) - datetime.strptime(date_str, '%Y-%m-%d')).days)

    dt_filters: str = f"""
        (
            ("time" >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
            AND "time" <= {max_window_end}/1000)
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= FromEpochSeconds({max_window_end}/1000 - 86400*7)/1000)
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*14), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= FromEpochSeconds({max_window_end}/1000 - 86400*14)/1000)
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*21), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= FromEpochSeconds({max_window_end}/1000 - 86400*21)/1000)
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*28), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= FromEpochSeconds({max_window_end}/1000 - 86400*28)/1000)
        )
    """ if is_today else f"""
        (
            ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
            AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(7+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(7+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(14+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(14+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(21+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(21+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(28+{day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*(28+{day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
        )
    """




    group_by_str: str = "group by order_date, block_type"
    cities_filter: str = ""

    if get_all_cities:
        dt_filters = f"""
            (("time" >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= {max_window_end}/1000)
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*%(past_days_diff)i), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= FromEpochSeconds({max_window_end}/1000 - 86400*%(past_days_diff)i)/1000))
            """ if is_today else f"""
            (("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff})), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                    AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
                OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+7)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                    AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+7-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')))
            """

        group_by_str = "group by order_date, block_type, city"
        cities_filter = """, lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_city_name', 'frontend_merchant_id', express_merchant_id) AS city"""
        if hour:
            group_by_str += """, order_hour"""
            cities_filter += """,hour("time"*1000, 'Asia/Kolkata') AS order_hour"""

    if is_store_query:
        dt_filters = f"""
        (
            ("time" >= dateTrunc('DAY', now(), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
            AND "time" <= {max_window_end}/1000)
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*7), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS') 
                AND "time" <= FromEpochSeconds({max_window_end}/1000 - 86400*7)/1000)
        )
        """ if is_today else f"""
        (
            ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*{day_diff}), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
            AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
            OR ("time" >= dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+7)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS')
                AND "time" < dateTrunc('DAY', FromEpochSeconds(now()/1000 - 86400*({day_diff}+7-1)), 'MILLISECONDS', 'Asia/Kolkata', 'SECONDS'))
        )
        """


        group_by_str = "group by order_date, block_type, city, frontend_merchant_id"
        cities_filter = """, lookup('merchant_outlet_facility_mapping_v2', 'frontend_merchant_city_name', 'frontend_merchant_id', express_merchant_id) AS city, express_merchant_id as frontend_merchant_id
        """
    CUTOFF_DATE_FILTER = "dt > '2025-05-22'"
    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"    

    cart_blocked_query = f"""
        SELECT 
            DATETIMECONVERT("time" + 19800, '1:SECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd', '1:DAYS') AS order_date,
            CASE        
                WHEN reason IN ('OUTSIDE_OPERATING_HOURS') 
                THEN 'express_serviceability_ooh_based_block'
                
                WHEN reason IN ('DISRUPTION_DEFAULT', 'DISRUPTION_POLYGON_THROTTLE', 'DISRUPTION_FESTIVAL', 'SERVICEABILITY_ROLLOUT_BLOCK', 'MAX_POSSIBLE_DISTANCE_BREACHED') 
                THEN 'express_serviceability_manual_based_block'
                
                WHEN reason IN ('MAX_POSSIBLE_PICKER_ETA_BREACHED', 'DISRUPTION_RAINS', 'MAX_POSSIBLE_FE_MANPOWER_BACKLOG_RATIO_BREACHED', 'NO_FE_AT_STORE', 'MAX_POSSIBLE_ETA_BREACHED', 'NO_PICKER_AT_STORE', 'MAX_POSSIBLE_PICKING_DURATION_BREACHED', 'MAX_POSSIBLE_FE_ETA_BREACHED')  
                THEN 'express_demand_based_block'
            END AS block_type,
            count(distinct user_id) AS blocked_users
            {cities_filter}
        FROM serviceability_checkouts_block_info_v1
        WHERE ({dt_filters})
            and serviceable = false
            and reason NOT IN ('INCORRECT_MERCHANT_FOR_LOCATION', 'REACHED_FULL_CAPACITY', 'DISRUPTION_STORE_LAUNCH')
            and block_type = 'COMPLETE'
            and downstream_service in ( 'Assembly-Bistro', 'bistro-checkout-service-primary', 'bistro-checkout-service-canary' )
            and {CUTOFF_DATE_FILTER}
            and {cutoff_date_condition}
            {dynamic_where_clause}
        {group_by_str}
        order by order_date
        LIMIT 50000
    """
    return cart_blocked_query

