from datetime import datetime, timedelta
from ..utils import generate_dt_filters, generate_dt_filters_custom_date
from ....common import product_metric as product_metric_query_utils
from typing import List, Union, Dict

def get_station_mapping_query() -> str:
    query = f"""
        SELECT DISTINCT station_id, station_name
        FROM analytics_bistro_product_station_mapping_v2
        LIMIT 1000;
    """
    return query

def get_station_metrics_query(metrics_sql: str,
                              dynamic_where_clause: str, 
                              date_str: str = None) -> str:
    """
    Get station metrics query for a specific date and its previous weeks
    """
    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    
    if date_str == (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d'):
        dt_filters = generate_dt_filters(date_str=date_str,
                                       date_column='order_date',
                                       timestamp_column='order_created_at',
                                       num_weeks=5)
    else:
        dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                   column_name='order_date',
                                                   num_intervals=4,
                                                   interval_days=7)

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    station_metrics_query = f"""
        SELECT
            order_date,
            {metrics_sql}
        FROM analytics_bistro_sub_order_item_prep_details_v1
        WHERE ({dt_filters})
        {dynamic_where_clause}
        AND {cutoff_date_condition}
        GROUP BY order_date
        ORDER BY order_date DESC
        LIMIT 50000
    """
    return station_metrics_query

def get_station_complaints_query(city: List = None,
                                 store: List = None,
                                 station: List = None,
                                 yesterday_metric: bool = False,
                                 date_str: str = None) -> tuple:
    """
    Get station complaints query for a specific date and its previous weeks
    """
    # Generate date filters using similar pattern as product metrics
    dt_filters = product_metric_query_utils.generate_dt_filters_product_metrics(
        date_column='dt', 
        timestamp_column='created_at', 
        timestamp_column_in_seconds=False, 
        is_daywise=False, 
        current_hour=True, 
        yesterday_metric=yesterday_metric,
        date_str=date_str, 
        number_of_days=7,  
        number_of_week=5
    )

    params = {}
    dynamic_where_clause = []
    
    if station:
        dynamic_where_clause.append("AND abpsm.station_id IN (%(station)s)")
        params["station"] = station

    if store:
        store_filter = "AND merchant_id IN (%(store)s)"
        params["store"] = store
    else:
        store_filter = ""
    
    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"dt >= '{cutoff_date}'"

    # Build city filter for subquery if needed
    city_subquery_filter = ""
    if city:
        city_subquery_filter = "AND city_name IN (%(city)s)"
        params["city"] = city

    dynamic_where_clause = ' '.join(dynamic_where_clause)
    
    station_complaints_query = f"""
        SET useMultistageEngine=true;

        SELECT 
            dt,
            foc.complaint_type,
            DISTINCTCOUNT(foc.complaint_id) AS total_complaint_count
        FROM 
            fact_order_complaints_v2 foc
        JOIN 
            analytics_bistro_product_station_mapping_v2 abpsm ON foc.product_id = abpsm.product_id 
        WHERE
            ({dt_filters})
            {dynamic_where_clause}
            AND foc.product_id IS NOT NULL
            AND foc.store_id IS NOT NULL
            AND {cutoff_date_condition}
            AND tenant IN (%(tenant)s)
            AND foc.store_id IN (
                select cast(outlet_id as VARCHAR) from outlet_facility_mapping_v1 
                where 1=1 {store_filter} {city_subquery_filter}
            )
            AND foc.product_id NOT IN (
                select cast(product_id as VARCHAR) from product_l0_l1_l2_mapping_v1 
                where l1_category NOT IN ('Ingredients')
            )
        GROUP BY 
            dt,
            foc.complaint_type
        ORDER BY
            dt DESC, foc.complaint_type
    """
    
    params["tenant"] = "BISTRO"
    
    return station_complaints_query, params

def get_station_metrics_by_station_query(metrics_sql: str,
                                        dynamic_where_clause: str,
                                        date_str: str = None) -> str:
    """
    Get station metrics query grouped by station for a specific date and its previous weeks
    """
    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    
    if date_str == (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d'):
        dt_filters = generate_dt_filters(date_str=date_str,
                                       date_column='order_date',
                                       timestamp_column='order_created_at',
                                       num_weeks=5)
    else:
        dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                   column_name='order_date',
                                                   num_intervals=4,
                                                   interval_days=7)

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"
    
    query = f"""
        SELECT
            order_date,
            station_id,
            {metrics_sql}
        FROM analytics_bistro_sub_order_item_prep_details_v1
        WHERE ({dt_filters})
        AND station_id IS NOT NULL
        AND {cutoff_date_condition}
        {dynamic_where_clause}
        GROUP BY order_date, station_id
        ORDER BY order_date DESC, station_id
        LIMIT 50000
    """
    return query