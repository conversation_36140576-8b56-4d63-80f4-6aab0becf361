from datetime import datetime, timedelta
from typing import List
from ..utils import generate_dt_filters, generate_dt_filters_custom_date

def get_all_order_metrics_query(metrics_sql, dynamic_where_clause: str,
                              date_str: str = None,
                              yesterday_metric: bool = False) -> str:
    """
    Get metrics query for a specific date and its previous weeks
    """
    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    
    if date_str == (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d'):
        dt_filters = generate_dt_filters(date_str=date_str,
                                       date_column='order_date',
                                       timestamp_column='insert_timestamp',
                                       num_weeks=5)
    else:
        dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                   column_name='order_date',
                                                   num_intervals=4,
                                                   interval_days=7)

    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"

    # Add promo metrics date condition
    if any(metric in metrics_sql for metric in ["promo_orders", "promo_percentage", "promo_charge", "avg_kpt", "avg_wait_time", "avg_prep_time", "avg_assembly_time", "percent_order_breaching_wait_prep_time", "percent_order_breaching_assembly_time", "new_transacting_users_count"]):
        dynamic_where_clause += " AND order_date >= '2025-04-14'"

    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    all_orders_metric_query = f"""
        SELECT
            order_date,
            {metrics_sql}
        FROM bistro_fact_order_details_v2
        WHERE ({dt_filters})
        and org_channel_id = 4
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        {DUMMY_MERCHANT_REMOVE_FILTER}
        AND {cutoff_date_condition}
        GROUP BY order_date
        LIMIT 50000
    """
    return all_orders_metric_query



def get_order_metrics_hourly_query(metrics_sql, dynamic_where_clause, yesterday_metric: bool = False, date_str: str = None)-> str :
    today_date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    is_today = date_str == today_date_str if date_str else not yesterday_metric

    selected_date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    selected_timestamp_ms = int(selected_date_obj.timestamp() * 1000)

    if is_today:
        dt_filters: str = """
            (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
            OR
            (order_date = DATETIMECONVERT(FromEpochSeconds(now()/1000  - 86400*7 ), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= FromEpochSeconds(now()/1000 - 86400*7))
        """
    else:
        dt_filters = generate_dt_filters_custom_date(
            date_str=date_str,
            column_name='order_date',
            num_intervals=1,     # today + 7 days ago
            interval_days=7
        )


    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"
    
    ORDER_METRICS_HOURLY_QUERY = f"""
        SELECT hour(insert_timestamp, 'Asia/Kolkata') AS order_hour,
            order_date,
            {metrics_sql}
        FROM bistro_fact_order_details_v2
        WHERE ({dt_filters})
        and org_channel_id = 4
        and city_name not in ('Not in service area')
        {dynamic_where_clause}
        {DUMMY_MERCHANT_REMOVE_FILTER}
        AND {cutoff_date_condition}
        GROUP BY hour(insert_timestamp, 'Asia/Kolkata'), order_date
        LIMIT 50000
        """
    return ORDER_METRICS_HOURLY_QUERY


def get_new_user_metrics_query(
        dynamic_where_clause: str = '', 
        dynamic_where_clause_today: str = '',
        yesterday_metric: bool = False,
        date_str: str = None) -> List[str]:

    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    
    if date_str == (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d'):
        dt_filters = generate_dt_filters(date_str=date_str,
                                       date_column='fod',
                                       timestamp_column='fod_ts',
                                       num_weeks=5)
    else:
        dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                   column_name='fod',
                                                   num_intervals=4,
                                                   interval_days=7)

    group_by_str: str = "group by fod"
    group_by_str_today: str = "group by order_date, new_user"

    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER_FOD = "AND front_end_merchant_id NOT IN (35758)"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition_hist = f"fod >= '{cutoff_date}'"
    cutoff_date_condition_today = f"order_date >= '{cutoff_date}'"

    new_user_hist_qry: str = f"""
    SELECT 
        fod,
        count(dim_customer_key) AS new_users
    FROM 
        bistro_users_fod_v2 
    WHERE 
        ({dt_filters})
        {dynamic_where_clause}
        {DUMMY_MERCHANT_REMOVE_FILTER_FOD}
        AND {cutoff_date_condition_hist}
    {group_by_str}
    LIMIT 100000
    """

    # Todo : Remove later
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"
    
    new_user_today_qry: str = f"""
    SELECT 
        order_date,
        count(DISTINCT customer_id) AS new_users, 
        CASE 
            WHEN lookup('bistro_users_fod_v2', 'city_name', 'dim_customer_key', customer_id) = 'null' 
                THEN 'new_user' 
            ELSE 'NA' 
        END AS new_user
    FROM 
        bistro_fact_order_details_v2 
    WHERE 
        order_date = DATETIMECONVERT(now(), '1:MILLISECONDS:EPOCH', '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS')
        {dynamic_where_clause_today}
    AND current_status = 'DELIVERED' 
    and org_channel_id = 4
    and city_name not in ('Not in service area')
    {DUMMY_MERCHANT_REMOVE_FILTER}
    AND {cutoff_date_condition_today}
    {group_by_str_today}
    HAVING new_user='new_user'
    LIMIT 100
    """

    return new_user_hist_qry, new_user_today_qry


def get_all_order_metrics_city_wise_query(dynamic_where_clause: str, 
                                          is_store_level: bool, 
                                          date_str, 
                                          hour: str = None) -> str:
    if is_store_level:
        select_store_or_city = f"""merchant_id as frontend_merchant_id, 
                                   merchant_name as frontend_merchant_name,
                                """
        group_by = "GROUP BY 1,2,3"
    else:
        select_store_or_city = "city_name as city,"
        group_by = "GROUP BY 1,2"

    if hour:
        hour_column = ", order_hour"
        group_by += ", order_hour"
    else:
        hour_column = ""
        
    dt_filters = generate_dt_filters(
        date_str=date_str, date_column='order_date', timestamp_column='insert_timestamp', num_weeks=2
    )
    DUMMY_MERCHANT_REMOVE_FILTER = "AND merchant_id NOT IN (35758)"


    return f"""
        SELECT
            {select_store_or_city}
            order_date,
            SUM(total_cost) as gmv,
            COUNT(DISTINCT(cart_id)) as order_count,
            100 * SUM (CASE WHEN current_status = 'CANCELLED' THEN 1 ELSE 0 END) / COUNT(id) as cancellation_percentage,
            SUM(total_cost)/COUNT(DISTINCT(cart_id)) as aov,
            100 * SUM(CASE WHEN (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as percentage_order_delivered_in_15mins
            {hour_column}
        FROM 
            bistro_fact_order_details_v2
        WHERE (
                ({dt_filters})
            )
            and org_channel_id = 4
            {dynamic_where_clause}
            and city_name not in ('null', 'Not in service area')
            {DUMMY_MERCHANT_REMOVE_FILTER}
        {group_by}
        LIMIT 50000
    """

def get_bistro_ath_metrics(dynamic_where_clause: str)->str:
    return f"""SELECT SUM(metric_value),
                CASE
                    WHEN metric_type='Cart Volume' THEN 'ath_order_count'
                    WHEN metric_type='GMV' THEN 'ath_gmv'
                END AS metric_key,
                city,
                dt
                from ath_metrics_bistro_v2
                where {dynamic_where_clause}
                group by metric_key,city,dt limit 1000"""