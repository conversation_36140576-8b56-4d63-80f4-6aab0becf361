from ..utils import generate_dt_filters, generate_dt_filters_custom_date
from datetime import datetime, timedelta

table_aliases = {
    "bistro_order_feedback": "bfeedback",
    "product_l0_l1_l2_mapping_v1": "productmap",
    "merchant_outlet_facility_mapping_v2": "merchantmap",
    "bistro_fact_order_details_v2": "bfact",
    "analytics_bistro_product_station_mapping_v2": "abpsm",
}

def build_rating_lookup_filters(filters_dict):
    global table_aliases

    dynamic_where_clause = []
    params = {}
    
    for field_name, filter_config in filters_dict.items():
        value = filter_config.get("value")
        if not value:
            continue

        filter_type = filter_config.get("type", "simple")
        main_table = filter_config.get("main_table", "bistro_order_feedback")
        main_table_alias = table_aliases.get(main_table, main_table)
        main_column = filter_config.get("main_column")
        cast_type = filter_config.get("cast_type", "")
        
        params[field_name] = value

        if filter_type == "simple":
            cast_expr = f"cast({main_table_alias}.{main_column} as {cast_type})" if cast_type else f"{main_table_alias}.{main_column}"
            clause = f"AND {cast_expr} IN (%({field_name})s)"
            dynamic_where_clause.append(clause)

        elif filter_type == "subquery":
            lookup_table = filter_config.get("lookup_table")
            lookup_column = filter_config.get("lookup_column")
            lookup_pk = filter_config.get("lookup_pk")
            lookup_cast_type = filter_config.get("lookup_cast_type", "")
            pk_expr = f"cast({lookup_pk} as {lookup_cast_type})" if lookup_cast_type else lookup_pk
            
            clause = f'''AND {main_table_alias}.{main_column} IN 
              ( SELECT {pk_expr} FROM {lookup_table} 
                WHERE {lookup_column} IN (%({field_name})s) 
              )'''
            dynamic_where_clause.append(clause)

        elif filter_type == "join":
            lookup_table = filter_config.get("lookup_table")
            lookup_column = filter_config.get("lookup_column")
            lookup_table_alias = table_aliases.get(lookup_table, lookup_table)
            cast_expr = f"cast({lookup_table_alias}.{lookup_column} as {cast_type})" if cast_type else f"{lookup_table_alias}.{lookup_column}"
            
            clause = f"AND {cast_expr} IN (%({field_name})s)"
            dynamic_where_clause.append(clause)

    return dynamic_where_clause, params


def get_ratings_query(dynamic_where_clause: str,
                      yesterday_metric: bool = False,
                      date_str: str = None,
                      is_daywise: bool = False,
                      current_hour: bool = True,
                      category_wise: bool = False,
                      has_product_filter: bool = False,
                      has_merchant_filter: bool = False,
                      ) -> str:
    global table_aliases

    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

    if yesterday_metric:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30) - timedelta(days=1)).strftime('%Y-%m-%d')
        
    is_today = date_str==(datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')


    dt_filters: str = """
    (order_date = DATETIMECONVERT( FromEpochSeconds(now())/1000 , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*7*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*7*1000) )
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*14*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*14*1000) )
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*21*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*21*1000) )
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*28*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*28*1000) )
""" if is_today else generate_dt_filters_custom_date(date_str=date_str,
                                                   column_name='order_date',
                                                   num_intervals=4,
                                                   interval_days=7)


    feedback_table = table_aliases['bistro_order_feedback']
    fact_table = table_aliases['bistro_fact_order_details_v2']
    merchant_map_table = table_aliases['merchant_outlet_facility_mapping_v2']
    product_map_table = table_aliases['product_l0_l1_l2_mapping_v1']
    
    joins = f"""
        FROM bistro_order_feedback {feedback_table}
    """
    
    joins += f"""
    JOIN bistro_fact_order_details_v2 {fact_table} ON {feedback_table}.order_id = {fact_table}.id
    """
    
    if has_product_filter or category_wise:
        joins += f"""
        JOIN product_l0_l1_l2_mapping_v1 {product_map_table} ON {feedback_table}.product_id = {product_map_table}.product_id
        """
    
    group_by_columns = f"{fact_table}.order_date, "
    
    category_select = ""
    category_groupby = ""
    if category_wise:
        category_select = f"{product_map_table}.product_name as item_name, "
        category_groupby = f"{product_map_table}.product_name, "

    no_ingredients_condition = ""
    if has_product_filter or category_wise:
        no_ingredients_condition = f"AND {product_map_table}.l1_category NOT IN ('Ingredients')"
    
    date_select = f"{fact_table}.order_date,"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"
    
    ratings_query = f"""
        SET useMultistageEngine=true;
        
        SELECT 
            {date_select}
            {category_select}
            {feedback_table}.rating,
            count(*) as rating_count
        {joins}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            {no_ingredients_condition}
            AND {cutoff_date_condition}
        GROUP BY 
            {group_by_columns}
            {category_groupby}
            {feedback_table}.rating
        LIMIT 500000
    """
    return ratings_query


def get_ratings_metrics_city_wise_query(dynamic_where_clause: str = "",
                                       is_store_level: bool = False,
                                       date_str: str = None,
                                       hour: str = None,
                                       past_days_diff: int = 7) -> str:

    dt_filters: str = """
        (order_date = DATETIMECONVERT( FromEpochSeconds(now())/1000 , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*7*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*7*1000) )
    """ if date_str == datetime.now().strftime('%Y-%m-%d') else """
        (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*1*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
        OR
        (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*8*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
    """
    dt_filters = dt_filters.replace("order_date", "bfact.order_date").replace("insert_timestamp", "bfact.insert_timestamp")

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"bfact.order_date >= '{cutoff_date}'"
    
    if is_store_level:
        return f"""
        SET useMultistageEngine=true;
        
        SELECT 
            bfact.order_date,
            bfact.merchant_id,
            merchantmap.frontend_merchant_name,
            SUM(bfeedback.rating * 1.0) / COUNT(*) as avg_rating,
            COUNT(*) as total_ratings,
            SUM(CASE WHEN bfeedback.rating = 1 THEN 1 ELSE 0 END) as rating_1_count,
            SUM(CASE WHEN bfeedback.rating = 2 THEN 1 ELSE 0 END) as rating_2_count,
            SUM(CASE WHEN bfeedback.rating = 3 THEN 1 ELSE 0 END) as rating_3_count,
            SUM(CASE WHEN bfeedback.rating = 4 THEN 1 ELSE 0 END) as rating_4_count,
            SUM(CASE WHEN bfeedback.rating = 5 THEN 1 ELSE 0 END) as rating_5_count
        FROM 
            bistro_order_feedback bfeedback
        JOIN 
            bistro_fact_order_details_v2 bfact ON bfeedback.order_id = bfact.id
        JOIN 
            merchant_outlet_facility_mapping_v2 merchantmap ON bfact.merchant_id = merchantmap.frontend_merchant_id
        WHERE 
            ({dt_filters}) -- bracket is important here
            {dynamic_where_clause}
            AND {cutoff_date_condition}
        GROUP BY
            bfact.order_date,
            bfact.merchant_id,
            merchantmap.frontend_merchant_name
        ORDER BY
            bfact.order_date DESC,
            avg_rating DESC
        LIMIT 50000
        """
    else:
        return f"""
        SET useMultistageEngine=true;
        
        SELECT 
            bfact.order_date,
            merchantmap.frontend_merchant_city_name,
            SUM(bfeedback.rating * 1.0) / COUNT(*) as avg_rating,
            COUNT(*) as total_ratings,
            SUM(CASE WHEN bfeedback.rating = 1 THEN 1 ELSE 0 END) as rating_1_count,
            SUM(CASE WHEN bfeedback.rating = 2 THEN 1 ELSE 0 END) as rating_2_count,
            SUM(CASE WHEN bfeedback.rating = 3 THEN 1 ELSE 0 END) as rating_3_count,
            SUM(CASE WHEN bfeedback.rating = 4 THEN 1 ELSE 0 END) as rating_4_count,
            SUM(CASE WHEN bfeedback.rating = 5 THEN 1 ELSE 0 END) as rating_5_count
        FROM 
            bistro_order_feedback bfeedback
        JOIN 
            bistro_fact_order_details_v2 bfact ON bfeedback.order_id = bfact.id
        JOIN 
            merchant_outlet_facility_mapping_v2 merchantmap ON bfact.merchant_id = merchantmap.frontend_merchant_id
        WHERE 
            ({dt_filters}) -- bracket is important here
            {dynamic_where_clause}
            AND {cutoff_date_condition}
        GROUP BY
            bfact.order_date,
            merchantmap.frontend_merchant_city_name
        ORDER BY
            bfact.order_date DESC,
            avg_rating DESC
        LIMIT 50000
        """

def get_station_ratings_query(dynamic_where_clause: str,
                             yesterday_metric: bool = False,
                             date_str: str = None,) -> str:
    global table_aliases

    if not date_str:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

    if yesterday_metric:
        date_str = (datetime.now() + timedelta(hours=5, minutes=30) - timedelta(days=1)).strftime('%Y-%m-%d')
        
    is_today = date_str==(datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

    dt_filters: str = """
    (order_date = DATETIMECONVERT( FromEpochSeconds(now())/1000 , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ))
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*7*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*7*1000) )
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*14*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*14*1000) )
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*21*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*21*1000) )
    OR
    (order_date = DATETIMECONVERT( (FromEpochSeconds(now())/1000  - 86400*28*1000) , '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' ) AND insert_timestamp <= (FromEpochSeconds(now())/1000  - 86400*28*1000) )
""" if is_today else generate_dt_filters_custom_date(date_str=date_str,
                                                   column_name='order_date',
                                                   num_intervals=4,
                                                   interval_days=7)

    # Table aliases
    feedback_table = table_aliases['bistro_order_feedback']
    fact_table = table_aliases['bistro_fact_order_details_v2']
    merchant_map_table = table_aliases['merchant_outlet_facility_mapping_v2']
    station_map_table = table_aliases['analytics_bistro_product_station_mapping_v2']

    joins = f"""
        FROM bistro_order_feedback {feedback_table}
    """
    
    joins += f"""
    JOIN bistro_fact_order_details_v2 {fact_table} ON {feedback_table}.order_id = {fact_table}.id
    """

    joins += f"""
        JOIN analytics_bistro_product_station_mapping_v2 {station_map_table} ON {feedback_table}.product_id = {station_map_table}.product_id
    """
    group_by_columns = f"{fact_table}.order_date, "
    date_select = f"{fact_table}.order_date,"
    
    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"{feedback_table}.dt >= '{cutoff_date}'"
    
    station_ratings_query = f"""
        SET useMultistageEngine=true;
        
        SELECT 
            {date_select}
            {feedback_table}.rating,
            count(*) as rating_count
        {joins}
        WHERE 
            ({dt_filters})
            {dynamic_where_clause}
            AND {cutoff_date_condition}
        GROUP BY 
            {group_by_columns}
            {feedback_table}.rating
        LIMIT 500000
    """
    return station_ratings_query