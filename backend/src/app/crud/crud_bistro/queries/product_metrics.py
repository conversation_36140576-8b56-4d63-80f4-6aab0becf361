from datetime import datetime, timedelta, timezone
from ....common import product_metric as product_metric_query_utils

def get_all_product_metrics_without_item_grouping_query(metrics_sql,
                                  dynamic_where_clause: str,
                                  yesterday_metric: bool = False,
                                  is_daywise: bool = False,
                                  current_hour: bool = True,
                                  date_str: str = None) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
                                date_column='order_date',
                                timestamp_column='insert_timestamp',
                                timestamp_column_in_seconds=False,
                                is_daywise=is_daywise,
                                current_hour=current_hour,
                                yesterday_metric=yesterday_metric,
                                date_str=date_str,
                                number_of_days=7,
                                number_of_week=5)

    date_column = "order_date"
    table_name = "bistro_fact_order_item_details_v2"
    
    no_ingredients_condition = "AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l1_category', 'product_id', item_product_id) NOT IN ('Ingredients')"
    launch_date_filter = " AND (order_date > '2024-11-22')"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"
    
    all_products_metric_query = f"""
        SELECT
            {date_column},
            {metrics_sql}
        FROM {table_name}
        WHERE ({dt_filters})
              {dynamic_where_clause}
              {launch_date_filter}
              {no_ingredients_condition}
              AND {cutoff_date_condition}
        GROUP BY
            {date_column}
        LIMIT 50000
    """
    return all_products_metric_query

def get_all_product_metrics_query(metrics_sql,
                                  dynamic_where_clause: str,
                                  yesterday_metric: bool = False,
                                  is_daywise: bool = False,
                                  current_hour: bool = True,
                                  date_str: str = None,
                                  has_ipc_metric: bool = False) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
                                date_column='order_date',
                                timestamp_column='insert_timestamp', 
                                timestamp_column_in_seconds=False, 
                                is_daywise=is_daywise, 
                                current_hour=current_hour, 
                                yesterday_metric=yesterday_metric, 
                                date_str=date_str,
                                number_of_days=7,  
                                number_of_week=5)

    date_column = "order_date"
    table_name = "bistro_fact_order_item_details_v2"
    category_select = "LOOKUP('product_l0_l1_l2_mapping_v1', 'product_name', 'product_id', item_product_id) as item_name"
    category_groupby = "LOOKUP('product_l0_l1_l2_mapping_v1', 'product_name', 'product_id', item_product_id)"
    
    no_ingredients_condition = "AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l1_category', 'product_id', item_product_id) NOT IN ('Ingredients')"
    launch_date_filter = " AND (order_date > '2024-11-22')"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"
    
    all_products_metric_query = f"""
        SELECT
            {date_column},
            {category_select},
            {metrics_sql}
        FROM {table_name}
        WHERE ({dt_filters})
              {dynamic_where_clause}
              {launch_date_filter}
              {no_ingredients_condition}
              AND {cutoff_date_condition}
        GROUP BY
            {date_column},
            {category_groupby}
        LIMIT 50000
    """
    return all_products_metric_query


def get_distinct_cart_ids_in_whole_city_store_query(dynamic_where_clause: str,
                                  yesterday_metric: bool = False,
                                  is_daywise: bool = False,
                                  current_hour: bool = True) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
                            date_column='order_date', 
                            timestamp_column='insert_timestamp', 
                            timestamp_column_in_seconds=False, 
                            is_daywise=is_daywise, 
                            current_hour=current_hour, 
                            yesterday_metric=yesterday_metric, 
                            number_of_days=7,  
                            number_of_week=5)
    
    order_by_columns = '''order_date'''
    table_name = "bistro_fact_order_item_details_v2"
    no_ingredients_condition = "AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l1_category', 'product_id', item_product_id) NOT IN ('Ingredients')"
    launch_date_filter = " AND (order_date > '2024-11-22')"

    cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    cutoff_date_condition = f"order_date >= '{cutoff_date}'"

    distinct_cart_ids_query = f"""
        SELECT 
            {order_by_columns},
            SEGMENTPARTITIONEDDISTINCTCOUNT(cart_id) as cart_ids
        FROM {table_name}
        WHERE ({dt_filters})
              {dynamic_where_clause}
              {launch_date_filter}
              {no_ingredients_condition}
              AND {cutoff_date_condition}
        GROUP BY {order_by_columns}
        LIMIT 50000
    """
    return distinct_cart_ids_query


def get_complaints_query(dynamic_where_clause: str,
                         yesterday_metric: bool = False,
                         is_daywise: bool = False,
                         current_hour: bool = True, 
                         date_str: str = None,
                         category_wise: bool = False) -> str:
    dt_filters: str = product_metric_query_utils.generate_dt_filters_product_metrics(
                            date_column='dt', 
                            timestamp_column='created_at', 
                            timestamp_column_in_seconds=False, 
                            is_daywise=is_daywise, 
                            current_hour=current_hour, 
                            yesterday_metric=yesterday_metric,
                            date_str=date_str, 
                            number_of_days=7,  
                            number_of_week=5)

    order_by_columns = "dt , "
    
    category_select = ""
    category_groupby = ""
    if category_wise:
        category_select = "LOOKUP('product_l0_l1_l2_mapping_v1', 'product_name', 'product_id', product_id) as item_name , "
        category_groupby = "LOOKUP('product_l0_l1_l2_mapping_v1', 'product_name', 'product_id', product_id) , "
    
    table_name = "fact_order_complaints_v2"
    no_ingredients_condition = "AND LOOKUP('product_l0_l1_l2_mapping_v1', 'l1_category', 'product_id', product_id) NOT IN ('Ingredients')"
    product_id_not_null_condition = "AND product_id is NOT NULL"
    store_id_not_null_condition = "AND store_id is NOT NULL"

    all_complaints_metric_query = f"""
        SELECT 
            {order_by_columns}
            {category_select}
            complaint_type,
            DISTINCTCOUNT(complaint_id)
        FROM {table_name}
        WHERE ({dt_filters})
            {dynamic_where_clause}
            {no_ingredients_condition}
            {product_id_not_null_condition}
            {store_id_not_null_condition}
        GROUP BY 
            {order_by_columns}
            {category_groupby}
            complaint_type
        LIMIT 50000
    """
    return all_complaints_metric_query


def get_all_field_list_query(field: str, table_name: str)-> str:
    all_field_list_query = f"""
        SELECT DISTINCT({field})
        FROM {table_name}
        WHERE l0_category = 'Bistro'
        AND   l1_category NOT IN ('Ingredients')
        LIMIT 50000
    """
    return all_field_list_query


def get_filtered_product_fieldwise_query(dynamic_where_clause: str, search_filter: str, table_name: str) -> str:
    filtered_product_fieldwise_query = f"""
        SELECT
            DISTINCT({search_filter})
        FROM {table_name}
        {'WHERE ' + dynamic_where_clause if dynamic_where_clause else ''}
        LIMIT 2000
    """
    return filtered_product_fieldwise_query
