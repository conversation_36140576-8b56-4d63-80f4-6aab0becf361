from datetime import datetime, timedelta

def get_store_city_mapping_query(dynamic_where_clause: str) -> str:
    columns = """
    frontend_merchant_id,
    frontend_merchant_name, 
    frontend_merchant_city_name,
    backend_merchant_id,
    pos_outlet_id,
    type
    """
    DUMMY_MERCHANT_REMOVE_FILTER = "AND frontend_merchant_id NOT IN (35758)"
    return f"""select {columns}
                from merchant_outlet_facility_mapping_v2
                where  (frontend_merchant_city_name not in ('Not in service area'))
                {dynamic_where_clause}
                {DUMMY_MERCHANT_REMOVE_FILTER}
                and tenant_name = 'BISTRO'
                limit 1000000"""


def get_active_stores_count(dynamic_where_clause: str, is_yesterday: bool = False, date_str: str = None) -> str:
    """
    Returns a query that counts distinct active stores live before selected date minus time offsets.
    live_date is a string in 'yyyy-MM-dd' format.
    """

    base_date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    trend_queries = []

    # T0 base: total outlets live on or before date_str
    t0_condition = [
        "COUNT(DISTINCT pos_outlet_id) AS total_lt_t0",
    ]

    # Build 7, 14, 21, 28 day comparisons
    for days_back in [7, 14, 21, 28]:
        target_date = (base_date_obj - timedelta(days=days_back)).strftime('%Y-%m-%d')
        trend_queries.append(
            f"SUM(CASE WHEN live_date <= '{target_date}' THEN 1 ELSE 0 END) AS total_lt_t{days_back}"
        )


    query = f"""
        SELECT {', '.join(t0_condition + trend_queries)}
        FROM merchant_outlet_facility_mapping_v2
        WHERE dim_is_outlet_active = 1
        AND tenant_name = 'BISTRO'
        {dynamic_where_clause}
        ORDER BY 1 DESC
        LIMIT 1
    """
    return query
