
def get_wtd_mtd_query(metric_type: str, dynamic_where_clause: str):
    if metric_type == "mtd":
        table_name = "bistro_agg_mtd_sonar_key_business_metrics"
    elif metric_type == "wtd":
        table_name = "bistro_agg_wtd_sonar_key_business_metrics"

    query = f"""
        SELECT * 
        FROM 
            {table_name}
        {("WHERE " + (dynamic_where_clause)) if dynamic_where_clause else ""}
        LIMIT 100000
    """
    return query
