from collections import defaultdict
from datetime import datetime, timedelta, timezone
import math
from decimal import Decimal
from app.schemas.order_metric import Metric, LocationMetric
from typing import List, Dict, Any
from app.schemas.product_metric import ProductMetricTypes
from ..utils import get_etl_snapshot_ts, create_metrics_data

IST = timezone(timedelta(hours=5, minutes=30))
UTC = timezone(timedelta(hours=0))

def process_citywise_results(api_results: List[Dict], metrics: List[str]) -> Dict:
    citywise_results = defaultdict(list)
    etl_snapshot_ts_ist_obj = get_etl_snapshot_ts(api_results)
    for row in api_results:
        metrics_data = create_metrics_data(row, metrics)
        citywise_results[(row["snapshot_date"], etl_snapshot_ts_ist_obj)].append(LocationMetric(type="city", name=row["city_name"], data=metrics_data))
    return citywise_results


def generate_dt_filters(date_str=None,
                        date_column="dt",
                        timestamp_column=None, num_weeks=5,
                        less_than_time="now()/1000", period_length=7, timestamp_column_in_seconds: bool = False,
                        is_yesterday: bool = False):
    date_format = '%Y-%m-%d'
    dt_filters = []

    ist_offset = timedelta(hours=5, minutes=30)
    date_ist = datetime.strptime(date_str, date_format)
    now_ist = datetime.now() + ist_offset
    if is_yesterday and date_ist.date() == now_ist.date():
        date_ist -= timedelta(days=1)

    if date_ist.date() == now_ist.date():
        for i in range(num_weeks):
            order_date = (date_ist - timedelta(days=period_length*i)).strftime(date_format)
            if timestamp_column:
                dt_filters.append(
                    f"({date_column} = '{order_date}' AND {timestamp_column} <= "
                    f"FromEpochSeconds({less_than_time} - 86400*{period_length*i}))" if not timestamp_column_in_seconds
                    else f"({date_column} = '{order_date}' AND {timestamp_column} <="
                         f" ({less_than_time} - 86400*{period_length*i}))"
                )
            else:
                dt_filters.append(
                    f"({date_column} = '{order_date}')")
    else:
        for i in range(num_weeks):
            order_date = (date_ist - timedelta(days=period_length*i)).strftime(date_format)
            dt_filters.append(f"({date_column} = '{order_date}')")

    return " OR\n".join(dt_filters)


def generate_dt_filters_custom_date(date_str: str, 
                                    column_name: str,
                                    num_intervals: int = 4, 
                                    interval_days: int = 7, 
                                    start_idx: int = 0) -> str:
    base_date = datetime.strptime(date_str, "%Y-%m-%d")
    filters = [
        f"({column_name} = '{(base_date - timedelta(days=i * interval_days)).strftime('%Y-%m-%d')}')"
        for i in range(start_idx, num_intervals + 1)
    ]
    return " OR\n".join(filters)

def get_dates_in_ist_format(start_date: str, jump: int, num_of_outputs: int):
    # Parse the start date string into a datetime object
    start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')

    # Calculate the list of dates based on the jump and number of outputs
    dates = []
    for i in range(num_of_outputs):
        # Calculate the date based on the jump
        target_date = start_date_obj - timedelta(days=i * jump)
        # Format the date as 'YYYY-MM-DD'
        formatted_date = target_date.strftime('%Y-%m-%d')
        dates.append(formatted_date)

    return dates

def get_date_str(is_yesterday: bool):
    today = datetime.now(IST)
    return (today - timedelta(days=1)).strftime('%Y-%m-%d') if is_yesterday else today.strftime('%Y-%m-%d')

