
from datetime import datetime, timezone, timedelta
from typing import List, Optional
from decimal import Decimal
from collections import defaultdict

from pinotdb import connect

from .queries import get_keyword_metrics_query
from app.schemas.order_metric import OrderDatewiseMetric, ConversionGroupMetric, DatewiseMetric
from app.core.metric_config import METRICS_TYPE_MAPPING, METRIC_NAME_MAPPING

class CRUDKeywordMetrics:
    
    def get_keyword_metrics(
        self,
        conn: connect,
        city: List = None,
        store: List = None,
        zone: List = None,
        start_date: Optional[str] = None,
    ) -> List[ConversionGroupMetric]:
        """
        Get the top 50 search keywords by volume along with their search-to-ATC conversion metrics
        
        Args:
            conn: Pinot database connection
            city: List of cities to filter by
            store: List of stores to filter by
            zone: List of zones to filter by
            start_date: Optional date string in YYYY-MM-DD format
            
        Returns:
            List of ConversionGroupMetric objects containing keyword metrics
        """
        # Always use merchant table if zone is provided
        table_name = f"daily_merchant_keyword_metrics" if (store or zone) else f"daily_city_keyword_metrics"

        dynamic_where_clause = []
        params = {}
        
        # Add city filter if provided
        if not city:
            city = ['Overall']
        dynamic_where_clause.append("AND city IN (%(cities)s)")
        params["cities"] = city
        
        # Add store filter if provided
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(store)s)")
            params["store"] = store
            
        # Add zone filter if provided
        if zone:
            dynamic_where_clause.append("AND LOOKUP('merchant_outlet_facility_mapping_v2', 'zone', 'frontend_merchant_id', merchant_id) IN (%(zone)s)")
            params["zone"] = zone

        curs = conn.cursor()

        # Generate and execute the query
        query = get_keyword_metrics_query(
            table_name=table_name,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            start_date=start_date,
        )
        
        curs.execute(query, params)
        
        # Handle zone-level aggregation
        if zone:
            # First, collect all data by merchant and keyword for aggregation
            merchant_keyword_data = defaultdict(lambda: defaultdict(list))
            for row in curs:
                date = row[0]
                keyword = row[1]
                merchant_id = row[2]
                t0_searches = float(row[3] or 0)
                t0_atc = float(row[4] or 0)
                t0_conversion = float(row[5] or 0)
                spike_percent = float(row[6] or 0)
                t7_searches = float(row[7] or 0)
                
                merchant_keyword_data[keyword][merchant_id].append({
                    'date': date,
                    't0_searches': t0_searches,
                    't0_atc': t0_atc,
                    't0_conversion': t0_conversion,
                    'spike_percent': spike_percent,
                    't7_searches': t7_searches
                })
            
            # Aggregate by keyword across all merchants
            results = defaultdict(list)
            for keyword, merchants_data in merchant_keyword_data.items():
                # Sum up metrics across all merchants for this keyword
                total_t0_searches = 0
                total_t0_atc = 0
                total_t7_searches = 0
                
                # Use the date from the first record (should be the same for all)
                date = next(iter(merchants_data.values()))[0]['date'] if merchants_data else None
                
                # Sum metrics across all merchants
                for merchant_id, data_list in merchants_data.items():
                    for data in data_list:
                        total_t0_searches += data['t0_searches']
                        total_t0_atc += data['t0_atc']
                        total_t7_searches += data['t7_searches']
                
                # Calculate zone-level metrics
                zone_t0_conversion_percent = (total_t0_atc / total_t0_searches * 100) if total_t0_searches > 0 else 0
                zone_spike_percent = ((total_t0_searches - total_t7_searches) / total_t7_searches * 100) if total_t7_searches > 0 else 0
                
                # Create metrics
                t0_searches_metrics = [
                    DatewiseMetric(date=date, metric=round(total_t0_searches, 2))
                ]
                t0_conversion_percent_metrics = [
                    DatewiseMetric(date=date, metric=round(zone_t0_conversion_percent, 2))
                ]
                spike_percent_7d_metrics = [
                    DatewiseMetric(date=date, metric=round(zone_spike_percent, 2))
                ]
                
                # Add to results
                results[keyword].append(
                    OrderDatewiseMetric(
                        name="Search Impressions",
                        data=t0_searches_metrics,
                        type="number"
                    )
                )
                results[keyword].append(
                    OrderDatewiseMetric(
                        name="Search Conversion %",
                        data=t0_conversion_percent_metrics,
                        type="percentage"
                    )
                )
                results[keyword].append(
                    OrderDatewiseMetric(
                        name="Spike % WoW1",
                        data=spike_percent_7d_metrics,
                        type="percentage"
                    )
                )
        else:
            # Process results directly if no zone aggregation is needed
            results = defaultdict(list)
            for row in curs:
                keyword = row[1]
                # Row indices will be different depending on whether we're using merchant table or not
                offset = 1 if "merchant" not in table_name else 2
                
                t0_searches_metrics = [
                    DatewiseMetric(date=row[0], metric=round(float(row[offset+1]), 2))
                ]
                t0_conversion_percent = [
                    DatewiseMetric(date=row[0], metric=round(float(row[offset+3]), 2))
                ]
                spike_percent_7d = [
                    DatewiseMetric(date=row[0], metric=round(float(row[offset+4]), 2))
                ]
                
                results[keyword].append(
                    OrderDatewiseMetric(
                        name="Search Impressions",
                        data=t0_searches_metrics,
                        type="number"
                    )
                )
                results[keyword].append(
                    OrderDatewiseMetric(
                        name="Search Conversion %",
                        data=t0_conversion_percent,
                        type="percentage"
                    )
                )
                results[keyword].append(
                    OrderDatewiseMetric(
                        name="Spike % WoW1",
                        data=spike_percent_7d,
                        type="percentage"
                    )
                )

        final_results = [
            ConversionGroupMetric(
                key=keyword,
                grain=keyword,
                metric=metrics
            )
            for keyword, metrics in results.items()
        ]
        
        # Filter out keywords with negative spikes
        filtered_results = [
            result for result in final_results
            if next(
                (m.data[0].metric for m in result.metric if m.name == "Spike % WoW1"),
                0
            ) >= 0
        ]
        
        sorted_results = sorted(
            filtered_results,
            key=lambda x: next(
                (m.data[0].metric for m in x.metric if m.name == "Search Impressions"),
                0
            ),
            reverse=True
        )

        return sorted_results[:30]

keyword_metrics = CRUDKeywordMetrics()
