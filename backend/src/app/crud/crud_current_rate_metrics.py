from typing import List
from datetime import datetime, timezone, timedelta
import collections, functools, operator

from pinotdb import connect


from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING
from app.schemas.order_metric import HourMetric, HourlyDateMetric, Metric
from app.schemas.current_rate_metric import CurrentRateDatewiseMetric, CurrentRateMetric

from .queries import (
    ORDER_METRICS_PROJECTED_HOURLY_QUERY,
    HAU_PROJECTED_HOURLY_QUERY,
)

IST = timezone(timedelta(hours=5, minutes=30))


class CrudCurrentRateMetrics:
    @staticmethod
    def _fetch_metric_aggregate(aggregate, metric: dict):
        return aggregate(metric.values())

    @staticmethod
    def _percentage_diff(total_today: float, total_past: float):
        return (total_today - total_past) / total_past

    @staticmethod
    def _average(metric: List):
        return sum(metric) / len(metric)

    def _sorted_datewise_result(self, metrics_wise_result):
        datewise_results = []
        for dt_str, result in metrics_wise_result["data"].items():
            datewise_results.append(
                {
                    "date": dt_str,
                    "date_diff": (
                        datetime.now(IST)
                        - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)
                    ).days,
                    "data": result,
                }
            )
        datewise_results.sort(key=lambda key_: key_.get("date"))
        return datewise_results

    def _calculate_till_now_diff_percentage(
        self, datewise_results, current_hour_aggregate_metric, till_now_hours
    ):
        till_now_diff_percentage = {}

        for past_metric in datewise_results[:-1]:
            till_now_past_hours_metric = {
                hr: past_metric["data"].get(hr, None) for hr in till_now_hours
            }
            till_now_past_hours_aggregate_metric = self._fetch_metric_aggregate(
                sum, till_now_past_hours_metric
            )
            dt = past_metric["date"]
            till_now_diff_percentage[dt] = self._percentage_diff(
                current_hour_aggregate_metric,
                till_now_past_hours_aggregate_metric,
            )
        return till_now_diff_percentage

    def _calculate_current_rate_from_now(
        self, datewise_results, till_now_diff_percentage, remaining_hours
    ):
        current_rate_from_now_metric = {}
        current_rate_from_now_past_hourly_metric = []
        for past_metric in datewise_results[:-1]:
            dt = past_metric["date"]
            dp = till_now_diff_percentage[dt]
            from_now_past_hours_metric = {
                hr: (past_metric["data"].get(hr, 0) * dp) + past_metric["data"].get(hr, 0)
                for hr in remaining_hours
            }
            current_rate_from_now_past_hourly_metric.append(from_now_past_hours_metric)
            current_rate_from_now_metric[dt] = self._fetch_metric_aggregate(
                sum, from_now_past_hours_metric
            )
        return current_rate_from_now_metric, current_rate_from_now_past_hourly_metric

    def _aggregate_daily_current_rate(
        self,
        datewise_results,
        metrics_wise_result,
        current_rate_total_metrics,
        current_date,
        current_hour_aggregate_metric,
        current_rate_from_now_metric,
    ):
        for result in datewise_results[:-1]:
            current_rate_total_metrics.append(
                {
                    "date": result["date"],
                    "metric": self._fetch_metric_aggregate(sum, result["data"]),
                },
            )
        current_rate_total_metrics.append(
            {
                "date": current_date,
                "metric": int(
                    current_hour_aggregate_metric
                    + self._average(current_rate_from_now_metric.values())
                ),
            }
        )
        return CurrentRateDatewiseMetric(
            name=metrics_wise_result["metric"],
            data=current_rate_total_metrics,
            type=metrics_wise_result["type"],
        )

    def _aggregate_hourly_current_rate(
        self,
        datewise_results,
        metrics_wise_result,
        current_rate_from_now_past_hourly_metric,
        current_date,
        past_days_diff,
    ):
        current_rate_from_now_hourly_metric = {
            key_: val_ / len(current_rate_from_now_past_hourly_metric)
            for key_, val_ in dict(
                functools.reduce(
                    operator.add,
                    map(
                        collections.Counter,
                        current_rate_from_now_past_hourly_metric,
                    ),
                )
            ).items()
        }
        past_day_result = [
            datewise_result
            for datewise_result in datewise_results
            if datewise_result["date_diff"] == int(past_days_diff)
        ]
        current_rate_hourly_metric = [
            {
                "date": current_date,
                "date_diff": 0,
                "data": current_rate_from_now_hourly_metric,
            },
        ]
        current_rate_hourly_metric.extend(past_day_result)

        hourly_current_rate_metric = []
        for metric in current_rate_hourly_metric:
            hourly_results = [
                HourMetric(
                    hour=hr,
                    data=[
                        Metric(
                            name=metrics_wise_result["metric"],
                            type=metrics_wise_result["type"],
                            metric=int(mt),
                        )
                    ],
                )
                for hr, mt in metric["data"].items()
            ]
            hourly_current_rate_metric.append(
                HourlyDateMetric(
                    date=metric["date"],
                    date_diff=metric["date_diff"],
                    data=hourly_results,
                )
            )
        return hourly_current_rate_metric

    def _calculate_current_rate_metric(
        self, metrics_wise_result: dict, past_days_diff=7, aggregate="daily"
    ):
        current_date = datetime.now(IST).strftime("%Y-%m-%d")
        today_metrics = metrics_wise_result["data"].get(current_date)
        current_rate_total_metrics = []
        # Number of Hours that have passed
        hours_passed = len(today_metrics.keys()) if today_metrics else 0
        if (today_metrics is not None) & (
            hours_passed >= 9
        ):  # If we have data for Today and 9 Hours have passed
            datewise_results = self._sorted_datewise_result(metrics_wise_result)
            # Hours that have passed
            till_now_hours = datewise_results[-1]["data"].keys()
            # Hours remaining
            remaining_hours = list(set(range(0, 24)) - set(till_now_hours))

            # Current hour aggregate (since sorted will always be -1 index)
            current_hour_aggregate_metric = self._fetch_metric_aggregate(
                sum, datewise_results[-1]["data"]
            )

            # Calculate diff percentage of metrics for past days
            till_now_diff_percentage = self._calculate_till_now_diff_percentage(
                datewise_results, current_hour_aggregate_metric, till_now_hours
            )

            # Project metric for remaining hours based on the percentage diff of past metrics at Datewise and Hourly level
            (
                current_rate_from_now_metric,
                current_rate_from_now_past_hourly_metric,
            ) = self._calculate_current_rate_from_now(
                datewise_results, till_now_diff_percentage, remaining_hours
            )

            if aggregate == "daily":
                # Daily aggregate
                return self._aggregate_daily_current_rate(
                    datewise_results,
                    metrics_wise_result,
                    current_rate_total_metrics,
                    current_date,
                    current_hour_aggregate_metric,
                    current_rate_from_now_metric,
                )
            elif aggregate == "hourly":
                # Hourly aggregate
                return self._aggregate_hourly_current_rate(
                    datewise_results,
                    metrics_wise_result,
                    current_rate_from_now_past_hourly_metric,
                    current_date,
                    past_days_diff,
                )
        return current_rate_total_metrics

    def get_current_rate_order_metrics(
        self, conn: connect, metrics: List
    ) -> List[CurrentRateMetric]:
        curs = conn.cursor()
        metrics_calc = [METRICS_CALC_MAPPING[metric] for metric in metrics]
        metrics_calc_sql = ", ".join(metrics_calc)
        curs.execute(
            ORDER_METRICS_PROJECTED_HOURLY_QUERY.format(metrics_sql=metrics_calc_sql)
        )
        metrics_wise_results = []
        for metric in metrics:
            metric_data = {
                "metric": METRIC_NAME_MAPPING[metric],
                "type": METRICS_TYPE_MAPPING[metric],
                "data": {},
            }
            metrics_wise_results.append(metric_data)
        for row in curs:
            idx = 0
            for _ in range(len(metrics_wise_results)):
                if not metrics_wise_results[idx]["data"].get(row[1]):
                    metrics_wise_results[idx]["data"][row[1]] = {}
                    metrics_wise_results[idx]["data"][row[1]][row[0]] = row[2 + idx]
                else:
                    metrics_wise_results[idx]["data"][row[1]][row[0]] = row[2 + idx]
                idx += 1

        current_rate_order_metric_results = []
        for metrics_wise_result in metrics_wise_results:
            current_rate_order_metric_results.append(
                self._calculate_current_rate_metric(metrics_wise_result)
            )
        return current_rate_order_metric_results

    def get_current_rate_hau_metrics(self, conn: connect, past_days_diff):
        curs = conn.cursor()
        metrics_wise_results = [
            {
                "metric": METRIC_NAME_MAPPING["hau"],
                "type": METRICS_TYPE_MAPPING["hau"],
                "data": {},
            }
        ]
        curs.execute(HAU_PROJECTED_HOURLY_QUERY)
        for row in curs:
            idx = 0
            for _ in range(len(metrics_wise_results)):
                if not metrics_wise_results[idx]["data"].get(row[1]):
                    metrics_wise_results[idx]["data"][row[1]] = {}
                    metrics_wise_results[idx]["data"][row[1]][row[0]] = row[3 + idx]
                else:
                    metrics_date_wise = metrics_wise_results[idx]["data"][row[1]]
                    if metrics_date_wise.get(row[0]):
                        metrics_date_wise[row[0]] += row[3 + idx]
                    else:
                        metrics_date_wise[row[0]] = row[3 + idx]
                idx += 1
        current_rate_hau_metric_results = []
        for metrics_wise_result in metrics_wise_results:
            current_rate_hau_metric_results.extend(
                self._calculate_current_rate_metric(
                    metrics_wise_result, past_days_diff, aggregate="hourly"
                )
            )
        return current_rate_hau_metric_results


current_rate_metrics = CrudCurrentRateMetrics()
