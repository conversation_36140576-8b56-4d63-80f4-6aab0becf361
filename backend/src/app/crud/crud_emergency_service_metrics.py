from datetime import datetime, timezone, timedelta
from typing import List, Union, Dict
from collections import defaultdict
import math

from pinotdb import connect

from app.api import deps,utils
import app.crud.utils as support_utils
from app.core.metric_config_emergency_service import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING
from .emergency_service_queries import get_all_order_metrics_query

from app.schemas.order_metric import OrderDatewiseMetric
IST = timezone(timedelta(hours=5, minutes=30))


class CRUDEmergencyServiceMetrics():
    
    def get_all_metrics(self, 
                        conn: connect, 
                        metrics: List = None, 
                        date_str: str = None, ) -> List[OrderDatewiseMetric]:

        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]

        query = get_all_order_metrics_query(metrics_sql = metrics_sql,
                                            dynamic_where_clause=" ".join(dynamic_where_clause),
                                            date_str=date_str)
        query = '''
            SET enableNullHandling = True;
            SET serverReturnFinalResult = True;
        ''' + query

        curs.execute(query, params)

        metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)

        for row in curs:
            order_date = datetime.strptime(row[0], "%Y-%m-%d")
            for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                metric_value = row[idx]
                if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) \
                        and not math.isinf(metric_value):
                    metric_value = round(float(metric_value), 2)
                else:
                    continue
                metrics_data[metric_name].append({"date": order_date, "metric": metric_value})

        results = [
            OrderDatewiseMetric(
                name=METRIC_NAME_MAPPING[metric_name],
                data=metrics_data[metric_name],
                type=METRICS_TYPE_MAPPING[metric_name]
            )
            for metric_name, _ in metrics_items
        ]
        return results

emergency_service_metrics = CRUDEmergencyServiceMetrics()