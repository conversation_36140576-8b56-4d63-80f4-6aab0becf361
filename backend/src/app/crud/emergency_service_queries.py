from .utils import generate_dt_filters_custom_date
from .queries import generate_dt_filters
from datetime import datetime, timedelta

def get_all_order_metrics_query(metrics_sql,dynamic_where_clause: str,
                                date_str: str = None,
                                is_hourly: bool = False) -> str:
    today_date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')
    if date_str == today_date_str: 
        dt_filters = generate_dt_filters(date_str=date_str,
                                         date_column='order_date', 
                                         timestamp_column='insert_timestamp', 
                                         num_weeks=5)
    else:
        dt_filters = generate_dt_filters_custom_date(date_str=date_str,
                                                     column_name='order_date', 
                                                     num_intervals=4,
                                                     interval_days=7,)
    
    hour_col = ""
    grp_by_hr = ""
    if is_hourly:
        hour_col += "order_hour,"
        grp_by_hr += ", order_hour"
    all_orders_metric_query = f"""
        SELECT {hour_col}
        order_date,
            {metrics_sql}
        FROM emergency_services_fact_order_details
        WHERE ({dt_filters})
        {dynamic_where_clause}
        GROUP BY order_date {grp_by_hr}
        LIMIT 50000
    """
    return all_orders_metric_query