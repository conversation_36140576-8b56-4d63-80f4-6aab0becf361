from datetime import datetime, timezone, timedelta
from collections import defaultdict

from pinotdb import connect

from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING
from .queries import CITY_LEVEL_AVAILABILITY_METRICS_QUERY
from app.schemas.availability_metric import HourlyDateMetrics, AvailabilityMetric, HourMetric, HourlyDateMetric

IST = timezone(timedelta(hours=5, minutes=30))


class CRUDAvailabilityMetrics:

    def get_hourly_availability_metrics(self, conn: connect, past_days_diff: int = 7) -> HourlyDateMetrics:
        curs = conn.cursor()
        get_last_hour_query = """
                    SELECT max(hour) FROM city_hourly_weighted_availability WHERE 
                                            date_ = DATETIMECONVERT(FromEpochSeconds(now()/1000), '1:MILLISECONDS:EPOCH',  '1:DAYS:SIMPLE_DATE_FORMAT:yyyy-MM-dd tz(Asia/Kolkata)', '1:DAYS' )
                """
        curs.execute(get_last_hour_query)
        get_last_hour_value = curs.fetchall()
        curs.execute(CITY_LEVEL_AVAILABILITY_METRICS_QUERY, {"past_days_diff": past_days_diff,
                                                             "max_hour": get_last_hour_value[0][0] if isinstance(
                                                                 get_last_hour_value[0][0], float) else -1})
        datewise_results = []
        hourly_results = defaultdict(list)
        for row in curs:
            result_metrics, idx = [], 0
            for metric in ['cum_weighted_availability', 'weighted_availability']:
                result_metrics.append(
                    AvailabilityMetric(name=METRIC_NAME_MAPPING[metric], metric=round(row[idx + 3], 2),
                                       type=METRICS_TYPE_MAPPING[metric]))
                idx += 1
            hourly_results[row[5]].append(
                HourMetric(type=row[0], city=row[1], hour=row[2], data=result_metrics))
        for dt_str, result in hourly_results.items():
            datewise_results.append(HourlyDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), date_diff=(
                    datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
        return HourlyDateMetrics(metrics=datewise_results)


availability_metrics = CRUDAvailabilityMetrics()
