from datetime import datetime
from datetime import datetime, timezone, timedelta
from collections import defaultdict
from typing import List, Union, Dict
import math

from pinotdb import connect

from app.schemas.metric import Metric
from app.core.metric_config import METRIC_NAME_MAPPING, METRICS_TYPE_MAPPING, METRICS_CALC_MAPPING
from app.schemas.order_metric import Metric, LocationDateMetric, LocationDateMetrics, LocationMetric, OrderDatewiseMetric, HourMetric, HourlyDateMetric, HourlyDateMetrics
from .queries import get_active_time_metrics_query, get_complaints_metrics_query, get_all_order_metrics_query, get_all_order_metrics_query_custom

IST = timezone(timedelta(hours=5, minutes=30))


class CRUDInStoreMetrics():
    def _calculate_total_orders(self, curs, metrics, 
                                city, store, status, app, 
                                yesterday_metric, is_hourly, 
                                city_wise, store_wise, 
                                custom_view, start_date):
        total_orders_dict = {}
        dynamic_where_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics]
        metrics_sql = ",\n".join(selected_sql)
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append(
                    "AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if city:
            dynamic_where_clause.append("AND city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND merchant_id IN (%(stores)s)")
            params["stores"] = store
        if not custom_view:
            query = get_all_order_metrics_query(metrics_sql=metrics_sql, 
                                                dynamic_where_clause=" ".join(dynamic_where_clause), 
                                                yesterday_metric=yesterday_metric, 
                                                is_hourly=is_hourly)
        else:
            query = get_all_order_metrics_query_custom(metrics_sql=metrics_sql, 
                                                dynamic_where_clause=" ".join(dynamic_where_clause), 
                                                is_hourly=is_hourly,
                                                start_date=start_date,)
        curs.execute(query, params)
        for row in curs:
            if is_hourly:
                order_hour = int(row[0])
                order_date = datetime.strptime(row[1], "%Y-%m-%d")
                if order_date not in total_orders_dict:
                    total_orders_dict[order_date] = {}
                total_orders_dict[order_date][order_hour] = row[2]
            else:
                order_date = datetime.strptime(row[0], "%Y-%m-%d")
                total_orders_dict[order_date] = row[1]
        return total_orders_dict


    def get_active_time_metrics(self, conn: connect, 
                                metrics: List = None, 
                                city: List = None, 
                                store: List = None, 
                                app: str = None, 
                                yesterday_metric: bool = False, 
                                is_hourly: bool = False, 
                                city_wise: bool = False,
                                store_wise: bool = False, 
                                merchant_type: str = None, 
                                merchant_ids: List = [],
                                custom_view: bool = False,
                                start_date: str = None, ) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        dynamic_where_clause = []
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key])
                         for key in metrics if key in METRICS_CALC_MAPPING]
        # TODO : create generic function for these filters as they are being used everywhere.
        if city:
            dynamic_where_clause.append(
                "AND frontend_merchant_city_name IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append(
                "AND frontend_merchant_id IN (%(stores)s)")
            params["stores"] = store
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append(
                    "AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if merchant_type:
            dynamic_where_clause.append("AND frontend_merchant_id IN (%(merchant_ids)s)")
            params["merchant_ids"] = merchant_ids
        query = get_active_time_metrics_query(metrics_sql=metrics_sql,
                                              dynamic_where_clause=" ".join(dynamic_where_clause), 
                                              yesterday_metric=yesterday_metric, 
                                              city=city, 
                                              store=store, 
                                              is_hourly=is_hourly,
                                              city_wise=city_wise,
                                              store_wise=store_wise,
                                              custom_view=custom_view,
                                              start_date=start_date)
        curs.execute(query, params)

        if not is_hourly:
            metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)
            results = []
            for row in curs:
                order_date = datetime.strptime(row[0], "%Y-%m-%d")
                for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                    metric_value = row[idx]
                    if isinstance(metric_value, (float, int, str)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                        metric_value = round(float(metric_value), 2)
                    else:
                        metric_value = 0.00  # Replace NaN or infinity with 0.0
                    metrics_data[metric_name].append(
                        {"date": order_date, "metric": metric_value})

            results = [
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING[metric_name],
                    data=metrics_data[metric_name],
                    type=METRICS_TYPE_MAPPING[metric_name]
                )
                for metric_name, _ in metrics_items
            ]
            return results
        elif is_hourly:
            datewise_results = []
            hourly_results = defaultdict(list)
            for row in curs:
                result_metrics, idx = [], 0
                for metric in metrics:
                    metric_value = row[idx+2]
                    if isinstance(metric_value, (float, int)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                        metric_value = round(float(metric_value), 2)
                    else:
                        metric_value = 0.00  # Replace NaN or infinity with 0.0
                    result_metrics.append(Metric(
                        name=METRIC_NAME_MAPPING[metric], metric=metric_value, type=METRICS_TYPE_MAPPING[metric]))
                    idx += 1
                hourly_results[row[1]].append(
                    HourMetric(hour=row[0], data=result_metrics))

            for dt_str, result in hourly_results.items():
                datewise_results.append(HourlyDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), date_diff=(
                    datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return HourlyDateMetrics(metrics=datewise_results)
        elif city_wise:
            results = []
            citywise_results = defaultdict(list)
            for row in curs:
                metrics_data = [
                    Metric(name=METRIC_NAME_MAPPING[metrics[0]], metric=round(
                        float(row[2]), 2), type=METRICS_TYPE_MAPPING[metrics[0]]),
                ]
                citywise_results[row[0]].append(
                    LocationMetric(type="city", name=row[1], data=metrics_data))
            for dt_str, result in citywise_results.items():
                results.append(LocationDateMetric(date=datetime.strptime(dt_str, "%Y-%m-%d"), etl_snapshot_ts_ist=None, date_diff=(
                    datetime.now(IST) - datetime.strptime(dt_str, "%Y-%m-%d").replace(tzinfo=IST)).days, data=result))
            return LocationDateMetrics(metrics=results)


    def get_complaints_metrics(
            self, 
            conn: connect,
            metrics: List = None, 
            city: List = None, 
            store: List = None, 
            status: List = None, 
            app: str = None, 
            yesterday_metric: bool = False,
            is_hourly: bool = False,
            city_wise: bool = False,
            store_wise: bool = False,
            merchant_type: str = None,
            merchant_ids: List = [],
            custom_view: bool = False,
            start_date: str = None, ) -> List[OrderDatewiseMetric]:
        curs = conn.cursor()
        total_orders = self._calculate_total_orders(curs, 
                                                    ["total_orders"], 
                                                    city, store, 
                                                    status, app, 
                                                    yesterday_metric, 
                                                    is_hourly, city_wise, store_wise,
                                                    custom_view, start_date )
        dynamic_where_clause = []
        dynamic_where_clause.append("AND rca_status IN ('COMPLETED')")
        params = {}
        selected_sql = [METRICS_CALC_MAPPING[key] for key in metrics if key in METRICS_CALC_MAPPING]
        metrics_sql = ",\n".join(selected_sql)
        metrics_items = [(key, METRICS_CALC_MAPPING[key]) for key in metrics if key in METRICS_CALC_MAPPING]
        if city:
            dynamic_where_clause.append("AND lookup('outlet_facility_mapping_v1','city_name','outlet_id', store_id) IN (%(cities)s)")
            params["cities"] = city
        if store:
            dynamic_where_clause.append("AND lookup('outlet_facility_mapping_v1','merchant_id','outlet_id', store_id) IN (%(stores)s)")
            params["stores"] = store
        if app:
            if app.casefold() == "blinkit".casefold():
                dynamic_where_clause.append(
                    "AND (org_channel_id LIKE '1' OR org_channel_id = 'null')")
            else:
                dynamic_where_clause.append("AND org_channel_id LIKE '2'")
        if merchant_type:
            dynamic_where_clause.append("AND lookup('outlet_facility_mapping_v1','merchant_id','outlet_id', store_id) IN (%(merchant_ids)s)")
            params["merchant_ids"] = merchant_ids
        query = get_complaints_metrics_query(
            metrics_sql=metrics_sql,
            dynamic_where_clause=" ".join(dynamic_where_clause),
            yesterday_metric=yesterday_metric,
            city=city,
            store=store,
            is_hourly=is_hourly,
            city_wise=city_wise,
            store_wise=True if store_wise or merchant_type else False,
            custom_view=custom_view, 
            start_date=start_date,
        )
        curs.execute(query, params)

        if not is_hourly:
            metrics_data: Dict[str, List[Dict[str, float]]] = defaultdict(list)
            results = []
            for row in curs:
                order_date = datetime.strptime(row[0], "%Y-%m-%d")
                for idx, (metric_name, _) in enumerate(metrics_items, start=1):
                    metric_value = row[idx]
                    if isinstance(metric_value, (float, int, str)) and not math.isnan(metric_value) and not math.isinf(metric_value):
                        metric_value = round(float(metric_value), 2)
                    else:
                        metric_value = 0.00  # Replace NaN or infinity with 0.0
                    metrics_data[metric_name].append({"date": order_date, "metric": metric_value})
                    

            complaints_percentage_data = []
            for complaint in metrics_data["total_complaints"]:
                date = complaint["date"]
                complaints = complaint["metric"]
                if date not in total_orders:
                    continue
                total_orders_date = total_orders[date]
                complaints_percentage = 100 * (complaints / total_orders_date)
                complaints_percentage_data.append({"date": date, "metric": round(float(complaints_percentage),2)})
            results = [
                OrderDatewiseMetric(
                    name=METRIC_NAME_MAPPING["complaints_percentage"],
                    data=complaints_percentage_data,
                    type=METRICS_TYPE_MAPPING["complaints_percentage"]
                )
            ]
            return results
        elif is_hourly:
            hourly_results = defaultdict(list)
            for row in curs:
                date = datetime.strptime(row[1], "%Y-%m-%d")
                hour = int(row[0])
                total_complaints_hourly = row[2]
                total_orders_hourly = total_orders[date][hour]
                complaints_percentage_hourly = 100 * \
                    (total_complaints_hourly / total_orders_hourly) if total_orders_hourly != 0 else 0

                hour_metric = HourMetric(hour=hour, data=[
                    Metric(name=METRIC_NAME_MAPPING["complaints_percentage"], metric=round(
                        float(complaints_percentage_hourly), 2), type=METRICS_TYPE_MAPPING["complaints_percentage"])
                ])
                hourly_results[date].append(hour_metric)
            datewise_results = []

            for dt, result in hourly_results.items():
                datewise_results.append(HourlyDateMetric(
                    date=dt,
                    date_diff=(datetime.now(IST) - dt.replace(tzinfo=IST)).days,
                    data=result
                ))

            hourly_date_metrics = HourlyDateMetrics(metrics=datewise_results)
            return hourly_date_metrics

instore_metrics = CRUDInStoreMetrics()
