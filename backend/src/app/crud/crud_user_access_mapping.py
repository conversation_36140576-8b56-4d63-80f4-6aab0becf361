from typing import Any, Dict, Optional, Union

from sqlalchemy.orm import Session
from app.models.user_access_mapping import UserAccessMapping
from app.models.page_group import PageGroup
from app.models.tenant import Tenant
from typing import List
from app.schemas.user_access_config import UserAccessConfig, UserCityStoreAccess

from app.core.config import settings

class CRUDUserAccessMapping():

    def get_mappings_by_user_id(self, db: Session, *, userId: int, tenants_access_info: list) -> Dict[str, UserAccessConfig]:
        user_access_info = {}
        for tenant_access_info in tenants_access_info: 
            tenant = db.query(Tenant).filter(Tenant.id == tenant_access_info['tenant_id']).first()
            userInfo = db.query(UserAccessMapping).filter(UserAccessMapping.user_id == userId, UserAccessMapping.tenant_id == tenant_access_info['tenant_id']).all()
            page_group = db.query(PageGroup).filter(PageGroup.id == tenant_access_info['page_group_id']).first().page_ids
            user_access_mapping_data: UserAccessConfig = {}
            user_access_mapping_data['userCityStoreAccessData'] = userInfo
            user_access_mapping_data['allowedPages'] = page_group
            user_access_mapping_data['isGlobalGroupAllowed'] = tenant_access_info['is_global_group_allowed']
            user_access_info[tenant.name] = user_access_mapping_data
        return user_access_info

user_access_mapping = CRUDUserAccessMapping()
