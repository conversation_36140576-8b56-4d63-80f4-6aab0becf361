from typing import Optional,List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps
from app.cache import ONE_HOUR_IN_SECONDS,ONE_DAY_IN_SECONDS, TEN_MINUTES
from app.cache.decorator import cache_metrics

from app.schemas.order_metric import HourlyDateMetrics
from app.schemas.current_rate_metric import CurrentRateMetric,ProjectionMetrics
from sqlalchemy.orm import Session

router = APIRouter()


@router.get(
    "/all",
    response_model=CurrentRateMetric,
    dependencies=[Depends(deps.get_current_user)],
)
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_current_rate_metrics(db_conn: connect = Depends(deps.get_pinot_db), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None)):
    """
    Get current_rate metrics at end of day
    """
    current_rate_order_metrics = crud.current_rate_metrics.get_current_rate_order_metrics(
        db_conn, metrics=["gmv", "order_count"]
    )
    non_empty_current_rate_order_metric = [
        current_rate_metric
        for current_rate_metric in current_rate_order_metrics
        if current_rate_metric != []
    ]
    if non_empty_current_rate_order_metric:
        pom = current_rate_order_metrics
    else:
        pom = non_empty_current_rate_order_metric

    projection_metrics_today = crud.projection_metrics.get_projection_metrics_daily(db_conn,today_projection=True, city = ['Overall'] if city is None else city,store = store)
    pom.extend(projection_metrics_today)
    db_conn.close()
    return CurrentRateMetric(metrics=pom)


@router.get(
    "/all/projection_metrics",
    response_model=CurrentRateMetric,
    dependencies=[Depends(deps.get_current_user)],
)
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_current_projection_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                                   db_postgres: Session = Depends(deps.get_postgres_db),
                                   city: Optional[List[str]] = Query(None), 
                                   store: Optional[List[str]] = Query(None), 
                                   zone: Optional[List[str]] = Query(None),
                                   group_id: Optional[str] = Query(None)):
    """
    Get current_rate metrics at end of day
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    projection_metrics_today = crud.projection_metrics.get_projection_metrics_daily(db_conn,today_projection=True, city = ['Overall'] if city is None else city,store = store, zone = zone)
    db_conn.close()
    return CurrentRateMetric(metrics=projection_metrics_today)


@router.get(
    "/hau/pan_india",
    response_model=HourlyDateMetrics,
    dependencies=[Depends(deps.get_current_user)],
)
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_current_rate_hau_metrics(
    db_conn: connect = Depends(deps.get_pinot_db), past_days_diff: Optional[int] = 7
):
    """
    Get current_rate metrics at end of day
    """
    current_rate_hau_metrics = crud.current_rate_metrics.get_current_rate_hau_metrics(
        db_conn, past_days_diff
    )
    db_conn.close()
    return HourlyDateMetrics(metrics=current_rate_hau_metrics)


@router.get("/bucket/projection_metrics", response_model=ProjectionMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=ONE_DAY_IN_SECONDS, force_cache_burst_at_eod=True)
def get_bucket_projection_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), zone: Optional[List[str]] = Query(None), yesterday_projection: Optional[bool] = Query(False), weekly_projection: Optional[bool] = Query(False), monthly_projection: Optional[bool] = Query(False)):
    """
    Get Projection Metrics (Yesterday, Weekly, Monthly)
    """
    deps.check_access(city=city,store=store, access_mapping=access_mapping)
    result = crud.projection_metrics.get_projection_metrics_bucket(db_conn,city = ['Overall'] if city is None else city,store = store, zone=zone,yesterday_projection=yesterday_projection,weekly_projection=weekly_projection,monthly_projection=monthly_projection)
    db_conn.close()
    return result

@router.get("/daily/projection_metrics", response_model=CurrentRateMetric, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=ONE_DAY_IN_SECONDS, force_cache_burst_at_eod=True)
def get_daily_projection_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), city: Optional[List[str]] = Query(None), projection_days: Optional[int] = Query(None, ge=0)):
    """
    Get Projection Metrics (Based on number of days)
    """
    deps.check_access(city=city,store=None,access_mapping=access_mapping)
    projection_metric = CurrentRateMetric(metrics = crud.projection_metrics.get_projection_metrics_daily(db_conn,city = ['Overall'] if city is None else city,projection_days = projection_days))
    db_conn.close()
    return projection_metric


@router.get("/daily/projection_metrics/events", 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=ONE_DAY_IN_SECONDS, force_cache_burst_at_eod=True)
def get_daily_projection_metrics_events(db_conn: connect = Depends(deps.get_pinot_db), 
                                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                        city: Optional[List[str]] = Query(None), 
                                        projection_days: Optional[int] = Query(None, ge=0)):
    """
    Get Projection Metrics (Based on number of days)
    """
    deps.check_access(city=city,store=None,access_mapping=access_mapping)
    projection_metric_events = crud.projection_metrics.get_projection_metrics_daily_events(db_conn,
                                                                                           city = ['Overall'] if city is None else city,
                                                                                           projection_days = projection_days)
    db_conn.close()
    return projection_metric_events
