import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Di<PERSON>, <PERSON><PERSON>

from fastapi import APIRouter, Depends, HTTPException
from app.core.jwt_auth import <PERSON><PERSON><PERSON><PERSON>
from starlette.requests import Request
from sqlalchemy.orm import Session

from app import crud, schemas
from app.core.config import settings
from app.api import deps, utils
from app.api.google_oauth import (
    fetch_oauth_session,
    generate_authorization_url,
    fetch_user_info, 
    is_allowed_domain,
)

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/login/google")
async def google_redirect_url():
    client = fetch_oauth_session()
    authorization_url = generate_authorization_url(client)
    return {"redirect_url": authorization_url}

@router.get("/auth/google/callback")
async def google_callback(request: Request, db: Session = Depends(deps.get_postgres_db)):
    client = fetch_oauth_session()
    user_info = fetch_user_info(client, request.query_params.get("code"))

    if not is_allowed_domain(user_info):
        logger.info(f"Login email for user: {user_info['email']} is not allowed")
        raise HTTPException(
            status_code=403, detail="You are not authorized to access this service"
        )

    user = crud.user.get_by_email(db, email=user_info["email"])

    if not user:
        user = crud.user.create(
            db,
            obj_in=schemas.UserCreate(
                email=user_info["email"],
                full_name=user_info["name"],
                image_url=user_info["picture"],
            ),
        )
    elif user and (not user.full_name or not user.image_url):
        user = crud.user.update(
            db,
            user_email=user.email,
            obj_in=schemas.UserCreate(
                email=user_info["email"],
                full_name=user_info["name"],
                image_url=user_info["picture"],
            ),
        )

    tenants_access_info = crud.user_tenant_mapping.get_tenant_mappings_by_user_id(db, user_id=user.id)

    if len(tenants_access_info) > 0:
        logger.info(f"Login successful for user: {user_info['email']}")
        access_mapping = crud.user_access_mapping.get_mappings_by_user_id(
            db, 
            userId=user.id, 
            tenants_access_info=tenants_access_info
        )
        
        user_access_mapping = utils.create_access_mapping(tenants_access_mapping=access_mapping)
        # Create JWT tokens with user claims
        token_data = {
            "sub": user.email,
            "user_access_mapping": user_access_mapping
        }
        access_token = JWTAuth.create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        # For now, use the same token for refresh (can be improved later)
        refresh_token = JWTAuth.create_access_token(
            data=token_data,
            expires_delta=timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        )

        return {
            "user": {
                "id": str(user.id),
                "email": user_info["email"],
                "picture": user_info["picture"],
                "name": user_info["name"],
                "access_token": access_token,
                "refresh_token": refresh_token,}
        }
    else:
        logger.info(f"Login disallowed for user: {user_info['email']}")
        raise HTTPException(
            status_code=403, detail="You are not authorized to access this service"
        )


@router.post('/auth/refresh')
def refresh(request: Request, db: Session = Depends(deps.get_postgres_db)):
    try:
        # Extract the refresh token from the request
        from app.core.jwt_auth import JWTAuth
        try:
            # Try to get the token from the request directly
            token_payload = JWTAuth.get_current_user(request)
        except HTTPException as e:
            logger.error(f"Refresh token verification failed: {e.detail}")
            raise HTTPException(
                status_code=403, detail="Invalid refresh token"
            )
        
        # Extract user email from JWT token
        current_user = token_payload.get("sub", "")
        if not current_user:
            logger.error("Refresh token has no sub claim")
            raise HTTPException(
                status_code=403, detail="Invalid refresh token format"
            )
        user = crud.user.get_by_email(db, email=current_user)
        if not user:
            logger.error(f"User not found for email: {current_user}")
            raise HTTPException(
                status_code=403, detail="You are not authorized to access this service"
            )

        tenants_access_info = crud.user_tenant_mapping.get_tenant_mappings_by_user_id(db, user_id=user.id)

        if not len(tenants_access_info) > 0:
            logger.info(f"Refresh token cannot be used for user: {current_user} - no tenant mappings")
            raise HTTPException(
                status_code=403, detail="You are not authorized to access this service"
            )
        
        access_mapping = crud.user_access_mapping.get_mappings_by_user_id(
            db=db,
            userId=user.id,
            tenants_access_info=tenants_access_info
        )
        
        user_access_mapping = utils.create_access_mapping(tenants_access_mapping=access_mapping)

        # Create new JWT tokens
        token_data = {
            "sub": user.email,
            "user_access_mapping": user_access_mapping
        }
        new_access_token = JWTAuth.create_access_token(
            data=token_data,
            expires_delta=timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        new_refresh_token = JWTAuth.create_access_token(
            data=token_data,
            expires_delta=timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        )

        logger.info(f"Refresh token successful for user: {current_user}")

        return {"access_token": new_access_token, "refresh_token": new_refresh_token}
    except Exception as e:
        logger.exception(f"Unexpected error in refresh token endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error processing refresh token"
        )


@router.get("/login/test-token")
def test_token(request: Request) -> Any:
    """
    Test access token
    """
    # Test token extraction and validation directly
    try:
        user_data = deps.get_current_user(request=request)
        return {
            "detail": "Access token is valid",
            "user": user_data["user"]
        }
    except HTTPException as e:
        # Let the exception be handled by FastAPI
        raise
    except Exception as e:
        logger.exception(f"Unexpected error in test-token endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error validating token"
        )
