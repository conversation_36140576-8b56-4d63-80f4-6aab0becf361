import logging
from typing import Optional, List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect
import httpx
from app import crud
from app.api import deps, utils
from app.cache.decorator import cache_metrics
from app.cache import THREE_MINUTES, THIRTY_MINUTES
from app.schemas.bad_stock import BadStockImages
from app.schemas.product_metric import FieldwiseList, FilteredProductFieldwise

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/images", response_model=BadStockImages,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
# DONT ADD CACHE HERE 
def get_bad_stock_images(db_conn: connect = Depends(deps.get_pinot_db),
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            item_name: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            start_date: str = Query(None),
                            end_date: str = Query(None),
                            item_id: Optional[List[str]] = Query(None),
                            er_id: Optional[List[str]] = Query(None)):
    """
    This gives the S3 URL Images of bad stock images from start_date to end_date based on filters 
    """
    bad_stock_images = crud.bad_stock.get_bad_stock_images(db_conn, 
                                                           l0_category=l0_category, l1_category=l1_category,
                                                           l2_category=l2_category, ptype=ptype,
                                                           item_name=item_name, brand=brand,
                                                           city=city, store=store,
                                                           start_date=start_date, end_date=end_date,
                                                           item_id=item_id, er_id=er_id)
    db_conn.close()
    return BadStockImages(bad_stock_images=bad_stock_images)


@router.get("/list/primary", response_model=FieldwiseList,
            dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THIRTY_MINUTES, force_cache_burst_at_eod=True)
def get_item_primary_details(db_conn: connect = Depends(deps.get_pinot_db),
                            search_filter: Optional[List[str]] = Query(None)):
    """
    Get complete list of all fields in search_filter
    """
    result = crud.bad_stock.get_all_field_list(db_conn, search_filter=search_filter)
    db_conn.close()
    return FieldwiseList(data=result)


@router.get("/filter", response_model=FilteredProductFieldwise,
            dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True)
def get_filtered_item_fieldwise(db_conn: connect = Depends(deps.get_pinot_db),
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            item_name: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            search_filter: Optional[str] = Query(None),
                            item_id: Optional[List[str]] = Query(None)):
    """
    Get search_filter field value based on filters
    """
    filtered_product_fieldwise = crud.bad_stock.get_filtered_item_fieldwise(db_conn, l0_category, 
                                                                            l1_category, l2_category, 
                                                                            ptype, item_name, 
                                                                            brand, search_filter,
                                                                            item_id)
    db_conn.close()
    return filtered_product_fieldwise
