import logging
from typing import Optional, List, Dict, Union
import datetime

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps,utils
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS,TEN_MINUTES, THIRTY_SECONDS, SIX_HOURS_IN_SECONDS, ELEVEN_MINUTES
from app.schemas.order_metric import OrderMetrics


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all/base-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_order_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                      access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                      metric: List[str] = Query(None), 
                      date_str: Optional[str] = Query(None),):
    """
    Get base metrics for T, T-7, T-14, T-21, T-28 for `date_str`
    """
    order_date_wise_metrics = crud.emergency_service_metrics.get_all_metrics(db_conn, 
                                                                             metrics=metric,
                                                                             date_str=date_str)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics
