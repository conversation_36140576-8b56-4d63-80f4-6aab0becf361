from typing import List, Optional, Dict

from fastapi import APIRouter, Depends, Query
import httpx
from app import crud
from app.api import deps,utils
from app.core.config import settings

from app.schemas.mtd_metric import MTDMetric
from app.schemas.order_metric import LocationDateMetrics
from app.cache.decorator import cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS

from pinotdb import connect

router = APIRouter()

@router.get("/all", 
            response_model=List[MTDMetric], 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True)
def get_mtd_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                    metric: List[str] = Query(None), 
                    zone: str = None, 
                    city: str = None,):
    """
    Get current and previous MTD metrics for Specific zone
    """
    is_sensitive = deps.check_access(None, None, access_mapping)
    mtd_metrics = crud.wtd_mtd_metrics.get_all_metrics(conn=db_conn, 
                                                   metrics=metric, 
                                                   zone=zone, 
                                                   city=city,
                                                   metric_type='mtd')
    mtd_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,metrics_list=mtd_metrics)
    return mtd_metrics


@router.get("/trends", 
            response_model=LocationDateMetrics, 
            dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True)
def get_all_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                    metric: List[str] = Query(None), 
                    zone: str = None, 
                    city:str = None,):
    """
    Get MTD order count metrics for cities
    """
    cities_metrics = crud.wtd_mtd_metrics.get_city_zone_metrics(conn=db_conn, 
                                                      metrics=metric, 
                                                      zone=zone, 
                                                      city=city,
                                                      metric_type='mtd',
                                                      isTrends=True)
    return cities_metrics
