from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app.api import deps
from app import crud
from app.schemas.order_metric import OrderMetrics
from app.schemas.product_metric import ProductDateWiseAllCityMetrics
from app.cache.decorator import dynamic_cache_metrics
from app.cache import THIRTY_SECONDS, FIVE_MINUTES
from app.schemas.product_metric import ProductMetrics, ProductHourWiseMetrics, ProductComplaints

router = APIRouter()


@router.get("/all/base-metrics", response_model=ProductMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_paas_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                     access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                     metric: List[str] = Query(None),
                     date_str: Optional[str] = Query(None),
                     city: Optional[List[str]] = Query(None),
                     store: Optional[List[str]] = Query(None),
                     print_type: Optional[str] = Query(None),
                     is_daywise: Optional[bool] = Query(None),
                     current_hour: Optional[bool] = Query(None),):
    """
    Get PAAS metrics for T, T-7, T-14, T-21, T-28 for `date_str`
    """
    if not metric:
        metric = [
            "order_count",
            "cancellation_percentage"
        ]

    paas_date_wise_metrics = crud.paas_metrics.get_all_metrics(db_conn,
                                                               metrics=metric,
                                                               date_str=date_str,
                                                               city=city,
                                                               store=store,
                                                               print_type=print_type,
                                                               is_daywise=is_daywise,
                                                               current_hour=current_hour)
    paas_metrics_response = ProductMetrics(metrics=paas_date_wise_metrics)
    db_conn.close()
    return paas_metrics_response


@router.get("/hourly", response_model=ProductHourWiseMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_paas_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            print_type: Optional[str] = Query(None),
                            date_str: Optional[str] = Query(None),):
    """
    Get PAAS hourly metrics for T, T-1, T-2 (last 3 days) like product details page
    """
    if not metric:
        metric = [
            "order_count",
            "cancellation_percentage"
        ]

    paas_hourly_metrics = crud.paas_metrics.get_hourly_metrics(db_conn,
                                                               metrics=metric,
                                                               city=city,
                                                               store=store,
                                                               print_type=print_type,
                                                               date_str=date_str)
    paas_hourly_response = ProductHourWiseMetrics(metrics=paas_hourly_metrics)
    db_conn.close()
    return paas_hourly_response


@router.get("/complaints", response_model=ProductComplaints,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_paas_complaints(db_conn: connect = Depends(deps.get_pinot_db),
                       access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                       city: Optional[List[str]] = Query(None),
                       store: Optional[List[str]] = Query(None),
                       print_type: Optional[str] = Query(None),
                       date_str: Optional[str] = Query(None),):
    """
    Get PAAS complaints data exactly like product details page
    """
    complaints_data = crud.paas_metrics.get_complaints_data(db_conn,
                                                           city=city,
                                                           store=store,
                                                           print_type=print_type,
                                                           date_str=date_str)
    db_conn.close()
    return ProductComplaints(complaints=complaints_data)


@router.get("/cities", response_model=ProductDateWiseAllCityMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_paas_metrics_all_cities(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            print_type: Optional[str] = Query(None),
                            date_str: Optional[str] = Query(None),):
    """
    If not yesterday_metric --> Get all PAAS metrics for T, T-7 for all cities
    Else --> Get all PAAS metrics for T-1, T-8 for all cities
    """
    # Default metrics if none specified
    if not metric:
        metric = [
            "order_count",
            "cancellation_percentage"
        ]
        
    paas_date_wise_metrics = crud.paas_metrics.get_all_metrics_all_cities(db_conn,
                                                                        metrics=metric,
                                                                        city=city,
                                                                        store=store,
                                                                        print_type=print_type,
                                                                        date_str=date_str
                                                                    )
    
    db_conn.close()
    return ProductDateWiseAllCityMetrics(metrics=paas_date_wise_metrics)
