from typing import Optional, List, Dict
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Depends, Query, HTTPException
from pinotdb import connect

from app import crud
from app.api import deps,utils
from app.cache.decorator import cache_metrics
from app.schemas.order_metric import LocationDateMetrics, HourlyDateMetrics, StoreDateMetrics
from app.crud.utils import is_yesterday_date
from sqlalchemy.orm import Session

router = APIRouter()


@router.get("/hourly", response_model=HourlyDateMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics()
def get_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                       db_postgres: Session = Depends(deps.get_postgres_db),
                       access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                       city: Optional[List[str]] = Query(None),
                       store: Optional[List[str]] = Query(None), 
                       zone: Optional[List[str]] = Query(None),
                       yesterday_metric: Optional[bool] = Query(False), 
                       group_id: Optional[str] = Query(None)):
    """
    Get rider login hr hourly metrics.
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    deps.check_access(city,store,access_mapping)
    hourly_metrics = crud.rider_metrics.get_rider_login_metrics(db_conn,
                                                                city=city,
                                                                store=store,
                                                                zone=zone,
                                                                yesterday_metric=yesterday_metric,
                                                                hourly_metrics=True)
    db_conn.close()
    return hourly_metrics


@router.get("/cities", response_model=LocationDateMetrics, dependencies=[Depends(deps.get_current_user)])
@cache_metrics()
def get_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                      db_postgres: Session = Depends(deps.get_postgres_db),
                      current_user: dict = Depends(deps.get_current_user),
                      past_days_diff: Optional[int] = Query(7, gt=0), 
                      date: Optional[str] = Query(None), 
                      hour: Optional[str] = Query(None),
                      enable_group_view: Optional[str] = Query(None)):
    """
    Get rider login hr metrics for all cities
    """
    # is_yesterday_date will be removed once we start using date directly rather than yesterday_metric
    cities_metrics = crud.rider_metrics.get_rider_login_metrics(db_conn, 
                                                                get_all_cities=True, 
                                                                yesterday_metric=is_yesterday_date(date), 
                                                                hour=hour)
    
    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)

        cities_metrics = utils.combine_cities_into_groups(cities_metrics, groups)

    db_conn.close()
    return cities_metrics

@router.get("/cities/stores", 
            response_model=StoreDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics()
def get_cities_stores_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                              db_postgres: Session = Depends(deps.get_postgres_db),
                              current_user: dict = Depends(deps.get_current_user),
                              past_days_diff: Optional[int] = Query(7, gt=0), 
                              city: Optional[List[str]] = Query(None), 
                              date: Optional[str] = Query(None),
                              hour: Optional[str] = Query(None),
                              enable_group_view: Optional[str] = Query(None),
                              group_id: Optional[int] = Query(None), 
                              enable_zone_view: Optional[str] = Query(None),
                              zone: Optional[str] = Query(None)):
    """
    Get rider login hr metrics for all stores
    """
    if group_id: 
        city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store=[])
    deps.check_access(city,None,access_mapping=access_mapping)

    # is_yesterday_date will be removed once we start using date directly rather than yesterday_metric
    stores_metrics = crud.rider_metrics.get_rider_login_metrics(
        db_conn, get_all_stores_for_city=True, city=city, yesterday_metric=is_yesterday_date(date), hour=hour, zone=zone)

    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)
        
        stores_metrics = utils.combine_stores_into_groups(stores_metrics, groups)
    elif enable_zone_view and not zone:
        stores_city_mapping = crud.store_metrics.get_stores_city_mapping(db_conn)
        stores_metrics = utils.combine_stores_into_zones(stores_metrics, stores_city_mapping)
    db_conn.close()
    return stores_metrics


@router.get("/new/cities", response_model=LocationDateMetrics, dependencies=[Depends(deps.get_current_user)])
@cache_metrics()
def get_new_rider_cities_metrics(
        db_conn: connect = Depends(deps.get_pinot_db),
        db_postgres: Session = Depends(deps.get_postgres_db),
        current_user: dict = Depends(deps.get_current_user),
        past_days_diff: Optional[int] = Query(7, gt=0), 
        city: Optional[List[str]] = Query(None), 
        date: Optional[str] = Query(None),
        hour: Optional[str] = Query(None),
        enable_group_view: Optional[str] = Query(None)):
    """
    Get new rider metrics for all cities
    """

    # is_yesterday_date will be removed once we start using date directly rather than yesterday_metric
    cities_metrics = crud.rider_metrics.get_new_rider_metrics(db_conn, get_all_cities=True, yesterday_metric=is_yesterday_date(date), hour=hour)
    
    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)

        cities_metrics = utils.combine_cities_into_groups(cities_metrics, groups)

    db_conn.close()
    return cities_metrics

@router.get("/new/cities/stores", response_model=StoreDateMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics()
def get_new_rider_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                                 access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                 db_postgres: Session = Depends(deps.get_postgres_db),
                                 current_user: dict = Depends(deps.get_current_user),    
                                 past_days_diff: Optional[int] = Query(7, gt=0), 
                                 city: Optional[List[str]] = Query(None), 
                                 date: Optional[str] = Query(None),
                                 hour: Optional[str] = Query(None),
                                 enable_group_view: Optional[str] = Query(None),
                                 group_id: Optional[int] = Query(None),
                                 enable_zone_view: Optional[str] = Query(None),
                                 zone: Optional[str] = Query(None)):
    """
    Get new rider metrics for all stores
    """
    if group_id: 
        city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store=[])

    deps.check_access(city,None,access_mapping=access_mapping)

    # is_yesterday_date will be removed once we start using date directly rather than yesterday_metric
    stores_metrics = crud.rider_metrics.get_new_rider_metrics(db_conn, get_all_stores_for_city=True, city=city, yesterday_metric=is_yesterday_date(date), hour=hour, zone=zone)

    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)
        stores_metrics = utils.combine_stores_into_groups(stores_metrics, groups)
    elif enable_zone_view and not zone:
        stores_city_mapping = crud.store_metrics.get_stores_city_mapping(db_conn)
        stores_metrics = utils.combine_stores_into_zones(stores_metrics, stores_city_mapping)
    db_conn.close()
    return stores_metrics
