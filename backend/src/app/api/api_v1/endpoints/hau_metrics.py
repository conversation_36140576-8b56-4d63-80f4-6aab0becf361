from typing import List, Optional, Dict

from fastapi import APIRouter, Depends, Query
from app import crud
from app.api import deps
from app.cache.decorator import cache_metrics
from sqlalchemy.orm import Session

from pinotdb import connect

from app.schemas.order_metric import HourlyDateMetrics


router = APIRouter()

@router.get("/pan_india", response_model=HourlyDateMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics()
def get_hourly_active_user(db_conn: connect = Depends(deps.get_pinot_db), 
                           db_postgres: Session = Depends(deps.get_postgres_db),
                           access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                           past_days_diff: Optional[int] = 7, 
                           city: Optional[List[str]] = Query(None), 
                           store: Optional[List[str]] = Query(None), 
                           zone: Optional[List[str]] = Query(None),
                           app: Optional[str] = Query(None), 
                           group_id: Optional[str] = Query(None)):
    """
    Get hourly active user count for current date in comparison with another
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    #is_sensitive will be used to filter out critical metrics in future
    is_sensitive = deps.check_access(city,store,access_mapping)
    hourly_metrics = crud.hau_metrics.get_hourly_active_users_current_date_metrics(conn= db_conn, past_days_diff=past_days_diff, cities=city, app=app, store=store, zone=zone)
    db_conn.close()
    return hourly_metrics

@router.get("/pan_india/yesterday", response_model=HourlyDateMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics()
def get_hourly_active_user_for_yesterday(db_conn: connect = Depends(deps.get_pinot_db), 
                                         db_postgres: Session = Depends(deps.get_postgres_db),
                                         access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                         app: Optional[str] = Query(None), 
                                         city: Optional[List[str]] = Query(None), 
                                         store: Optional[List[str]] = Query(None), 
                                         group_id: Optional[str] = Query(None)):
    """
    Get hourly active user count for yesterday in comparison with last week
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    #is_sensitive will be used to filter out critical metrics in future
    is_sensitive = deps.check_access(city,store,access_mapping)
    hourly_metrics = crud.hau_metrics.get_yesterday_hourly_active_users_metrics(conn= db_conn, app=app, city=city, store=store)
    db_conn.close()
    return hourly_metrics
