from typing import Optional, List, Dict, Union
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Depends, Query, HTTPException
from pinotdb import connect

from app import crud
from app.api import deps
from app.cache.decorator import cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS, THREE_MINUTES, ELEVEN_MINUTES
from app.schemas.order_metric import WarehouseStoreDateMetrics, OutletsList, WarehouseSlotMetrics, OrderDatewiseMetric

router = APIRouter()


@router.get("/outbound_metrics", 
            response_model=WarehouseStoreDateMetrics,
            dependencies=[Depends(deps.get_current_user)]
            )
@cache_metrics(expire=THREE_MINUTES)
def get_outbound_metrics(
    db_conn: connect = Depends(deps.get_pinot_db), 
    outlet: Optional[List[str]] = Query(None), 
    previous_shift: Optional[bool] = Query(False)
    ):
    warehouse_metrics = crud.warehouse_metrics.get_outbound_metrics(db_conn, outlet=outlet, previous_shift=previous_shift)
    db_conn.close()
    return warehouse_metrics


@router.get("/manpower_metrics", 
            response_model=WarehouseStoreDateMetrics,
            dependencies=[Depends(deps.get_current_user)]
            )
@cache_metrics(expire=ELEVEN_MINUTES)
def get_manpower_metrics(
    db_conn: connect = Depends(deps.get_pinot_db), 
    outlet: Optional[List[str]] = Query(None)
    ):
    warehouse_metrics = crud.warehouse_metrics.get_manpower_metrics(db_conn, outlet=outlet)
    db_conn.close()
    return warehouse_metrics


@router.get("/availability", 
            response_model=Union[Dict[str, List[OrderDatewiseMetric]], List[OrderDatewiseMetric]],
            dependencies=[Depends(deps.get_current_user)]
            )
@cache_metrics(expire=ELEVEN_MINUTES)
def get_availability_metrics(
    db_conn: connect = Depends(deps.get_pinot_db), 
    outlet: Optional[List[str]] = Query(None),
    is_slot_wise: Optional[bool] = Query(False),
    is_merchant_wise: Optional[bool] = Query(False),
    merchant_id: Optional[List[str]] = Query(None),
    slot: Optional[List[str]] = Query(None)
    ):
    warehouse_metrics = crud.warehouse_metrics.get_availability_metrics(db_conn, outlet=outlet, is_slot_wise=is_slot_wise, merchant_id=merchant_id, is_merchant_wise=is_merchant_wise, slot=slot)
    db_conn.close()
    return warehouse_metrics


@router.get("/slot_wise_metrics", 
            response_model=Dict[str, WarehouseSlotMetrics],
            dependencies=[Depends(deps.get_current_user)]
            )
@cache_metrics(expire=THREE_MINUTES)
def get_slot_wise_metrics(
    db_conn: connect = Depends(deps.get_pinot_db), 
    outlet: Optional[List[str]] = Query(None),
    previous_shift: Optional[bool] = Query(False)
    ):
    warehouse_metrics = crud.warehouse_metrics.get_dispatch_slot_metrics(db_conn, outlet=outlet, previous_shift=previous_shift)
    db_conn.close()
    return warehouse_metrics


@router.get("/warehousemapping", response_model=OutletsList, dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True)
def get_warehouse_filters(db_conn: connect = Depends(deps.get_pinot_db)):
    """
    Get stores list in each city
    """
    result = crud.warehouse_metrics.get_warehouse_mapping(db_conn)
    db_conn.close()
    return result

