import logging
from typing import Optional, List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps, utils
from app.cache.decorator import cache_metrics
from app.cache import THREE_MINUTES, FIFTEEN_MINUTES, SIX_HOURS_IN_SECONDS
from app.schemas.product_metric import L0CategoryMetrics, L1CategoryMetrics
from app.schemas.order_metric import ConversionGroupMetric

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all", 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True)
def get_l0_category_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                         metric: Optional[str] = Query(None), 
                         city: Optional[List[str]] = Query(None), ):
    category_date_wise_metrics = crud.category_metrics.get_all_metrics(db_conn, 
                                                                       metric=metric,
                                                                       city=city, )
    db_conn.close()
    return category_date_wise_metrics


@router.get("/yesterday/all", 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_l0_category_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                         metric: Optional[str] = Query(None), 
                         city: Optional[List[str]] = Query(None), ):
    category_date_wise_metrics = crud.category_metrics.get_all_metrics(db_conn, 
                                                                       metric=metric,
                                                                       city=city, 
                                                                       yesterday_metric=True,)
    db_conn.close()
    return category_date_wise_metrics


@router.get("/l1",
            response_model=L1CategoryMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True)
def get_l1_category_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                         l0_category: str = Query(..., description="L0 category to filter on"),
                         metric: Optional[str] = Query(None),
                         city: Optional[List[str]] = Query(None)):
    """
    Get L1 category metrics for a given L0 category
    
    Args:
        db_conn: Database connection
        l0_category: L0 category to filter on (required)
        metric: Metric to fetch
        city: Cities to filter on
        
    Returns:
        List of L1 category metrics for the given L0 category
    """
    l1_category_metrics = crud.category_metrics.get_l1_category_metrics(db_conn,
                                                                      l0_category=l0_category,
                                                                      metric=metric,
                                                                      city=city)
    db_conn.close()
    return l1_category_metrics


@router.get("/l1/yesterday",
            response_model=L1CategoryMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_l1_category_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                         l0_category: str = Query(..., description="L0 category to filter on"),
                         metric: Optional[str] = Query(None),
                         city: Optional[List[str]] = Query(None)):
    """
    Get yesterday's L1 category metrics for a given L0 category
    
    Args:
        db_conn: Database connection
        l0_category: L0 category to filter on (required)
        metric: Metric to fetch
        city: Cities to filter on
        
    Returns:
        List of L1 category metrics for the given L0 category from yesterday
    """
    l1_category_metrics = crud.category_metrics.get_l1_category_metrics(db_conn,
                                                                      l0_category=l0_category,
                                                                      metric=metric,
                                                                      city=city,
                                                                      yesterday_metric=True)
    db_conn.close()
    return l1_category_metrics


@router.get("/insight",
            response_model=List[ConversionGroupMetric],
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=FIFTEEN_MINUTES)
def get_category_metrics_insights(db_conn: connect = Depends(deps.get_pinot_db), 
                      access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                      yesterday_metric: bool = Query(False), 
                      city: Optional[List[str]] = Query(None),):
    """
    Get category (l0_category) insight metrics 
    'Unique Impressions', 'Search Impressions', 'Search Conv %', 'FE Inventory', 'FE Availability'
    """
    category_insight_metrics = crud.category_metrics.get_category_insights_metrics(db_conn,
                                                            yesterday_view=yesterday_metric,
                                                            city=city,)
    db_conn.close()
    return category_insight_metrics
