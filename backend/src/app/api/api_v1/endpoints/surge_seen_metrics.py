from typing import Optional, List

from fastapi import APIRouter, Depends, Query, HTTPException
from pinotdb import connect

from app import crud
from app.api import deps
from app.cache.decorator import cache_metrics
from app.schemas.order_metric import LocationDateMetrics, HourlyDateMetrics

router = APIRouter()


@router.get("/hourly", response_model=HourlyDateMetrics, dependencies=[Depends(deps.get_current_user)])
@cache_metrics()
def get_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db), city: Optional[List[str]] = Query(None),
                                store: Optional[List[str]] = Query(None), yesterday_metric: Optional[bool] = Query(False)):
    """
    Get all surge seen hourly metrics.
    """

    # hourly_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn,
    #                                                                 city=city,
    #                                                                 store=store,
    #                                                                 yesterday_metric=yesterday_metric,
    #                                                                 hourly_metrics=True)
    # return hourly_metrics


@router.get("/cities", response_model=LocationDateMetrics, dependencies=[Depends(deps.get_current_user)])
@cache_metrics()
def get_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                             past_days_diff: Optional[int] = Query(7, gt=0)):
    """
    Get surge seen metrics for all cities
    """
    # cities_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn, get_all_cities=True)
    # return cities_metrics
