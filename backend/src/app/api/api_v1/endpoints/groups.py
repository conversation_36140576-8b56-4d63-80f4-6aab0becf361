import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.cache.decorator import cache_metrics
from app.cache import THIRTY_SECONDS, THREE_MINUTES, ONE_HOUR_IN_SECONDS, FIVE_MINUTES
from app.schemas.group import (
    GroupResponse,
    GroupCreate,
    GroupUpdate,
    GroupWithMappings,
)

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/", response_model=GroupResponse)
def create_group(
    *,
    db: Session = Depends(deps.get_postgres_db),
    group: GroupCreate,
    current_user: dict = Depends(deps.get_current_user)
):
    """
    Create new group.
    """
    deps.validate_group_creation_access(
        db=db,
        current_user=current_user,
        city_store_mappings=group.mappings,
        is_global=group.is_global
    )
    
    db_user = crud.user.get_by_email(db, email=current_user["user"])
    return crud.group.create(db, group=group, user_id=db_user.id)


@router.put("/{group_id}", response_model=GroupResponse)
def update_group(
    *,
    db: Session = Depends(deps.get_postgres_db),
    group_id: int,
    group: GroupUpdate,
    current_user: dict = Depends(deps.get_current_user)
):
    """
    Update existing group.
    """
    db_group = crud.group.get(db, id=group_id)
    if not db_group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    db_user = crud.user.get_by_email(db, email=current_user["user"])
    if db_group.created_by != db_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    deps.validate_group_creation_access(
        db=db,
        current_user=current_user,
        city_store_mappings=group.mappings,
        is_global=group.is_global
    )

    return crud.group.update(db, db_group=db_group, update_group=group)


@router.delete("/{group_id}")
def delete_group(
    *,
    db: Session = Depends(deps.get_postgres_db),
    group_id: int,
    current_user: dict = Depends(deps.get_current_user)
):
    """
    Delete a group.
    """
    group = crud.group.get(db, id=group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    
    db_user = crud.user.get_by_email(db, email=current_user["user"])
    if group.created_by != db_user.id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    crud.group.delete(db, id=group_id)
    return {"status": "success"}


@router.get(
    "/all",
    response_model=List[GroupResponse],
    dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THREE_MINUTES)
def get_all_groups(
    db: Session = Depends(deps.get_postgres_db),
):
    """
    Get all groups.
    """
    return crud.group.get_all(db)


@router.get(
    "/global",
    response_model=List[GroupResponse],
    dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THREE_MINUTES)
def get_global_groups(
    db: Session = Depends(deps.get_postgres_db),
):
    """
    Get all global groups.
    """
    return crud.group.get_global_groups(db)


@router.get(
    "/user-groups",
    response_model=List[GroupResponse],
    dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THIRTY_SECONDS)
def get_user_groups(
    db: Session = Depends(deps.get_postgres_db),
    current_user: dict = Depends(deps.get_current_user)
):
    """
    Get groups owned by the current user.
    """
    db_user = crud.user.get_by_email(db, email=current_user["user"])
    return crud.group.get_user_groups(db, user_id=db_user.id)


@router.get(
    "/accessible-groups",
    response_model=List[GroupWithMappings],
    dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THIRTY_SECONDS)
def get_accessible_groups(
    db: Session = Depends(deps.get_postgres_db),
    current_user: dict = Depends(deps.get_current_user)
):
    """
    Get all groups accessible to the current user:
    - Global groups (filtered by user's city/store access)
    - Groups created by the current user
    Returns deduplicated list of groups.
    """
    db_user = crud.user.get_by_email(db, email=current_user["user"])
    return crud.group.get_accessible_groups(db, db_user)


@router.get(
    "/type/{group_type}",
    response_model=List[GroupResponse],
    dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THREE_MINUTES)
def get_groups_by_type(
    group_type: str,
    db: Session = Depends(deps.get_postgres_db),
):
    """
    Get all groups of a specific type.
    """
    return crud.group.get_by_type(db, group_type=group_type)


@router.get(
    "/{group_id}",
    response_model=GroupWithMappings,
    dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THIRTY_SECONDS)
def get_group(
    group_id: int,
    db: Session = Depends(deps.get_postgres_db),
):
    """
    Get specific group by ID with its mappings.
    """
    group = crud.group.get_with_mappings(db, id=group_id)
    if not group:
        raise HTTPException(status_code=404, detail="Group not found")
    return group
