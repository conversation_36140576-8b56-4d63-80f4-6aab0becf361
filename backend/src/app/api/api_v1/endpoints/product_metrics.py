import logging
from typing import Optional, List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps, utils
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import FIVE_MINUTES, FIFTEEN_MINUTES, SIX_HOURS_IN_SECONDS
from app.schemas.product_metric import ProductMetrics, FieldwiseList, FilteredProductFieldwise, ProductDateWiseAllCityMetrics, ProductHourWiseMetrics, ProductComplaints, ProductMetricTypes
from app.schemas.order_metric import ConversionGroupMetric

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all", response_model=ProductMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_product_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            city: Optional[List[str]] = Query(None), 
                            store: Optional[List[str]] = Query(None),
                            is_daywise: Optional[bool] = Query(None),
                            yesterday_metric: Optional[bool] = Query(None),
                            current_hour: Optional[bool] = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    If is_daywise = True 
        If not yesterday_metric 
            current_hour --> T, T-1, T-2, T-3, T-4, T-5, T-6 (till the current_hour data only)
            Else --> T, T-1, T-2, T-3, T-4, T-5, T-6 but (T-1, ... complete day data)
        Else --> T-1, T-2, T-3, T-4, T-5, T-6, T-7
    Else 
        If not yesterday_metric --> Get metrics for T, T-7, T-14, T-21, T-28
        Else --> Get metrics for T-1, T-8, T-15, T-22, T-29
    """
    product_date_wise_metrics = crud.product_metrics.get_all_metrics(db_conn, metrics=metric,
                                                                     l0_category=l0_category, l1_category=l1_category,
                                                                     l2_category=l2_category, ptype=ptype,
                                                                     pname=pname, brand=brand,
                                                                     city=city, store=store,
                                                                     is_daywise=is_daywise,
                                                                     yesterday_metric=yesterday_metric,
                                                                     current_hour=current_hour,
                                                                     pid=pid)

    db_conn.close()
    return ProductMetrics(metrics=product_date_wise_metrics)


@router.get("/list/primary", response_model=FieldwiseList,
            dependencies=[Depends(deps.get_current_user)])
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_product_primary_details(db_conn: connect = Depends(deps.get_pinot_db),
                            search_filter: Optional[List[str]] = Query(None)):
    """
    Get complete list of all fields in search_filter
    """
    result = crud.product_metrics.get_all_field_list(db_conn, search_filter=search_filter)
    db_conn.close()
    return FieldwiseList(data=result)


@router.get("/filter", response_model=FilteredProductFieldwise,
            dependencies=[Depends(deps.get_current_user)])
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_filtered_product_fieldwise(db_conn: connect = Depends(deps.get_pinot_db),
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            search_filter: Optional[str] = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    Get search_filter field value based on filters
    """
    filtered_product_fieldwise = crud.product_metrics.get_filtered_product_fieldwise(db_conn, l0_category, 
                                                                                     l1_category, l2_category, 
                                                                                     ptype, pname, 
                                                                                     brand, search_filter,
                                                                                     pid)
    db_conn.close()
    return filtered_product_fieldwise


@router.get("/cities", response_model=ProductDateWiseAllCityMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_product_metrics_all_cities(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            yesterday_metric: Optional[bool] = Query(None),
                            city: Optional[list[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    If not yesterday_metric --> Get all metrics for T, T-7 for all cities 
    Else --> Get all metrics for T-1, T-8 for all cities
    """
    product_date_wise_metrics = crud.product_metrics.get_all_metrics_all_cities(db_conn, metrics=metric,
                                                                     l0_category=l0_category, l1_category=l1_category,
                                                                     l2_category=l2_category, ptype=ptype,
                                                                     pname=pname, brand=brand,
                                                                     yesterday_metric = yesterday_metric,
                                                                     city=city, store=store,
                                                                     pid=pid)
    db_conn.close()
    return ProductDateWiseAllCityMetrics(metrics=product_date_wise_metrics)


@router.get("/hourly", response_model=ProductHourWiseMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_product_metrics_hourly(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            city: Optional[List[str]] = Query(None), 
                            store: Optional[List[str]] = Query(None),
                            yesterday_metric: Optional[bool] = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    Get all metrics for T, T-1, T-2 hourwise 
    if yesterday_metric: T-1, T-2, T-3 hourwise
    """
    product_hour_wise_metrics = crud.product_metrics.get_all_metrics_hourwise(db_conn, metrics=metric,
                                                                     l0_category=l0_category, l1_category=l1_category,
                                                                     l2_category=l2_category, ptype=ptype,
                                                                     pname=pname, brand=brand,
                                                                     city=city, store=store,
                                                                     yesterday_metric=yesterday_metric,
                                                                     pid=pid)
    product_metrics = ProductHourWiseMetrics(metrics=product_hour_wise_metrics)
    db_conn.close()
    return product_metrics


@router.get("/ptype_metrics", response_model=List[ConversionGroupMetric], dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=FIFTEEN_MINUTES)
def get_ptype_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), yesterday_metric: bool = Query(False), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), ptype: Optional[List[str]] = Query(None)):
    """
    get ptype metrics
    """
    is_sensitive = deps.check_access(city=city,store=store,access_mapping=access_mapping)
    ptype_metrics = crud.insights_metrics.get_ptype_metrics(db_conn,
                                                            yesterday_view=yesterday_metric,
                                                            city=city,store=store,
                                                            ptype_filter_list=ptype,
                                                            metrics_type=ProductMetricTypes.product)
    db_conn.close()
    return ptype_metrics


@router.get("/complaints", response_model=ProductComplaints,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_product_complaints(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            city: Optional[List[str]] = Query(None), 
                            store: Optional[List[str]] = Query(None),
                            is_daywise: Optional[bool] = Query(None),
                            yesterday_metric: Optional[bool] = Query(None),
                            current_hour: Optional[bool] = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    If is_daywise = True 
        If not yesterday_metric 
            current_hour --> T, T-1, T-2, T-3, T-4, T-5, T-6 (till the current_hour data only)
            Else --> T, T-1, T-2, T-3, T-4, T-5, T-6 but (T-1, ... complete day data)
        Else --> T-1, T-2, T-3, T-4, T-5, T-6, T-7
    Else 
        If not yesterday_metric --> Get metrics for T, T-7, T-14, T-21, T-28
        Else --> Get metrics for T-1, T-8, T-15, T-22, T-29
    """

    complaints_category_and_day_wise_metrics = crud.product_metrics.get_complaints(db_conn, 
                                                                     l0_category=l0_category, l1_category=l1_category,
                                                                     l2_category=l2_category, ptype=ptype,
                                                                     pname=pname, brand=brand,
                                                                     city=city, store=store,
                                                                     is_daywise=is_daywise,
                                                                     yesterday_metric=yesterday_metric,
                                                                     current_hour = current_hour,
                                                                     pid=pid)

    db_conn.close()
    return ProductComplaints(complaints=complaints_category_and_day_wise_metrics)

