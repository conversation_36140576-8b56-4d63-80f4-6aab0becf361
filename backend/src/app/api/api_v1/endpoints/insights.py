
from typing import Optional,List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps
from app.cache.decorator import cache_metrics
from app.schemas.order_metric import ConversionGroupMetric
from app.cache import FIFTEEN_MINUTES

router = APIRouter()

@router.get("/conversion_funnel", response_model=List[ConversionGroupMetric], dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=FIFTEEN_MINUTES)
def get_conversion_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), yesterday_metric: bool = Query(False), filter_hour: int = Query(-1), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), store_response: Optional[bool] = Query(False)):
    """
    get funnel conversion metrics 
    """
    is_sensitive = deps.check_access(city=city,store=store,access_mapping=access_mapping)
    if store_response:
        stores_funnel_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,filter_hour=filter_hour,
                                                                                    yesterday_view=yesterday_metric,
                                                                                    insights_view=True,
                                                                                    city=city,store=store,store_response=store_response)
        db_conn.close()
        return stores_funnel_metrics
    else:
        cities_funnel_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,filter_hour=filter_hour,
                                                                                    yesterday_view=yesterday_metric,
                                                                                    insights_view=True,
                                                                                    city=city)
        db_conn.close()
        return cities_funnel_metrics

@router.get("/ptype_metrics", response_model=List[ConversionGroupMetric], dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=FIFTEEN_MINUTES)
def get_ptype_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), yesterday_metric: bool = Query(False), filter_hour: int = Query(-1), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), metrics_type:Optional[str] = Query(''), l0_category:Optional[List[str]] = Query(None)):
    """
    get ptype metrics
    """
    is_sensitive = deps.check_access(city=city,store=store,access_mapping=access_mapping)
    ptype_metrics = crud.insights_metrics.get_ptype_metrics(db_conn,filter_hour=filter_hour,
                                                            yesterday_view=yesterday_metric,
                                                            city=city,store=store,
                                                            metrics_type=metrics_type,
                                                            l0_category=l0_category)
    db_conn.close()
    return ptype_metrics


@router.get("/item_metrics", response_model=List[ConversionGroupMetric], dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=FIFTEEN_MINUTES)
def get_item_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), yesterday_metric: bool = Query(False), filter_hour: int = Query(-1), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), product_type: Optional[str] = Query(None)):
    """
    get item metrics
    """
    is_sensitive = deps.check_access(city=city,store=store,access_mapping=access_mapping)
    item_metrics = crud.insights_metrics.get_item_metrics(db_conn,filter_hour=filter_hour,
                                                            yesterday_view=yesterday_metric,
                                                            city=city,store=store,product_type=product_type)
    db_conn.close()
    return item_metrics