from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, Path
from pinotdb import connect
from typing import Optional, List
from sqlalchemy.orm import Session

from app import crud
from app.api import deps
from app.schemas.order_metric import LocationDateMetrics, StoresCityList, StoreMetricsResponse
from app.cache.decorator import cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS, TEN_SECONDS, THREE_MINUTES, FIVE_MINUTES, THIRTY_SECONDS

router = APIRouter()

@router.get("/stressed/cities/base/{base_days_diff}/comparision/{comparative_days_diff}", response_model=LocationDateMetrics, dependencies=[Depends(deps.get_current_user)])
@cache_metrics()
def get_stressed_stores(db_conn: connect = Depends(deps.get_pinot_db), base_days_diff: int = Path(..., ge=0), comparative_days_diff: int = Path(...,gt=0)):
    """
    Get stressed stores list
    """
    result = crud.store_metrics.get_datewise_stressed_stores_count(db_conn, base_days_diff=base_days_diff, comparative_days_diff=comparative_days_diff)
    db_conn.close()
    return result

@router.get("/storecitymapping", response_model=StoresCityList, dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True)
def get_stores_city_filters(db_conn: connect = Depends(deps.get_pinot_db)):
    """
    Get stores list in each city
    """
    result = crud.store_metrics.get_stores_city_mapping(db_conn)
    db_conn.close()
    return result

@router.get("/store_metrics", response_model=StoreMetricsResponse, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=TEN_SECONDS, force_cache_burst_at_eod=True)
def get_store_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), city: Optional[List[str]] = Query(None)):
    """
    Get surge and eta and order count metrics for each store
    """
    deps.check_access(city=city,store=None,access_mapping=access_mapping)
    result = crud.store_metrics.get_surge_eta_store_metrics(db_conn,city = city)
    db_conn.close()
    return result

@router.get("/active_stores", response_model=int, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True)
def get_active_store_metrics(db_conn: connect = Depends(deps.get_pinot_db), access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), city: Optional[List[str]] = Query(None)):
    """
    Get count of current active and serving stores
    """
    deps.check_access(city=city,store=None,access_mapping=access_mapping)
    result = crud.store_metrics.get_active_stores_count_metric(db_conn,city = city)
    db_conn.close()
    return result

@router.get("/cities_and_groups", response_model=List[Dict[str, Any]], dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=THIRTY_SECONDS)
def get_cities_and_groups(
    db_conn: connect = Depends(deps.get_pinot_db),
    db: Session = Depends(deps.get_postgres_db),
    current_user: dict = Depends(deps.get_current_user)
):
    """
    Get list of groups and cities that are not part of any group
    """
    all_cities = crud.store_metrics.get_all_cities(db_conn)
    
    db_user = crud.user.get_by_email(db, email=current_user["user"])
    groups = crud.group.get_accessible_groups(db, db_user)
    
    cities_in_groups = set()
    for group in groups:
        if group.type == "CITY":
            for mapping in group.mappings:
                cities_in_groups.add(mapping.city)
    
    non_group_cities = [city for city in all_cities if city not in cities_in_groups]
    
    response = []
    for group in groups:
        if group.type == "CITY":
            response.append({
                "type": "group",
                "id": group.id,
                "name": group.name,
                "is_global": group.is_global,
                "mappings": [{"city": m.city, "stores": m.stores} for m in group.mappings]
            })
    for city in non_group_cities:
        response.append({
            "type": "city",
            "name": city
        })
    
    db_conn.close()
    return response