import logging
from typing import Optional, List, Dict, Union
import datetime

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps,utils
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS,TEN_MINUTES, THIRTY_SECONDS, SIX_HOURS_IN_SECONDS, ELEVEN_MINUTES
from app.schemas.order_metric import LocationDateMetrics, HourlyDateMetrics, OrderMetrics, StoreDateMetrics, ConversionGroupMetric, AthDatewiseMetric
from app.core.metric_config import SURGE_SEEN_METRICS, SURGE_SEEN_METRICS_HOURLY
from sqlalchemy.orm import Session

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_order_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None),
                            status: Optional[List[str]] = Query(None)):
    """
    Get all metrics for T, T-7, T-14, T-21, T-28
    """
    # We would be changing the query logic internally in future based on the sensitive flag
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_mertics_group_1 = metric[:4]
    order_mertics_group_2 = metric[4:7]
    order_mertics_group_3 = metric[7:]

    ##### Splitting fact order metrics into 3 groups to make query light weight ###############
    order_date_wise_metrics = crud.order_metrics.get_all_metrics(db_conn, metrics=order_mertics_group_1,
                                                                 city=city, status=status,
                                                                 app=app, store=store)

    order_date_wise_metrics_group_2 = crud.order_metrics.get_all_metrics(db_conn, metrics=order_mertics_group_2,
                                                                 city=city, status=status,
                                                                 app=app, store=store)
    order_date_wise_metrics.extend(order_date_wise_metrics_group_2)

    order_date_wise_metrics_group_3 = crud.order_metrics.get_all_metrics(db_conn, metrics=order_mertics_group_3,
                                                                 city=city, status=status,
                                                                 app=app, store=store)
    order_date_wise_metrics.extend(order_date_wise_metrics_group_3)
    ##############################################################################################

    new_user_metrics = crud.order_metrics.get_new_users_count_metrics(db_conn,
                                                                      city=city,
                                                                      store=store,
                                                                      is_yesterday=False)
    order_date_wise_metrics.extend(new_user_metrics)
    rider_metrics = crud.rider_metrics.get_rider_login_metrics(db_conn,
                                                               city=city,
                                                               store=store)
    new_rider_metrics = crud.rider_metrics.get_new_rider_metrics(db_conn,
                                                                 city=city,
                                                                 store=store)
    order_date_wise_metrics.extend(rider_metrics)
    order_date_wise_metrics.extend(new_rider_metrics)
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics, \
        serviceability_based_cart_block_conversion_date_wise_metrics = crud.dau_metrics.get_dau_metrics(db_conn,
                                                                                city=city,
                                                                                app=app,
                                                                                store=store)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(serviceability_based_cart_block_conversion_date_wise_metrics)

    funnel_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store)
    order_date_wise_metrics.extend(funnel_conversion_metrics)
    surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn,city=city,store=store)
    order_date_wise_metrics.extend(surge_seen_metrics)

    if not store:
        active_store_count_metrics = crud.store_metrics.get_active_stores_count_metric(db_conn,
                                                                                       city=city,
                                                                                       is_yesterday=False,
                                                                                       get_trend=True)
        order_date_wise_metrics.extend(active_store_count_metrics)
        order = crud.order_metrics.find_models_in_list(order_date_wise_metrics, "Cart Volume")
        opd_per_store = crud.order_metrics.calculate_ratios_based_on_two_models_input(
            order[0].data,
            active_store_count_metrics[0].data,
            "opd_per_store"
        )
        order_date_wise_metrics.extend(opd_per_store)
    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/yesterday/all",
            response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True, force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_order_metrics_for_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                                          access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                          app: Optional[str] = Query(None), metric: List[str] = Query(None),
                                          city: Optional[List[str]] = Query(None),
                                          store: Optional[List[str]] = Query(None)):
    """
    Get all metrics for T-1, T-8, T-15, T-22, T-29
    """
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_mertics_group_1 = metric[:4]
    order_mertics_group_2 = metric[4:7]
    order_mertics_group_3 = metric[7:]

    ##### Splitting fact order metrics into 3 groups to make query light weight ###############
    order_date_wise_metrics = crud.order_metrics.get_all_metrics(db_conn, metrics=order_mertics_group_1, city=city,
                                                                 app=app, store=store, yesterday_metric=True)

    order_date_wise_metrics_group_2 = crud.order_metrics.get_all_metrics(db_conn, metrics=order_mertics_group_2, city=city,
                                                                 app=app, store=store, yesterday_metric=True)

    order_date_wise_metrics.extend(order_date_wise_metrics_group_2)

    order_date_wise_metrics_group_3 = crud.order_metrics.get_all_metrics(db_conn, metrics=order_mertics_group_3, city=city,
                                                                 app=app, store=store, yesterday_metric=True)

    order_date_wise_metrics.extend(order_date_wise_metrics_group_3)
    ##############################################################################################

    new_user_metrics = crud.order_metrics.get_new_users_count_metrics(db_conn,
                                                                      city=city,
                                                                      store=store,
                                                                      is_yesterday=True)
    order_date_wise_metrics.extend(new_user_metrics)
    rider_metrics = crud.rider_metrics.get_rider_login_metrics(db_conn,
                                                               city=city,
                                                               store=store,
                                                               yesterday_metric=True)
    new_rider_metrics = crud.rider_metrics.get_new_rider_metrics(db_conn,
                                                                 city=city,
                                                                 store=store,
                                                                 yesterday_metric=True)
    order_date_wise_metrics.extend(rider_metrics)
    order_date_wise_metrics.extend(new_rider_metrics)
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics,\
        serviceability_based_cart_block_conversion_date_wise_metrics = \
        crud.dau_metrics.get_yesterday_dau_metrics(db_conn,
                                                   app,
                                                   city=city,
                                                   store=store)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(serviceability_based_cart_block_conversion_date_wise_metrics)
    funnel_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store,
                                                                                    yesterday_view=True)
    order_date_wise_metrics.extend(funnel_conversion_metrics)
    surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn,city=city,store=store,yesterday_metric=True)
    order_date_wise_metrics.extend(surge_seen_metrics)

    if not store:
        active_store_count_metrics = crud.store_metrics.get_active_stores_count_metric(db_conn,
                                                                                       city=city,
                                                                                       is_yesterday=True,
                                                                                       get_trend=True)
        order_date_wise_metrics.extend(active_store_count_metrics)
        order = crud.order_metrics.find_models_in_list(order_date_wise_metrics, "Cart Volume")
        opd_per_store = crud.order_metrics.calculate_ratios_based_on_two_models_input(
            order[0].data,
            active_store_count_metrics[0].data,
            "opd_per_store"
        )
        order_date_wise_metrics.extend(opd_per_store)
    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/ath", response_model=AthDatewiseMetric,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True)
def get_ath_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                    db_postgres: Session = Depends(deps.get_postgres_db),
                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                    metric: List[str] = Query(None), city: Optional[List[str]] = Query(None),
                    store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None),
                    status: Optional[List[str]] = Query(None),
                    group_id: Optional[str] = Query(None)):
    """
    Get Ath metrics
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    if not store:
        ath_metrics = crud.order_metrics.get_ath_metrics(db_conn, city=['overall'] if city is None else city)
    else:
        ath_metrics = []
    db_conn.close()
    return AthDatewiseMetric(ath_data=ath_metrics)


@router.get("/cities",
            response_model=LocationDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                       db_postgres: Session = Depends(deps.get_postgres_db),
                       access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                       current_user: dict = Depends(deps.get_current_user),
                       past_days_diff: Optional[int] = Query(7, gt=0), 
                       app: str = Query(None),
                       date: Optional[str] = Query(None), 
                       hour: Optional[str] = Query(None),
                       enable_group_view: Optional[str] = Query(None),):
    """
    Get all metrics for cities (excluding DAU metrics)
    """
    is_sensitive = deps.check_access(None,None,access_mapping)
    # todo: pass today's date till we apply datepicker on cities page
    if not date:
        date = datetime.datetime.now(datetime.timezone(
        datetime.timedelta(hours=5, minutes=30))).strftime('%Y-%m-%d')
    cities_metrics = crud.order_metrics.get_city_wise_current_date_metrics(db_conn, past_days_diff=past_days_diff,
                                                                                       app=app, is_sensitive=is_sensitive,
                                                                                       date_str=date, hour=hour)
    
    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)

        cities_metrics = utils.combine_cities_into_groups(cities_metrics, groups)

    db_conn.close()
    return cities_metrics


@router.get("/cities/dau",
            response_model=LocationDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_cities_dau_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                          db_postgres: Session = Depends(deps.get_postgres_db),
                          access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                          current_user: dict = Depends(deps.get_current_user),
                          past_days_diff: Optional[int] = Query(7, gt=0), 
                          app: str = Query(None),
                          date: Optional[str] = Query(None), 
                          hour: Optional[str] = Query(None),
                          enable_group_view: Optional[str] = Query(None),):
    """
    Get DAU metrics for cities
    """
    is_sensitive = deps.check_access(None,None,access_mapping)
    if not date:
        date = datetime.datetime.now(datetime.timezone(
        datetime.timedelta(hours=5, minutes=30))).strftime('%Y-%m-%d')
    cities_dau_metrics = crud.order_metrics.get_city_wise_dau_metrics(db_conn, past_days_diff=past_days_diff,
                                                                      app=app, is_sensitive=is_sensitive,
                                                                      date_str=date, hour=hour)
    
    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)

        cities_dau_metrics = utils.combine_cities_into_groups(cities_dau_metrics, groups)

    db_conn.close()
    return cities_dau_metrics


@router.get("/cities/stores", response_model=StoreDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_cities_stores_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            current_user: dict = Depends(deps.get_current_user),
                            past_days_diff: Optional[int] = Query(7, gt=0), 
                            app: str = Query(None),
                            city: Optional[List[str]] = Query(None), 
                            date: Optional[str] = Query(None), 
                            hour: Optional[str] = Query(None),
                            enable_group_view: Optional[str] = Query(None),
                            group_id: Optional[int] = Query(None),
                            enable_zone_view: Optional[str] = Query(None),
                            zone: Optional[List[str]] = Query(None)):
    """
    Get all metrics for stores (excluding DAU metrics)
    """
    if group_id: 
        city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store=[])
    is_sensitive = deps.check_access(city, None, access_mapping)
    stores_metrics = crud.order_metrics.get_city_wise_current_date_metrics(db_conn, past_days_diff=past_days_diff,
                                                                            app=app, is_sensitive=is_sensitive,
                                                                            city=city, date_str=date, hour=hour, zone=zone)
    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)
        
        stores_metrics = utils.combine_stores_into_groups(stores_metrics, groups)
    elif enable_zone_view and not zone:
        stores_city_mapping = crud.store_metrics.get_stores_city_mapping(db_conn)
        stores_metrics = utils.combine_stores_into_zones(stores_metrics, stores_city_mapping)

    db_conn.close()
    return stores_metrics


@router.get("/cities/stores/dau", response_model=StoreDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_cities_stores_dau_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                                 db_postgres: Session = Depends(deps.get_postgres_db),
                                 access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                 current_user: dict = Depends(deps.get_current_user),
                                 past_days_diff: Optional[int] = Query(7, gt=0), 
                                 app: str = Query(None),
                                 city: Optional[List[str]] = Query(None), 
                                 date: Optional[str] = Query(None), 
                                 hour: Optional[str] = Query(None),
                                 enable_group_view: Optional[str] = Query(None),
                                 group_id: Optional[int] = Query(None),
                                 enable_zone_view: Optional[str] = Query(None),
                                 zone: Optional[List[str]] = Query(None)):
    """
    Get DAU metrics for stores
    """
    if group_id: 
        city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store=[])
    is_sensitive = deps.check_access(city, None, access_mapping)
    stores_dau_metrics = crud.order_metrics.get_city_wise_dau_metrics(db_conn, past_days_diff=past_days_diff,
                                                                      app=app, is_sensitive=is_sensitive,
                                                                      city=city, date_str=date, hour=hour, zone=zone)
    if enable_group_view:
        db_user = crud.user.get_by_email(db_postgres, email=current_user["user"])
        groups = crud.group.get_accessible_groups(db_postgres, db_user)
        
        stores_dau_metrics = utils.combine_stores_into_groups(stores_dau_metrics, groups)
    elif enable_zone_view and not zone:
        stores_city_mapping = crud.store_metrics.get_stores_city_mapping(db_conn)
        stores_dau_metrics = utils.combine_stores_into_zones(stores_dau_metrics, stores_city_mapping)

    db_conn.close()
    return stores_dau_metrics


@router.get("/cities/base/comparision", response_model=Union[LocationDateMetrics, List[ConversionGroupMetric]],
            dependencies=[Depends(deps.get_current_user)])
# We are caching this right now for one day, since we are only using it yesterday
# Will have to remove it if we start using it some place help
@cache_metrics()
def get_comparitive_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                                         app: Optional[str] = Query(None), metric: List[str] = Query(None),
                                         yesterday_metric: bool = False,
                                         current_status: Optional[List[str]] = Query(None),
                                         city_wise: bool = Query(None), store_wise: bool = Query(None),
                                         is_critical_metrics: bool = Query(None), city: str = Query(None)):
    """
    Get cities metrics for comparision
    """
    cities_metrics = crud.order_metrics.get_city_wise_comparitive_date_metrics(
        db_conn, app=app, metrics=metric, yesterday_metric=yesterday_metric,
        status=current_status, city_wise=city_wise, store_wise=store_wise,
        is_critical_metrics=is_critical_metrics, city=city)
    db_conn.close()
    return cities_metrics


# !! TODO | Separate this into 2 today and yesterday as small cache is being applied for yesterday since there is 1 API 
@router.get("/hourly", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics()
def get_hourly_metrics_base(db_conn: connect = Depends(deps.get_pinot_db),
                        db_postgres: Session = Depends(deps.get_postgres_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        metric: List[str] = Query(None), 
                        city: Optional[List[str]] = Query(None),
                        store: Optional[List[str]] = Query(None), 
                        zone: Optional[List[str]] = Query(None),
                        app: Optional[str] = Query(None),
                        yesterday_metric: bool = False, 
                        status: Optional[List[str]] = Query(None),
                        merchant_type: str = None,
                        group_id: Optional[str] = Query(None)):
    """
    Get hourly metrics
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return HourlyDateMetrics(metrics=[])
    hourly_metrics = crud.order_metrics.get_hourly_metrics(db_conn,
                                                           metrics=metric,
                                                           cities=city,
                                                           app=app,
                                                           stores=store,
                                                           yesterday_metric=yesterday_metric,
                                                           status=status,
                                                           merchant_type=merchant_type,
                                                           merchant_ids=merchant_ids,
                                                           is_sensitive=is_sensitive,
                                                           zone=zone
                                                           )
    db_conn.close()
    return hourly_metrics


@router.get("/hourly/funnel", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_hourly_funnel_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                              db_postgres: Session = Depends(deps.get_postgres_db),
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                              city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), zone: Optional[List[str]] = Query(None),
                              app: Optional[str] = Query(None), yesterday_metric: bool = False,
                              group_id: Optional[str] = Query(None)):
    """
    Get hourly metrics
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)
    hourly_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store,
                                                                                    zone=zone,
                                                                                    hourly_view=True,
                                                                                    yesterday_view=yesterday_metric,
                                                                                    group_view=group_id != None)
    db_conn.close()
    return hourly_conversion_metrics

@router.get("/hourly/surge", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_hourly_metrics_surge(db_conn: connect = Depends(deps.get_pinot_db),
                        db_postgres: Session = Depends(deps.get_postgres_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        city: Optional[List[str]] = Query(None),
                        zone: Optional[List[str]] = Query(None),
                        store: Optional[List[str]] = Query(None),
                        yesterday_metric: bool = False, 
                        group_id: Optional[str] = Query(None)):
    """
    Get hourly surge seen metrics
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)
    hourly_surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn,
                                                           city=city,
                                                           store=store,
                                                           zone=zone,
                                                           is_hourly=True,
                                                           yesterday_metric=yesterday_metric)
    db_conn.close()
    return hourly_surge_seen_metrics
