import logging
from typing import Optional, List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect
import httpx
from app import crud
from app.api import deps, utils
from app.cache.decorator import cache_metrics
from app.cache import THREE_MINUTES
from app.schemas.complaints import ComplaintOrdersImages

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/images", response_model=ComplaintOrdersImages,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True)
def get_complaint_images(db_conn: connect = Depends(deps.get_pinot_db),
                            http_client: httpx.Client = Depends(deps.get_http_session),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            start_date: str = Query(None),
                            end_date: str = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    This gives the S3 URL Images of product complaint images from start_date to end_date based on filters 
    """
    complaint_images = crud.complaints.get_complaint_images(db_conn, http_client=http_client,
                                                            l0_category=l0_category, l1_category=l1_category,
                                                            l2_category=l2_category, ptype=ptype,
                                                            pname=pname, brand=brand,
                                                            city=city, store=store,
                                                            start_date=start_date, end_date=end_date,
                                                            pid=pid)

    return ComplaintOrdersImages(complaints_data=complaint_images)
