import logging
from typing import Optional, List, Dict
import datetime

from fastapi import APIRouter, Depends, Query, HTTPException
from pinotdb import connect

from app import crud
from app.api import deps, utils
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import ONE_DAY_IN_SECONDS, ONE_HOUR_IN_SECONDS, TEN_MINUTES, SIX_HOURS_IN_SECONDS
from app.schemas.order_metric import HourlyDateMetrics, OrderMetrics

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all/order", 
            response_model=OrderMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_instore_order_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                              metric: List[str] = Query(None),
                              city: Optional[List[str]] = Query(None), 
                              store: Optional[List[str]] = Query(None), 
                              app: Optional[str] = Query(None), 
                              status: Optional[List[str]] = Query(None), 
                              yesterday_metric: bool = False, 
                              merchant_type: str = None):
    """
    Get all instore order metrics for T, T-7, T-14, T-21, T-28
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])
    order_date_wise_metrics = crud.order_metrics.get_all_metrics(db_conn, 
                                                                 metrics=metric, 
                                                                 city=city, 
                                                                 status=status, 
                                                                 app=app, 
                                                                 store=store, 
                                                                 yesterday_metric=yesterday_metric, 
                                                                 merchant_type=merchant_type, 
                                                                 merchant_ids=merchant_ids)
    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/custom/all/order", 
            response_model=OrderMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_instore_order_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                              metric: List[str] = Query(None), 
                              city: Optional[List[str]] = Query(None), 
                              store: Optional[List[str]] = Query(None), 
                              app: Optional[str] = Query(None), 
                              status: Optional[List[str]] = Query(None), 
                              merchant_type: str = None,
                              start_date: Optional[str] = Query(None), ):
    """
    Get all instore order metrics for T, T-7, T-14, T-21, T-28 from `start_date`
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])
    order_date_wise_metrics = crud.order_metrics.get_all_metrics_custom(db_conn, metrics=metric, 
                                                                 city=city, 
                                                                 status=status,
                                                                 app=app, 
                                                                 store=store, 
                                                                 merchant_type=merchant_type, 
                                                                 merchant_ids=merchant_ids,
                                                                 start_date=start_date)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=order_date_wise_metrics)

    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/all/dau", 
            response_model=OrderMetrics, dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_instore_dau_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                            city: Optional[List[str]] = Query(None), 
                            store: Optional[List[str]] = Query(None), 
                            app: Optional[str] = Query(None), 
                            status: Optional[List[str]] = Query(None), 
                            yesterday_metric: bool = False, 
                            merchant_type: str = None):
    """
    Get all instore dau metrics for T, T-7, T-14, T-21, T-28
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    metrics = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])

    _, _, demand_based_cart_block_conversion_date_wise_metrics, _, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
        _, _, _, _, _, _, _, _, _ = crud.dau_metrics.get_dau_metrics(db_conn, city=city, app=app, store=store, yesterday_metric=yesterday_metric, merchant_type=merchant_type, merchant_ids=merchant_ids)
    
    metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)

    metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=metrics)
    dau_metrics = OrderMetrics(metrics=metrics)
    db_conn.close()
    return dau_metrics



@router.get("/custom/all/dau", 
            response_model=OrderMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_instore_dau_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                              metric: List[str] = Query(None), 
                              city: Optional[List[str]] = Query(None), 
                              store: Optional[List[str]] = Query(None), 
                              app: Optional[str] = Query(None), 
                              status: Optional[List[str]] = Query(None), 
                              merchant_type: str = None,
                              start_date: Optional[str] = Query(None), ):
    """
    Get all instore dau metrics for T, T-7, T-14, T-21, T-28 from `start_date`
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])
    metrics = []
    _, _, demand_based_cart_block_conversion_date_wise_metrics , _, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
         _, _, _, _, _, _, _, _, _ = crud.dau_metrics.get_custom_dau_metrics(db_conn, 
                                                            city=city, 
                                                            app=app, 
                                                            store=store, 
                                                            merchant_type=merchant_type, 
                                                            merchant_ids=merchant_ids, 
                                                            start_date=start_date)
    
    metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)

    metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=metrics)
    
    order_metrics = OrderMetrics(metrics=metrics)
    db_conn.close()
    return order_metrics


@router.get("/all/active-time", 
            response_model=OrderMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_instore_active_time_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                    metric: List[str] = Query(None), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None), status: Optional[List[str]] = Query(None), yesterday_metric: bool = False, merchant_type: str = None):
    """
    Get all instore active time metrics for T, T-7, T-14, T-21, T-28
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])

    active_hours_date_wise_metrics = crud.instore_metrics.get_active_time_metrics(db_conn, metrics=metric, city=city, app=app, store=store, yesterday_metric=yesterday_metric, merchant_type=merchant_type, merchant_ids=merchant_ids)
    active_hours_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=active_hours_date_wise_metrics)
    active_time_metrics = OrderMetrics(metrics=active_hours_date_wise_metrics)
    db_conn.close()
    return active_time_metrics


@router.get("/custom/all/active-time", 
            response_model=OrderMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_instore_active_time_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                              metric: List[str] = Query(None), 
                              city: Optional[List[str]] = Query(None), 
                              store: Optional[List[str]] = Query(None), 
                              app: Optional[str] = Query(None), 
                              status: Optional[List[str]] = Query(None), 
                              merchant_type: str = None,
                              start_date: Optional[str] = Query(None), ):
    """
    Get all instore active time for T, T-7, T-14, T-21, T-28 from `start_date`
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])

    active_hours_date_wise_metrics = crud.instore_metrics.get_active_time_metrics(db_conn, 
                                                                                  metrics=metric,
                                                                                  city=city, 
                                                                                  app=app, store=store, 
                                                                                  merchant_type=merchant_type, 
                                                                                  merchant_ids=merchant_ids,
                                                                                  custom_view=True,
                                                                                  start_date=start_date)

    active_hours_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=active_hours_date_wise_metrics)
    
    active_time_metrics = OrderMetrics(metrics=active_hours_date_wise_metrics)
    db_conn.close()
    return active_time_metrics


@router.get("/all/complaints", 
            response_model=OrderMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True)
def get_instore_complaints_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                    metric: List[str] = Query(None), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None), status: Optional[List[str]] = Query(None), yesterday_metric: bool = False, merchant_type: str = None):
    """
    Get all instore complaits metrics for T, T-7, T-14, T-21, T-28
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])
    complaints_date_wise_metrics = crud.instore_metrics.get_complaints_metrics(db_conn, metrics=metric, city=city, app=app, store=store, yesterday_metric=yesterday_metric, merchant_type=merchant_type, merchant_ids=merchant_ids)
    complaints_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=complaints_date_wise_metrics)
    complaints_metrics = OrderMetrics(metrics=complaints_date_wise_metrics)
    db_conn.close()
    return complaints_metrics


@router.get("/custom/all/complaints", 
            response_model=OrderMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_instore_complaints_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                              metric: List[str] = Query(None), 
                              city: Optional[List[str]] = Query(None), 
                              store: Optional[List[str]] = Query(None), 
                              app: Optional[str] = Query(None), 
                              status: Optional[List[str]] = Query(None), 
                              merchant_type: str = None,
                              start_date: Optional[str] = Query(None), ):
    """
    Get all instore complaits metrics for T, T-7, T-14, T-21, T-28 from `start_date`
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return OrderMetrics(metrics=[])

    complaints_date_wise_metrics = crud.instore_metrics.get_complaints_metrics(db_conn, 
                                                                               metrics=metric, 
                                                                               city=city, 
                                                                               app=app, 
                                                                               store=store, 
                                                                               merchant_type=merchant_type, 
                                                                               merchant_ids=merchant_ids,
                                                                               custom_view=True,
                                                                               start_date=start_date)

    complaints_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive, metrics_list=complaints_date_wise_metrics)
    
    complaints_metrics = OrderMetrics(metrics=complaints_date_wise_metrics)
    db_conn.close()
    return complaints_metrics


@router.get("/hourly", 
            response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics()
def get_active_time_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                                     access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                     metric: List[str] = Query(None), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None), yesterday_metric: bool = False, status: Optional[List[str]] = Query(None), merchant_type: str = None):
    """
    Get Intores Active time hourly metrics
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return HourlyDateMetrics(metrics=[])
    active_hours_hourly_metrics = crud.instore_metrics.get_active_time_metrics(db_conn, metrics=metric, city=city, app=app, store=store, yesterday_metric=yesterday_metric, is_hourly=True, merchant_type=merchant_type, merchant_ids=merchant_ids)
    db_conn.close()
    return active_hours_hourly_metrics


@router.get("/custom/hourly", response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_active_time_hourly_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                                   access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                   metric: List[str] = Query(None), 
                                   city: Optional[List[str]] = Query(None), 
                                   store: Optional[List[str]] = Query(None), 
                                   app: Optional[str] = Query(None), 
                                   status: Optional[List[str]] = Query(None), 
                                   merchant_type: str = None,
                                   start_date: Optional[str] = Query(None), ):
    """
    Get Intores Active time hourly metrics from `start_date`
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return HourlyDateMetrics(metrics=[])
    active_hours_hourly_metrics = crud.instore_metrics.get_active_time_metrics(
        db_conn, metrics=metric, city=city, app=app, store=store, is_hourly=True, merchant_type=merchant_type, merchant_ids=merchant_ids, custom_view=True, start_date=start_date)
    db_conn.close()
    return active_hours_hourly_metrics


@router.get("/hourly/complaints", response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics()
def get_complaints_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                                  access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                  metric: List[str] = Query(None), city: Optional[List[str]] = Query(None), store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None), yesterday_metric: bool = False, status: Optional[List[str]] = Query(None), merchant_type: str = None):
    """
    Get Intores complaints hourly metrics
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return HourlyDateMetrics(metrics=[])
    active_hours_hourly_metrics = crud.instore_metrics.get_complaints_metrics(db_conn, metrics=metric, city=city, app=app, store=store, yesterday_metric=yesterday_metric, is_hourly=True, status=status, merchant_type=merchant_type, merchant_ids=merchant_ids)
    db_conn.close()
    return active_hours_hourly_metrics


@router.get("/custom/hourly/complaints", response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True)
def get_complaints_hourly_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                                  access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                  metric: List[str] = Query(None), 
                                  city: Optional[List[str]] = Query(None), 
                                  store: Optional[List[str]] = Query(None), 
                                  app: Optional[str] = Query(None), 
                                  status: Optional[List[str]] = Query(None), 
                                  merchant_type: str = None, 
                                  start_date: Optional[str] = Query(None), ):
    """
    Get Intores complaints hourly metrics from `start_date`
    """
    is_sensitive = deps.check_access(city, store, access_mapping)
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return HourlyDateMetrics(metrics=[])
    active_hours_hourly_metrics = crud.instore_metrics.get_complaints_metrics(db_conn, 
                                                                              metrics=metric,
                                                                              city=city, app=app, 
                                                                              store=store, 
                                                                              is_hourly=True, status=status, 
                                                                              merchant_type=merchant_type, merchant_ids=merchant_ids,
                                                                              custom_view=True, start_date=start_date)
    db_conn.close()
    return active_hours_hourly_metrics
