from fastapi import APIRouter, Depends, Query
from typing import Optional
from app import crud
from app.api import deps
from pinotdb import connect
from app.schemas.availability_metric import HourlyDateMetrics
from app.cache.decorator import cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS

router = APIRouter()

@router.get("/cities", response_model=HourlyDateMetrics, dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS)
def get_availability_metrics(db_conn: connect = Depends(deps.get_pinot_db), past_days_diff: Optional[int] = Query(7, gt=0)):
    """
    Get all availability metrics for cities
    """
    availability_cities_metrics = crud.availability_metrics.get_hourly_availability_metrics(db_conn,
                                                                                     past_days_diff=past_days_diff)
    db_conn.close()
    return availability_cities_metrics
