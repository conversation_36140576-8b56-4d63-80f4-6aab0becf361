from fastapi import APIRouter

from app.api.api_v1.endpoints import surge_seen_metrics, hau_metrics, login, order_metrics, mtd_metrics, availability_metrics, store_metrics, cache, wtd_metrics, current_rate_metrics, rider_metrics, insights, instore_metrics, warehouse_metrics, product_metrics, complaints, category_metrics, bad_stock, emergency_service_metrics, paas_metrics, groups

api_router = APIRouter()
api_router.include_router(login.router, tags=["login"])
api_router.include_router(order_metrics.router, prefix="/order-metrics", tags=["order_metrics"])
api_router.include_router(mtd_metrics.router, prefix="/mtd_metrics", tags=["mtd_metrics"])
api_router.include_router(wtd_metrics.router, prefix="/wtd_metrics", tags=["wtd_metrics"])
api_router.include_router(hau_metrics.router, prefix="/hau", tags=["hau_metrics"])
api_router.include_router(surge_seen_metrics.router, prefix="/surge_seen", tags=["surge_seen_metrics"])
api_router.include_router(rider_metrics.router, prefix="/rider_metrics", tags=["rider_metrics"])
api_router.include_router(availability_metrics.router, prefix="/availability_metrics", tags=["availability_metrics"])
api_router.include_router(store_metrics.router, prefix="/stores", tags=["store_metrics"])
api_router.include_router(cache.router, prefix="/cache", tags=["cache"])
api_router.include_router(current_rate_metrics.router, prefix="/current_rate_metrics", tags=["current_rate_metrics"])
api_router.include_router(insights.router, prefix="/insights", tags=["insights"])
api_router.include_router(instore_metrics.router,prefix="/instore-metrics", tags=["instore"])
api_router.include_router(warehouse_metrics.router,prefix="/warehouses", tags=["warehouse_metrics"])
api_router.include_router(product_metrics.router, prefix="/product-metrics", tags=["product_metric"])
api_router.include_router(complaints.router, prefix="/complaints", tags=["complaints"])
api_router.include_router(category_metrics.router, prefix="/category-metrics", tags=["category_metric"])
api_router.include_router(bad_stock.router, prefix="/bad_stock", tags=["bad_stock"])
api_router.include_router(emergency_service_metrics.router, prefix="/emergency-service-metrics", tags=["emergency_service_metrics"])
api_router.include_router(paas_metrics.router, prefix="/paas-metrics", tags=["paas_metrics"])
api_router.include_router(groups.router, prefix="/groups", tags=["groups"])
