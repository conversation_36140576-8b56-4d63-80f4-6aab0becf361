import json

from requests_oauthlib import OAuth2Session

from app.core.config import settings


def fetch_oauth_session():
    client_id = settings.GOOGLE_CLIENT_ID
    redirect_uri = settings.GOOGLE_REDIRECT_URI
    scope = [
        "https://www.googleapis.com/auth/userinfo.email",
        "https://www.googleapis.com/auth/userinfo.profile"
    ]
    return OAuth2Session(client_id, scope=scope, redirect_uri=redirect_uri)


def generate_authorization_url(oauth_provider):
    authorization_url, state = oauth_provider.authorization_url(
        "https://accounts.google.com/o/oauth2/v2/auth",
        access_type="offline",
        prompt="select_account"
    )
    return authorization_url


def fetch_user_info(oauth_provider, authorization_token):
    client_secret = settings.GOOGLE_CLIENT_SECRET
    oauth_provider.fetch_token(
        "https://www.googleapis.com/oauth2/v4/token",
        client_secret=client_secret,
        code=authorization_token
    )
    r = oauth_provider.get('https://www.googleapis.com/oauth2/v1/userinfo')
    return json.loads(r.content)


def is_allowed_domain(user):
    if user.get('hd'):
        return user.get('hd') in ['grofers.com', 'handsontrades.com', 'zomato.com']
    return False
