import email
import logging
import httpx
from typing import Generator, Dict, List, Any, Optional

from fastapi import HTTPException, status, Depends, Request
from app.core.jwt_auth import get_current_user as jwt_get_current_user, get_jwt_identity
from sqlalchemy.orm import Session
from app.core.config import settings
from app.core import const
from app import crud

from app.db.session import engine, SessionLocal

logger = logging.getLogger(__name__)

ALL_CITY_KEY: str = "all"
ALL_STORE_KEY: str = "-1"


def get_pinot_db() -> Generator:
    try:
        # Getting a connection from the pool, but using raw_connection to maintain compatibility
        conn = engine.raw_connection()
        yield conn
    except Exception as e:
        logger.error(f"Error getting Pinot connection: {e}")
        raise
    finally:
        # Properly close the connection to return it to the pool
        if conn is not None:
            try:
                conn.close()
            except Exception as close_error:
                logger.error(f"Error closing Pinot connection: {close_error}")


def get_postgres_db() -> Generator:
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


def get_http_session() -> httpx.Client:
    try:
        timeout = httpx.Timeout(5.0, connect=2.0)
        limits = httpx.Limits(max_keepalive_connections=5, max_connections=10)
        client = httpx.Client(limits=limits, timeout=timeout)
        yield client
    finally:
        client.close


def get_current_user(request: Request = None, current_user: Dict[str, Any] = Depends(jwt_get_current_user)):
    """Get current user from JWT token"""
    if not current_user and request:
        # Fallback to request-based extraction if the regular dependency fails
        from app.core.jwt_auth import JWTAuth
        try:
            current_user = JWTAuth.get_current_user(request)
        except HTTPException as e:
            logger.error(f"Authentication error: {e.detail}")
            raise
    
    if not current_user:
        logger.error("Authentication failed: No user data in token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed"
        )
    
    user_id = current_user.get("sub", "")
    if not user_id:
        logger.error("Authentication failed: No sub claim in token")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token format"
        )
        
    return {"user": user_id}


def get_access_mapping(current_user: Dict[str, Any] = Depends(jwt_get_current_user)) -> Dict[str, Dict[int, bool]]:
    try:
        # Get the access mapping from the token claims
        mapping_dict = current_user.get('user_access_mapping', {})
    except:
        raise HTTPException(status_code=403, detail="Unable to fetch access mapping from auth token")
    
    return mapping_dict


def check_access(city: List[str], store: List[str], access_mapping: Dict[str, Dict[int, bool]], tenant: str = 'Blinkit') -> bool:
    access_mapping = access_mapping[tenant]
    if city:
        city_selected = city[0]
        # TODO: Need to handle for multiple cities and stores.
        if store:
            store_selected = store[0]
            store_id = str(store_selected)
            if city_selected in access_mapping:
                if store_id in access_mapping[city_selected]:
                    return access_mapping[city_selected][store_id]
                else:
                    if ALL_STORE_KEY in access_mapping[city_selected]:
                        return access_mapping[city_selected][ALL_STORE_KEY]
                    return check_global_access(access_mapping)
            else:
                return check_global_access(access_mapping)
        else:
            if city_selected in access_mapping:
                return access_mapping[city_selected][ALL_STORE_KEY]
            else:
                return check_global_access(access_mapping)
    else:
        return check_global_access(access_mapping)


def check_global_access(access_mapping: Dict[str, Dict[int, bool]]) -> bool:
    if ALL_CITY_KEY in access_mapping:
        return access_mapping[ALL_CITY_KEY][ALL_STORE_KEY]
    else:
        raise HTTPException(status_code=401, detail="Access denied")


def validate_group_creation_access(
    db: Session,
    current_user: Dict[str, str],
    is_global: Optional[bool],
    city_store_mappings: Optional[List[Dict[str, Any]]],
    tenant_id: int = const.BLINKIT_TENANT_ID
) -> bool:
    """
    Validates if user has access to create groups with specified cities/stores and global groups. 
    """
    user_email = current_user["user"]
    current_user = crud.user.get_by_email(db, email=user_email)
    if not current_user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )
    
    tenant_mappings = crud.user_tenant_mapping.get_tenant_mappings_by_user_id(
        db=db, user_id=current_user.id
    )
    tenant_access = next(
        (mapping for mapping in tenant_mappings if mapping["tenant_id"] == tenant_id),
        None
    )
    if not tenant_access:
        raise HTTPException(
            status_code=403,
            detail="User does not have access to this tenant"
        )

    if is_global: 
        if not tenant_access["is_global_group_allowed"]:
            raise HTTPException(
                status_code=403,
                detail="User does not have permission to create global groups"
            )

    if city_store_mappings: 
        access_mapping = crud.user_access_mapping.get_mappings_by_user_id(
            db=db,
            userId=current_user.id,
            tenants_access_info=[tenant_access]
        )

        tenant_name = const.TENANT_NAME_MAPPING[tenant_id]
        user_access_data = access_mapping[tenant_name]['userCityStoreAccessData']

        # For global users, check if they have 'all' city access
        if any(mapping.city_name == 'all' for mapping in user_access_data):
            return True

        # Validate cities and their stores access
        for city_mapping in city_store_mappings:
            city = city_mapping.city
            stores = city_mapping.stores

            # Check if user has access to this city
            city_mappings = [m for m in user_access_data if m.city_name == city]
            if not city_mappings:
                raise HTTPException(
                    status_code=403,
                    detail=f"User does not have access to city: {city}"
                )
            
            # Check if user has all store access for this city
            has_all_store_access = any(m.store_id == -1 for m in city_mappings)
            if has_all_store_access:
                continue

            # Validate individual store access
            for store in stores:
                store_id = int(store) if store != '-1' else -1
                if store_id != -1 and not any(m.store_id == store_id for m in city_mappings):
                    raise HTTPException(
                        status_code=403,
                        detail=f"User does not have access to store {store} in city {city}"
                    )

    return True