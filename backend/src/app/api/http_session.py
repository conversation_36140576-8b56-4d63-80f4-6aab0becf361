from requests import Session
from requests.adapters import HTT<PERSON>dapter
from urllib3 import Retry

import logging

logger = logging.getLogger(__name__)


class HttpSession:

    def __init__(self, service_user: str = None, service_password: str = None, session_headers: dict = None):
        def log_response(response, *args, **kwargs):
            logger.info(f"{response.url} [status:{response.status_code} request: {int(response.elapsed.total_seconds()*1000)}ms]")

        self._session = Session()
        retry = Retry(
            total=3,
            read=3,
            connect=3,
            backoff_factor=0.1,
            status_forcelist=[500, 502, 503, 504]
        )
        adapter = HTTPAdapter(max_retries=retry)
        self._session.mount('http://', adapter)
        self._session.mount('https://', adapter)
        if service_user:
            self._session.auth = (service_user, service_password)
        self._session.headers = session_headers
        self._session.hooks = {
            'response': log_response
        }

    def get_session(self):
        return self._session
