from fastapi import APIRouter

from app.api.api_v2.endpoints import order_metrics, hau_metrics, rider_metrics, current_rate_metrics
 
api_router_v2 = APIRouter()
api_router_v2.include_router(order_metrics.router, prefix="/order-metrics", tags=["order_metrics"])
api_router_v2.include_router(hau_metrics.router, prefix="/hau", tags=["hau_metrics"])
api_router_v2.include_router(rider_metrics.router, prefix="/rider_metrics", tags=["rider_metrics"])
api_router_v2.include_router(current_rate_metrics.router, prefix="/current_rate_metrics", tags=["current_rate_metrics"])
