from typing import Optional, List, Dict
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Depends, Query, HTTPException
from pinotdb import connect

from app import crud
from app.api import deps
from app.cache.decorator import cache_metrics
from app.schemas.order_metric import LocationDateMetrics, HourlyDateMetrics, StoreDateMetrics
from app.crud.utils import is_yesterday_date
from sqlalchemy.orm import Session

router = APIRouter()


@router.get("/custom/hourly", response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics() # !! can this be inc  
def get_hourly_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                              db_postgres: Session = Depends(deps.get_postgres_db),
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                              city: Optional[List[str]] = Query(None),
                              store: Optional[List[str]] = Query(None), 
                              start_date: Optional[str] = Query(None), 
                              zone: Optional[List[str]] = Query(None),
                              group_id: Optional[str] = Query(None)):
    """
    Get rider login hr hourly metrics for `start_date` as T and T-7
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    deps.check_access(city,store,access_mapping)
    hourly_metrics = crud.rider_metrics.get_rider_login_metrics_custom(db_conn,
                                                                city=city,
                                                                store=store,
                                                                zone=zone,
                                                                hourly_metrics=True,
                                                                start_date=start_date)
    db_conn.close()
    return hourly_metrics
