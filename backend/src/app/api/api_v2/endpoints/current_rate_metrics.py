from typing import Optional,List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps
from app.cache import ONE_DAY_IN_SECONDS, FIVE_MINUTES
from app.cache.decorator import cache_metrics
from sqlalchemy.orm import Session

from app.schemas.order_metric import HourlyDateMetrics
from app.schemas.current_rate_metric import CurrentRateMetric, ProjectionMetrics, CurrentRateDailyHourly

router = APIRouter()


@router.get(
    "/all",
    response_model=CurrentRateDailyHourly,
    dependencies=[Depends(deps.get_current_user)],
)
@cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_all_projection_metrics_daily_hourly(db_conn: connect = Depends(deps.get_pinot_db), 
                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                             city: Optional[List[str]] = Query(None),):
    """
    Get all metrics in the projection tab: 
    - daily current rate metrics 
    - daily projected metrics 
    - hourly 
        - Cart Volume Actual Today
        - Cart Volume Projected Today
        - Cart Volume WoW
    """
    deps.check_access(city=city,store=None,access_mapping=access_mapping)
    projection_metrics_daily_hourly_metrics = crud.current_rate_metrics_v2.get_all_projection_metrics(db_conn,
                                                                                                      city=city)
    db_conn.close()
    return projection_metrics_daily_hourly_metrics


@router.get("/custom/bucket/projection_metrics", response_model=CurrentRateMetric, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=ONE_DAY_IN_SECONDS, force_cache_burst_at_eod=True)
def get_projection_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db), 
                                  db_postgres: Session = Depends(deps.get_postgres_db),
                                  access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                                  city: Optional[List[str]] = Query(None),
                                  zone: Optional[List[str]] = Query(None), 
                                  store: Optional[List[str]] = Query(None),
                                  start_date: Optional[str] = Query(None), 
                                  group_id: Optional[str] = Query(None)):
    """
    Get Projection Metrics for `start_date`
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    deps.check_access(city=city,store=store,access_mapping=access_mapping)
    result = crud.projection_metrics.get_projection_metrics_daily(db_conn,
                                                          city = ['Overall'] if city is None else city,
                                                          store=store,
                                                          zone=zone,
                                                          start_date=start_date)
    db_conn.close()
    return CurrentRateMetric(metrics=result)
