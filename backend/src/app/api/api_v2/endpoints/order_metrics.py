import logging
from typing import Optional, List, Dict, Union
import datetime

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app import crud
from app.api import deps,utils
from app.cache.decorator import cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS, TEN_MINUTES, THIRTY_SECONDS, SIX_HOURS_IN_SECONDS, ELEVEN_MINUTES, THREE_MINUTES, FIFTEEN_MINUTES, FIVE_MINUTES
from app.schemas.order_metric import LocationDateMetrics, HourlyDateMetrics, OrderMetrics, StoreDateMetrics, ConversionGroupMetric, AthDatewiseMetric, OrdersPerMinute
from sqlalchemy.orm import Session

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all/base-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_order_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        db_postgres: Session = Depends(deps.get_postgres_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        metric: List[str] = Query(None), 
                        city: Optional[List[str]] = Query(None),
                        store: Optional[List[str]] = Query(None), 
                        app: Optional[str] = Query(None),
                        zone: Optional[List[str]] = Query(None),
                        status: Optional[List[str]] = Query(None),
                        group_id: Optional[str] = Query(None)):
    """
    Get base metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = crud.order_metrics.get_all_metrics(db_conn, metrics=metric,
                                                                 city=city, status=status,
                                                                 app=app, store=store, zone=zone)

    if "order_count" in metric and not store:
        active_store_count_metrics = crud.store_metrics.get_active_stores_count_metric(db_conn,
                                                                                       city=city,
                                                                                       is_yesterday=False,
                                                                                       get_trend=True, zone=zone)
        order_date_wise_metrics.extend(active_store_count_metrics)
        order = crud.order_metrics.find_models_in_list(order_date_wise_metrics, "Cart Volume")
        opd_per_store = crud.order_metrics.calculate_ratios_based_on_two_models_input(
            order[0].data,
            active_store_count_metrics[0].data,
            "opd_per_store"
        )
        order_date_wise_metrics.extend(opd_per_store)


    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


# [DEPRICATED]
# @router.get("/all/new-user-metrics", response_model=OrderMetrics,
#             dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
# @cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
# def get_new_user_metrics(db_conn: connect = Depends(deps.get_pinot_db),
#                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
#                             city: Optional[List[str]] = Query(None),
#                             store: Optional[List[str]] = Query(None)):
#     """
#     Get new-user-metrics for T, T-7, T-14, T-21, T-28
#     """
#     is_sensitive = deps.check_access(city, store, access_mapping)

#     order_date_wise_metrics = []
#     new_user_metrics = crud.order_metrics.get_new_users_count_metrics(db_conn,
#                                                                       city=city,
#                                                                       store=store,
#                                                                       is_yesterday=False)
#     order_date_wise_metrics.extend(new_user_metrics)

#     order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
#     order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
#                                                              metrics_list=order_date_wise_metrics)
#     db_conn.close()
#     return order_metrics 


@router.get("/all/rider-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_rider_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None), 
                            app: Optional[str] = Query(None),
                            status: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get rider-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    rider_metrics = crud.rider_metrics.get_rider_login_metrics(db_conn,
                                                               city=city,
                                                               store=store,
                                                               zone=zone)
    new_rider_metrics = crud.rider_metrics.get_new_rider_metrics(db_conn,
                                                                 city=city,
                                                                 store=store, 
                                                                 zone=zone)
    order_date_wise_metrics.extend(rider_metrics)
    order_date_wise_metrics.extend(new_rider_metrics)
    
    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/all/dau-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True)
def get_dau_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            app: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get dau-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics, \
        express_demand_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_manual_based_cart_block_conversion_date_wise_metrics, \
        longtail_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        longtail_serviceability_manual_based_cart_block_conversion_date_wise_metrics, \
        longtail_demand_based_cart_block_conversion_date_wise_metrics, \
        high_priority_users_demand_block_perc, high_priority_users_ooh_block_perc, high_priority_users_manual_block_perc, high_priority_users_perc = crud.dau_metrics.get_dau_metrics(db_conn,
                                                                                city=city,
                                                                                app=app,
                                                                                store=store,
                                                                                zone=zone)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_manual_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_serviceability_manual_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(high_priority_users_demand_block_perc)
    # TEMPORALLY REMOVE high_priority_users_ooh_block_perc and high_priority_users_manual_block_perc
    # order_date_wise_metrics.extend(high_priority_users_ooh_block_perc)
    # order_date_wise_metrics.extend(high_priority_users_manual_block_perc)
    order_date_wise_metrics.extend(high_priority_users_perc)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/all/funnel-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_funnel_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get funnel-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    funnel_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store,
                                                                                    zone=zone,
                                                                                    group_view = group_id != None)
    order_date_wise_metrics.extend(funnel_conversion_metrics)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/all/surge-seen-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True)
def get_surge_seen_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get surge-seen-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn,city=city,store=store, zone=zone)
    order_date_wise_metrics.extend(surge_seen_metrics)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 

# -----------------------------------------------------------------------------

@router.get("/yesterday/all/base-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_order_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None), 
                            app: Optional[str] = Query(None),
                            status: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get base metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = crud.order_metrics.get_all_metrics(db_conn, metrics=metric,
                                                                 city=city, status=status,
                                                                 app=app, store=store,
                                                                 yesterday_metric=True)

    if "order_count" in metric and not store:
        active_store_count_metrics = crud.store_metrics.get_active_stores_count_metric(db_conn,
                                                                                       city=city,
                                                                                       is_yesterday=True,
                                                                                       get_trend=True)
        order_date_wise_metrics.extend(active_store_count_metrics)
        order = crud.order_metrics.find_models_in_list(order_date_wise_metrics, "Cart Volume")
        opd_per_store = crud.order_metrics.calculate_ratios_based_on_two_models_input(
            order[0].data,
            active_store_count_metrics[0].data,
            "opd_per_store"
        )
        order_date_wise_metrics.extend(opd_per_store)


    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


# [DEPRICATED]
# @router.get("/yesterday/all/new-user-metrics", response_model=OrderMetrics,
#             dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
# @cache_metrics(
#     expire=SIX_HOURS_IN_SECONDS, 
#     force_cache_burst_at_eod=True, 
#     force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
# def get_new_user_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
#                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
#                             city: Optional[List[str]] = Query(None),
#                             store: Optional[List[str]] = Query(None)):
#     """
#     Get new-user-metrics for T, T-7, T-14, T-21, T-28
#     """
#     is_sensitive = deps.check_access(city, store, access_mapping)

#     order_date_wise_metrics = []
#     new_user_metrics = crud.order_metrics.get_new_users_count_metrics(db_conn,
#                                                                       city=city,
#                                                                       store=store,
#                                                                       is_yesterday=True)
#     order_date_wise_metrics.extend(new_user_metrics)

#     order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
#     order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
#                                                              metrics_list=order_date_wise_metrics)
#     db_conn.close()
#     return order_metrics 


@router.get("/yesterday/all/rider-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_rider_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None), 
                            app: Optional[str] = Query(None),
                            status: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get rider-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    rider_metrics = crud.rider_metrics.get_rider_login_metrics(db_conn,
                                                               city=city,
                                                               store=store,
                                                               yesterday_metric=True)
    new_rider_metrics = crud.rider_metrics.get_new_rider_metrics(db_conn,
                                                                 city=city,
                                                                 store=store,
                                                                 yesterday_metric=True)
    order_date_wise_metrics.extend(rider_metrics)
    order_date_wise_metrics.extend(new_rider_metrics)
    
    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/yesterday/all/dau-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_dau_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            app: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get dau-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics, \
        express_demand_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_manual_based_cart_block_conversion_date_wise_metrics, \
        longtail_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        longtail_serviceability_manual_based_cart_block_conversion_date_wise_metrics, \
        longtail_demand_based_cart_block_conversion_date_wise_metrics, \
        high_priority_users_demand_block_perc, high_priority_users_ooh_block_perc, high_priority_users_manual_block_perc, high_priority_users_perc = crud.dau_metrics.get_yesterday_dau_metrics(db_conn,
                                                                                city=city,
                                                                                app=app,
                                                                                store=store)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_manual_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_serviceability_manual_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(high_priority_users_demand_block_perc)
    # TEMPORALLY REMOVE high_priority_users_ooh_block_perc and high_priority_users_manual_block_perc
    # order_date_wise_metrics.extend(high_priority_users_ooh_block_perc)
    # order_date_wise_metrics.extend(high_priority_users_manual_block_perc)
    order_date_wise_metrics.extend(high_priority_users_perc)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/yesterday/all/funnel-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_funnel_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get funnel-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    funnel_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store,
                                                                                    yesterday_view=True)
    order_date_wise_metrics.extend(funnel_conversion_metrics)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/yesterday/all/surge-seen-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_surge_seen_metrics_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get funnel-metrics for T, T-7, T-14, T-21, T-28
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics(db_conn,city=city,store=store, yesterday_metric=True)
    order_date_wise_metrics.extend(surge_seen_metrics)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/orders-per-minute", response_model=OrdersPerMinute,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True)
def get_orders_per_minute(db_conn: connect = Depends(deps.get_pinot_db),
                          db_postgres: Session = Depends(deps.get_postgres_db),
                          access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                          date_str: Optional[str] = Query(None),
                          city: Optional[List[str]] = Query(None),
                          store: Optional[List[str]] = Query(None),
                          zone: Optional[List[str]] = Query(None),
                          group_id: Optional[str] = Query(None)):
    """
    Get orders per minute (OPM) metrics for a specific date
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    # Get orders per minute data
    result = crud.order_metrics.get_orders_per_minute(
        conn=db_conn,
        date_str=date_str,
        city=city,
        store=store,
        zone=zone
    )
    
    db_conn.close()
    return result

# ----------------------------------------------------------------------------- custom dt filters

@router.get("/custom/all/base-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_order_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            metric: List[str] = Query(None), 
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None), 
                            zone: Optional[List[str]] = Query(None),
                            app: Optional[str] = Query(None),
                            status: Optional[List[str]] = Query(None),
                            start_date: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get base metrics for T, T-7, T-14, T-21, T-28 where T is passed as `start_date`
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = crud.order_metrics.get_all_metrics_custom(db_conn, metrics=metric,
                                                                 city=city, status=status,
                                                                 app=app, store=store, zone=zone,
                                                                 start_date= start_date)

    if "order_count" in metric and not store:
        active_store_count_metrics = crud.store_metrics.get_active_stores_count_metric_custom(db_conn,
                                                                                       city=city,
                                                                                       start_date=start_date,
                                                                                       get_trend=True,
                                                                                       zone=zone)
        order_date_wise_metrics.extend(active_store_count_metrics)
        order = crud.order_metrics.find_models_in_list(order_date_wise_metrics, "Cart Volume")
        opd_per_store = crud.order_metrics.calculate_ratios_based_on_two_models_input(
            order[0].data,
            active_store_count_metrics[0].data,
            "opd_per_store"
        )
        order_date_wise_metrics.extend(opd_per_store)


    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


# [DEPRICATED]
# @router.get("/custom/all/new-user-metrics", response_model=OrderMetrics,
#             dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
# @cache_metrics(
#     expire=SIX_HOURS_IN_SECONDS, 
#     force_cache_burst_at_eod=True, 
#     force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
# def get_new_user_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
#                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
#                             city: Optional[List[str]] = Query(None),
#                             store: Optional[List[str]] = Query(None),
#                             start_date: Optional[str] = Query(None), ):
#     """
#     Get new-user-metrics for T, T-7, T-14, T-21, T-28 where T is passed as `start_date`
#     """
#     is_sensitive = deps.check_access(city, store, access_mapping)

#     order_date_wise_metrics = []
#     new_user_metrics = crud.order_metrics.get_new_users_count_metrics_custom(db_conn,
#                                                                       city=city,
#                                                                       store=store,
#                                                                       start_date=start_date)
#     order_date_wise_metrics.extend(new_user_metrics)

#     order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
#     order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
#                                                              metrics_list=order_date_wise_metrics)
#     db_conn.close()
#     return order_metrics 


@router.get("/custom/all/rider-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_rider_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None), 
                            start_date: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get rider-metrics for T, T-7, T-14, T-21, T-28 where T is passed as `start_date`
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    rider_metrics = crud.rider_metrics.get_rider_login_metrics_custom(db_conn,
                                                               city=city,
                                                               store=store,
                                                               zone=zone,
                                                               start_date=start_date)
    new_rider_metrics = crud.rider_metrics.get_new_rider_metrics_custom(db_conn,
                                                                 city=city,
                                                                 store=store,
                                                                 zone=zone,
                                                                 start_date=start_date)
    order_date_wise_metrics.extend(rider_metrics)
    order_date_wise_metrics.extend(new_rider_metrics)
    
    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/custom/all/dau-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_dau_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            app: Optional[str] = Query(None),
                            start_date: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get dau-metrics for T, T-7, T-14, T-21, T-28 where T is passed as `start_date`
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics, \
        express_demand_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_manual_based_cart_block_conversion_date_wise_metrics, \
        longtail_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        longtail_serviceability_manual_based_cart_block_conversion_date_wise_metrics, \
        longtail_demand_based_cart_block_conversion_date_wise_metrics, \
        high_priority_users_demand_block_perc, high_priority_users_ooh_block_perc, high_priority_users_manual_block_perc, high_priority_users_perc = crud.dau_metrics.get_custom_dau_metrics(db_conn,
                                                                                city=city,
                                                                                app=app,
                                                                                store=store,
                                                                                zone=zone,
                                                                                start_date = start_date)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_manual_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_serviceability_manual_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(longtail_demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(high_priority_users_demand_block_perc)
    # TEMPORALLY REMOVE high_priority_users_ooh_block_perc and high_priority_users_manual_block_perc
    # order_date_wise_metrics.extend(high_priority_users_ooh_block_perc)
    # order_date_wise_metrics.extend(high_priority_users_manual_block_perc)
    order_date_wise_metrics.extend(high_priority_users_perc)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/custom/all/funnel-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_funnel_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            start_date: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get funnel-metrics for T, T-7, T-14, T-21, T-28 where T is passed as `start_date`
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    funnel_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics_custom(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store,
                                                                                    zone=zone,
                                                                                    start_date=start_date,
                                                                                    group_view = group_id != None)
    order_date_wise_metrics.extend(funnel_conversion_metrics)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/custom/all/surge-seen-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_surge_seen_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                            db_postgres: Session = Depends(deps.get_postgres_db),
                            access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                            city: Optional[List[str]] = Query(None),
                            zone: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            start_date: Optional[str] = Query(None),
                            group_id: Optional[str] = Query(None)):
    """
    Get surge-seen-metrics for T, T-7, T-14, T-21, T-28 where T is passed as `start_date`
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)

    order_date_wise_metrics = []
    surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics_custom(db_conn, city=city,
                                                                        store=store, 
                                                                        zone=zone,
                                                                        start_date=start_date)
    order_date_wise_metrics.extend(surge_seen_metrics)

    order_date_wise_metrics = utils.remove_sensitive_metrics(is_sensitive=is_sensitive,
                                                             metrics_list=order_date_wise_metrics)
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 

# ----------------------------------------------------------------------------- custom dt filters | Hourly View 

@router.get("/custom/hourly", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(
    expire=SIX_HOURS_IN_SECONDS, 
    force_cache_burst_at_eod=True, 
    force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
def get_hourly_metrics_custom_base(db_conn: connect = Depends(deps.get_pinot_db),
                             db_postgres: Session = Depends(deps.get_postgres_db),
                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                             metric: List[str] = Query(None), 
                             city: Optional[List[str]] = Query(None),
                             zone: Optional[List[str]] = Query(None),
                             store: Optional[List[str]] = Query(None), 
                             app: Optional[str] = Query(None),
                             status: Optional[List[str]] = Query(None),
                             merchant_type: str = None,
                             start_date: Optional[str] = Query(None),
                             group_id: Optional[str] = Query(None)):
    """
    Get hourly metrics for `start_date` as T and T-7 
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)
    
    # !! Check: can this query be removed and handled later 
    merchant_ids = []
    if merchant_type:
        merchant_ids = crud.order_metrics.get_merchant_ids(db_conn, city, merchant_type)
        if not merchant_ids:
            return HourlyDateMetrics(metrics=[])
    
    hourly_metrics = crud.order_metrics.get_hourly_metrics(db_conn, metrics=metric,
                                                           cities=city, app=app,
                                                           stores=store, zone=zone, status=status,
                                                           merchant_type=merchant_type, merchant_ids=merchant_ids,
                                                           is_sensitive=is_sensitive, 
                                                           custom_view=True,
                                                           start_date = start_date)
    db_conn.close()
    return hourly_metrics


@router.get("/custom/hourly/funnel", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True) # !! Check this, can be inc 
def get_hourly_funnel_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                                    db_postgres: Session = Depends(deps.get_postgres_db),
                                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                    city: Optional[List[str]] = Query(None), 
                                    zone: Optional[List[str]] = Query(None),
                                    store: Optional[List[str]] = Query(None),
                                    app: Optional[str] = Query(None), 
                                    start_date: Optional[str] = Query(None),
                                    group_id: Optional[str] = Query(None)):
    """
    Get hourly metrics for `start_date` as T and T-7 
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)
    hourly_conversion_metrics = crud.insights_metrics.get_funnel_conversion_metrics_custom(db_conn,
                                                                                    city=['Overall'] if city is None
                                                                                    else city,
                                                                                    store=store,
                                                                                    zone=zone,
                                                                                    hourly_view=True,
                                                                                    start_date=start_date,
                                                                                    group_view=group_id != None)
    db_conn.close()
    return hourly_conversion_metrics


@router.get("/custom/cities/base/comparision", response_model=Union[LocationDateMetrics, List[ConversionGroupMetric]],
            dependencies=[Depends(deps.get_current_user)])
@cache_metrics() # !! Check this, can be inc 
def get_comparitive_cities_metrics_custom(db_conn: connect = Depends(deps.get_pinot_db),
                                         app: Optional[str] = Query(None), 
                                         metric: List[str] = Query(None),
                                         current_status: Optional[List[str]] = Query(None),
                                         city_wise: bool = Query(None), 
                                         store_wise: bool = Query(None),
                                         is_critical_metrics: bool = Query(None), 
                                         city: str = Query(None),
                                         start_date: Optional[str] = Query(None),):
    """
    Get cities metrics for comparision for `start_date`
    """
    cities_metrics = crud.order_metrics.get_city_wise_comparitive_date_metrics(
                                db_conn, app=app, 
                                metrics=metric, 
                                status=current_status, 
                                city_wise=city_wise, 
                                store_wise=store_wise,
                                is_critical_metrics=is_critical_metrics, 
                                city=city,
                                custom_view = True,
                                start_date = start_date)
    db_conn.close()
    return cities_metrics


@router.get("/custom/hourly/surge", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True) # !! can be inc
def get_hourly_metrics_custom_surge(db_conn: connect = Depends(deps.get_pinot_db),
                             db_postgres: Session = Depends(deps.get_postgres_db),
                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                             city: Optional[List[str]] = Query(None),
                             store: Optional[List[str]] = Query(None),
                             zone: Optional[List[str]] = Query(None),
                             start_date: Optional[str] = Query(None),
                             group_id: Optional[str] = Query(None)):
    """
    Get hourly surge seen metrics for `start_date` as T and T-7 
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city, store, access_mapping)
    hourly_surge_seen_metrics = crud.surge_seen_metrics.get_surge_seen_metrics_custom(db_conn,
                                                           city=city,
                                                           store=store,
                                                           zone=zone,
                                                           is_hourly=True,
                                                           start_date=start_date)
    db_conn.close()
    return hourly_surge_seen_metrics

# ----------------------------------------------------------------------------- keyword metrics

@router.get("/keyword_metrics", response_model=List[ConversionGroupMetric],
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=FIFTEEN_MINUTES)
def get_keyword_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                       db_postgres: Session = Depends(deps.get_postgres_db),
                       access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                       start_date: Optional[str] = Query(None),
                       city: Optional[List[str]] = Query(None),
                       store: Optional[List[str]] = Query(None),
                       zone: Optional[List[str]] = Query(None),
                       group_id: Optional[str] = Query(None)):
    """
    Get top 50 search keywords by volume along with their search-to-ATC conversion metrics
    """
    city, store = crud.group.get_city_store_from_group(db_postgres, group_id, city, store)
    is_sensitive = deps.check_access(city=city, store=store, access_mapping=access_mapping)
    
    keyword_metrics_data = crud.keyword_metrics.get_keyword_metrics(db_conn,
                                                            city=city,
                                                            store=store,
                                                            zone=zone,
                                                            start_date=start_date)

    db_conn.close()
    return keyword_metrics_data
