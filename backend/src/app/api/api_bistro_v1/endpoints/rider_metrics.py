from typing import Optional, List, Dict
from datetime import datetime, timedelta, timezone

from fastapi import APIRouter, Depends, Query, HTTPException
from pinotdb import connect

from app.crud import crud_bistro
from app.api import deps
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import THIRTY_SECONDS, SIX_HOURS_IN_SECONDS
from app.schemas.order_metric import HourlyDateMetrics, OrderMetrics, LocationDateMetrics, StoreDateMetrics
from app.crud.utils import is_yesterday_date

router = APIRouter()


@router.get("/hourly", response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
            )
@dynamic_cache_metrics(expire=THIRTY_SECONDS*2, force_cache_burst_at_eod=True, tenant='Bistro')
def get_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                       access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                       city: Optional[List[str]] = Query(None),
                       store: Optional[List[str]] = Query(None),
                       date_str: Optional[str] = Query(None)):
    """
    Get rider login hr hourly metrics.
    """
    hourly_metrics = crud_bistro.rider_metrics.get_rider_login_metrics(db_conn,
                                                                city=city,
                                                                store=store,
                                                                yesterday_metric=False,
                                                                hourly_metrics=True,
                                                                date_str=date_str)
    db_conn.close()
    return hourly_metrics


@router.get("/yesterday/hourly", response_model=HourlyDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
            )
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_hourly_yesterday_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                       access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                       city: Optional[List[str]] = Query(None),
                       store: Optional[List[str]] = Query(None)):
    """
    Get rider login hr hourly metrics.
    """
    hourly_metrics = crud_bistro.rider_metrics.get_rider_login_metrics(db_conn,
                                                                city=city,
                                                                store=store,
                                                                yesterday_metric=True,
                                                                hourly_metrics=True)
    db_conn.close()
    return hourly_metrics



@router.get("/all", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
            )
@dynamic_cache_metrics(expire=THIRTY_SECONDS*2, force_cache_burst_at_eod=True, tenant='Bistro')
def get_rider_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                      access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                      city: Optional[List[str]] = Query(None),
                      store: Optional[List[str]] = Query(None),
                      date_str: Optional[str] = Query(None)):
    """
    Get rider-metrics for T, T-7, T-14, T-21, T-28
    """

    order_date_wise_metrics = []
    rider_metrics = crud_bistro.rider_metrics.get_rider_login_metrics(db_conn,
                                                               city=city,
                                                               store=store,
                                                               date_str=date_str)
    order_date_wise_metrics.extend(rider_metrics)
    
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/yesterday/all", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
            )
@cache_metrics(expire=SIX_HOURS_IN_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_yesterday_rider_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                      access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                      city: Optional[List[str]] = Query(None),
                      store: Optional[List[str]] = Query(None)):
    """
    Get rider-metrics for T, T-7, T-14, T-21, T-28
    """

    order_date_wise_metrics = []
    rider_metrics = crud_bistro.rider_metrics.get_rider_login_metrics(db_conn,
                                                               city=city,
                                                               store=store,
                                                               yesterday_metric=True)
    order_date_wise_metrics.extend(rider_metrics)
    
    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics 


@router.get("/cities", 
            response_model=LocationDateMetrics, 
            dependencies=[Depends(deps.get_current_user)]
)
@dynamic_cache_metrics(tenant='Bistro')
def get_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                       past_days_diff: Optional[int] = Query(7, gt=0), 
                       date: Optional[str] = Query(None), 
                       hour: Optional[str] = Query(None),):
    """
    Get rider login hr metrics for all cities
    """
    # is_yesterday_date will be removed once we start using date directly rather than yesterday_metric
    cities_metrics = crud_bistro.rider_metrics.get_rider_login_metrics(db_conn, 
                                                                       get_all_cities=True, 
                                                                       yesterday_metric=is_yesterday_date(date), 
                                                                       hour=hour,
                                                                       date_str=date)
    db_conn.close()
    return cities_metrics


@router.get("/cities/stores", 
            response_model=StoreDateMetrics, 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(tenant='Bistro')
def get_cities_stores_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                              access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                              past_days_diff: Optional[int] = Query(7, gt=0), 
                              city: Optional[List[str]] = Query(None), 
                              date: Optional[str] = Query(None),
                              hour: Optional[str] = Query(None),):
    """
    Get rider login hr metrics for all stores
    """
    # is_yesterday_date will be removed once we start using date directly rather than yesterday_metric
    cities_metrics = crud_bistro.rider_metrics.get_rider_login_metrics(db_conn, 
                                                                       get_all_stores_for_city=True, 
                                                                       city=city, 
                                                                       yesterday_metric=is_yesterday_date(date), 
                                                                       hour=hour,
                                                                       date_str=date)
    db_conn.close()
    return cities_metrics