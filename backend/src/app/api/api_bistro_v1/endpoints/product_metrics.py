import logging
from typing import Optional, List, Dict

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app.crud import crud_bistro
from app.api import deps, utils
from app.cache.decorator import cache_metrics
from app.cache import THREE_MINUTES, FIFTEEN_MINUTES, FIVE_MINUTES
from app.schemas.product_metric import ProductMetrics, CategoryMetrics, FieldwiseList, FilteredProductFieldwise, ProductDateWiseAllCityMetrics, ProductHourWiseMetrics, ProductComplaints, ProductMetricTypes, ItemComplaints, ItemRatings
from app.schemas.order_metric import ConversionGroupMetric, OrderMetrics, OrderDatewiseMetric

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all", response_model=CategoryMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_product_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        metric: List[str] = Query(None), 
                        l0_category: Optional[List[str]] = Query(None),
                        l1_category: Optional[List[str]] = Query(None),
                        l2_category: Optional[List[str]] = Query(None),
                        ptype: Optional[List[str]] = Query(None),
                        pname: Optional[List[str]] = Query(None),
                        brand: Optional[List[str]] = Query(None),
                        city: Optional[List[str]] = Query(None), 
                        store: Optional[List[str]] = Query(None),
                        is_daywise: Optional[bool] = Query(None),
                        yesterday_metric: Optional[bool] = Query(None),
                        current_hour: Optional[bool] = Query(None),
                        pid: Optional[List[str]] = Query(None)):
    """
    If is_daywise = True 
        If not yesterday_metric 
            current_hour --> T, T-1, T-2, T-3, T-4, T-5, T-6 (till the current_hour data only)
            Else --> T, T-1, T-2, T-3, T-4, T-5, T-6 but (T-1, ... complete day data)
        Else --> T-1, T-2, T-3, T-4, T-5, T-6, T-7
    Else 
        If not yesterday_metric --> Get metrics for T, T-7, T-14, T-21, T-28
        Else --> Get metrics for T-1, T-8, T-15, T-22, T-29
    """
    product_date_wise_metrics = crud_bistro.product_metrics.get_all_metrics(db_conn, metrics=metric,
                                                                     l0_category=l0_category, l1_category=l1_category,
                                                                     l2_category=l2_category, ptype=ptype,
                                                                     pname=pname, brand=brand,
                                                                     city=city, store=store,
                                                                     is_daywise=is_daywise,
                                                                     yesterday_metric=yesterday_metric,
                                                                     current_hour=current_hour,
                                                                     pid=pid)

    db_conn.close()
    return CategoryMetrics(metrics=product_date_wise_metrics)


@router.get("/complaints", 
            response_model=ItemComplaints,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_complaints_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        l0_category: Optional[List[str]] = Query(None),
                        l1_category: Optional[List[str]] = Query(None),
                        l2_category: Optional[List[str]] = Query(None),
                        ptype: Optional[List[str]] = Query(None),
                        pname: Optional[List[str]] = Query(None),
                        brand: Optional[List[str]] = Query(None),
                        city: Optional[List[str]] = Query(None), 
                        store: Optional[List[str]] = Query(None),
                        is_daywise: Optional[bool] = Query(None),
                        yesterday_metric: Optional[bool] = Query(None),
                        current_hour: Optional[bool] = Query(None),
                        pid: Optional[List[str]] = Query(None)):
    complaints_metrics = crud_bistro.product_metrics.complaints_metrics(db_conn, 
                                                                     l0_category=l0_category, l1_category=l1_category,
                                                                     l2_category=l2_category, ptype=ptype,
                                                                     pname=pname, brand=brand,
                                                                     city=city, store=store,
                                                                     is_daywise=is_daywise,
                                                                     yesterday_metric=yesterday_metric,
                                                                     current_hour=current_hour,
                                                                     pid=pid, 
                                                                     category_wise=True)
    db_conn.close()
    return ItemComplaints(complaints=complaints_metrics)


@router.get("/list/primary", response_model=FieldwiseList,
            dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_product_primary_details(db_conn: connect = Depends(deps.get_pinot_db),
                            search_filter: Optional[List[str]] = Query(None)):
    """
    Get complete list of all fields in search_filter
    """
    result = crud_bistro.product_metrics.get_all_field_list(db_conn, search_filter=search_filter)
    db_conn.close()
    return FieldwiseList(data=result)


@router.get("/filter", response_model=FilteredProductFieldwise,
            dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=FIVE_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_filtered_product_fieldwise(db_conn: connect = Depends(deps.get_pinot_db),
                            l0_category: Optional[List[str]] = Query(None),
                            l1_category: Optional[List[str]] = Query(None),
                            l2_category: Optional[List[str]] = Query(None),
                            ptype: Optional[List[str]] = Query(None),
                            pname: Optional[List[str]] = Query(None),
                            brand: Optional[List[str]] = Query(None),
                            search_filter: Optional[str] = Query(None),
                            pid: Optional[List[str]] = Query(None)):
    """
    Get search_filter field value based on filters
    """
    filtered_product_fieldwise = crud_bistro.product_metrics.get_filtered_product_fieldwise(db_conn, l0_category,
                                                                                     l1_category, l2_category,
                                                                                     ptype, pname,
                                                                                     brand, search_filter,
                                                                                     pid)
    db_conn.close()
    return filtered_product_fieldwise


@router.get("/ipc-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_ipc_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                    city: Optional[List[str]] = Query(None),
                    store: Optional[List[str]] = Query(None),
                    is_daywise: Optional[bool] = Query(None),
                    yesterday_metric: Optional[bool] = Query(None),
                    current_hour: Optional[bool] = Query(None),
                    date_str: Optional[str] = Query(None)):
    """
    Get IPC metrics for products across all items without grouping by item
    """
    order_metrics_list = crud_bistro.product_metrics.get_metrics_without_item_grouping(
        db_conn,
        metrics=["ipc"],
        l0_category=['Bistro'],
        city=city,
        store=store,
        is_daywise=is_daywise,
        yesterday_metric=yesterday_metric,
        current_hour=current_hour,
        date_str=date_str
    )
    
    db_conn.close()
    return OrderMetrics(metrics=order_metrics_list)
    