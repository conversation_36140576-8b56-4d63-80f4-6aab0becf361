import logging
from typing import Optional, List, Dict, Union
import datetime

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app.crud import crud_bistro
from app.api import deps
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS, THIRTY_SECONDS, SIX_HOURS_IN_SECONDS, ELEVEN_MINUTES, TEN_MINUTES
from app.schemas.order_metric import OrderMetrics, HourlyDateMetrics, LocationDateMetrics, StoreDateMetrics, AthDatewiseMetric
from app.schemas.product_metric import ComplaintsTypeWise

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/all/base-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_order_metrics(
    db_conn: connect = Depends(deps.get_pinot_db),
    metric: List[str] = Query(None), 
    city: Optional[List[str]] = Query(None),
    store: Optional[List[str]] = Query(None),
    date_str: Optional[str] = Query(None)
):
    """
    Get base metrics for specified date and its previous weeks
    """
    order_date_wise_metrics = crud_bistro.order_metrics.get_all_metrics(
        db_conn, 
        metrics=metric,
        city=city, 
        store=store,
        date_str=date_str
    )

    if "order_count" in metric and not store:
        active_store_count_metrics = crud_bistro.store_metrics.get_active_stores_count_metric(db_conn,
                                                                                       city=city,
                                                                                       is_yesterday=False,
                                                                                       date_str=date_str)
        order_date_wise_metrics.extend(active_store_count_metrics)

    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/all/dau-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user)])
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_dau_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),
                            date_str: Optional[str] = Query(None)):
    """
    Get HAU metrics for T, T-7, T-14, T-21, T-28
    """

    order_date_wise_metrics = []
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_manual_based_cart_block_conversion_date_wise_metrics = crud_bistro.dau_metrics.get_dau_metrics(db_conn,
                                                                                city=city,
                                                                                store=store,
                                                                                date_str=date_str)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_manual_based_cart_block_conversion_date_wise_metrics)

    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


# @router.get("/all/ntu-metrics", response_model=OrderMetrics,
#             dependencies=[Depends(deps.get_current_user)])
# @cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
# def get_ntu_metrics(db_conn: connect = Depends(deps.get_pinot_db),
#                             city: Optional[List[str]] = Query(None),
#                             store: Optional[List[str]] = Query(None),
#                             date_str: Optional[str] = Query(None)):
#     """
#     Get NTU metrics for T, T-7, T-14, T-21, T-28
#     """

#     order_date_wise_metrics = []
#     ntu_metrics = crud_bistro.order_metrics.get_ntu_metrics(db_conn,
#                                                             city=city,
#                                                             store=store,
#                                                             date_str=date_str)
#     order_date_wise_metrics.extend(ntu_metrics)

#     order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
#     db_conn.close()
#     return order_metrics


@router.get("/yesterday/all/base-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user)])
@dynamic_cache_metrics(expire=SIX_HOURS_IN_SECONDS, 
               force_cache_burst_at_eod=True, 
               force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES],
               tenant='Bistro')
def get_order_metrics_for_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                                          metric: List[str] = Query(None),
                                          city: Optional[List[str]] = Query(None),
                                          store: Optional[List[str]] = Query(None)):
    """
    Get all metrics for T-1, T-8, T-15, T-22, T-29
    """

    order_date_wise_metrics = crud_bistro.order_metrics.get_all_metrics(db_conn, metrics=metric,
                                                                 city=city, store=store, yesterday_metric=True)
    
    if "order_count" in metric and not store:
        active_store_count_metrics = crud_bistro.store_metrics.get_active_stores_count_metric(db_conn,
                                                                                       city=city,
                                                                                       is_yesterday=True)
        order_date_wise_metrics.extend(active_store_count_metrics)

    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


@router.get("/yesterday/all/dau-metrics", response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user)])
@dynamic_cache_metrics(expire=SIX_HOURS_IN_SECONDS, 
               force_cache_burst_at_eod=True, 
               force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES],
                tenant='Bistro')
def get_dau_metrics_for_yesterday(db_conn: connect = Depends(deps.get_pinot_db),
                            city: Optional[List[str]] = Query(None),
                            store: Optional[List[str]] = Query(None),):
    """
    Get HAU metrics for T-1, T-8, T-15, T-22, T-29
    """
    order_date_wise_metrics = []
    dau_date_wise_metrics, dau_conversion_date_wise_metrics, demand_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics, \
        express_serviceability_manual_based_cart_block_conversion_date_wise_metrics = crud_bistro.dau_metrics.get_dau_metrics(db_conn,
                                                                                city=city,
                                                                                store=store,
                                                                                yesterday_metric=True)
    order_date_wise_metrics.extend(dau_date_wise_metrics)
    order_date_wise_metrics.extend(dau_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(demand_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_ooh_based_cart_block_conversion_date_wise_metrics)
    order_date_wise_metrics.extend(express_serviceability_manual_based_cart_block_conversion_date_wise_metrics)

    order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
    db_conn.close()
    return order_metrics


# @router.get("/yesterday/all/ntu-metrics", response_model=OrderMetrics,
#             dependencies=[Depends(deps.get_current_user)])
# @cache_metrics(
#     expire=SIX_HOURS_IN_SECONDS, 
#     force_cache_burst_at_eod=True, 
#     force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES])
# def get_yesterday_ntu_metrics(db_conn: connect = Depends(deps.get_pinot_db),
#                             city: Optional[List[str]] = Query(None),
#                             store: Optional[List[str]] = Query(None),):
#     """
#     Get NTU metrics for T-1, T-8, T-15, T-22, T-29
#     """

#     order_date_wise_metrics = []
#     ntu_metrics = crud_bistro.order_metrics.get_ntu_metrics(db_conn,
#                                                             city=city,
#                                                             store=store,
#                                                             yesterday_metric=True)
#     order_date_wise_metrics.extend(ntu_metrics)

#     order_metrics = OrderMetrics(metrics=order_date_wise_metrics)
#     db_conn.close()
#     return order_metrics


@router.get("/hourly", response_model=HourlyDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@dynamic_cache_metrics(tenant='Bistro')
def get_hourly_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                             access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                             metric: List[str] = Query(None), city: Optional[List[str]] = Query(None),
                             store: Optional[List[str]] = Query(None), app: Optional[str] = Query(None),
                             yesterday_metric: bool = False, status: Optional[List[str]] = Query(None),
                             date_str: Optional[str] = Query(None)):
    """
    Get hourly metrics
    """
    is_sensitive = deps.check_access(city, store, access_mapping, tenant='Bistro')
    hourly_metrics = crud_bistro.order_metrics.get_hourly_metrics(db_conn,
                                                           metrics=metric,
                                                           cities=city,
                                                           app=app,
                                                           stores=store,
                                                           yesterday_metric=yesterday_metric,
                                                           status=status,
                                                           date_str=date_str)
    db_conn.close()
    return hourly_metrics


@router.get("/complaints", 
            response_model=ComplaintsTypeWise,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_complaints_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        city: Optional[List[str]] = Query(None), 
                        store: Optional[List[str]] = Query(None),
                        yesterday_metric: Optional[bool] = Query(None),
                        date_str: Optional[str] = Query(None)):
    complaints_metrics = crud_bistro.product_metrics.complaints_metrics(db_conn, 
                                                                     city=city, store=store,
                                                                     yesterday_metric=yesterday_metric,
                                                                     category_wise=False,
                                                                     date_str=date_str)
    db_conn.close()
    return ComplaintsTypeWise(complaints=complaints_metrics)

# ----------------------------------------------------------------------------- City/Stores Page

@router.get("/cities",
            response_model=LocationDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_cities_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                             past_days_diff: Optional[int] = Query(7, gt=0), app: str = Query(None),
                             date: Optional[str] = Query(None), hour: Optional[str] = Query(None)):
    """
    Get all metrics for cities
    """
    if not date:
        date = datetime.datetime.now(datetime.timezone(datetime.timedelta(hours=5, minutes=30))).strftime('%Y-%m-%d')
    cities_metrics = crud_bistro.order_metrics.get_city_wise_current_date_metrics(db_conn, past_days_diff=past_days_diff,
                                                                                  date_str=date, hour=hour)
    db_conn.close()
    return cities_metrics


@router.get("/cities/stores", 
            response_model=StoreDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_cities_stores_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                                    past_days_diff: Optional[int] = Query(7, gt=0), app: str = Query(None),
                                    city: Optional[List[str]] = Query(None), date: Optional[str] = Query(None), hour: Optional[str] = Query(None)):
    """
    Get all metrics for stores
    """
    stores_metrics = crud_bistro.order_metrics.get_city_wise_current_date_metrics(db_conn, past_days_diff=past_days_diff,
                                                                           city=city, date_str=date, hour=hour)
    db_conn.close()
    return stores_metrics

@router.get("/ath", response_model=AthDatewiseMetric,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_ath_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                    metric: Optional[List[str]] = Query(None),
                    city: Optional[List[str]] = Query(None),
                    store: Optional[List[str]] = Query(None)):
    """
    Get Ath metrics for Bistro
    """
    if not store:
        ath_metrics = crud_bistro.order_metrics.get_ath_metrics(db_conn, city=['overall'] if city is None else city)
    else:
        ath_metrics = []
    db_conn.close()
    return AthDatewiseMetric(ath_data=ath_metrics)
