import logging
from typing import Optional, List, Dict, Union
import datetime

from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app.crud import crud_bistro
from app.api import deps 
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import  THIRTY_SECONDS, TEN_MINUTES
from app.schemas.order_metric import OrderMetrics, LocationDateMetrics, StoreDateMetrics, StationRatingsResponse
from app.schemas.product_metric import CategoryMetrics
from app.cache import THREE_MINUTES


router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/order", 
            response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_order_ratings_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        city: Optional[List[str]] = Query(None), 
                        store: Optional[List[str]] = Query(None),
                        yesterday_metric: Optional[bool] = Query(None),
                        date_str: Optional[str] = Query(None)):

    rating_metrics = crud_bistro.rating_metrics.rating_metrics(db_conn, 
                                                                yesterday_metric=yesterday_metric,
                                                                city=city, store=store,
                                                                category_wise=False,
                                                                date_str=date_str,
                                                                )
    db_conn.close()
    return OrderMetrics(metrics=rating_metrics)


@router.get("/product", 
            response_model=CategoryMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=THREE_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_product_ratings_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        l0_category: Optional[List[str]] = Query(None),
                        l1_category: Optional[List[str]] = Query(None),
                        l2_category: Optional[List[str]] = Query(None),
                        ptype: Optional[List[str]] = Query(None),
                        pname: Optional[List[str]] = Query(None),
                        brand: Optional[List[str]] = Query(None),
                        is_daywise: Optional[bool] = Query(None),
                        yesterday_metric: Optional[bool] = Query(None),
                        current_hour: Optional[bool] = Query(None),
                        pid: Optional[List[str]] = Query(None),
                        city: Optional[List[str]] = Query(None), 
                        store: Optional[List[str]] = Query(None),
                        ):

    rating_metrics = crud_bistro.rating_metrics.rating_metrics(db_conn, 
                                                                l0_category=l0_category, l1_category=l1_category,
                                                                l2_category=l2_category, ptype=ptype,
                                                                pname=pname, brand=brand,
                                                                is_daywise=is_daywise,
                                                                yesterday_metric=yesterday_metric,
                                                                current_hour=current_hour,
                                                                pid=pid,
                                                                city=city, store=store,
                                                                category_wise=True,
                                                                )
    db_conn.close()
    return CategoryMetrics(metrics=rating_metrics)


@router.get("/cities", 
            response_model=LocationDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_city_ratings_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        date: Optional[str] = Query(None), 
                        past_days_diff: Optional[int] = Query(7, gt=0), 
                        hour: Optional[str] = Query(None) , ):

    city_rating_metrics = crud_bistro.rating_metrics.get_city_wise_ratings_metrics(db_conn, 
                                                                date_str=date, 
                                                                hour=hour,
                                                                past_days_diff=past_days_diff)
    db_conn.close()
    return city_rating_metrics


@router.get("/cities/stores", 
            response_model=StoreDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_store_ratings_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        city: Optional[List[str]] = Query(None), 
                        date: Optional[str] = Query(None), 
                        past_days_diff: Optional[int] = Query(7, gt=0), 
                        hour: Optional[str] = Query(None) , ):

    store_rating_metrics = crud_bistro.rating_metrics.get_city_wise_ratings_metrics(db_conn, 
                                                                city=city, 
                                                                date_str=date, 
                                                                hour=hour,
                                                                past_days_diff=past_days_diff)
    db_conn.close()
    return store_rating_metrics


@router.get("/stations", 
            response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=TEN_MINUTES, force_cache_burst_at_eod=True, tenant='Bistro')
def get_station_ratings_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                        access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                        store: Optional[List[str]] = Query(None), 
                        city: Optional[List[str]] = Query(None), 
                        station: Optional[str] = Query(None),
                        date_str: Optional[str] = Query(None),):

    station_rating_metrics = crud_bistro.rating_metrics.get_station_ratings_metrics(db_conn, 
                                                                store=store, 
                                                                city=city,
                                                                station=station,
                                                                date_str=date_str,)

    rating_metric=OrderMetrics(metrics=station_rating_metrics)
    db_conn.close()
    return rating_metric
