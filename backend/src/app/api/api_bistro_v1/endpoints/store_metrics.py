from fastapi import APIRouter, Depends
from pinotdb import connect


from app.crud import crud_bistro
from app.api import deps
from app.schemas.order_metric import  StoresCityList
from app.cache.decorator import cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS

router = APIRouter()


@router.get("/storecitymapping", response_model=StoresCityList, dependencies=[Depends(deps.get_current_user)])
@cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_stores_city_filters(db_conn: connect = Depends(deps.get_pinot_db)):
    """
    Get bistro stores list in each city
    """
    result = crud_bistro.store_metrics.get_stores_city_mapping(db_conn)
    db_conn.close()
    return result