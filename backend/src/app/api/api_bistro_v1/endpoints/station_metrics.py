from typing import Optional, List, Dict
from fastapi import APIRouter, Depends, Query
from pinotdb import connect

from app.crud import crud_bistro
from app.api import deps
from app.schemas.order_metric import StationMapping, OrderMetrics, LocationDateMetrics
from app.schemas.product_metric import ComplaintsTypeWise, ComplaintsDateMetricTypeWise
from app.cache.decorator import cache_metrics, dynamic_cache_metrics
from app.cache import ONE_HOUR_IN_SECONDS, THIRTY_SECONDS

router = APIRouter()


@router.get("/stationmapping", 
            response_model=StationMapping, 
            dependencies=[Depends(deps.get_current_user)]
)
@dynamic_cache_metrics(expire=ONE_HOUR_IN_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_station_mapping(db_conn: connect = Depends(deps.get_pinot_db)):
    """
    Get station mapping
    """
    metrics = crud_bistro.station_metrics.get_station_mapping(db_conn)
    station_mapping = StationMapping(metrics=metrics)
    db_conn.close()
    return station_mapping


@router.get("/base-metrics", 
            response_model=OrderMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_station_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                               access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                               store: Optional[List[str]] = Query(None),
                               city: Optional[List[str]] = Query(None),
                               station: Optional[str] = Query(None),
                               date_str: Optional[str] = Query(None),
                               metric: Optional[List[str]] = Query(None)):
    """
    Get wait and prep times by station for a specific store and time period. (Average wait and prep time)
    """
    metrics = crud_bistro.station_metrics.get_station_metrics(db_conn, 
                                                              city=city, 
                                                              store=store, 
                                                              station=station, 
                                                              date_str=date_str,
                                                              metrics=metric)
    db_conn.close()
    return OrderMetrics(metrics=metrics)


@router.get("/complaints", 
            response_model=ComplaintsTypeWise,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_station_complaints_metrics(db_conn: connect = Depends(deps.get_pinot_db),
                                   access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                   city: Optional[List[str]] = Query(None),
                                   store: Optional[List[str]] = Query(None),
                                   station: Optional[List[str]] = Query(None),
                                   date_str: Optional[str] = Query(None)):
    """
    Get complaints count by station for a specific store and time period
    """
    complaints_metrics = crud_bistro.station_metrics.get_station_complaints(db_conn, 
                                                                            city=city, 
                                                                            store=store,
                                                                            station=station,
                                                                            date_str=date_str)
    db_conn.close()
    return ComplaintsTypeWise(complaints=complaints_metrics)


@router.get("/group-metrics",
            response_model=LocationDateMetrics,
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@dynamic_cache_metrics(expire=THIRTY_SECONDS, force_cache_burst_at_eod=True, tenant='Bistro')
def get_station_metrics_by_station(db_conn: connect = Depends(deps.get_pinot_db),
                                  access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping),
                                  city: Optional[List[str]] = Query(None),
                                  store: Optional[List[str]] = Query(None),
                                  station: Optional[List[str]] = Query(None),
                                  date_str: Optional[str] = Query(None),
                                  metric: Optional[List[str]] = Query(None),
                                  past_days_diff: Optional[int] = Query(7, gt=0)):
    """
    Get metrics grouped by station for a specific store and time period
    """
    station_metrics = crud_bistro.station_metrics.get_station_metrics_by_station(db_conn,
                                                                                 city=city,
                                                                                 store=store,
                                                                                 station=station,
                                                                                 date_str=date_str,
                                                                                 metrics=metric,
                                                                                 past_days_diff=past_days_diff)
    db_conn.close()
    return station_metrics