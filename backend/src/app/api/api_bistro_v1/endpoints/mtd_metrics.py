from typing import List, Optional, Dict

from fastapi import APIRouter, Depends, Query
import httpx
from app.crud import crud_bistro
from app.api import deps,utils
from app.core.config import settings

from app.schemas.mtd_metric import MTDMetric
from app.schemas.order_metric import LocationDateMetrics
from app.cache.decorator import cache_metrics
from app.cache import THIRTY_MINUTES, ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS, ELEVEN_MINUTES

from pinotdb import connect

router = APIRouter()

@router.get("/all", 
            response_model=List[MTDMetric], 
            dependencies=[Depends(deps.get_current_user), Depends(deps.get_access_mapping)]
)
@cache_metrics(expire=THIRTY_MINUTES, 
               force_cache_burst_at_eod=True, 
               force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES],
               tenant='Bistro')
def get_mtd_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                    access_mapping: Dict[str, Dict[int, bool]] = Depends(deps.get_access_mapping), 
                    metric: List[str] = Query(None), 
                    city: List[str] = Query(None)):
    """
    Get current and previous MTD metrics for Specific city
    """
    mtd_metrics = crud_bistro.wtd_mtd_metrics.get_all_metrics(conn=db_conn, 
                                                   metrics=metric, 
                                                   city=city,
                                                   metric_type='mtd')
    return mtd_metrics


@router.get("/trends", 
            response_model=LocationDateMetrics, 
            dependencies=[Depends(deps.get_current_user)]
)
@cache_metrics(expire=THIRTY_MINUTES, 
               force_cache_burst_at_eod=True, 
               force_cache_burst_seconds_after_midnight_list=[ELEVEN_MINUTES, ONE_HOUR_IN_SECONDS + ELEVEN_MINUTES],
               tenant='Bistro')
def get_all_metrics(db_conn: connect = Depends(deps.get_pinot_db), 
                    metric: List[str] = Query(None), 
                    city: List[str] = Query(None)):
    """
    Get MTD order count metrics for all cities
    """
    cities_metrics = crud_bistro.wtd_mtd_metrics.get_city_zone_metrics(conn=db_conn, 
                                                      metrics=metric, 
                                                      city=city,
                                                      metric_type='mtd',
                                                      isTrends=True)
    return cities_metrics
