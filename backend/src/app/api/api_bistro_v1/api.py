from fastapi import APIRouter

from app.api.api_bistro_v1.endpoints import order_metrics, hau_metrics, product_metrics, store_metrics, rider_metrics, wtd_metrics, mtd_metrics, rating_metrics, station_metrics
api_router_bistro = APIRouter()
api_router_bistro.include_router(order_metrics.router, prefix="/order-metrics", tags=["order_metrics"])
api_router_bistro.include_router(hau_metrics.router, prefix="/hau", tags=["hau_metrics"])
api_router_bistro.include_router(product_metrics.router, prefix="/product-metrics", tags=["product-metrics"])
api_router_bistro.include_router(store_metrics.router, prefix="/stores", tags=["store_metrics"])
api_router_bistro.include_router(rider_metrics.router, prefix="/rider-metrics", tags=["rider_metrics"])
api_router_bistro.include_router(wtd_metrics.router, prefix="/wtd_metrics", tags=["wtd_metrics"])
api_router_bistro.include_router(mtd_metrics.router, prefix="/mtd_metrics", tags=["mtd_metrics"])
api_router_bistro.include_router(rating_metrics.router, prefix="/rating-metrics", tags=["rating_metrics"])
api_router_bistro.include_router(station_metrics.router, prefix="/station-metrics", tags=["station_metrics"])
