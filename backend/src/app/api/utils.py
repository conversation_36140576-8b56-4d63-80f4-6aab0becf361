import logging
from typing import Dict, List
from app.schemas.user_access_config import UserAccessConfig
from app.core.config import settings
from app.core.metric_config import SENSITIVE_METRICS
import math
from app.schemas.order_metric import LocationDateMetrics, StoreDateMetrics, StoresCityMapping
from app.schemas.group import GroupWithMappings
from app.core.metric_config import METRIC_NAME_MAPPING, WEIGHTED_AVERAGE_METRICS, SKIP_LONGTAIL_MERCHANT_METRICS

logger = logging.getLogger(__name__)

def create_access_mapping(tenants_access_mapping: UserAccessConfig) -> Dict[ str, Dict[str, Dict[int, bool]]]:
    user_tenant_access_mapping = {}
    for tenant_name, tenant_access_mapping in tenants_access_mapping.items():
        user_access_mapping: Dict[str, Dict[int, bool]] = {}
        
        for mapping in tenant_access_mapping['userCityStoreAccessData']:
            city_name = mapping.city_name
            store_id = str(mapping.store_id)
            is_sensitive = mapping.is_sensitive

            if city_name not in user_access_mapping:
                user_access_mapping[city_name] = {}
        
            user_access_mapping[city_name][store_id] = is_sensitive
        user_access_mapping['allowed_pages'] = tenant_access_mapping['allowedPages']
        user_access_mapping['is_global_group_allowed'] = tenant_access_mapping['isGlobalGroupAllowed']
        user_tenant_access_mapping[tenant_name] = user_access_mapping
    return user_tenant_access_mapping

def remove_sensitive_metrics(is_sensitive: bool, metrics_list: List) -> List:
     if is_sensitive == False:
        # Convert OrderDatewiseMetric instances to dictionaries for easy removal
        metrics_dicts = [metric.dict() for metric in metrics_list]
        non_sensitive_metrics = []
        # Remove the metrics based on the "name" attribute
        for metric in metrics_dicts:
            if metric["name"] not in SENSITIVE_METRICS:
                non_sensitive_metrics.append(metric)
        return non_sensitive_metrics
     else:
          return metrics_list


def combine_cities_into_groups(cities_metrics: LocationDateMetrics, 
                               groups: List[GroupWithMappings]):
    cities_in_groups = set()
    transformed_metrics = []

    for metric in cities_metrics.metrics: # multiple dates
        new_data = []
        group_data = []

        for group in groups:
            if group.type == "CITY":
                group_cities = [mapping.city for mapping in group.mappings]
                cities_in_groups.update(group_cities)
                
                group_metrics = {
                    "type": "group",
                    "name": group.name,
                    "data": []
                }
                
                city_metrics = [city_data for city_data in metric.data 
                            if city_data.type == "city" and city_data.name in group_cities]
                
                if city_metrics:
                    # Get order counts for all cities in the group
                    order_counts = {}
                    for city in city_metrics:
                        order_metric = next((m for m in city.data if m.name == METRIC_NAME_MAPPING["order_count"]), None)
                        if order_metric:
                            order_counts[city.name] = order_metric.metric

                    for metric_type in city_metrics[0].data:
                        aggregated_metric = {
                            "name": metric_type.name,
                            "type": metric_type.type
                        }
                        
                        if metric_type.name in WEIGHTED_AVERAGE_METRICS:
                            # Calculate weighted average
                            total_weight = sum(order_counts.values())
                            if total_weight > 0:
                                weighted_sum = 0
                                for city in city_metrics:
                                    city_order_count = order_counts.get(city.name, 0)
                                    metric_value = next((m.metric for m in city.data if m.name == metric_type.name), 0)
                                    weighted_sum += metric_value * city_order_count
                                aggregated_metric["metric"] = float(weighted_sum / total_weight)
                            else:
                                # Fallback to simple average if no order counts
                                aggregated_metric["metric"] = float(sum(
                                    next((m.metric for m in city.data if m.name == metric_type.name), 0)for city in city_metrics
                                ) / len(city_metrics))
                        else:
                            aggregated_metric["metric"] = sum(
                                next((m.metric for m in city.data if m.name == metric_type.name), 0)
                                for city in city_metrics
                            )
                        aggregated_metric["metric"] = float(round(aggregated_metric["metric"], 2))
                        group_metrics["data"].append(aggregated_metric)
                    
                    group_data.append(group_metrics)

        for city_data in metric.data:
            if city_data.type == "city" and city_data.name not in cities_in_groups:
                new_data.append(city_data)

        metric.data = new_data + group_data
        transformed_metrics.append(metric)

    cities_metrics.metrics = transformed_metrics

    return cities_metrics


def combine_stores_into_groups(stores_metrics: StoreDateMetrics, 
                               groups: List[GroupWithMappings]):
    transformed_metrics = []
    for metric in stores_metrics.metrics:
        grouped_store_ids = set()
        
        combined_data = []
        for group in groups:
            if group.type == "STORE":
                group_stores = [store for mapping in group.mappings for store in mapping.stores]
                grouped_store_ids.update(group_stores)
                
                group_data = {
                    "frontend_merchant_name": group.name,
                    "frontend_merchant_id": f"group_{group.id}",
                    "data": []
                }
                
                store_metrics = [store_data for store_data in metric.data 
                                if store_data.frontend_merchant_id in group_stores]
                
                if store_metrics:
                    # Get order counts for all stores in the group
                    order_counts = {}
                    for store in store_metrics:
                        order_metric = next((m for m in store.data if m.name == METRIC_NAME_MAPPING["order_count"]), None)
                        if order_metric:
                            order_counts[store.frontend_merchant_id] = order_metric.metric

                    for metric_type in store_metrics[0].data:
                        aggregated_metric = {
                            "name": metric_type.name,
                            "type": metric_type.type
                        }
                        
                        if metric_type.name in WEIGHTED_AVERAGE_METRICS:
                            # Calculate weighted average
                            total_weight = sum(order_counts.values())
                            if total_weight > 0:
                                weighted_sum = 0
                                for store in store_metrics:
                                    store_order_count = order_counts.get(store.frontend_merchant_id, 0)
                                    metric_value = next((m.metric for m in store.data if m.name == metric_type.name), 0)
                                    weighted_sum += metric_value * store_order_count
                                aggregated_metric["metric"] = float(weighted_sum / total_weight)
                            else:
                                # Fallback to simple average if no order counts
                                aggregated_metric["metric"] = float(sum(
                                    next((m.metric for m in store.data if m.name == metric_type.name), 0)
                                    for store in store_metrics
                                ) / len(store_metrics))
                        else:
                            aggregated_metric["metric"] = sum(
                                next((m.metric for m in store.data if m.name == metric_type.name), 0)
                                for store in store_metrics
                            )
                        aggregated_metric["metric"] = float(round(aggregated_metric["metric"], 2))
                        group_data["data"].append(aggregated_metric)
                    
                    combined_data.append(group_data)
        
        # Add individual stores that are not part of any group
        non_grouped_stores = [
            store_data for store_data in metric.data 
            if store_data.frontend_merchant_id not in grouped_store_ids
        ]
        combined_data.extend(non_grouped_stores)
        
        transformed_metric = metric.copy()
        transformed_metric.data = combined_data
        transformed_metrics.append(transformed_metric)
    stores_metrics.metrics = transformed_metrics
    return stores_metrics


def combine_stores_into_zones(stores_metrics: StoreDateMetrics, 
                              stores_city_mapping):
    from app.schemas.order_metric import StoreMetric, StoreDateMetric
    from app.schemas.metric import Metric
    
    transformed_metrics = []
    
    for metric in stores_metrics.metrics:
        # Create zone mapping from stores_city_mapping
        zone_store_mapping = {}
        store_longtail_mapping = {}
        
        # Handle both StoresCityList object and direct filters list
        city_mappings = stores_city_mapping.filters if hasattr(stores_city_mapping, 'filters') else stores_city_mapping
        
        for city_mapping in city_mappings:
            zone = city_mapping.zone
            if zone and zone not in zone_store_mapping:
                zone_store_mapping[zone] = []
            
            if zone:
                for store in city_mapping.data:
                    zone_store_mapping[zone].append(store.frontend_merchant_id)
                    store_longtail_mapping[store.frontend_merchant_id] = store.is_longtail_outlet
        
        grouped_store_ids = set()
        combined_data = []
        
        # Process each zone
        for zone_name, zone_store_ids in zone_store_mapping.items():
            grouped_store_ids.update(zone_store_ids)
            
            store_metrics = [store_data for store_data in metric.data 
                            if store_data.frontend_merchant_id in zone_store_ids]
            
            if store_metrics:
                # Get order counts for all stores in the zone
                order_counts = {}
                for store in store_metrics:
                    order_metric = next((m for m in store.data if m.name == METRIC_NAME_MAPPING["order_count"]), None)
                    if order_metric:
                        order_counts[store.frontend_merchant_id] = order_metric.metric

                zone_metrics_data = []
                
                for metric_type in store_metrics[0].data:
                    # Filter out longtail stores for specific metrics
                    filtered_store_metrics = store_metrics
                    if metric_type.name in SKIP_LONGTAIL_MERCHANT_METRICS:
                        filtered_store_metrics = [
                            store for store in store_metrics 
                            if not store_longtail_mapping.get(store.frontend_merchant_id, False)
                        ]
                    
                    if not filtered_store_metrics:
                        # If no stores left after filtering, skip this metric
                        continue
                    
                    aggregated_value = 0
                    
                    if metric_type.name in WEIGHTED_AVERAGE_METRICS:
                        # Calculate weighted average using filtered stores
                        filtered_order_counts = {
                            store_id: count for store_id, count in order_counts.items()
                            if store_id in [s.frontend_merchant_id for s in filtered_store_metrics]
                        }
                        total_weight = sum(filtered_order_counts.values())
                        
                        if total_weight > 0:
                            weighted_sum = 0
                            for store in filtered_store_metrics:
                                store_order_count = filtered_order_counts.get(store.frontend_merchant_id, 0)
                                metric_value = next((m.metric for m in store.data if m.name == metric_type.name), 0)
                                weighted_sum += metric_value * store_order_count
                            aggregated_value = weighted_sum / total_weight
                        else:
                            # Fallback to simple average if no order counts
                            aggregated_value = sum(
                                next((m.metric for m in store.data if m.name == metric_type.name), 0)
                                for store in filtered_store_metrics
                            ) / len(filtered_store_metrics)
                    else:
                        aggregated_value = sum(
                            next((m.metric for m in store.data if m.name == metric_type.name), 0)
                            for store in filtered_store_metrics
                        )
                    
                    # Create proper Metric object
                    zone_metric = Metric(
                        name=metric_type.name,
                        type=metric_type.type,
                        metric=round(aggregated_value, 2)
                    )
                    zone_metrics_data.append(zone_metric)
                
                # Create proper StoreMetric object for the zone
                zone_store_metric = StoreMetric(
                    frontend_merchant_name=zone_name,
                    frontend_merchant_id=zone_name,
                    data=zone_metrics_data
                )
                combined_data.append(zone_store_metric)
        
        # Add individual stores that are not part of any zone
        non_grouped_stores = [
            store_data for store_data in metric.data 
            if store_data.frontend_merchant_id not in grouped_store_ids
        ]
        # Removing the stores that are not part of any zone
        # combined_data.extend(non_grouped_stores)
        
        # Create proper StoreDateMetric object
        transformed_metric = StoreDateMetric(
            date=metric.date,
            etl_snapshot_ts_ist=metric.etl_snapshot_ts_ist,
            date_diff=metric.date_diff,
            data=combined_data
        )
        transformed_metrics.append(transformed_metric)
    
    # Return proper StoreDateMetrics object
    return StoreDateMetrics(metrics=transformed_metrics)