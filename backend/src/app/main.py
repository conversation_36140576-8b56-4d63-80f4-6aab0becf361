import logging
from logging.config import dictConfig
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
# from fastapi_jwt_auth.exceptions import AuthJWTException  # Removed due to Pydantic v2 incompatibility
from starlette.middleware.cors import CORSMiddleware

from app.api.api_v1.api import api_router
from app.api.api_bistro_v1.api import api_router_bistro
from app.api.api_v2.api import api_router_v2
from app.core.config import settings
from app.core.log_config import LogConfig
from app.cache import cache
from ddtrace import tracer

# Update the logger
dictConfig(LogConfig().dict())
logger = logging.getLogger(__name__)

try:
    tracer.configure(hostname=settings.DD_AGENT_HOST)
    logger.info("Datadog setup completed. Tracking your application.")
except Exception as e:
    logger.info("Error in Datadog setup : ", e)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting the application")
    cache.connect()
    yield
    # Shutdown
    logger.info("Shutting down the application")
    cache.close()


app = FastAPI(title="Sonar", openapi_url=f"{settings.API_V1_STR}/openapi.json", lifespan=lifespan)

# Set all CORS enabled origins
# For development, allow all origins
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=settings.API_V1_STR)
app.include_router(api_router_bistro, prefix=settings.API_BISTRO_V1_STR)
app.include_router(api_router_v2, prefix=settings.API_V2_STR)
