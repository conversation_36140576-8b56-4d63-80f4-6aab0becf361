from datetime import datetime, timedelta
from typing import Optional, List

class ProductMetric():    
    def get_complete_day_data(self, date_column, starting_date_ist, number_of_entries, period_length, date_format, dates: Optional[List[str]] = None):
        dt_filters = []
        
        if dates is not None:
            # Use provided dates
            for date_str in dates:
                dt_filters.append(f"({date_column} = '{date_str}')")
        else:
            # Original logic
            for i in range(number_of_entries):
                order_date = (starting_date_ist - timedelta(days=period_length*i)).strftime(date_format)
                dt_filters.append(f"({date_column} = '{order_date}')")
        return dt_filters
    

    def get_day_data_till_current_timestamp(self, date_column, starting_date_ist, number_of_entries, period_length, date_format, 
                                            timestamp_column=None, timestamp_column_in_seconds=False, less_than_time_in_utc_ms=None, dates: Optional[List[str]] = None):
        NOW_UTC = datetime.utcnow()
        MS_PER_DAY = 86400 * 1000
        
        if less_than_time_in_utc_ms is None:
            less_than_time_in_utc_ms = int(NOW_UTC.timestamp() * 1000)
        
        dt_filters = []
        
        if dates is not None:
            # Use provided dates with timestamp logic
            now_ist = datetime.now() + timedelta(hours=5, minutes=30)
            today_str = now_ist.strftime('%Y-%m-%d')
            
            for date_str in dates:
                if timestamp_column:
                    # Apply timestamp filtering only for today
                    date_obj = datetime.strptime(date_str, date_format)
                    days_diff = (datetime.strptime(today_str, date_format) - date_obj).days
                    offset_ms = MS_PER_DAY * days_diff
                    cutoff_ms = less_than_time_in_utc_ms - offset_ms
                    
                    if timestamp_column_in_seconds:
                        filter_str = f"({date_column} = '{date_str}' AND {timestamp_column} <= {cutoff_ms/1000})"
                    else:
                        filter_str = f"({date_column} = '{date_str}' AND {timestamp_column} <= {cutoff_ms})"
                    dt_filters.append(filter_str)
                else:
                    dt_filters.append(f"({date_column} = '{date_str}')")
        else:
            # Original logic
            for i in range(number_of_entries):
                order_date = (starting_date_ist - timedelta(days=period_length*i)).strftime(date_format)
                if timestamp_column:
                    # Calculate offset in milliseconds
                    offset_ms = MS_PER_DAY * period_length * i
                    cutoff_ms = less_than_time_in_utc_ms - offset_ms
                    
                    if timestamp_column_in_seconds:
                        filter_str = f"({date_column} = '{order_date}' AND {timestamp_column} <= {cutoff_ms/1000})"
                    else:
                        filter_str = f"({date_column} = '{order_date}' AND {timestamp_column} <= {cutoff_ms})"
                    dt_filters.append(filter_str)
                else:
                    dt_filters.append(f"({date_column} = '{order_date}')")
        return dt_filters
    
    
    def generate_dt_filters_product_metrics(
            self,
            date_column,
            timestamp_column=None, 
            timestamp_column_in_seconds: bool = False,
            is_daywise: bool = False,
            yesterday_metric: bool = False,
            current_hour: bool = True,
            date_str: str = None,
            number_of_days: int = 7,
            number_of_week: int = 5,
            less_than_time_in_utc_ms: Optional[int] = None,
            dates: Optional[List[str]] = None):

        if not date_str:
            date_str = (datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

        if yesterday_metric:
            date_str = (datetime.now() + timedelta(hours=5, minutes=30) - timedelta(days=1)).strftime('%Y-%m-%d')
            

        date_format = '%Y-%m-%d'
        starting_date_ist = datetime.strptime(date_str, date_format)

        is_today = date_str==(datetime.now() + timedelta(hours=5, minutes=30)).strftime('%Y-%m-%d')

        dt_filters = None
        if is_daywise: # daywise 
            if is_today: # today
                if current_hour:
                    dt_filters = self.get_day_data_till_current_timestamp(date_column=date_column, starting_date_ist=starting_date_ist,
                                                                    number_of_entries = number_of_days, period_length=1, date_format=date_format,
                                                                    timestamp_column=timestamp_column, timestamp_column_in_seconds=timestamp_column_in_seconds,
                                                                    less_than_time_in_utc_ms=less_than_time_in_utc_ms, dates=dates)
                else: 
                    dt_filters = self.get_complete_day_data(date_column=date_column, starting_date_ist=starting_date_ist,
                                                    number_of_entries = number_of_days , period_length=1, date_format=date_format, dates=dates)
            else: # yesterday
                dt_filters = self.get_complete_day_data(date_column=date_column, starting_date_ist=starting_date_ist,
                                                    number_of_entries = number_of_days, period_length=1, date_format=date_format, dates=dates)
        else: # week 
            if is_today: # today
                dt_filters = self.get_day_data_till_current_timestamp(date_column=date_column, starting_date_ist=starting_date_ist,
                                                                    number_of_entries = number_of_week, period_length=7, date_format=date_format,
                                                                    timestamp_column=timestamp_column, timestamp_column_in_seconds=timestamp_column_in_seconds,
                                                                    less_than_time_in_utc_ms=less_than_time_in_utc_ms, dates=dates)
            else: # yesterday 
                dt_filters = self.get_complete_day_data(date_column=date_column, starting_date_ist=starting_date_ist,
                                                    number_of_entries = number_of_week, period_length=7, date_format=date_format, dates=dates)
        return " OR\n".join(dt_filters)

product_metric = ProductMetric()