
METRIC_NAME_MAPPING: dict = {
    "total_cases": "Total Cases",
    "approved_cases": "Approved Cases",
    "cancelled_cases": "Cancelled Cases",
    "completed_cases": "Completed Cases",
    "active_cases": "Active Cases",
    "avg_eta_show": "Average ETA Shown",
}

METRICS_TYPE_MAPPING: dict = {
    "total_cases": "number",
    "approved_cases": "number",
    "cancelled_cases": "number",
    "completed_cases": "number",
    "active_cases": "number",
    "avg_eta_show": "time",
}

METRICS_CALC_MAPPING: dict = {
    "total_cases": "COUNT(DISTINCT(cart_id)) as total_cases",
    "approved_cases": "DISTINCTCOUNT( CASE WHEN current_status = 'APPROVED' THEN cart_id ELSE null END)",
    "cancelled_cases": "DISTINCTCOUNT( CASE WHEN current_status = 'CANCELLED' THEN cart_id ELSE null END)",
    "completed_cases": "DISTINCTCOUNT( CASE WHEN current_status = 'COMPLETED' THEN cart_id ELSE null END)",
    "active_cases": "DISTINCTCOUNT( CASE WHEN current_status NOT IN ('CANCELLED' , 'COMPLETED') THEN cart_id ELSE null END)",
    "avg_eta_show": "AVG(eta_shown)*60 as avg_eta_shown",
}
