import os
from typing import Any, Dict, List, Optional, Union

# from fastapi_jwt_auth import AuthJWT  # Removed due to Pydantic v2 incompatibility
from pydantic import AnyHttpUrl, field_validator, model_validator, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    API_BISTRO_V1_STR: str = "/api/bistro/v1"

    API_V2_STR: str = "/api/v2"
    
    # JWT settings
    JWT_TOKEN_PREFIX: str = "Bearer"
    
    # For development, allow all origins
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8081",
    ]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    PROJECT_NAME: str = "sonar"

    GOOGLE_CLIENT_ID: str = ""
    GOOGLE_CLIENT_SECRET: str = ""
    GOOGLE_REDIRECT_URI: str = ""
    USER_REGISTRAION_ALLOWED: bool = False


    # Auth Settings
    authjwt_secret_key: str = ""
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 15
    ALGORITHM: str = "HS256"
    REFRESH_TOKEN_EXPIRE_DAYS: int = 5

    # SENTRY_DSN: Optional[HttpUrl] = None

    # @validator("SENTRY_DSN", pre=True)
    # def sentry_dsn_can_be_blank(cls, v: str) -> Optional[str]:
    #     if len(v) == 0:
    #         return None
    #     return v

    POSTGRES_HOST: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    # Override it if there is some issue in pinot
    # This will force it to use redis cache if present
    FORCE_REDIS_CACHE: bool = False
    REDIS_HOST_URL: str

    @model_validator(mode="before")
    @classmethod
    def assemble_db_connection(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        if isinstance(values.get("SQLALCHEMY_DATABASE_URI"), str):
            return values

        if "SQLALCHEMY_DATABASE_URI" not in values or values["SQLALCHEMY_DATABASE_URI"] is None:
            # Pydantic v2 PostgresDsn construction
            postgres_user = values.get("POSTGRES_USER")
            postgres_password = values.get("POSTGRES_PASSWORD")
            postgres_host = values.get("POSTGRES_HOST")
            postgres_db = values.get("POSTGRES_DB") or ""

            # Return as string, not PostgresDsn object
            values["SQLALCHEMY_DATABASE_URI"] = f"postgresql://{postgres_user}:{postgres_password}@{postgres_host}/{postgres_db}"
        return values

    PINOT_DB_URL: str = ""
    COMPLAINT_IMAGES_URL: str = ""
    COMPLAINT_IMAGES_TOKEN: str = ""
    
    DD_AGENT_HOST: str = ""
    DD_SERVICE: str = ""
    DD_ENV: str = ""
    DD_TRACE_ENABLED: str = ""
    DD_PROFILING_ENABLED: str = ""
    DD_SERVICE_MAPPING: str = ""

    TEST_S3_BUCKET: str = ""
    BAD_STOCK_S3_BUCKET: str = ""
    AWS_S3_REGION: str = ""

    model_config = ConfigDict(case_sensitive=True)


settings = Settings()
