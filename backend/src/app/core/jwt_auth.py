"""
Simple JWT Authentication replacement for fastapi-jwt-auth
Compatible with Pydantic v2
"""
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
from jose import JWTError, jwt, ExpiredSignatureError
from fastapi import HTT<PERSON>Exception, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.core.config import settings

# Set up logger
logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer(auto_error=False)  # Setting auto_error=False makes it optional

# JWT token prefix
JWT_TOKEN_PREFIX = "Bearer"

class JWTAuth:
    """Simple JWT Authentication class"""
    
    @staticmethod
    def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Create a new access token"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, settings.authjwt_secret_key, algorithm=settings.ALGORITHM)
        return encoded_jwt
    
    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """Verify and decode a JWT token"""
        try:
            payload = jwt.decode(token, settings.authjwt_secret_key, algorithms=[settings.ALGORITHM])
            logger.debug(f"Token verified successfully")
            return payload
        except ExpiredSignatureError:
            logger.warning(f"Token expired")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired",
                headers={"WWW-Authenticate": f"{JWT_TOKEN_PREFIX} realm='expired'"},
            )
        except JWTError as e:
            logger.warning(f"JWT verification failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": f"{JWT_TOKEN_PREFIX} realm='invalid_token'"},
            )
    
    @staticmethod
    def extract_token_from_request(request: Request) -> str:
        """Extract token from various sources in the request"""
        # Try Authorization header first (Bearer token)
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith(f"{JWT_TOKEN_PREFIX} "):
            return auth_header.replace(f"{JWT_TOKEN_PREFIX} ", "")
        
        # Try request cookies
        token = request.cookies.get("access_token")
        if token:
            return token
        
        # Try query parameters
        token = request.query_params.get("token")
        if token:
            return token
        
        # No token found
        logger.warning("No token found in request")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No authentication token found",
            headers={"WWW-Authenticate": f"{JWT_TOKEN_PREFIX}"},
        )
    
    @staticmethod
    def get_current_user(request: Request) -> Dict[str, Any]:
        """Get current user from request"""
        token = JWTAuth.extract_token_from_request(request)
        return JWTAuth.verify_token(token)

def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security), request: Request = None) -> Dict[str, Any]:
    """Dependency to get current user from JWT token"""
    if credentials:
        token = credentials.credentials
        return JWTAuth.verify_token(token)
    elif request:
        # Fallback to custom token extraction
        return JWTAuth.get_current_user(request)
    else:
        logger.error("Authentication failed: No credentials or request object")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required",
            headers={"WWW-Authenticate": f"{JWT_TOKEN_PREFIX}"},
        )

# Backward compatibility aliases
def jwt_required():
    """Decorator replacement for @jwt_required()"""
    return Depends(get_current_user)

def get_jwt_identity(current_user: Dict[str, Any] = Depends(get_current_user)) -> str:
    """Get user identity from JWT token"""
    return current_user.get("sub", "")
