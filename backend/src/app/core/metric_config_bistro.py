from typing import List

METRIC_NAME_MAPPING: dict = {
    "gmv": "GMV",
    "aov": "AOV", 
    "order_count": "Cart Volume",
    "ath_gmv": "ATH GMV",
    "ath_order_count": "ATH Cart Volume",
    "avg_checkout_to_enroute": "Checkout to Enroute Time",
    "percentage_orders_delivered_in_15mins": "%Delivery < 15mins",
    "percentage_orders_arrived_doorstep_in_15mins": "% orders at doorstep in 15m",
    "cancellation_percentage": "Order Cancellation",
    "surge_shown_carts_percentage": "Surge Checkouts %",
    "billed_to_assigned": "Direct Handover %",
    "ipc": "IPC",
    "batched_orders": "Batched Order %",
    "total_items_sold": "Total Items Sold",
    "canceled_quantity": "Canceled Quantity",
    "cart_pen":"Cart Pen",
    "unique_carts":"Unique Carts",
    "new_transacting_users_count": "New Transacting Customers",
    "enroute_to_billed": "Rider Handshake time",
    "direct_handover_wait_time": "Direct Rider Handshake time",
    "indirect_handover_wait_time": "Indirect Rider Handshake time",
    "enroute_to_delivery": "Enroute to Delivery time",
    "enroute_to_doorstep": "Enroute to Doorstep time",
    "dau": "Daily Active Users",
    "dau_conversion_percentage": "DAU Conversion Percentage",
    "percentage_cod_orders": "COD orders %",
    "promo_percentage": "Promo %",
    "promo_orders": "Promo Orders",
    "promo_charge": "Promo Charge",
    "avg_kpt": "Average KPT",
    "avg_wait_time": "Average Wait Time",
    "avg_prep_time": "Average Preparation Time",
    "avg_assembly_time": "Average Assembly Time",
    "percent_order_breaching_wait_prep_time": "% Orders Breaching Wait+Prep Time",
    "percent_order_breaching_assembly_time": "% Orders Breaching Assembly Time",
    "order_volume": "Order Volume",
    "active_stores_count": "Active Stores Count",
    "rain_surge_carts_percentage": "Rain Order %"
}

METRICS_TYPE_MAPPING: dict = {
    "aov": "currency", 
    "gmv": "currency", 
    "ath_gmv": "currency",
    "ath_order_count": "number",
    "order_count": "number",
    "avg_checkout_to_enroute": "time",
    "percentage_orders_delivered_in_15mins": "percentage",
    "percentage_orders_arrived_doorstep_in_15mins": "percentage",
    "cancellation_percentage": "percentage",
    "surge_shown_carts_percentage": "percentage",
    "billed_to_assigned": "percentage",
    "ipc": "number",
    "batched_orders": "percentage",
    "total_items_sold": "number",
    "canceled_quantity": "number",
    "cart_pen":"percentage",
    "unique_carts":"number",
    "new_transacting_users_count": "number",
    "enroute_to_billed": "time",
    "direct_handover_wait_time": "time",
    "indirect_handover_wait_time": "time",
    "enroute_to_delivery": "time",
    "enroute_to_doorstep": "time",
    "dau": "number",
    "dau_conversion_percentage": "percentage",
    "percentage_cod_orders": "percentage",
    "promo_percentage": "percentage",
    "promo_orders": "number",
    "promo_charge": "currency",
    "avg_kpt": "time",
    "avg_wait_time": "time",
    "avg_prep_time": "time",
    "avg_assembly_time": "time",
    "percent_order_breaching_wait_prep_time": "percentage",
    "percent_order_breaching_assembly_time": "percentage",
    "order_volume": "number",
    "active_stores_count": "number",
    "rain_surge_carts_percentage": "percentage"
}

SENSITIVE_METRICS: List[str] = ["AOV", "GMV"]

METRICS_CALC_MAPPING: dict = {
    "gmv": "SUM(total_cost) as gmv",
    "aov": "SUM(total_cost)/COUNT(DISTINCT(cart_id)) as aov",
    "order_count": "COUNT(DISTINCT(cart_id)) as order_count",
    "avg_checkout_to_enroute": "(AVG(CASE WHEN (order_enroute_timestamp - checkout_timestamp) > 0 then (order_enroute_timestamp - checkout_timestamp) else 0 end))/1000.0 AS avg_checkout_to_enroute",
    "percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as percentage_order_delivered_in_15mins",
    "percentage_orders_arrived_doorstep_in_15mins": "100 * SUM(CASE WHEN (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((reached_doorstep_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN current_status = 'DELIVERED' AND reached_doorstep_timestamp IS NOT NULL THEN 1 ELSE 0 END) as percentage_order_arrived_doorstep_in_15mins",
    "cancellation_percentage": "100 * SUM (CASE WHEN current_status = 'CANCELLED' THEN 1 ELSE 0 END) / COUNT(id) as cancellation_percentage",
    "surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN surge_amount > 0 THEN cart_id ELSE null END) ) / (COUNT(DISTINCT(cart_id))) as surge_shown_carts_percentage",
    "billed_to_assigned": "100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / count(id) as percentage_order_assigned_before_billing",
    "ipc": "SUM(total_items_quantity * 1.00)/COUNT(DISTINCT(cart_id))",
    "batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN is_batched_order = true THEN id ELSE null END) ) / COUNT(id) as batched_orders",
    "enroute_to_billed": "AVG(CASE WHEN (order_enroute_timestamp - order_billed_timestamp) >= 0 then order_enroute_timestamp - order_billed_timestamp else 0 end)/1000.0 as avg_wait_time_rider_handshake",
    "direct_handover_wait_time": "AVG(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 AND (order_enroute_timestamp - order_billed_timestamp) >= 0 then order_enroute_timestamp - order_billed_timestamp else 0 end)/1000.0 as direct_handover_wait_time",
    "indirect_handover_wait_time": "AVG(CASE WHEN (rider_assigned_timestamp - order_billed_timestamp) >= 0 AND (order_enroute_timestamp - order_billed_timestamp) >= 0 then order_enroute_timestamp - order_billed_timestamp else 0 end)/1000.0 as indirect_handover_wait_time",
    "enroute_to_delivery": "AVG(CASE WHEN (delivery_timestamp - order_enroute_timestamp) >= 0 then delivery_timestamp - order_enroute_timestamp else 0 end)/1000.0 as avg_enroute_to_delivery_time",
    "enroute_to_doorstep": "AVG(CASE WHEN (reached_doorstep_timestamp - order_enroute_timestamp) >= 0 then reached_doorstep_timestamp - order_enroute_timestamp else 0 end)/1000.0 as avg_enroute_to_doorstep_time",
    "percentage_cod_orders": "100 * (DISTINCTCOUNT(CASE WHEN payment_mode = 'COD' THEN cart_id ELSE NULL END)) / DISTINCTCOUNT(cart_id) as percentage_cod_orders",
    "promo_percentage": "100.0 * DISTINCTCOUNT(CASE WHEN discount > 0 THEN cart_id ELSE null END) / COUNT(DISTINCT(cart_id)) AS promo_percentage",
    "promo_orders": "SUM(CASE WHEN discount > 0 THEN 1 ELSE 0 END) AS promo_orders",
    "promo_charge": "SUM(CASE WHEN discount > 0 THEN discount ELSE 0 END)*1.0 / COUNT(DISTINCT(cart_id)) AS promo_charge",
    "avg_kpt": "AVG(CASE WHEN (order_billed_timestamp - approved_timestamp) > 0 THEN (order_billed_timestamp - approved_timestamp) ELSE 0 END)/1000.0 as avg_kpt",
    "avg_wait_time": "AVG(CASE WHEN (order_prep_start_timestamp - approved_timestamp) > 0 THEN (order_prep_start_timestamp - approved_timestamp) ELSE 0 END)/1000.0 as avg_wait_time",
    "avg_prep_time": "AVG(CASE WHEN (order_prep_end_timestamp - order_prep_start_timestamp) > 0 THEN (order_prep_end_timestamp - order_prep_start_timestamp) ELSE 0 END)/1000.0 as avg_prep_time",
    "avg_assembly_time": "AVG(CASE WHEN (order_billed_timestamp - order_prep_end_timestamp) > 0 THEN (order_billed_timestamp - order_prep_end_timestamp) ELSE 0 END)/1000.0 as avg_assembly_time",
    "percent_order_breaching_wait_prep_time": "100 * SUM(CASE WHEN ((order_prep_end_timestamp - approved_timestamp) / 1000 - (instore_eta - assembly_time) * 60) > 60 THEN 1 ELSE 0 END) / COUNT(id) as percent_order_breaching_wait_prep_time",
    "percent_order_breaching_assembly_time": "100 * SUM(CASE WHEN ((order_billed_timestamp - order_prep_end_timestamp) / 1000 - (assembly_time * 60)) > 60 THEN 1 ELSE 0 END) / COUNT(id) as percent_order_breaching_assembly_time",
    "order_volume": "COUNT(DISTINCT(id)) as order_volume",
    "new_transacting_users_count": "DISTINCTCOUNT(CASE WHEN order_count_life_time = 0 THEN customer_id ELSE null END) AS new_transacting_users_count",
    "rain_surge_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN surge_type = 'rain_surge' THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) as rain_surge_carts_percentage",
}

PRODUCT_METRICS_CALC_MAPPING : dict = {
    "gmv" : "SUM(total_item_cost) as gmv",
    "order_count" : "COUNT(DISTINCT(cart_id)) as order_count",
    "total_items_sold" : "SUM(item_quantity) as total_items_sold",
    "canceled_quantity" : "SUM(CASE WHEN current_status = 'Order Cancelled' THEN item_quantity ELSE 0 END) as canceled_quantity",
    "aov" : "SUM(total_item_cost)/COUNT(DISTINCT(cart_id)) as aov",
    "unique_carts" : "COUNT(DISTINCT(cart_id)) as unique_carts",
    "ipc" : "SUM(item_quantity * 1.00)/COUNT(DISTINCT(cart_id)) AS ipc",
}

PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING: dict = {
    "l0_category" : "l0_category",
    "l1_category" : "l1_category",
    "l2_category" : "l2_category",
    "ptype" : "product_type",
    "pname" : "product_name",  # Changed from "item_name" to "product_name"
    "brand" : "brand_name",
    "pid" : "product_id"       # Changed from "item_product_id" to "product_id"
}

RATING_METRICS_NAME_MAPPING = {
    "total_count": "Total Count",
    "average_rating": "Average Rating",
    "rating_1_count": "Rating 1 Count",
    "rating_2_count": "Rating 2 Count",
    "rating_3_count": "Rating 3 Count",
    "rating_4_count": "Rating 4 Count",
    "rating_5_count": "Rating 5 Count",
}

RATING_METRICS_TYPE_MAPPING = {
    "total_count": "number",
    "average_rating": "number",
    "rating_1_count": "number",
    "rating_2_count": "number",
    "rating_3_count": "number",
    "rating_4_count": "number",
    "rating_5_count": "number",
}

STATION_METRICS_CALC_MAPPING = {
    "station_cart_volume": "COUNT(DISTINCT(order_id)) AS cart_volume",
    "station_avg_kpt": "AVG(kpt) AS avg_kpt",
    "station_wait_time": "AVG(prep_start_timestamp - GREATEST(order_created_at, defer_end_timestamp)) AS avg_wait_time",
    "station_prep_time": "AVG(prep_end_timestamp - prep_start_timestamp) AS avg_prep_time"
}

STATION_METRIC_NAME_MAPPING = {
    "station_cart_volume": "Station Cart Volume",
    "station_avg_kpt": "Station Average KPT",
    "station_wait_time": "Station Wait Time",
    "station_prep_time": "Station Preparation Time"
}

STATION_METRICS_TYPE_MAPPING = {
    "station_cart_volume": "number",
    "station_avg_kpt": "time",
    "station_wait_time": "time",
    "station_prep_time": "time"
}