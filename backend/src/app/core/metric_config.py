from typing import List

METRIC_NAME_MAPPING: dict = {"aov": "AOV", "gmv": "GMV","ath_gmv": "ATH GMV", "order_count": "Cart Volume","ath_order_count": "ATH Cart Volume",
                             "delivered_order_count": "Delivered Cart Volume", "avg_item_count": "Unique SKU/Order",
                             "total_items_per_order": "Total Items/Order", "total_items_sold": "Total Items Sold",
                             "transacting_users_count": "Transacting Customers", "order_per_store": "Order Per Store/Day",
                             "new_transacting_users_count": "New Transacting Customers",
                             "cancellation_percentage": "Order Cancellation", "hau": "Hourly Active Users",
                             "dau": "Daily Active Users", "mau": "Monthly Active Users", "wau": "Weekly Active Users",
                             "rm_percentage": "Retained Margin", "rm": "Absolute Retained Margin",
                             "order_conversion": "Order Conversion", "weighted_availability": "Weighted Availability",
                             "cum_weighted_availability": "Cumulative Weighted Availability",
                             "percentage_orders_delivered_in_10mins": "%Delivery < 10mins",
                             "percentage_orders_delivered_in_15mins": "%Delivery < 15mins",
                             "percentage_orders_delivered_in_30mins": "%Delivery < 30mins",
                             "percentage_orders_arrived_in_10mins": "% orders arrived in 10mins",
                             "percentage_orders_arrived_in_15mins": "% orders arrived in 15mins",
                             "surge_shown_carts_percentage": "Surge Checkouts %",
                             "surge_paid_carts_percentage": "Surge Paid Checkouts %",
                             "rain_surge_carts_percentage": "Rain Surge Orders %",
                             "surge_seen_percentage": "Surge Shown %",
                             "serviceability_based_cart_blocks_conversion_percentage": "Unserviceable DAU %",
                             "express_serviceability_ooh_based_cart_blocks_conversion_percentage": "Express OOH Based Block %",
                             "express_serviceability_manual_based_cart_blocks_conversion_percentage": "Express Manual Based Block %",
                             "demand_based_cart_blocks_conversion_percentage": "Demand Based Block %",
                             "express_demand_based_cart_blocks_conversion_percentage": "Express Demand Based Block %",
                             "longtail_serviceability_ooh_based_cart_blocks_conversion_percentage": "Longtail OOH Based Block %",
                             "longtail_serviceability_manual_based_cart_blocks_conversion_percentage": "Longtail Manual Based Block %",
                             "longtail_demand_based_cart_blocks_conversion_percentage": "Longtail Demand Based Block %",
                             "active_stores_count": "Active Stores Count",
                             "opd_per_store": "OPD Per Store",
                             "dau_conversion_percentage": "DAU Conversion Percentage",
                             "hau_conversion_percentage": "HAU Conversion Percentage",
                             "eta_shown_lesser_than_10_mins": "% ETA below 10mins",
                             "eta_shown_lesser_than_15_mins": "<15 mins ETA%",
                             "eta_shown_lesser_than_30_mins": "% ETA below 30mins", "delivery_cost": "Delivery Charge",
                             "dc_revenue": "Delivery Cost Revenue", "rider_login_hrs": "Rider Login hrs",
                             "new_riders": "New Riders", "rain_order_percentage": "Rain Order %",
                             "billed_to_assigned": "Direct Handover %",
                             "checkout_to_picker_assigned": "%Picker assigned 10 secs",
                             "projected_order_count": "Projected Cart Volume", "projected_gmv": "Projected GMV",
                             "batched_orders": "Batched Order %", "platform_cost": "Customer Paid Charges",
                             "dau_to_atc_conv": "ATC %", "atc_to_cv_conv": "CV %", "cv_to_transact_conv": "C2Co %",
                             "overall_conv": "Overall Conv %",
                             "percent_instore_sla_less_than_150_sec": "Instore SLA % < 2.5 mins",
                             "ppi_in_seconds": "Picking Time Per Item", "picker_surge_orders": "Picker Surge Orders %",
                             "rider_plus_picker_surge_orders": "Both Surge Orders %", "sm_fill_rate": "Fill Rate %",
                             "total_procured_items_quantity": "IPO", "total_active_time": "Total Active Hrs",
                             "picker_active_time_percentage": "Picker Active Time %",
                             "putter_active_time_percentage": "Putter Active Time %",
                             "auditor_active_time_percentage": "Auditor Active Time %",
                             "fnv_active_time_percentage": "FNV Active Time %", "orders_picked_per_hour": "OPH",
                             "items_putaway_per_hour": "IPH", "new_manpower": "New Manpower",
                             "total_complaints": "Total Complaints",
                             "picker_assigned_to_picking_start": "Picking Start % (5 secs)",
                             "total_orders": "Total Orders", "complaints_percentage": "Complaints %","search_conversion": "Search Conv %",
                             "search": "Search Impressions", "unique_impressions": "Unique Impressions", 
                             "conv_per_contribution": "Conv Δ Contri", "sb_atc_contribution": "ATC Δ Serv Contri", 
                             "db_atc_contribution": "ATC Δ Demand Contri", "misc_atc_contribution": "ATC Δ Misc Contri", 
                             "sb_c2co_contribution": "C2Co Δ Serv Contri", "db_c2co_contribution": "C2Co Δ Demand Contri",
                             "express_order_count": "Express Cart Volume",
                             "longtail_order_count": "Longtail Cart Volume",
                             "super_longtail_order_count": "Super Longtail Cart Volume",
                             "unicorn_order_count": "Unicorn Cart Volume",
                             "express_free_delivery_percentage": "Express Free DC %",
                             "longtail_free_delivery_percentage": "Longtail Free DC %",
                             "super_longtail_free_delivery_percentage": "Super Longtail Free DC %",
                             "unicorn_free_delivery_percentage": "Unicorn Free DC %",
                             "express_percentage_orders_delivered_in_10mins": "Express %Delivery < 10mins",
                             "longtail_percentage_orders_delivered_in_10mins": "Longtail %Delivery < 10mins",
                             "super_longtail_percentage_orders_delivered_in_10mins": "Super Longtail %Delivery < 10mins",
                             "unicorn_percentage_orders_delivered_in_10mins": "Unicorn %Delivery < 10mins",
                             "express_billed_to_assigned": "Express Direct Handover %",
                             "longtail_billed_to_assigned": "Longtail Direct Handover %",
                             "super_longtail_billed_to_assigned": "Super Longtail Direct Handover %",
                             "unicorn_billed_to_assigned": "Unicorn Direct Handover %",
                             "express_surge_shown_carts_percentage": "Express Surge Checkouts %",
                             "longtail_surge_shown_carts_percentage": "Longtail Surge Checkouts %",
                             "super_longtail_surge_shown_carts_percentage": "Super Longtail Surge Checkouts %",
                             "unicorn_surge_shown_carts_percentage": "Unicorn Surge Checkouts %",
                             "express_surge_paid_carts_percentage": "Express Surge Paid Checkouts %",
                             "longtail_surge_paid_carts_percentage": "Longtail Surge Paid Checkouts %",
                             "super_longtail_surge_paid_carts_percentage": "Super Longtail Surge Paid Checkouts %",
                             "unicorn_surge_paid_carts_percentage": "Unicorn Surge Paid Checkouts %",
                             "express_batched_orders": "Express Batched Order %",
                             "longtail_batched_orders": "Longtail Batched Order %",
                             "super_longtail_batched_orders": "Super Longtail Batched Order %",
                             "unicorn_batched_orders": "Unicorn Batched Order %",
                             "express_rain_order_percentage": "Express Rain Order %",
                             "longtail_rain_order_percentage": "Longtail Rain Order %",
                             "super_longtail_rain_order_percentage": "Super Longtail Rain Order %",
                             "unicorn_rain_order_percentage": "Unicorn Rain Order %",
                             "express_percentage_orders_delivered_in_15mins": "Express %Delivery < 15mins",
                             "longtail_percentage_orders_delivered_in_15mins": "Longtail %Delivery < 15mins",
                             "super_longtail_percentage_orders_delivered_in_15mins": "Super Longtail %Delivery < 15mins",
                             "unicorn_percentage_orders_delivered_in_15mins": "Unicorn %Delivery < 15mins",
                             "express_percentage_orders_delivered_in_30mins": "Express %Delivery < 30mins",
                             "longtail_percentage_orders_delivered_in_30mins": "Longtail %Delivery < 30mins",
                             "super_longtail_percentage_orders_delivered_in_30mins": "Super Longtail %Delivery < 30mins",
                             "unicorn_percentage_orders_delivered_in_30mins": "Unicorn %Delivery < 30mins",
                             "express_cancellation_percentage": "Express Order Cancellation",
                             "longtail_cancellation_percentage": "Longtail Order Cancellation",
                             "super_longtail_cancellation_percentage": "Super Longtail Order Cancellation",
                             "unicorn_cancellation_percentage": "Unicorn Order Cancellation",
                             "express_checkout_to_picker_assigned": "Express %Picker assigned 10 secs",
                             "longtail_checkout_to_picker_assigned": "Longtail %Picker assigned 10 secs",
                             "super_longtail_checkout_to_picker_assigned": "Super Longtail %Picker assigned 10 secs",
                             "unicorn_checkout_to_picker_assigned": "Unicorn %Picker assigned 10 secs",
                             "misc_c2co_contribution": "C2Co Δ Misc Contri","availability": "FE Availability",
                             "canceled_quantity": "Canceled Quantity", "asp" : "Average Selling Price",
                             "block_atc_lost_per_diff": "Blocks Contribution to ATC", "dslo_atc_lost_per_diff":"DAU Contribution to ATC", 
                             "misc_atc_lost_per_diff":"Avail & Other Contribution to ATC",
                              "block_c2co_lost_per_diff":"Blocks Contribution to C2Co", "dslo_c2co_lost_per_diff":"DAU Contribution to C2Co", 
                              "misc_c2co_lost_per_diff":"Avail & Other Contribution to C2Co", "dslo_surge_lost_per_diff":"Surge Contribution",
                              "cnt_inventory":"FE Inventory", "ipc":"IPC", "unique_carts":"Unique Carts", "aov_contribution":"AOV Contribution", "cart_pen":"Cart Pen",
                              "surge_seen_percentage":"Surge Seen %", "rider_surge_seen_percentage":"Rider Surge Seen %", "picker_surge_seen_percentage":"Picker Surge Seen %", "rain_surge_seen_percentage":"Rain Surge Seen %",
                              "search_spike": "Search Spike %", "conv_drop": "Conversion Drop %" ,
                              "top_ptype_item_rank":"Item Rank", "ars_created_ts_ist":"ARS Created", "item_quantity_t0":"Sold Qty T0",
                              "item_quantity_t1":"Sold Qty T1", "cycle_cpd_sum":"ARS CPD", "initial_demand_qty":"ARS Demand", 
                              "fe_inventory_qty":"FE Inventory", "be_inventory_qty":"BE Inventory", "v1_indent":"Indent V1",
                              "indent_raised_qty":"STO Raised", "total_truncation_qty":"Truncation Qty", "billed_qty":"Billed Qty",
                              "intransit_qty":"In Transit Qty", "grn_qty":"GRN Qty", "dn_qty":"DN", "b2b_qty":"B2B", "current_availability":"Current Availability",
                              "current_inventory_count":"Total Inventory", "truncation_reason":"Truncation Reason", "max_grn_ts_ist":"Max GRN Time",
                              "open_sto_qty_cal":"STO Qty", "v1_demand_gap":"V1 Demand Gap", "indent_gap":"Indent Gap", "billed_fill_rate":"Billed Fill Rate",
                              "dispatch_fill_rate":"Dispatch Fill Rate", "grn_fill_rate":"GRN Fill Rate","dn_fill_rate":"DN Fill Rate", "b2b_fill_rate":"B2B Fill Rate",
                              "stores_with_0_inventory":"Stores with Unavailability", "stores_with_sales_spike":"Store with Sale Spike", "stores_with_indent_gap":"Store - STO Fill Rate < 90 %",
                              "stores_with_storage_truncation":"Store with Storage Truncation", "stores_with_fleet_truncation":"Store with Fleet Truncation",
                              "stores_with_manpower_truncation":"Store with Manpower Truncation", "stores_with_inward_truncation":"Store with Inward Truncation",
                              "stores_with_grn_gap":"Store - GRN Fill Rate < 90 %", "merch_page_impr":"Merchandising Page DAUs","merch_page_atc":"Merchandising Page ATC%",
                              "cat_grid_page_impr":"Cat Grid PLP DAUs", "cat_grid_page_atc":"Cat Grid PLP ATC%","cnt_be_inventory":"BE Inventory",
                              "item_quantity":"Sold Qty T0","free_delivery_percentage":"Free DC %","freebie_order_percentage":"Freebie %", "total_od_active_time":"On-Demand Active Hours",
                              "true_fill_rate": "True Fill Rate %", "search_atc_lost_per_diff" : "Category Mix", "slot_charge":"Surge Charge", "handling_charge":"Handling Charge", 
                              "convenience_charge":"Convenience Charge", "night_charge":"Night Charge", "small_cart_charge":"Small Cart Charge", "promo_percentage": "Promo %", "promo_orders": "Promo Orders", "promo_charge":"Promo Charge", 
                              "high_priority_users_demand_block_percentage": "Priority Customers Demand Block %", "high_priority_users_ooh_block_percentage": "Priority Customers OOH Block %", "high_priority_users_manual_block_percentage": "Priority Customers Manual Block %", "high_priority_users_percentage": "Priority Customers %",
                              "union_order_count": "Union Cart Volume",
                              "union_free_delivery_percentage": "Union Free DC %",
                              "union_percentage_orders_delivered_in_10mins": "Union %Delivery < 10mins",
                              "union_billed_to_assigned": "Union Direct Handover %",
                              "union_surge_shown_carts_percentage": "Union Surge Checkouts %",
                              "union_surge_paid_carts_percentage": "Union Surge Paid Checkouts %",
                              "union_batched_orders": "Union Batched Order %",
                              "union_rain_order_percentage": "Union Rain Order %",
                              "union_percentage_orders_delivered_in_15mins": "Union %Delivery < 15mins",
                              "union_percentage_orders_delivered_in_30mins": "Union %Delivery < 30mins",
                              "union_cancellation_percentage": "Union Order Cancellation",
                              "union_checkout_to_picker_assigned": "Union %Picker assigned 10 secs",

                              "unique_order_ids" : "Order Count",
                             }
WAREHOUSE_METRICS_NAME_MAPPING: dict = {
    "line_items": "Line Items", "created_qty":"Created Qty", "cancelled_qty": "Cancelled Qty", "pick_fill_rate": "Picking Fill Rate",
    "packaging_fill_rate":"Packaging Fill Rate", "sort_fill_rate": "Sort Fill Rate", "dispatch_fill_rate": "Dispatch Fill Rate",
    "pick_otif": "Picking OTIF", "packaging_otif":"Packaging OTIF", "sort_otif": "Sort OTIF", "dispatch_otif": "Dispatch OTIF",
    "picking_pendency": "Picking Pendency", "packaging_pendency":"Packaging Pendency", "sort_pendency": "Sort Pendency", 
    "dispatch_pendency": "Dispatch Pendency", "irt": "IRT %", "raise_audit":"Raise Audit %", "putters": "Putters", "pickers":"Pickers", 
    "packers": "Packers", "sorters":"Sorters", "auditors": "Auditors",
    "be_availability": "BE %", "fe_availability": "FE %", "fe_availability_post_sto": "FE (Post STO) %"
}
WAREHOUSE_METRICS_TYPE_MAPPING: dict = {
    "line_items": "number","created_qty":"number","cancelled_qty": "number","pick_fill_rate": "percentage","packaging_fill_rate":"percentage",
    "sort_fill_rate": "percentage","dispatch_fill_rate": "percentage","pick_otif": "percentage","packaging_otif":"percentage",
    "sort_otif": "percentage","dispatch_otif": "percentage","picking_pendency": "number","packaging_pendency":"number","sort_pendency": "number",
    "dispatch_pendency": "number","irt": "percentage","raise_audit":"percentage", "putters": "number", "pickers":"number", "packers": "number", 
    "sorters":"number", "auditors": "number",
    "be_availability": "percentage", "fe_availability": "percentage", "fe_availability_post_sto": "percentage"
}
FILTER_NAME_MAPPING: dict = {"city": "City", "order_status": "Order Status"}
METRICS_TYPE_MAPPING: dict = {"aov": "currency", "gmv": "currency", "ath_gmv": "currency", "order_count": "number",
                               "ath_order_count": "number", "delivered_order_count": "number", "avg_item_count": "number",
                              "total_items_per_order": "number", "total_items_sold": "number",
                              "transacting_users_count": "number", "new_transacting_users_count": "number",
                              "cancellation_percentage": "percentage", "hau": "number", "order_per_store": "number",
                              "dau": "number", "mau": "number", "wau": "number", "rm_percentage": "percentage",
                              "rm": "currency", "order_conversion": "percentage",
                              "weighted_availability": "percentage", "cum_weighted_availability": "percentage",
                              "percentage_orders_delivered_in_30mins": "percentage",
                              "percentage_orders_delivered_in_15mins": "percentage",
                              "percentage_orders_delivered_in_10mins": "percentage",
                              "percentage_orders_arrived_in_10mins": "percentage",
                              "percentage_orders_arrived_in_15mins": "percentage",
                              "surge_shown_carts_percentage": "percentage",
                              "surge_paid_carts_percentage": "percentage",
                              "rain_surge_carts_percentage": "percentage",
                              "surge_seen_percentage": "percentage",
                              "express_order_count": "number",
                              "longtail_order_count": "number",
                              "super_longtail_order_count": "number",
                              "unicorn_order_count": "number",
                              "express_free_delivery_percentage": "percentage",
                              "longtail_free_delivery_percentage": "percentage",
                              "super_longtail_free_delivery_percentage": "percentage",
                              "unicorn_free_delivery_percentage": "percentage",
                              "express_percentage_orders_delivered_in_10mins": "percentage",
                              "longtail_percentage_orders_delivered_in_10mins": "percentage",
                              "super_longtail_percentage_orders_delivered_in_10mins": "percentage",
                              "unicorn_percentage_orders_delivered_in_10mins": "percentage",
                              "express_billed_to_assigned": "percentage",
                              "longtail_billed_to_assigned": "percentage",
                              "super_longtail_billed_to_assigned": "percentage",
                              "unicorn_billed_to_assigned": "percentage",
                              "express_surge_shown_carts_percentage": "percentage",
                              "longtail_surge_shown_carts_percentage": "percentage",
                              "super_longtail_surge_shown_carts_percentage": "percentage",
                              "unicorn_surge_shown_carts_percentage": "percentage",
                              "express_surge_paid_carts_percentage": "percentage",
                              "longtail_surge_paid_carts_percentage": "percentage",
                              "super_longtail_surge_paid_carts_percentage": "percentage",
                              "unicorn_surge_paid_carts_percentage": "percentage",
                              "express_batched_orders": "percentage",
                              "longtail_batched_orders": "percentage",
                              "super_longtail_batched_orders": "percentage",
                              "unicorn_batched_orders": "percentage",
                              "express_rain_order_percentage": "percentage",
                              "longtail_rain_order_percentage": "percentage",
                              "super_longtail_rain_order_percentage": "percentage",
                              "unicorn_rain_order_percentage": "percentage",
                              "express_percentage_orders_delivered_in_15mins": "percentage",
                              "longtail_percentage_orders_delivered_in_15mins": "percentage",
                              "super_longtail_percentage_orders_delivered_in_15mins": "percentage",
                              "unicorn_percentage_orders_delivered_in_15mins": "percentage",
                              "express_percentage_orders_delivered_in_30mins": "percentage",
                              "longtail_percentage_orders_delivered_in_30mins": "percentage",
                              "super_longtail_percentage_orders_delivered_in_30mins": "percentage",
                              "unicorn_percentage_orders_delivered_in_30mins": "percentage",
                              "express_cancellation_percentage": "percentage",
                              "longtail_cancellation_percentage": "percentage",
                              "super_longtail_cancellation_percentage": "percentage",
                              "unicorn_cancellation_percentage": "percentage",
                              "express_checkout_to_picker_assigned": "percentage",
                              "longtail_checkout_to_picker_assigned": "percentage",
                              "super_longtail_checkout_to_picker_assigned": "percentage",
                              "unicorn_checkout_to_picker_assigned": "percentage",
                              "serviceability_based_cart_blocks_conversion_percentage": "percentage",
                              "express_serviceability_ooh_based_cart_blocks_conversion_percentage": "percentage",
                              "express_serviceability_manual_based_cart_blocks_conversion_percentage": "percentage",
                              "demand_based_cart_blocks_conversion_percentage": "percentage",
                              "express_demand_based_cart_blocks_conversion_percentage": "percentage",
                              "longtail_serviceability_ooh_based_cart_blocks_conversion_percentage": "percentage",
                              "longtail_serviceability_manual_based_cart_blocks_conversion_percentage": "percentage",
                              "longtail_demand_based_cart_blocks_conversion_percentage": "percentage",
                              "active_stores_count": "number", "opd_per_store": "number",
                              "dau_conversion_percentage": "percentage",
                              "hau_conversion_percentage": "percentage",
                              "eta_shown_lesser_than_10_mins": "percentage",
                              "eta_shown_lesser_than_15_mins": "percentage",
                              "eta_shown_lesser_than_30_mins": "percentage", "delivery_cost": "currency",
                              "dc_revenue": "currency", "rider_login_hrs": "number", "new_riders": "number",
                              "billed_to_assigned": "percentage", "rain_order_percentage": "percentage",
                              "checkout_to_picker_assigned": "percentage", "projected_order_count": "number",
                              "projected_gmv": "currency", "platform_cost": "currency", "batched_orders": "percentage",
                              "dau_to_atc_conv": "percentage", "atc_to_cv_conv": "percentage",
                              "cv_to_transact_conv": "percentage", "overall_conv": "percentage",
                              "percent_instore_sla_less_than_150_sec": "percentage", "ppi_in_seconds": "time",
                              "picker_surge_orders": "percentage", "rider_plus_picker_surge_orders": "percentage",
                              "sm_fill_rate": "percentage", "total_procured_items_quantity": "number",
                              "total_active_time": "time", "picker_active_time_percentage": "percentage",
                              "putter_active_time_percentage": "percentage",
                              "auditor_active_time_percentage": "percentage",
                              "fnv_active_time_percentage": "percentage", "items_putaway_per_hour": "number",
                              "orders_picked_per_hour": "number", "new_manpower": "number",
                              "total_complaints": "number", "picker_assigned_to_picking_start": "percentage",
                              "total_orders": "number", "complaints_percentage": "percentage", "search_conversion": "percentage", 
                              "search": "number", "unique_impressions": "number", "conv_per_contribution": "percentage", 
                              "db_atc_contribution": "percentage", "sb_atc_contribution": "percentage", 
                              "misc_atc_contribution": "percentage", "sb_c2co_contribution": "percentage", 
                              "db_c2co_contribution": "percentage", "misc_c2co_contribution": "percentage", 
                              "availability": "percentage", "canceled_quantity": "number", "asp": "currency", 
                              "block_atc_lost_per_diff":"percentage", "dslo_atc_lost_per_diff":"percentage",
                              "misc_atc_lost_per_diff":"percentage", "block_c2co_lost_per_diff":"percentage",
                              "dslo_c2co_lost_per_diff":"percentage", "misc_c2co_lost_per_diff":"percentage",
                              "dslo_surge_lost_per_diff":"percentage", "cnt_inventory":"number", "ipc":"number",
                              "unique_carts":"number", "aov_contribution":"number","cart_pen":"percentage", "surge_seen_percentage":"percentage",
                              "rider_surge_seen_percentage":"percentage","rain_surge_seen_percentage":"percentage",
                              "picker_surge_seen_percentage":"percentage", "search_spike": "percentage", "conv_drop": "percentage",
                              "top_ptype_item_rank":"number", "ars_created_ts_ist":"string", "item_quantity_t0":"number",
                              "item_quantity_t1":"number", "cycle_cpd_sum":"number", "initial_demand_qty":"number", 
                              "fe_inventory_qty":"number", "be_inventory_qty":"number", "v1_indent":"number",
                              "indent_raised_qty":"number", "total_truncation_qty":"number", "billed_qty":"number",
                              "intransit_qty":"number", "grn_qty":"number", "dn_qty":"number", "b2b_qty":"number", "current_availability":"percentage",
                              "current_inventory_count":"number", "truncation_reason":"string", "max_grn_ts_ist":"string",
                              "open_sto_qty_cal":"number", "v1_demand_gap":"percentage", "indent_gap":"percentage", "billed_fill_rate":"percentage",
                              "dispatch_fill_rate":"percentage", "grn_fill_rate":"percentage","dn_fill_rate":"percentage", "b2b_fill_rate":"percentage",
                              "stores_with_0_inventory":"number", "stores_with_sales_spike":"number", "stores_with_indent_gap":"number",
                              "stores_with_storage_truncation":"number", "stores_with_fleet_truncation":"number",
                              "stores_with_manpower_truncation":"number", "stores_with_inward_truncation":"number",
                              "stores_with_grn_gap":"number", "merch_page_impr":"number","merch_page_atc":"percentage","cat_grid_page_impr":"number",
                              "cat_grid_page_atc":"percentage","cnt_be_inventory":"number", "item_quantity":"number",
                              "free_delivery_percentage":"percentage", "freebie_order_percentage":"percentage", "total_od_active_time":"time",
                              "true_fill_rate": "percentage", "search_atc_lost_per_diff" : "percentage", "slot_charge": "currency", "handling_charge": "currency",
                              "convenience_charge": "currency", "night_charge": "currency", "small_cart_charge": "currency", "promo_percentage": "percentage", "promo_orders": "number", "promo_charge": "currency", 
                              "high_priority_users_demand_block_percentage": "percentage",
                              "high_priority_users_ooh_block_percentage": "percentage",
                              "high_priority_users_manual_block_percentage": "percentage", "high_priority_users_percentage": "percentage",
                              "union_order_count": "number",
                              "union_free_delivery_percentage": "percentage",
                              "union_percentage_orders_delivered_in_10mins": "percentage",
                              "union_billed_to_assigned": "percentage",
                              "union_surge_shown_carts_percentage": "percentage",
                              "union_surge_paid_carts_percentage": "percentage",
                              "union_batched_orders": "percentage",
                              "union_rain_order_percentage": "percentage",
                              "union_percentage_orders_delivered_in_15mins": "percentage",
                              "union_percentage_orders_delivered_in_30mins": "percentage",
                              "union_cancellation_percentage": "percentage",
                              "union_checkout_to_picker_assigned": "percentage",

                              "unique_order_ids" : "number",
                    }
SENSITIVE_METRICS: List[str] = ["AOV", "GMV"]
METRICS_CALC_MAPPING: dict = {
    "gmv": "SUM(total_cost) as gmv",
    "order_count": "COUNT(DISTINCT(cart_id)) as order_count",
    "total_orders": "COUNT((id)) as total_orders",
    "surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN surge_amount > 0 THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) as surge_shown_carts_percentage",
    "surge_paid_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN checkout_slot_charge > 0 THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) as surge_paid_carts_percentage",
    "rain_surge_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN surge_type = 'rain_surge' THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) as rain_surge_carts_percentage",
    "aov": "SUM(total_cost)/COUNT(DISTINCT(cart_id)) as aov",
    "avg_item_count": "SUM(item_count)/COUNT((id)) as avg_item_count",
    "transacting_users_count": "COUNT(DISTINCT customer_id) as transacting_users_count",
    "cancellation_percentage": "100 * SUM (CASE WHEN current_status = 'CANCELLED' THEN 1 ELSE 0 END) / COUNT(id) as cancellation_percentage",
    "total_items_sold": "SUM(total_items_quantity) as total_items_sold",
    #"new_transacting_users_count": "COUNT (DISTINCT CASE WHEN is_first_order_completed = false THEN customer_id ELSE -1 END)-1 as new_transacting_users_count",
    "delivery_cost": "sum(delivery_cost)*1.0/COUNT(DISTINCT(cart_id)) as delivery_cost",
    "percentage_orders_delivered_in_10mins": "100 * SUM(CASE WHEN (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 600000) then 1 else 0 end) / SUM(CASE WHEN current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as percentage_order_delivered_in_10mins",
    "percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as percentage_order_delivered_in_15mins",
    "percentage_orders_delivered_in_30mins": "100 * SUM(CASE WHEN (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 1800000) then 1 else 0 end) / SUM(CASE WHEN current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as percentage_order_delivered_in_30mins",
    "percentage_orders_arrived_in_10mins": "100 * SUM(CASE WHEN (reached_doorstep_timestamp - insert_timestamp) <= 600000 then 1 else 0 end) / count(id) as percentage_order_arrived_in_10mins",
    "percentage_orders_arrived_in_15mins": "100 * SUM(CASE WHEN (reached_doorstep_timestamp - insert_timestamp) <= 900000 then 1 else 0 end) / count(id) as percentage_order_arrived_in_15mins",
    "checkout_to_picker_assigned": "100 * SUM(CASE WHEN (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / count(id) as percentage_checkout_to_picker_assigned",
    "billed_to_assigned": "100 * SUM(CASE WHEN (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / count(id) as percentage_order_assigned_before_billing",
    "rain_order_percentage": "100 * SUM(CASE WHEN serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / count(id) as percentage_rain_order",
    # "platform_cost": "sum(delivery_cost+slot_charge+handling_charge+convenience_charge+night_charge+small_cart_charge)*1.0/COUNT(DISTINCT(cart_id)) as platform_cost",\
    "platform_cost": "sum(delivery_cost+slot_charge+handling_charge+convenience_charge+night_charge+small_cart_charge)*1.0/(COUNT(DISTINCT(cart_id)) -1) AS platform_cost",
    "batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN is_batched_order = true THEN id ELSE null END) ) / COUNT(id) as batched_orders",
    
    # Assortment type based metrics - Express (including null)
    "express_order_count": "COUNT(DISTINCT(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN cart_id ELSE null END)) as express_order_count",
    "express_free_delivery_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') AND delivery_cost = 0 THEN cart_id ELSE null END) / DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN cart_id ELSE null END)) AS express_free_delivery_percentage",
    "express_percentage_orders_delivered_in_10mins": "100 * SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 600000) then 1 else 0 end) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as express_percentage_orders_delivered_in_10mins",
    "express_billed_to_assigned": "100 * SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN 1 ELSE 0 END) as express_billed_to_assigned",
    "express_surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') AND surge_amount > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN cart_id ELSE null END) as express_surge_shown_carts_percentage",
    "express_surge_paid_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') AND checkout_slot_charge > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN cart_id ELSE null END) as express_surge_paid_carts_percentage",
    "express_batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') AND is_batched_order = true THEN id ELSE null END) ) / COUNT(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN id ELSE null END) as express_batched_orders",
    "express_rain_order_percentage": "100 * SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN 1 ELSE 0 END) as express_rain_order_percentage",
    "express_percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as express_percentage_orders_delivered_in_15mins",
    "express_percentage_orders_delivered_in_30mins": "100 * SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 1800000) then 1 else 0 end) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as express_percentage_orders_delivered_in_30mins",
    "express_cancellation_percentage": "100 * SUM (CASE WHEN assortment_type IN ('express', NULL, 'null') AND current_status = 'CANCELLED' THEN 1 ELSE 0 END) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN 1 ELSE 0 END) as express_cancellation_percentage",
    "express_checkout_to_picker_assigned": "100 * SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') AND (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / SUM(CASE WHEN assortment_type IN ('express', NULL, 'null') THEN 1 ELSE 0 END) as express_checkout_to_picker_assigned",

    # Assortment type based metrics - Longtail
    "longtail_order_count": "COUNT(DISTINCT(CASE WHEN assortment_type = 'longtail' THEN cart_id ELSE null END)) as longtail_order_count",
    "longtail_free_delivery_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' AND delivery_cost = 0 THEN cart_id ELSE null END) / DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' THEN cart_id ELSE null END)) AS longtail_free_delivery_percentage",
    "longtail_percentage_orders_delivered_in_10mins": "100 * SUM(CASE WHEN assortment_type = 'longtail' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 600000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'longtail' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as longtail_percentage_orders_delivered_in_10mins",
    "longtail_billed_to_assigned": "100 * SUM(CASE WHEN assortment_type = 'longtail' AND (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'longtail' THEN 1 ELSE 0 END) as longtail_billed_to_assigned",
    "longtail_surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' AND surge_amount > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' THEN cart_id ELSE null END) as longtail_surge_shown_carts_percentage",
    "longtail_surge_paid_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' AND checkout_slot_charge > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' THEN cart_id ELSE null END) as longtail_surge_paid_carts_percentage",
    "longtail_batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'longtail' AND is_batched_order = true THEN id ELSE null END) ) / COUNT(CASE WHEN assortment_type = 'longtail' THEN id ELSE null END) as longtail_batched_orders",
    "longtail_rain_order_percentage": "100 * SUM(CASE WHEN assortment_type = 'longtail' AND serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'longtail' THEN 1 ELSE 0 END) as longtail_rain_order_percentage",
    "longtail_percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN assortment_type = 'longtail' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'longtail' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as longtail_percentage_orders_delivered_in_15mins",
    "longtail_percentage_orders_delivered_in_30mins": "100 * SUM(CASE WHEN assortment_type = 'longtail' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 1800000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'longtail' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as longtail_percentage_orders_delivered_in_30mins",
    "longtail_cancellation_percentage": "100 * SUM (CASE WHEN assortment_type = 'longtail' AND current_status = 'CANCELLED' THEN 1 ELSE 0 END) / SUM(CASE WHEN assortment_type = 'longtail' THEN 1 ELSE 0 END) as longtail_cancellation_percentage",
    "longtail_checkout_to_picker_assigned": "100 * SUM(CASE WHEN assortment_type = 'longtail' AND (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'longtail' THEN 1 ELSE 0 END) as longtail_checkout_to_picker_assigned",

    # Assortment type based metrics - Super Longtail
    "super_longtail_order_count": "COUNT(DISTINCT(CASE WHEN assortment_type = 'super_longtail' THEN cart_id ELSE null END)) as super_longtail_order_count",
    "super_longtail_free_delivery_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' AND delivery_cost = 0 THEN cart_id ELSE null END) / DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' THEN cart_id ELSE null END)) AS super_longtail_free_delivery_percentage",
    "super_longtail_percentage_orders_delivered_in_10mins": "100 * SUM(CASE WHEN assortment_type = 'super_longtail' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 600000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'super_longtail' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as super_longtail_percentage_orders_delivered_in_10mins",
    "super_longtail_billed_to_assigned": "100 * SUM(CASE WHEN assortment_type = 'super_longtail' AND (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'super_longtail' THEN 1 ELSE 0 END) as super_longtail_billed_to_assigned",
    "super_longtail_surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' AND surge_amount > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' THEN cart_id ELSE null END) as super_longtail_surge_shown_carts_percentage",
    "super_longtail_surge_paid_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' AND checkout_slot_charge > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' THEN cart_id ELSE null END) as super_longtail_surge_paid_carts_percentage",
    "super_longtail_batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'super_longtail' AND is_batched_order = true THEN id ELSE null END) ) / COUNT(CASE WHEN assortment_type = 'super_longtail' THEN id ELSE null END) as super_longtail_batched_orders",
    "super_longtail_rain_order_percentage": "100 * SUM(CASE WHEN assortment_type = 'super_longtail' AND serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'super_longtail' THEN 1 ELSE 0 END) as super_longtail_rain_order_percentage",
    "super_longtail_percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN assortment_type = 'super_longtail' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'super_longtail' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as super_longtail_percentage_orders_delivered_in_15mins",
    "super_longtail_percentage_orders_delivered_in_30mins": "100 * SUM(CASE WHEN assortment_type = 'super_longtail' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 1800000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'super_longtail' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as super_longtail_percentage_orders_delivered_in_30mins",
    "super_longtail_cancellation_percentage": "100 * SUM (CASE WHEN assortment_type = 'super_longtail' AND current_status = 'CANCELLED' THEN 1 ELSE 0 END) / SUM(CASE WHEN assortment_type = 'super_longtail' THEN 1 ELSE 0 END) as super_longtail_cancellation_percentage",
    "super_longtail_checkout_to_picker_assigned": "100 * SUM(CASE WHEN assortment_type = 'super_longtail' AND (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'super_longtail' THEN 1 ELSE 0 END) as super_longtail_checkout_to_picker_assigned",

    # Assortment type based metrics - Unicorn
    "unicorn_order_count": "COUNT(DISTINCT(CASE WHEN assortment_type = 'unicorn' THEN cart_id ELSE null END)) as unicorn_order_count",
    "unicorn_free_delivery_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' AND delivery_cost = 0 THEN cart_id ELSE null END) / DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' THEN cart_id ELSE null END)) AS unicorn_free_delivery_percentage",
    "unicorn_percentage_orders_delivered_in_10mins": "100 * SUM(CASE WHEN assortment_type = 'unicorn' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 600000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'unicorn' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as unicorn_percentage_orders_delivered_in_10mins",
    "unicorn_billed_to_assigned": "100 * SUM(CASE WHEN assortment_type = 'unicorn' AND (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'unicorn' THEN 1 ELSE 0 END) as unicorn_billed_to_assigned",
    "unicorn_surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' AND surge_amount > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' THEN cart_id ELSE null END) as unicorn_surge_shown_carts_percentage",
    "unicorn_surge_paid_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' AND checkout_slot_charge > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' THEN cart_id ELSE null END) as unicorn_surge_paid_carts_percentage",
    "unicorn_batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN assortment_type = 'unicorn' AND is_batched_order = true THEN id ELSE null END) ) / COUNT(CASE WHEN assortment_type = 'unicorn' THEN id ELSE null END) as unicorn_batched_orders",
    "unicorn_rain_order_percentage": "100 * SUM(CASE WHEN assortment_type = 'unicorn' AND serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'unicorn' THEN 1 ELSE 0 END) as unicorn_rain_order_percentage",
    "unicorn_percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN assortment_type = 'unicorn' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'unicorn' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as unicorn_percentage_orders_delivered_in_15mins",
    "unicorn_percentage_orders_delivered_in_30mins": "100 * SUM(CASE WHEN assortment_type = 'unicorn' AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 1800000) then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'unicorn' AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as unicorn_percentage_orders_delivered_in_30mins",
    "unicorn_cancellation_percentage": "100 * SUM (CASE WHEN assortment_type = 'unicorn' AND current_status = 'CANCELLED' THEN 1 ELSE 0 END) / SUM(CASE WHEN assortment_type = 'unicorn' THEN 1 ELSE 0 END) as unicorn_cancellation_percentage",
    "unicorn_checkout_to_picker_assigned": "100 * SUM(CASE WHEN assortment_type = 'unicorn' AND (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / SUM(CASE WHEN assortment_type = 'unicorn' THEN 1 ELSE 0 END) as unicorn_checkout_to_picker_assigned",
    
    # Union order metrics
    "union_order_count": "COUNT(DISTINCT(CASE WHEN is_union_order = true THEN cart_id ELSE null END)) as union_order_count",
    "union_free_delivery_percentage": "100 * (DISTINCTCOUNT(CASE WHEN is_union_order = true AND delivery_cost = 0 THEN cart_id ELSE null END) / DISTINCTCOUNT(CASE WHEN is_union_order = true THEN cart_id ELSE null END)) AS union_free_delivery_percentage",
    "union_percentage_orders_delivered_in_10mins": "100 * SUM(CASE WHEN is_union_order = true AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 600000) then 1 else 0 end) / SUM(CASE WHEN is_union_order = true AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as union_percentage_orders_delivered_in_10mins",
    "union_billed_to_assigned": "100 * SUM(CASE WHEN is_union_order = true AND (order_billed_timestamp - rider_assigned_timestamp) >= 0 then 1 else 0 end) / SUM(CASE WHEN is_union_order = true THEN 1 ELSE 0 END) as union_billed_to_assigned",
    "union_surge_shown_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN is_union_order = true AND surge_amount > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN is_union_order = true THEN cart_id ELSE null END) as union_surge_shown_carts_percentage",
    "union_surge_paid_carts_percentage": "100 * (DISTINCTCOUNT(CASE WHEN is_union_order = true AND checkout_slot_charge > 0 THEN cart_id ELSE null END) ) / DISTINCTCOUNT(CASE WHEN is_union_order = true THEN cart_id ELSE null END) as union_surge_paid_carts_percentage",
    "union_batched_orders": "100 * (DISTINCTCOUNT(CASE WHEN is_union_order = true AND is_batched_order = true THEN id ELSE null END) ) / COUNT(CASE WHEN is_union_order = true THEN id ELSE null END) as union_batched_orders",
    "union_rain_order_percentage": "100 * SUM(CASE WHEN is_union_order = true AND serviceability_reason = 'DISRUPTION_RAINS' then 1 else 0 end) / SUM(CASE WHEN is_union_order = true THEN 1 ELSE 0 END) as union_rain_order_percentage",
    "union_percentage_orders_delivered_in_15mins": "100 * SUM(CASE WHEN is_union_order = true AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 900000) then 1 else 0 end) / SUM(CASE WHEN is_union_order = true AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as union_percentage_orders_delivered_in_15mins",
    "union_percentage_orders_delivered_in_30mins": "100 * SUM(CASE WHEN is_union_order = true AND (current_status = 'DELIVERED') and (delivery_timestamp is not NULL) and ((delivery_timestamp - insert_timestamp) <= 1800000) then 1 else 0 end) / SUM(CASE WHEN is_union_order = true AND current_status = 'DELIVERED' AND delivery_timestamp IS NOT NULL THEN 1 ELSE 0 END) as union_percentage_orders_delivered_in_30mins",
    "union_cancellation_percentage": "100 * SUM (CASE WHEN is_union_order = true AND current_status = 'CANCELLED' THEN 1 ELSE 0 END) / SUM(CASE WHEN is_union_order = true THEN 1 ELSE 0 END) as union_cancellation_percentage",
    "union_checkout_to_picker_assigned": "100 * SUM(CASE WHEN is_union_order = true AND (picker_assigned_timestamp - picker_assignment_queued_time) <= 10000 then 1 else 0 end) / SUM(CASE WHEN is_union_order = true THEN 1 ELSE 0 END) as union_checkout_to_picker_assigned",
    
    "percent_instore_sla_less_than_150_sec": "100 * SUM(CASE WHEN ((order_billed_timestamp - picker_assignment_queued_time) <= 150000) THEN 1 ELSE 0 END) / (COUNT((id)) -1) AS percent_instore_sla_less_than_150_sec",
    "ppi_in_seconds": "SUM((CASE WHEN (pick_completion_time < 0 OR picking_start_time < 0) THEN 0 ELSE pick_completion_time - picking_start_time END)/1000) / SUM(((total_items_quantity - jsonextractscalar(paas_metrics, '$.quantity', 'INT', 0)) + jsonextractscalar(paas_metrics, '$.sku', 'INT', 0))) as ppi_in_seconds",
    "picker_surge_orders": "100 * (DISTINCTCOUNT( CASE WHEN surge_type = 'mec_picker_surge' THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) AS picker_surge_orders",
    "rider_plus_picker_surge_orders": "100 * (DISTINCTCOUNT( CASE WHEN surge_type = 'mec_fe_surge_and_mec_picker_surge' THEN cart_id ELSE null END) ) / COUNT(DISTINCT(cart_id)) AS both_surge_orders",
    "sm_fill_rate": "100 * ((SUM( case when (sm_fill_rate = 100 AND current_status != 'APPROVED') then 1 else 0 end)))/(SUM( CASE WHEN current_status != 'APPROVED' then 1 else 0 end)) as sm_fill_rate",
    "total_procured_items_quantity": "SUM(total_procured_items_quantity) / count(id) as total_procured_items_quantity",
    "total_active_time": "SUM(total_active_time) AS total_active_time_in_seconds",
    "picker_active_time_percentage": "100 * (SUM(picker_active_time) / SUM(total_active_time)) AS picker_active_time_percentage",
    "putter_active_time_percentage": "100 * (SUM(putter_active_time) / SUM(total_active_time)) AS putter_active_time_percentage",
    "auditor_active_time_percentage": "100 * (SUM(auditor_active_time) / SUM(total_active_time)) AS auditor_active_time_percentage",
    "fnv_active_time_percentage": "100 * (SUM(fnv_active_time) / SUM(total_active_time)) AS fnv_active_time_percentage",
    "items_putaway_per_hour": "(SUM(packaged_putaway+milk_putaway+perishables_others_putaway) / (SUM(putter_active_time)/3600)) AS items_putaway_per_hour",
    "orders_picked_per_hour": "(SUM(total_orders) / (SUM(picker_active_time)/3600)) AS orders_picked_per_hour",
    "new_manpower": "SUM(new_manpower) AS new_manpower",
    "total_complaints": "COUNT(1) as total_complaints",
    "picker_assigned_to_picking_start": "100 * SUM(CASE WHEN (picking_start_time - picker_assigned_timestamp) <= 5000 then 1 else 0 end) / count(id) as percentage_picker_assigned_to_picking_start",
    "total_cart_instances": "LASTWITHTIME(total_carts, window_end_epoch, 'LONG') AS total_cart_instances",
    "surge_cart_instances": "LASTWITHTIME(total_surge_carts, window_end_epoch, 'LONG') AS surge_cart_instances",
    "rider_surge_cart_instances": "LASTWITHTIME(rider_surge_carts, window_end_epoch, 'LONG') AS rider_surge_cart_instances",
    "picker_surge_cart_instances": "LASTWITHTIME(picker_surge_carts, window_end_epoch, 'LONG') AS picker_surge_cart_instances",
    "rain_surge_cart_instances": "LASTWITHTIME(rain_surge_carts, window_end_epoch, 'LONG') AS rain_surge_cart_instances",
    "free_delivery_percentage": "100 * (DISTINCTCOUNT(CASE WHEN delivery_cost = 0 THEN cart_id ELSE null END) / COUNT(DISTINCT cart_id)) AS free_delivery_percentage",
    "total_od_active_time": "SUM(total_od_active_time) AS total_od_active_time_in_seconds",
    "true_fill_rate": "100.00 * (SUM( case when (sm_fill_rate = 100 AND current_status != 'APPROVED' AND id NOT IN (SELECT order_id from fact_order_complaints where dt in (%(required_dates)s) and complaint_type in ('item_missing', 'wrong_items_received') limit 100000)) then 1 else 0 end ))/(SUM( CASE WHEN current_status != 'APPROVED' then 1 else 0 end)) as true_fill_rate",
    "slot_charge": "sum(slot_charge)*1.0/(COUNT(DISTINCT(cart_id)) -1) AS slot_charge",
    "handling_charge": "sum(handling_charge)*1.0/(COUNT(DISTINCT(cart_id)) -1) AS handling_charge",
    "convenience_charge": "sum(convenience_charge)*1.0/(COUNT(DISTINCT(cart_id)) -1) AS convenience_charge",
    "night_charge": "sum(night_charge)*1.0/(COUNT(DISTINCT(cart_id)) -1) AS night_charge",
    "small_cart_charge": "sum(small_cart_charge)*1.0/COUNT(DISTINCT(cart_id)) AS small_cart_charge",
    "promo_percentage": "100.0 * DISTINCTCOUNT(CASE WHEN discount > 0 THEN cart_id ELSE null END) / COUNT(DISTINCT(cart_id)) AS promo_percentage",
    "promo_orders": "SUM(CASE WHEN discount > 0 THEN 1 ELSE 0 END) AS promo_orders",
    "promo_charge": "SUM(CASE WHEN discount > 0 THEN discount ELSE 0 END)*1.0 / COUNT(DISTINCT(cart_id)) AS promo_charge",
    "new_transacting_users_count": "DISTINCTCOUNT(CASE WHEN order_count_life_time = 0 THEN customer_id ELSE null END) AS new_transacting_users_count",
}
CRITICAL_HAVING_CONDITION: dict = {
    "ppi_in_seconds": "ppi_in_seconds > 20",
    "sm_fill_rate": "sm_fill_rate < 99.5",
    "billed_to_assigned": "1=1"
}
WAREHOUSE_METRICS_CALC_MAPPING: dict = {
    "created_qty": "SUM(CASE WHEN event_name='ORDER_CREATED' THEN sku_quantity ELSE 0 END) AS created_qty",
    "cancelled_qty": "SUM(CASE WHEN event_name='ORDER_CANCELLED' THEN sku_quantity ELSE 0 END) AS cancelled_qty",
    "line_items": "SUM(CASE WHEN event_name='ORDER_CREATED' THEN sku_count ELSE 0 END) AS line_items",
    
    "irt": "(SUM(CASE WHEN event_name='PICK_LIST_CREATED' AND picklist_type='IRT_PICK_LIST' THEN sku_quantity ELSE 0 END)/SUM(CASE WHEN event_name IN ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND') AND picklist_type NOT IN ('IRT_PICK_LIST') THEN sku_quantity ELSE 0 END))*100 AS irt_qty",
    "raise_audit": "(SUM(CASE WHEN event_name='PICK_LIST_ITEM_LOCATION_NOT_FOUND' THEN sku_count ELSE 0 END)/SUM(CASE WHEN event_name IN ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND') THEN sku_count ELSE 0 END))*100 AS raise_audit",
    
    "wave_created_qty": "SUM(CASE WHEN event_name='PICK_LIST_CREATED' AND picklist_type<>'IRT_PICK_LIST' THEN sku_quantity ELSE 0 END) AS wave_created_qty",
    "picked_qty": "SUM(CASE WHEN event_name IN ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND') THEN sku_quantity ELSE 0 END) AS picked_qty",
    "packaging_qty": "SUM(CASE WHEN event_name='PACKAGING_ACTIVITY_COMPLETED' THEN sku_quantity ELSE 0  END) AS packaging_qty",
    "sorting_qty": "SUM(CASE WHEN event_name='CONTAINER_SORTED' THEN sku_quantity ELSE 0 END) AS sorting_qty",
    "dispatch_qty": "SUM(CASE WHEN event_name='CONTAINER_DISPATCHED' THEN sku_quantity ELSE 0 END) AS dispatch_qty",

    "picking_pendency": """SUM(CASE WHEN event_name = 'PICK_LIST_CREATED' AND picklist_type NOT IN ('IRT_PICK_LIST') THEN sku_quantity ELSE 0 END) 
        - SUM(CASE WHEN event_name IN ('PICK_LIST_CANCELLED', 'PICK_LIST_PARTIALLY_CANCELLED') AND picklist_type NOT IN ('IRT_PICK_LIST') THEN sku_quantity ELSE 0 END) 
        - SUM(CASE WHEN event_name IN ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND') THEN sku_quantity ELSE 0 END) AS picking_pendency""",
    "packaging_pendency": """SUM(CASE WHEN event_name = 'PICK_LIST_COMPLETED' THEN sku_quantity ELSE 0 END) 
        - SUM(CASE WHEN event_name = 'PACKAGING_ACTIVITY_COMPLETED' THEN sku_quantity ELSE 0 END) AS packaging_pendency""",
    # "sorting_pendency": """SUM(CASE WHEN event_name = 'PACKAGING_ACTIVITY_COMPLETED' THEN sku_quantity ELSE 0 END) 
    #     - SUM(CASE WHEN event_name = 'CONTAINER_SORTED' THEN sku_quantity ELSE 0 END) AS sorting_pendency""",
    # "dispatch_pendency": """SUM(CASE WHEN event_name = 'CONTAINER_SORTED' THEN sku_quantity ELSE 0 END) 
    #     - SUM(CASE WHEN event_name = 'CONTAINER_DISPATCHED' THEN sku_quantity ELSE 0 END) AS dispatch_pendency""",

    "be_weighted_availability": "LASTWITHTIME(be_weighted_availability, updated_at, 'DOUBLE') AS be_availability",
    "fe_weighted_availability": "LASTWITHTIME(fe_weighted_availability, updated_at, 'DOUBLE') AS fe_availability",
    "fe_weighted_availability_post_sto": "LASTWITHTIME(fe_weighted_availability_post_sto, updated_at, 'DOUBLE') AS fe_availability_post_sto",

    "putters": "LASTWITHTIME(putters, updated_at, 'INT') AS putters",
    "pickers": "LASTWITHTIME(pickers, updated_at, 'INT') AS pickers",
    "packers": "LASTWITHTIME(packers, updated_at, 'INT') AS packers",
    "sorters": "LASTWITHTIME(sorters, updated_at, 'INT') AS sorters",
    "auditors": "LASTWITHTIME(auditors, updated_at, 'INT') AS auditors",
    
    "on_time_picked_qty": "SUM(CASE WHEN event_name IN ('PICK_LIST_PICK_ITEM', 'PICK_LIST_ITEM_NOT_FOUND') AND dispatch_time - insert_ts >= 5400 THEN sku_quantity ELSE 0 END) AS on_time_picked_qty",
    "on_time_packed_qty": "SUM(CASE WHEN event_name='PACKAGING_ACTIVITY_COMPLETED' AND dispatch_time - insert_ts >= 3600 THEN sku_quantity ELSE 0 END) AS on_time_packed_qty",
    "on_time_sorted_qty": "SUM(CASE WHEN event_name='CONTAINER_SORTED' AND dispatch_time - insert_ts >= 1800 THEN sku_quantity ELSE 0 END) AS on_time_sorted_qty",
    "on_time_dispatch_qty": "SUM(CASE WHEN event_name='CONTAINER_DISPATCHED' AND dispatch_time - insert_ts >= -900 THEN sku_quantity ELSE 0 END) AS on_time_dispatch_qty",
}
INSIGHTS_CONVERSION_METRICS_MAPPING : dict = {
    "overall_conv": "100 * (SUM(transacting_users) / SUM(dau)) AS overall_conv",
    "dau_to_atc_conv": "100 * (SUM(atc) / SUM(dau)) AS dau_to_atc_conv",
    "cv_to_transact_conv": "100 * (SUM(transacting_users) / SUM(cart_visit)) AS cv_to_transact_conv",
    "demand_based_cart_blocks_conversion_percentage": "100 * (SUM(db_block_users) / SUM(dau)) AS demand_based_cart_blocks_conversion_percentage",
    "serviceability_based_cart_blocks_conversion_percentage": "100 * (SUM(sb_block_users) / SUM(dau)) AS serviceability_based_cart_blocks_conversion_percentage"
}
CONVERSION_METRICS_MAPPING : dict = {
    "dau_to_atc_conv": "100 * (SUM(atc) / SUM(dau)) AS dau_to_atc_conv",
    "cv_to_transact_conv": "100 * (SUM(transacting_users) / SUM(cart_visit)) AS cv_to_transact_conv"
}
METRICS_CALC_MAPPING_PTYPE : dict = {
    "dau_to_atc_conv": "100 * (SUM(atc) / SUM(dau)) AS dau_to_atc_conv",
    "search_conversion": "100 * (SUM(search_atc) / SUM(search)) AS search_conversion",
}
MERCHANDISING_METRICS_CALC_MAPPING_PTYPE : dict = {
    "dau_to_atc_conv": "100 * (SUM(atc) / SUM(dau)) AS dau_to_atc_conv",
    "search_conversion": "100 * (SUM(search_atc) / SUM(search)) AS search_conversion",
    "merch_page_atc": "100 * (SUM(merch_page_impr_atc) / SUM(merch_page_impr)) AS merch_page_atc",
    "cat_grid_page_atc": "100 * (SUM(cat_grid_page_impr_atc) / SUM(cat_grid_page_impr)) AS cat_grid_page_atc",
}

MERCHANDISING_METRICS_CALC_MAPPING_CATEGORY : dict = {
    "search_conversion": "100 * (SUM(search_atc) / SUM(search)) AS search_conversion",
}

CONVERSION_COLUMNS_TO_QUERY: List = ["dau","atc","cart_visit","transacting_users"]
INSIGHTS_CONVERSION_COLUMNS_TO_QUERY: List = ["dau","atc","cart_visit","transacting_users","sb_block_users","db_block_users"]
INSIGHTS_CONTRIBUTION_COLUMNS_TO_QUERY: List = ["conv_per_adj_diff", "block_atc_lost_per_diff", "dslo_atc_lost_per_diff", "misc_atc_lost_per_diff",
                                                "block_c2co_lost_per_diff", "dslo_c2co_lost_per_diff", "misc_c2co_lost_per_diff", "dslo_surge_lost_per_diff", "search_atc_lost_per_diff"]
INSIGHTS_CONTRIBUTION_METRICS: List = ["conv_per_contribution", "block_atc_lost_per_diff", "dslo_atc_lost_per_diff", "misc_atc_lost_per_diff",
                                        "block_c2co_lost_per_diff", "dslo_c2co_lost_per_diff", "misc_c2co_lost_per_diff", "dslo_surge_lost_per_diff", "search_atc_lost_per_diff"]
PTYPE_COLUMNS_TO_QUERY: List = ["dau","atc","search","search_atc","availability"]
PTYPE_COLUMNS_FOR_PRODUCT_TO_QUERY: List = ["dau","atc","search","search_atc","availability","cnt_inventory","merch_page_impr","merch_page_impr_atc","cat_grid_page_impr","cat_grid_page_impr_atc","cnt_be_inventory"]
CATEGORY_COLUMNS_TO_QUERY: List = [ "dau", "atc", "search", "search_atc", "availability", "cnt_inventory" ]

ITEM_COLUMNS_TO_QUERY: dict = {
    "city": ["ars_created_ts_ist","item_quantity","item_quantity_t1","stores_with_0_inventory","stores_with_sales_spike","stores_with_indent_gap","stores_with_storage_truncation","stores_with_fleet_truncation","stores_with_manpower_truncation","stores_with_inward_truncation","stores_with_grn_gap"],
    "store": ["top_ptype_item_rank","ars_created_ts_ist","item_quantity_t0","item_quantity_t1","cycle_cpd_sum","initial_demand_qty","fe_inventory_qty","be_inventory_qty","v1_indent","indent_raised_qty","total_truncation_qty","billed_qty","intransit_qty","grn_qty","dn_qty","b2b_qty","current_availability","current_inventory_count","truncation_reason","max_grn_ts_ist","open_sto_qty_cal","v1_demand_gap","indent_gap","billed_fill_rate","dispatch_fill_rate","grn_fill_rate","dn_fill_rate","b2b_fill_rate"]
}

METRICS_TABLES_MAPPING: dict = {
    "funnel_hourly": {
        "city": "hourly_city_conversion_v2",
        "store": "hourly_merchant_conversion_v2"
    },
    "funnel_daily": {
        "city": "daily_city_conversion_v2",
        "store": "daily_merchant_conversion_v2"
    }
}
METRICS_TABLES_MAPPING_PTYPE: dict = {
    "ptype_hourly": {
        "city": "hourly_city_ptype_metrics",
        "store": "hourly_merchant_ptype_metrics"
    },
    "ptype_daily": {
        "city": "daily_city_ptype_metrics",
        "store": "daily_merchant_ptype_metrics"
    }
}

METRICS_TABLES_MAPPING_CATEGORY: dict = {
    "category_hourly": {
        "city": "hourly_city_l0category_metrics",
        "store": "hourly_merchant_l0category_metrics"
    },
    "category_daily": {
        "city": "daily_city_l0category_metrics",
        "store": "daily_merchant_l0category_metrics"
    }
}

PRODUCT_METRICS_CALC_MAPPING : dict = {
    "gmv" : "SUM(total_item_cost) as gmv",
    "order_count" : "COUNT(DISTINCT(cart_id)) as order_count",
    "total_items_sold" : "SUM(item_quantity) as total_items_sold",
    "new_transacting_users_count" : "DISTINCTCOUNT(CASE WHEN order_count_life_time = 0 THEN customer_id ELSE null END) AS new_transacting_users_count",
    "transacting_users_count" : "COUNT(DISTINCT customer_id) as transacting_users_count",
    "canceled_quantity" : "SUM(CASE WHEN current_status = 'CANCELLED' THEN item_quantity ELSE 0 END) as canceled_quantity",
    "cancellation_percentage": "100 * SUM (CASE WHEN current_status = 'CANCELLED' THEN 1 ELSE 0 END) / COUNT(cart_id) as cancellation_percentage",
    "asp" : "AVG(total_item_cost / item_quantity) as asp",
    "ipc" : "SUM(item_quantity * 1.00)/COUNT(DISTINCT cart_id) AS ipc",
    "aov" : "SUM(total_item_cost)/COUNT(DISTINCT cart_id) as aov",
    "unique_carts" : "COUNT(DISTINCT cart_id) as unique_carts",
    "aov_contribution": "SUM(total_item_cost) as aov_contribution",
    "freebie_order_percentage": "(SUM(CASE WHEN product_type IN ('Free Gift', 'Special Offer') THEN 1 ELSE 0 END) / COUNT(DISTINCT cart_id)) * 100 AS freebie_order_percentage",
    "unique_order_ids" : "COUNT(id) as unique_order_ids",
}

PRODUCT_FIELD_FRONTEND_TO_BACKEND_MAPPING: dict = {
    "l0_category" : "l0_category",
    "l1_category" : "l1_category",
    "l2_category" : "l2_category",
    "ptype" : "product_type",
    "pname" : "product_name",
    "brand" : "brand_name",
    "pid" : "item_product_id"
}
ITEM_FIELD_FRONTEND_TO_BACKEND_MAPPING: dict = {
    "l0_category" : "l0_category",
    "l1_category" : "l1_category",
    "l2_category" : "l2_category",
    "ptype" : "p_type",
    "item_name" : "item_name",
    "brand" : "brand_name",
    "item_id" : "item_id"
}
SURGE_SEEN_METRICS: List = ["total_cart_instances","surge_cart_instances","rider_surge_cart_instances","picker_surge_cart_instances","rain_surge_cart_instances"]
SURGE_SEEN_METRICS_HOURLY: List = ["total_cart_instances","surge_cart_instances"]

CATEGORY_PAGE_NAME_CONVERT_MAPPING = {
    'Unique Carts': "cart_pen",
    'AOV Contribution': "aov_contribution"
}

WEIGHTED_AVERAGE_METRICS = [
    "DAU Conversion Percentage",
    "Demand Based Block %",
    "Unserviceable DAU %",
    "Express OOH Based Block %",
    "Express Manual Based Block %",
    "AOV",
    "Surge Checkouts %",
    "Rain Order %"
]

SKIP_LONGTAIL_MERCHANT_METRICS = [
    "Daily Active Users",
    "DAU Conversion Percentage",
    "Demand Based Block %",
    "Unserviceable DAU %",
    "Express OOH Based Block %",
    "Express Manual Based Block %",
    "Surge Checkouts %",
    "Rain Order %"
]
