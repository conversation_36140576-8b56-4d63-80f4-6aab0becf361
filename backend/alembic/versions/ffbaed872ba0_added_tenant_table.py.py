"""Added tenant table

Revision ID: ffbaed872ba0
Revises: 6f82cf18870e
Create Date: 2024-11-20 12:56:45.576161

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ffbaed872ba0'
down_revision = '6f82cf18870e'
branch_labels = None
depends_on = None


def upgrade():
    # Create tenant table
    op.create_table(
        'tenant',
        sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('tenant_id_seq'::regclass)"), autoincrement=True, nullable=False),
        sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint('id', name='tenant_pkey'),
        sa.UniqueConstraint('name', name='tenant_name_key'),
        postgresql_ignore_search_path=False
    )
    
    op.create_table(
        'user_tenant_mapping',
        sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column('is_allowed', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True),
        sa.Column('page_group_id', sa.INTEGER(), autoincrement=False, nullable=True),
        sa.ForeignKeyConstraint(['page_group_id'], ['page_group.id'], name='user_tenant_mapping_user_id_fkey'),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenant.id'], name='user_tenant_mapping_tenant_id_fkey'),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_tenant_mapping_page_group_id_fkey'),
        sa.PrimaryKeyConstraint('id', name='user_tenant_mapping_pkey'),
        sa.UniqueConstraint('user_id', 'tenant_id', name='user_tenant_mapping_user_id_tenant_id_key')
    )

    op.add_column('page', sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('page_tenant_id_fkey', 'page', 'tenant', ['tenant_id'], ['id'])
    op.drop_constraint('page_page_name_key', 'page', type_='unique')
    op.create_unique_constraint('page_tenant_name_unique', 'page', ['tenant_id', 'page_name'])

    op.add_column('page_group', sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('page_group_tenant_id_fkey', 'page_group', 'tenant', ['tenant_id'], ['id'])
    op.drop_constraint('page_group_group_name_key', 'page_group', type_='unique')
    op.create_unique_constraint('page_group_tenant_name_unique', 'page_group', ['tenant_id', 'group_name'])

    op.add_column('user_access_mapping', sa.Column('tenant_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('user_access_mapping_tenant_id_fkey', 'user_access_mapping', 'tenant', ['tenant_id'], ['id'])
    op.drop_constraint('user_access_mapping_user_city_store_key', 'user_access_mapping', type_='unique')
    op.create_unique_constraint('user_access_mapping_user_city_store_tenant_key', 'user_access_mapping', ['user_id', 'city_name', 'store_id', 'tenant_id'])

    # Insert initial data into tenant
    op.execute("INSERT INTO tenant (name) VALUES ('Blinkit'), ('Bistro')")

    # Update existing data in tables
    op.execute("UPDATE page SET tenant_id = 1")
    op.execute("UPDATE page_group SET tenant_id = 1")
    op.execute("UPDATE user_access_mapping SET tenant_id = 1")

    # One-time migration to insert into user_tenant_mapping
    op.execute("""
        INSERT INTO user_tenant_mapping (user_id, tenant_id, is_allowed, page_group_id)
        SELECT id, 1 AS tenant_id, is_allowed, page_group_id
        FROM "user"
    """)

    # Insert additional data into page and page_group
    op.execute("""
        INSERT INTO page (page_name, tenant_id) VALUES ('Home', 2)
    """)
    op.execute("""
        INSERT INTO page_group (group_name, page_ids, tenant_id) 
        VALUES ('Default', ARRAY[]::INTEGER[], 2), ('Home', ARRAY[16], 2)
    """)


def downgrade():
    op.drop_constraint('page_tenant_name_unique', 'page', type_='unique')
    op.drop_constraint('page_tenant_id_fkey', 'page', type_='foreignkey')
    op.drop_column('page', 'tenant_id')

    op.drop_constraint('page_group_tenant_name_unique', 'page_group', type_='unique')
    op.drop_constraint('page_group_tenant_id_fkey', 'page_group', type_='foreignkey')
    op.drop_column('page_group', 'tenant_id')

    op.drop_constraint('user_access_mapping_user_city_store_tenant_key', 'user_access_mapping', type_='unique')
    op.create_unique_constraint('user_access_mapping_user_city_store_key', 'user_access_mapping', ['user_id', 'city_name', 'store_id'])
    op.drop_constraint('user_access_mapping_tenant_id_fkey', 'user_access_mapping', type_='foreignkey')
    op.drop_column('user_access_mapping', 'tenant_id')

    op.drop_table('tenant_user')
    op.drop_table('tenant')
