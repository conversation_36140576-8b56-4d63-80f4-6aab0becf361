from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6c7747bc0fef'
down_revision = 'ffbaed872ba0'
branch_labels = None
depends_on = None


def upgrade():
    # Create groups table
    op.create_table(
        'groups',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('type', sa.String(), nullable=False),
        sa.Column('is_global', sa.<PERSON>(), server_default=sa.text('false'), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['created_by'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_groups_id', 'groups', ['id'], unique=False)
    op.create_unique_constraint('unique_group_name_per_user', 'groups', ['name', 'created_by'])

    # Create group_city_store_mapping table
    op.create_table(
        'group_city_store_mapping',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('group_id', sa.Integer(), nullable=False),
        sa.Column('city', sa.String(), nullable=False),
        sa.Column('store', sa.String(), server_default='-1', nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_group_city_store_mapping_id', 'group_city_store_mapping', ['id'], unique=False)
    op.create_unique_constraint('unique_group_city_store', 'group_city_store_mapping', ['group_id', 'city', 'store'])

    # Add new column to user_tenant_mapping
    op.add_column('user_tenant_mapping',
        sa.Column('is_global_group_allowed', sa.Boolean(), server_default='false', nullable=False)
    )


def downgrade():
    # Drop column from user_tenant_mapping
    op.drop_column('user_tenant_mapping', 'is_global_group_allowed')

    # Drop tables
    op.drop_table('group_city_store_mapping')
    op.drop_table('groups')
