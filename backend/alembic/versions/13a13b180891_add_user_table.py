"""add user table

Revision ID: 13a13b180891
Revises:
Create Date: 2022-04-05 12:17:49.265928

"""
from alembic import op
from sqlalchemy.sql import func
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '13a13b180891'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.Column('full_name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('is_allowed', sa.<PERSON>an(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now()),
    sa.<PERSON>onstraint('id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_index(op.f('ix_user_full_name'), 'user', ['full_name'], unique=False)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.drop_index(op.f('ix_user_full_name'), table_name='user')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    # ### end Alembic commands ###
