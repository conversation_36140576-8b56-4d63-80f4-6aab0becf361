"""added model for access mapping

Revision ID: f6c8567da65d
Revises: 13a13b180891
Create Date: 2023-07-25 05:08:18.602522

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f6c8567da65d'
down_revision = '13a13b180891'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    ### please create trigger function to for updated_at manually
    op.create_table('user_access_mapping',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('city_name', sa.String(), server_default='all', nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('is_sensitive', sa.<PERSON>(), server_default=sa.text('false'), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'city_name', 'store_id', name='user_access_mapping_user_city_store_key')
    )
    op.create_index(op.f('ix_user_access_mapping_id'), 'user_access_mapping', ['id'], unique=False)
    op.alter_column('user', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.TIMESTAMP(),
               nullable=False,
               existing_server_default=sa.text('now()'))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user', 'created_at',
               existing_type=sa.TIMESTAMP(),
               type_=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_index(op.f('ix_user_access_mapping_id'), table_name='user_access_mapping')
    op.drop_table('user_access_mapping')
    # ### end Alembic commands ###