"""Added page table

Revision ID: 6f82cf18870e
Revises: f6c8567da65d
Create Date: 2024-09-24 14:37:24.880196

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6f82cf18870e'
down_revision = 'f6c8567da65d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('page',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('page_name', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('page_name')
    )
    op.create_index(op.f('ix_page_id'), 'page', ['id'], unique=False)
    op.create_table('page_group',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('group_name', sa.String(), nullable=False),
    sa.Column('page_ids', sa.ARRAY(sa.Integer()), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('group_name')
    )
    op.create_index(op.f('ix_page_group_id'), 'page_group', ['id'], unique=False)
    op.add_column('user', sa.Column('page_group_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'user', 'page_group', ['page_group_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_column('user', 'page_group_id')
    op.drop_index(op.f('ix_page_group_id'), table_name='page_group')
    op.drop_table('page_group')
    op.drop_index(op.f('ix_page_id'), table_name='page')
    op.drop_table('page')
    # ### end Alembic commands ###