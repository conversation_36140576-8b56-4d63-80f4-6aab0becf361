FROM public.ecr.aws/zomato/python:3.9-slim

# Possible Values : production, test
ARG ENVIRONMENT=production

ENV PYTHONFAULTHANDLER=1 \
  PYTHONUNBUFFERED=1 \
  # pip:
  PIP_NO_CACHE_DIR=off \
  PIP_DISABLE_PIP_VERSION_CHECK=on \
  # poetry:
  POETRY_VERSION=1.7.1 \
  POETRY_NO_INTERACTION=1 \
  POETRY_VIRTUALENVS_CREATE=false \
  POETRY_CACHE_DIR='/var/cache/pypoetry' \
  PATH="$PATH:/root/.local/bin"

RUN apt-get update && \
    apt-get install --no-install-recommends -y \
        curl \
        build-essential \
        gcc \
        g++ \
        && curl -sSL 'https://install.python-poetry.org' | python - \
        && poetry --version \
        && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY pyproject.toml ./

# Generate lock file and install dependencies
RUN poetry lock --no-update && \
    poetry install --no-root $(if [ "$ENVIRONMENT" = 'production' ]; then echo '--only main'; fi)

COPY . .

RUN poetry build && pip install dist/*.whl

WORKDIR /app/src

EXPOSE 8080

CMD ["ddtrace-run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "2"]
