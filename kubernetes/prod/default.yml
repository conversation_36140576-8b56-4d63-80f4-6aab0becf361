secrets:
  authjwt_secret_key: {{jwt_secret_key}}
  PINOT_DB_URL: '{{databases_pinot_pinot_db_url}}'
  GOOGLE_CLIENT_ID: "{{google_client_google_client_id}}"
  GOOGLE_CLIENT_SECRET: "{{google_client_google_client_secret}}"
  OAUTHLIB_RELAX_TOKEN_SCOPE: 1 # To Fix : Warning: <PERSON><PERSON> has changed
  OAUTHLIB_INSECURE_TRANSPORT: 1 # To Fix : Warning: OAuth2 must utilize https
  POSTGRES_HOST: {{ databases_postgres_sonar_sonar_app_writer_host }}
  POSTGRES_USER: {{ databases_postgres_sonar_sonar_app_writer_user }}
  POSTGRES_PASSWORD: {{ databases_postgres_sonar_sonar_app_writer_password }}
  POSTGRES_DB: {{ databases_postgres_sonar_sonar_app_writer_database }}
  # Override it if there is some issue in pinot
  # This will force it to use redis cache if present
  FORCE_REDIS_CACHE: false
  REDIS_HOST_URL: {{ databases_redis_prod_data_viz_host_url }}
  KEY_BUSINESS_METRICS_URL: {{ redash_metrics_mtd_key_business_metrics_url_prod }}
  WTD_KEY_BUSINESS_METRICS_URL: {{ redash_metrics_wtd_key_business_metrics_url_prod }}
  COMPLAINT_IMAGES_URL: {{ complaint_images_complaint_image_url }}
  COMPLAINT_IMAGES_TOKEN: {{ complaint_images_token }}
  AWS_S3_REGION: {{ bad_stocks_aws_s3_region }}
  BAD_STOCK_S3_BUCKET: {{ bad_stocks_bad_stock_s3_bucket }}
  TEST_S3_BUCKET: {{ bad_stocks_test_s3_bucket }}

podAnnotations:
  grofers.io/business-service: data-reporting
  grofers.io/component: sonar-web
  grofers.io/component-role: web
  grofers.io/service: sonar
  grofers.io/team: data-engineering
  grofers.io/tribe: data

backend:
  env:
  - name: DD_AGENT_HOST
    valueFrom: 
      fieldRef:
        fieldPath: status.hostIP
  - name: DD_SERVICE
    value: "sonar"
  - name: DD_ENV
    value: "primary"
  - name: DD_PROFILING_ENABLED
    value: "false"
  - name: DD_TRACE_ENABLED
    value: "true"
  - name: DD_SERVICE_MAPPING
    value: "postgres:postgres-sonar,redis:prod-data-viz-sonar,requests:pinot-sonar"

aws:
  serviceAccountName: blinkit-prod-sonar-prod-eks-role
  serviceAccountArn: arn:aws:iam::************:role/blinkit-prod-sonar-prod-eks-role
