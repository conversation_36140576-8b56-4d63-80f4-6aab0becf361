apiVersion: skaffold/v2beta26
kind: Config
build:
  artifacts:
  - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/sonar/frontend
    context: ./frontend
  - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/sonar/backend
    context: ./backend
  tagPolicy:
    gitCommit:
      variant: AbbrevCommitSha
profiles:
- name: prod
  deploy:
    helm:
      hooks:
        before:
        - host:
            command: ["python","scripts/config-setup.py","--env","prod"]
      releases:
      - name: prod-sonar
        chartPath: chart
        valuesFiles:
        - prod-common.yml
        - prod-config.yml
        artifactOverrides:
          backendImage: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/sonar/backend
          frontendImage: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/sonar/frontend
        wait: true
- name: preprod
  deploy:
    helm:
      hooks:
        before:
        - host:
            command: ["python","scripts/config-setup.py","--env","preprod"]
      releases:
      - name: preprod-sonar
        chartPath: chart
        valuesFiles:
        - preprod-common.yml
        - preprod-config.yml
        # overriding the values here which are changed for preprod
        # it helps us simlify the overall workflow
        setValues:
          replicaCount: 1
          secrets:
            GOOGLE_REDIRECT_URI: https://sonar-preprod.grofer.io/auth/google/callback
            # Overriding to avoid cache conflict
            PROJECT_NAME: sonar_preprod
        artifactOverrides:
          backendImage: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/sonar/backend
          frontendImage: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/sonar/frontend
        wait: true
