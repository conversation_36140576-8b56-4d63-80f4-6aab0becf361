def loginECR(){
    withAWS(region: 'ap-southeast-1', credentials: 'prod-jenkins-aws-username-password') {
        sh(script: 'aws ecr get-login-password | docker login --username AWS --password-stdin 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com')
  }
}
pipeline {
  agent any
  options {
    buildDiscarder(logRotator(numToKeepStr: '15', daysToKeepStr: '5'))
    disableConcurrentBuilds()
    skipStagesAfterUnstable()
  }

  environment {
    VAULT_AUTH_GITHUB_TOKEN = credentials('airflow')
    SLACK_CHANNEL = "#bl-data-deployments"
    MFT_NAMESPACE = env.JOB_NAME.replace("/", "-").toLowerCase()
    PROJECT = env.JOB_NAME.toLowerCase()
  }

  stages {
    stage("Docker Login") {
      steps {
        loginECR()
      }
    }
    
    stage("Ready for deployment to preprod") {
      when {
        anyOf {
          branch 'master'
          branch "preprod-*"
        }
      }
      steps {
        slackSend channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: The preprod build is ready for deployment: ${env.RUN_DISPLAY_URL}"
      }
    }

    stage('Deploy in preprod') {
      when {
        anyOf {
          branch 'master'
          branch "preprod-*"
        }
      }
      steps {
          //  slackSend channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: Preprod deployment FROZEN temporarily. Build: ${env.RUN_DISPLAY_URL}"
          slackSend channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: Deploying master in pre-production. Build: ${env.RUN_DISPLAY_URL}"
          withKubeConfig([credentialsId: 'prod-eks-kubeconfig']) {
            sh 'kubectl config set-context --current --namespace=data'
            sh 'skaffold run --profile preprod'
          }
      }
    }

    stage("Ready for deployment to prod") {
      when {
        branch 'master'
      }
      steps {
        slackSend channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: The master build is ready for prod deployment: ${env.RUN_DISPLAY_URL}"
      }
    }

    stage("Deploy to prod") {
      when {
        beforeInput true
        branch 'master'
      }
      input {
        message "Do you want to proceed for production deployment?"
      }
      steps {
        slackSend channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: Deploying master to production. Build: ${env.RUN_DISPLAY_URL}"
        withKubeConfig([credentialsId: 'prod-eks-kubeconfig']) {
          sh 'kubectl config set-context --current --namespace=data'
          sh 'skaffold run -p prod'
        }
      }
    }
  }

  post {
    success {
      script {
        if (env.BRANCH_NAME == "master") {
          slackSend(color: "good", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} completed successfully. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
        }
      }
    }

    failure {
      script {
        slackSend(color: "danger", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} failed. Please contact data on call. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
      }
    }
  }
}
